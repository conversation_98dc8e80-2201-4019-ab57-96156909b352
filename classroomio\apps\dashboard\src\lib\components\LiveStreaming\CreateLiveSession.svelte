<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { LiveSession, MeetingConfig } from '$lib/utils/types/liveStreaming';
  import { liveSessionService } from '$lib/utils/services/liveStreaming';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Add, 
    Calendar, 
    Time, 
    Video,
    Settings,
    Security,
    Save,
    Close
  } from 'carbon-icons-svelte';

  export let batchId: string;
  export let subjectId: string | null = null;
  export let chapterId: string | null = null;
  export let lessonId: string | null = null;
  export let isOpen: boolean = false;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    sessionCreated: { session: LiveSession };
    close: {};
  }>();

  let creating = false;
  let error: string | null = null;

  // Form data
  let title = '';
  let description = '';
  let scheduledStart = '';
  let scheduledEnd = '';
  let sessionType: 'class' | 'exam' | 'meeting' | 'workshop' = 'class';
  let maxParticipants = 100;
  let isRecorded = true;

  // Meeting configuration
  let meetingConfig: MeetingConfig = {
    provider: 'bigbluebutton',
    waiting_room_enabled: true,
    chat_enabled: true,
    screen_sharing_enabled: true,
    recording_enabled: true,
    breakout_rooms_enabled: true,
    polls_enabled: true,
    whiteboard_enabled: true,
    max_video_streams: 25,
    audio_only_mode: false,
    layout_settings: {
      default_layout: 'gallery',
      allow_layout_change: true
    },
    quality_settings: {
      video_quality: 'auto',
      audio_quality: 'high'
    }
  };

  // Security settings
  let securitySettings = {
    device_verification_required: true,
    ip_restrictions: [],
    geo_restrictions: [],
    watermark_enabled: false,
    recording_protection: false,
    screenshot_protection: false,
    attendance_tracking: true,
    proctoring_enabled: false,
    lockdown_mode: false,
    session_timeout_minutes: 480
  };

  $: instructorId = $globalStore.user?.id;
  $: canCreate = title && scheduledStart && scheduledEnd && instructorId;

  // Set default times (1 hour from now, 2 hours duration)
  function setDefaultTimes() {
    const now = new Date();
    const startTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
    const endTime = new Date(startTime.getTime() + 2 * 60 * 60 * 1000); // 2 hours duration

    scheduledStart = startTime.toISOString().slice(0, 16);
    scheduledEnd = endTime.toISOString().slice(0, 16);
  }

  function updateEndTime() {
    if (scheduledStart) {
      const start = new Date(scheduledStart);
      const end = new Date(start.getTime() + 2 * 60 * 60 * 1000); // Default 2 hours
      scheduledEnd = end.toISOString().slice(0, 16);
    }
  }

  async function createSession() {
    if (!canCreate) return;

    try {
      creating = true;
      error = null;

      const sessionData: Omit<LiveSession, 'id' | 'created_at' | 'updated_at'> = {
        title,
        description: description || undefined,
        batch_id: batchId,
        subject_id: subjectId || undefined,
        chapter_id: chapterId || undefined,
        lesson_id: lessonId || undefined,
        instructor_id: instructorId!,
        scheduled_start: new Date(scheduledStart).toISOString(),
        scheduled_end: new Date(scheduledEnd).toISOString(),
        status: 'scheduled',
        session_type: sessionType,
        max_participants: maxParticipants,
        is_recorded: isRecorded,
        meeting_config: meetingConfig,
        security_settings: securitySettings,
        metadata: {}
      };

      const session = await liveSessionService.createSession(sessionData);

      dispatch('sessionCreated', { session });
      resetForm();
      
    } catch (err) {
      console.error('Error creating session:', err);
      error = err.message || 'Failed to create session';
    } finally {
      creating = false;
    }
  }

  function resetForm() {
    title = '';
    description = '';
    scheduledStart = '';
    scheduledEnd = '';
    sessionType = 'class';
    maxParticipants = 100;
    isRecorded = true;
    error = null;
  }

  function closeModal() {
    resetForm();
    dispatch('close', {});
  }

  // Initialize default times when component mounts
  if (isOpen && !scheduledStart) {
    setDefaultTimes();
  }
</script>

{#if isOpen}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <Video size={24} class="text-primary-600 mr-3" />
          <h2 class="text-xl font-bold text-gray-900 dark:text-white">
            {$t('live_session.create_session', { default: 'Create Live Session' })}
          </h2>
        </div>
        <button
          on:click={closeModal}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6">
        {#if error}
          <div class="p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg">
            <p class="text-red-700 dark:text-red-300">{error}</p>
          </div>
        {/if}

        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {$t('live_session.basic_info', { default: 'Basic Information' })}
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.title', { default: 'Session Title' })} *
              </label>
              <input
                type="text"
                bind:value={title}
                placeholder={$t('live_session.title_placeholder', { default: 'Enter session title' })}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>

            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.description', { default: 'Description' })}
              </label>
              <textarea
                bind:value={description}
                placeholder={$t('live_session.description_placeholder', { default: 'Enter session description' })}
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              ></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.session_type', { default: 'Session Type' })}
              </label>
              <select
                bind:value={sessionType}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="class">{$t('live_session.type_class', { default: 'Class' })}</option>
                <option value="exam">{$t('live_session.type_exam', { default: 'Exam' })}</option>
                <option value="meeting">{$t('live_session.type_meeting', { default: 'Meeting' })}</option>
                <option value="workshop">{$t('live_session.type_workshop', { default: 'Workshop' })}</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.max_participants', { default: 'Max Participants' })}
              </label>
              <input
                type="number"
                bind:value={maxParticipants}
                min="1"
                max="500"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.start_time', { default: 'Start Time' })} *
              </label>
              <input
                type="datetime-local"
                bind:value={scheduledStart}
                on:change={updateEndTime}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.end_time', { default: 'End Time' })} *
              </label>
              <input
                type="datetime-local"
                bind:value={scheduledEnd}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>
          </div>
        </div>

        <!-- Meeting Settings -->
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {$t('live_session.meeting_settings', { default: 'Meeting Settings' })}
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.provider', { default: 'Meeting Provider' })}
              </label>
              <select
                bind:value={meetingConfig.provider}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="bigbluebutton">BigBlueButton</option>
                <option value="jitsi">Jitsi Meet</option>
                <option value="zoom">Zoom</option>
                <option value="teams">Microsoft Teams</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {$t('live_session.video_quality', { default: 'Video Quality' })}
              </label>
              <select
                bind:value={meetingConfig.quality_settings.video_quality}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="auto">{$t('live_session.quality_auto', { default: 'Auto' })}</option>
                <option value="high">{$t('live_session.quality_high', { default: 'High' })}</option>
                <option value="medium">{$t('live_session.quality_medium', { default: 'Medium' })}</option>
                <option value="low">{$t('live_session.quality_low', { default: 'Low' })}</option>
              </select>
            </div>
          </div>

          <!-- Feature Toggles -->
          <div class="mt-4 space-y-3">
            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={isRecorded}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.enable_recording', { default: 'Enable session recording' })}
              </span>
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={meetingConfig.waiting_room_enabled}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.waiting_room', { default: 'Enable waiting room' })}
              </span>
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={meetingConfig.chat_enabled}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.enable_chat', { default: 'Enable chat' })}
              </span>
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={meetingConfig.screen_sharing_enabled}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.screen_sharing', { default: 'Enable screen sharing' })}
              </span>
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={meetingConfig.breakout_rooms_enabled}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.breakout_rooms', { default: 'Enable breakout rooms' })}
              </span>
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={meetingConfig.polls_enabled}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.enable_polls', { default: 'Enable polls' })}
              </span>
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={meetingConfig.whiteboard_enabled}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.enable_whiteboard', { default: 'Enable whiteboard' })}
              </span>
            </label>
          </div>
        </div>

        <!-- Security Settings -->
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {$t('live_session.security_settings', { default: 'Security Settings' })}
          </h3>
          
          <div class="space-y-3">
            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={securitySettings.device_verification_required}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.device_verification', { default: 'Require device verification' })}
              </span>
            </label>

            <label class="flex items-center">
              <input
                type="checkbox"
                bind:checked={securitySettings.attendance_tracking}
                class="mr-3"
              />
              <span class="text-gray-900 dark:text-white">
                {$t('live_session.attendance_tracking', { default: 'Enable attendance tracking' })}
              </span>
            </label>

            {#if sessionType === 'exam'}
              <label class="flex items-center">
                <input
                  type="checkbox"
                  bind:checked={securitySettings.proctoring_enabled}
                  class="mr-3"
                />
                <span class="text-gray-900 dark:text-white">
                  {$t('live_session.enable_proctoring', { default: 'Enable proctoring' })}
                </span>
              </label>

              <label class="flex items-center">
                <input
                  type="checkbox"
                  bind:checked={securitySettings.lockdown_mode}
                  class="mr-3"
                />
                <span class="text-gray-900 dark:text-white">
                  {$t('live_session.lockdown_mode', { default: 'Enable lockdown mode' })}
                </span>
              </label>
            {/if}
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={closeModal}
        >
          {$t('live_session.cancel', { default: 'Cancel' })}
        </PrimaryButton>
        
        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={createSession}
          disabled={!canCreate || creating}
        >
          {#if creating}
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
          {:else}
            <Save size={20} class="mr-2" />
          {/if}
          {$t('live_session.create', { default: 'Create Session' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Custom styles for the modal */
</style>
