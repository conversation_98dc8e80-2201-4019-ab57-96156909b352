<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { DoubtSubmission, DoubtResponse } from '$lib/utils/types/communication';
  import { doubtService } from '$lib/utils/services/communication';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Add, 
    Send, 
    Attachment, 
    Close,
    ThumbsUp,
    View,
    Time,
    User,
    CheckmarkFilled,
    Warning,
    Filter,
    Search
  } from 'carbon-icons-svelte';

  export let batchId: string;
  export let subjectId: string | null = null;
  export let chapterId: string | null = null;
  export let lessonId: string | null = null;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    doubtSubmitted: { doubt: DoubtSubmission };
    doubtResolved: { doubtId: string };
  }>();

  let doubts = writable<DoubtSubmission[]>([]);
  let selectedDoubt = writable<DoubtSubmission | null>(null);
  let doubtResponses = writable<DoubtResponse[]>([]);
  let loading = true;
  let error: string | null = null;
  let showSubmitForm = false;
  let submitting = false;

  // Form data
  let doubtTitle = '';
  let doubtDescription = '';
  let doubtType: 'general' | 'concept' | 'homework' | 'exam' | 'technical' = 'general';
  let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';
  let tags: string[] = [];
  let attachments: File[] = [];

  // Response form
  let responseText = '';
  let responding = false;

  // Filters
  let statusFilter = 'all';
  let priorityFilter = 'all';
  let searchQuery = '';

  $: userId = $globalStore.user?.id;
  $: isInstructor = $globalStore.role === 'teacher' || $globalStore.isOrgAdmin;
  $: isStudent = $globalStore.role === 'student';

  onMount(async () => {
    await loadDoubts();
  });

  async function loadDoubts() {
    try {
      loading = true;
      error = null;

      const filters: any = {};
      if (statusFilter !== 'all') filters.status = statusFilter;
      if (priorityFilter !== 'all') filters.priority = priorityFilter;
      if (subjectId) filters.subject_id = subjectId;

      const doubtsData = await doubtService.getBatchDoubts(batchId, filters);
      doubts.set(doubtsData);

    } catch (err) {
      console.error('Error loading doubts:', err);
      error = err.message || 'Failed to load doubts';
    } finally {
      loading = false;
    }
  }

  async function submitDoubt() {
    if (!userId || !doubtTitle.trim() || !doubtDescription.trim()) return;

    try {
      submitting = true;
      error = null;

      const doubtData = {
        student_id: userId,
        batch_id: batchId,
        subject_id: subjectId,
        chapter_id: chapterId,
        lesson_id: lessonId,
        title: doubtTitle.trim(),
        description: doubtDescription.trim(),
        doubt_type: doubtType,
        priority: priority,
        tags: tags,
        attachments: [], // File upload would be handled separately
        is_anonymous: false,
        metadata: {}
      };

      const doubtId = await doubtService.submitDoubt(doubtData);
      
      // Reload doubts
      await loadDoubts();
      
      // Reset form
      resetForm();
      showSubmitForm = false;

      dispatch('doubtSubmitted', { doubt: { ...doubtData, id: doubtId } as DoubtSubmission });

    } catch (err) {
      console.error('Error submitting doubt:', err);
      error = err.message || 'Failed to submit doubt';
    } finally {
      submitting = false;
    }
  }

  async function selectDoubt(doubt: DoubtSubmission) {
    selectedDoubt.set(doubt);
    
    // Increment views
    await doubtService.incrementViews(doubt.id);
    
    // Load responses
    const responses = await doubtService.getDoubtResponses(doubt.id);
    doubtResponses.set(responses);
  }

  async function respondToDoubt() {
    if (!$selectedDoubt || !userId || !responseText.trim()) return;

    try {
      responding = true;
      error = null;

      const responseData = {
        doubt_id: $selectedDoubt.id,
        responder_id: userId,
        response_text: responseText.trim(),
        response_type: 'text' as const,
        attachments: [],
        is_solution: false,
        is_helpful: false,
        metadata: {}
      };

      await doubtService.respondToDoubt(responseData);
      
      // Reload responses
      const responses = await doubtService.getDoubtResponses($selectedDoubt.id);
      doubtResponses.set(responses);
      
      // Reset response form
      responseText = '';

    } catch (err) {
      console.error('Error responding to doubt:', err);
      error = err.message || 'Failed to respond to doubt';
    } finally {
      responding = false;
    }
  }

  async function assignDoubt(doubtId: string) {
    if (!userId || !isInstructor) return;

    try {
      await doubtService.assignDoubt(doubtId, userId);
      await loadDoubts();
    } catch (err) {
      console.error('Error assigning doubt:', err);
      error = err.message || 'Failed to assign doubt';
    }
  }

  async function upvoteDoubt(doubtId: string) {
    try {
      await doubtService.upvoteDoubt(doubtId);
      await loadDoubts();
    } catch (err) {
      console.error('Error upvoting doubt:', err);
    }
  }

  function resetForm() {
    doubtTitle = '';
    doubtDescription = '';
    doubtType = 'general';
    priority = 'medium';
    tags = [];
    attachments = [];
  }

  function addTag(tag: string) {
    if (tag.trim() && !tags.includes(tag.trim())) {
      tags = [...tags, tag.trim()];
    }
  }

  function removeTag(index: number) {
    tags = tags.filter((_, i) => i !== index);
  }

  function getPriorityColor(priority: string): string {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'resolved': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'in_progress': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'assigned': return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300';
      case 'pending': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  }

  function formatTimeAgo(timestamp: string): string {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  }

  $: filteredDoubts = $doubts.filter(doubt => {
    if (searchQuery && !doubt.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !doubt.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    return true;
  });
</script>

<div class="doubt-clearing-system {className}">
  {#if !$selectedDoubt}
    <!-- Doubt List View -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('doubts.title', { default: 'Doubt Clearing' })}
        </h2>
        
        {#if isStudent}
          <PrimaryButton
            variant={VARIANTS.CONTAINED}
            onClick={() => showSubmitForm = true}
          >
            <Add size={20} class="mr-2" />
            {$t('doubts.ask_doubt', { default: 'Ask Doubt' })}
          </PrimaryButton>
        {/if}
      </div>

      <!-- Filters -->
      <div class="flex items-center space-x-4 mb-6">
        <div class="flex items-center">
          <Search size={20} class="mr-2 text-gray-500" />
          <input
            type="text"
            bind:value={searchQuery}
            placeholder={$t('doubts.search_placeholder', { default: 'Search doubts...' })}
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
          />
        </div>

        <select 
          bind:value={statusFilter}
          on:change={loadDoubts}
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
        >
          <option value="all">{$t('doubts.all_status', { default: 'All Status' })}</option>
          <option value="pending">{$t('doubts.pending', { default: 'Pending' })}</option>
          <option value="assigned">{$t('doubts.assigned', { default: 'Assigned' })}</option>
          <option value="in_progress">{$t('doubts.in_progress', { default: 'In Progress' })}</option>
          <option value="resolved">{$t('doubts.resolved', { default: 'Resolved' })}</option>
        </select>

        <select 
          bind:value={priorityFilter}
          on:change={loadDoubts}
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
        >
          <option value="all">{$t('doubts.all_priority', { default: 'All Priority' })}</option>
          <option value="urgent">{$t('doubts.urgent', { default: 'Urgent' })}</option>
          <option value="high">{$t('doubts.high', { default: 'High' })}</option>
          <option value="medium">{$t('doubts.medium', { default: 'Medium' })}</option>
          <option value="low">{$t('doubts.low', { default: 'Low' })}</option>
        </select>
      </div>
    </div>

    {#if loading}
      <Box className="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {$t('doubts.loading', { default: 'Loading Doubts' })}
        </h3>
      </Box>

    {:else if error}
      <Box className="text-center py-12">
        <Warning size={48} class="text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {$t('doubts.error', { default: 'Error Loading Doubts' })}
        </h3>
        <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
        <PrimaryButton onClick={loadDoubts}>
          {$t('doubts.retry', { default: 'Retry' })}
        </PrimaryButton>
      </Box>

    {:else if filteredDoubts.length === 0}
      <Box className="text-center py-12">
        <User size={48} class="text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {$t('doubts.no_doubts', { default: 'No Doubts Found' })}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          {$t('doubts.no_doubts_desc', { default: 'No doubts match your current filters' })}
        </p>
      </Box>

    {:else}
      <!-- Doubts List -->
      <div class="space-y-4">
        {#each filteredDoubts as doubt (doubt.id)}
          <Box className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => selectDoubt(doubt)}>
            <div class="p-4">
              <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {doubt.title}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400 text-sm line-clamp-2">
                    {doubt.description}
                  </p>
                </div>
                
                <div class="flex items-center space-x-2 ml-4">
                  <span class="px-2 py-1 text-xs rounded-full {getPriorityColor(doubt.priority)}">
                    {doubt.priority.toUpperCase()}
                  </span>
                  <span class="px-2 py-1 text-xs rounded-full {getStatusColor(doubt.status)}">
                    {doubt.status.toUpperCase()}
                  </span>
                </div>
              </div>

              <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <div class="flex items-center space-x-4">
                  <div class="flex items-center">
                    <User size={16} class="mr-1" />
                    <span>{doubt.student?.fullname || 'Anonymous'}</span>
                  </div>
                  
                  <div class="flex items-center">
                    <Time size={16} class="mr-1" />
                    <span>{formatTimeAgo(doubt.created_at)}</span>
                  </div>
                  
                  <div class="flex items-center">
                    <View size={16} class="mr-1" />
                    <span>{doubt.views}</span>
                  </div>
                  
                  <div class="flex items-center">
                    <ThumbsUp size={16} class="mr-1" />
                    <span>{doubt.upvotes}</span>
                  </div>
                </div>

                {#if isInstructor && doubt.status === 'pending'}
                  <PrimaryButton
                    variant={VARIANTS.OUTLINED}
                    onClick={(e) => {
                      e.stopPropagation();
                      assignDoubt(doubt.id);
                    }}
                    size="sm"
                  >
                    {$t('doubts.assign_to_me', { default: 'Assign to Me' })}
                  </PrimaryButton>
                {/if}
              </div>

              <!-- Tags -->
              {#if doubt.tags && doubt.tags.length > 0}
                <div class="flex flex-wrap gap-2 mt-3">
                  {#each doubt.tags as tag}
                    <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">
                      #{tag}
                    </span>
                  {/each}
                </div>
              {/if}
            </div>
          </Box>
        {/each}
      </div>
    {/if}

  {:else}
    <!-- Doubt Detail View -->
    <div class="mb-4">
      <PrimaryButton
        variant={VARIANTS.OUTLINED}
        onClick={() => selectedDoubt.set(null)}
      >
        ← {$t('doubts.back_to_list', { default: 'Back to List' })}
      </PrimaryButton>
    </div>

    <Box className="mb-6">
      <div class="p-6">
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {$selectedDoubt.title}
            </h1>
            <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
              <div class="flex items-center">
                <User size={16} class="mr-1" />
                <span>{$selectedDoubt.student?.fullname || 'Anonymous'}</span>
              </div>
              <div class="flex items-center">
                <Time size={16} class="mr-1" />
                <span>{formatTimeAgo($selectedDoubt.created_at)}</span>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <span class="px-3 py-1 text-sm rounded-full {getPriorityColor($selectedDoubt.priority)}">
              {$selectedDoubt.priority.toUpperCase()}
            </span>
            <span class="px-3 py-1 text-sm rounded-full {getStatusColor($selectedDoubt.status)}">
              {$selectedDoubt.status.toUpperCase()}
            </span>
          </div>
        </div>

        <div class="prose dark:prose-invert max-w-none mb-4">
          <p>{$selectedDoubt.description}</p>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <button
              on:click={() => upvoteDoubt($selectedDoubt.id)}
              class="flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600"
            >
              <ThumbsUp size={20} class="mr-1" />
              <span>{$selectedDoubt.upvotes}</span>
            </button>
            
            <div class="flex items-center text-gray-600 dark:text-gray-400">
              <View size={20} class="mr-1" />
              <span>{$selectedDoubt.views}</span>
            </div>
          </div>
        </div>
      </div>
    </Box>

    <!-- Responses -->
    <Box>
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {$t('doubts.responses', { default: 'Responses' })} ({$doubtResponses.length})
        </h3>

        <div class="space-y-4 mb-6">
          {#each $doubtResponses as response (response.id)}
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3">
                    <span class="text-primary-600 dark:text-primary-300 text-sm font-semibold">
                      {response.responder?.fullname?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">
                      {response.responder?.fullname || 'Unknown'}
                    </p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      {formatTimeAgo(response.created_at)}
                    </p>
                  </div>
                </div>
                
                {#if response.is_solution}
                  <span class="px-2 py-1 text-xs bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300 rounded">
                    <CheckmarkFilled size={12} class="inline mr-1" />
                    {$t('doubts.solution', { default: 'Solution' })}
                  </span>
                {/if}
              </div>
              
              <div class="prose dark:prose-invert max-w-none">
                <p>{response.response_text}</p>
              </div>
            </div>
          {/each}
        </div>

        <!-- Response Form -->
        {#if isInstructor || $selectedDoubt.student_id === userId}
          <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">
              {$t('doubts.add_response', { default: 'Add Response' })}
            </h4>
            
            <div class="space-y-4">
              <textarea
                bind:value={responseText}
                placeholder={$t('doubts.response_placeholder', { default: 'Type your response...' })}
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              ></textarea>
              
              <div class="flex items-center justify-end">
                <PrimaryButton
                  variant={VARIANTS.CONTAINED}
                  onClick={respondToDoubt}
                  disabled={!responseText.trim() || responding}
                >
                  {#if responding}
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  {:else}
                    <Send size={20} class="mr-2" />
                  {/if}
                  {$t('doubts.send_response', { default: 'Send Response' })}
                </PrimaryButton>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </Box>
  {/if}
</div>

<!-- Submit Doubt Modal -->
{#if showSubmitForm}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('doubts.submit_doubt', { default: 'Submit Doubt' })}
        </h2>
        <button
          on:click={() => showSubmitForm = false}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('doubts.title', { default: 'Title' })} *
          </label>
          <input
            type="text"
            bind:value={doubtTitle}
            placeholder={$t('doubts.title_placeholder', { default: 'Brief description of your doubt' })}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('doubts.description', { default: 'Description' })} *
          </label>
          <textarea
            bind:value={doubtDescription}
            placeholder={$t('doubts.description_placeholder', { default: 'Detailed description of your doubt' })}
            rows="6"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          ></textarea>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('doubts.type', { default: 'Type' })}
            </label>
            <select
              bind:value={doubtType}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="general">{$t('doubts.type_general', { default: 'General' })}</option>
              <option value="concept">{$t('doubts.type_concept', { default: 'Concept' })}</option>
              <option value="homework">{$t('doubts.type_homework', { default: 'Homework' })}</option>
              <option value="exam">{$t('doubts.type_exam', { default: 'Exam' })}</option>
              <option value="technical">{$t('doubts.type_technical', { default: 'Technical' })}</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('doubts.priority', { default: 'Priority' })}
            </label>
            <select
              bind:value={priority}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="low">{$t('doubts.low', { default: 'Low' })}</option>
              <option value="medium">{$t('doubts.medium', { default: 'Medium' })}</option>
              <option value="high">{$t('doubts.high', { default: 'High' })}</option>
              <option value="urgent">{$t('doubts.urgent', { default: 'Urgent' })}</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => showSubmitForm = false}
        >
          {$t('doubts.cancel', { default: 'Cancel' })}
        </PrimaryButton>
        
        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={submitDoubt}
          disabled={!doubtTitle.trim() || !doubtDescription.trim() || submitting}
        >
          {#if submitting}
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
          {:else}
            <Send size={20} class="mr-2" />
          {/if}
          {$t('doubts.submit', { default: 'Submit Doubt' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
