<script>
  import { onMount } from 'svelte';
  
  let mounted = false;
  
  onMount(() => {
    mounted = true;
    console.log('Simple ClassroomIO page loaded');
  });
</script>

<svelte:head>
  <title>ClassroomIO - Simple Test</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 flex items-center justify-center">
  <div class="max-w-md w-full bg-white rounded-lg shadow-md p-8">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">
        Classroom<span class="text-blue-600">IO</span>
      </h1>
      
      {#if mounted}
        <div class="text-green-600 mb-4">
          ✅ Application Successfully Loaded!
        </div>
      {:else}
        <div class="text-yellow-600 mb-4">
          ⏳ Loading...
        </div>
      {/if}
      
      <p class="text-gray-600 mb-6">
        The operating system for classrooms of the future 🚀
      </p>
      
      <div class="space-y-3">
        <a 
          href="/test" 
          class="block w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
        >
          Test Page
        </a>
        
        <a 
          href="/" 
          class="block w-full bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700 transition-colors"
        >
          Full Application
        </a>
      </div>
      
      <div class="mt-6 text-sm text-gray-500">
        <p>Self-hosted mode: Enabled</p>
        <p>Environment: Development</p>
      </div>
    </div>
  </div>
</div>
