{"name": "dashboard", "description": "classroomio", "version": "0.0.1", "main": "main.js", "scripts": {"dev": "vite dev --port 5173", "build": "vite build", "preview": "vite preview", "lint": "eslint ./src", "clean": "rm -rf node_modules", "format": "prettier . --write .", "format:check": "prettier . --check .", "export": "vite build --entry \"/ /404 login signup forgot onboarding courses org lms course\"", "postexport": "mv __sapper__/export/404/index.html __sapper__/export/404.html", "start": "node build", "prepare": "svelte-kit sync", "type-check": "svelte-check --tsconfig ./tsconfig.json", "test": "vitest", "test:unit": "vitest run --reporter=verbose --coverage", "test:integration": "vitest run --config vitest.integration.config.ts", "test:components": "vitest run src/lib/components/**/*.test.ts", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:load": "k6 run tests/load/load-test.js", "test:smoke": "playwright test tests/smoke/", "test:health": "node tests/health/health-check.js", "db:migrate": "supabase db push", "db:migrate:test": "supabase db push --db-url $DATABASE_URL", "db:seed": "node scripts/seed.js", "db:seed:test": "node scripts/seed-test.js", "docker:build": "docker build -f Dockerfile.production -t classroomio/app:latest .", "docker:run": "docker run -p 3000:3000 classroomio/app:latest", "deploy:staging": "bash scripts/deploy.sh staging", "deploy:production": "bash scripts/deploy.sh production", "ci": "cp .env.example .env && vite build && vite preview", "script:translate": "node ./scripts/translate.cjs"}, "dependencies": {"@carbon/charts-svelte": "^1.13.6", "@lemonsqueezy/lemonsqueezy.js": "^2.2.0", "@polar-sh/sveltekit": "^0.2.1", "@supabase/supabase-js": "^2.45.4", "@sveltejs/adapter-auto": "^2.1.0", "@sveltejs/adapter-node": "^1.3.1", "@sveltejs/adapter-vercel": "^3.0.3", "@sveltekit-i18n/base": "^1.3.7", "@sveltekit-i18n/parser-icu": "^1.0.8", "@tailwindcss/forms": "^0.5.4", "@types/pluralize": "0.0.30", "ai": "^2.1.31", "axios": "^1.7.7", "body-parser": "^1.20.2", "bufferutil": "^4.0.9", "canvas-confetti": "^1.6.0", "clsx": "^2.1.1", "color2k": "^2.0.3", "cookie-parser": "^1.4.6", "copy-to-clipboard": "^3.3.3", "d3": "^7.8.5", "d3-cloud": "^1.2.7", "d3-sankey": "^0.12.3", "dayjs": "^1.11.10", "encoding": "^0.1.13", "hotkeys-js": "^3.11.2", "html-to-image": "^1.11.11", "is-valid-domain": "^0.1.6", "js-yaml": "^4.1.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "lodash": "^4.17.21", "openai-edge": "^1.2.2", "papaparse": "^5.4.1", "pluralize": "^8.0.0", "posthog-js": "^1.180.1", "shared": "workspace:*", "sirv": "^2.0.3", "stripe": "^17.3.1", "svelte-awesome-color-picker": "^3.1.4", "svelte-email": "^0.0.4", "svelte-meta-tags": "^3.1.2", "sveltekit-i18n": "^2.4.2", "tailwind-merge": "^2.6.0", "tldts": "^6.1.41", "unsplash-js": "^7.0.18", "utf-8-validate": "^6.0.5", "wait-on": "^7.0.1", "zod": "^3.21.4", "vidstack": "^1.12.9", "plyr": "^3.7.8", "hls.js": "^1.5.15", "dashjs": "^4.7.4"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/preset-typescript": "^7.23.3", "@babel/runtime": "^7.22.6", "@sveltejs/kit": "^1.29.0", "@tailwindcss/typography": "^0.5.9", "@testing-library/jest-dom": "^6.1.5", "@testing-library/svelte": "^4.0.5", "@types/jest": "^29.5.10", "@types/lodash": "^4.14.196", "all-object-keys": "^2.2.0", "autoprefixer": "^10.4.14", "babel-jest": "^29.7.0", "carbon-components-svelte": "^0.79.0", "carbon-icons-svelte": "^12.1.0", "diff": "^5.2.0", "dotenv": "^16.3.1", "jessy": "^3.1.1", "jest": "^29.7.0", "postcss": "^8.4.24", "postcss-load-config": "^4.0.1", "qrcode": "^1.5.3", "sass": "^1.64.2", "svelte": "^4.1.2", "svelte-calendar": "^3.1.6", "svelte-dnd-action": "^0.9.24", "svelte-jester": "^3.0.0", "svelte-loading-spinners": "^0.3.4", "svelte-preprocess": "^5.0.4", "tailwindcss": "^3.3.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig": "workspace:*", "tslib": "^2.6.1", "typescript": "^5.1.6", "vite": "^4.4.8", "vitest": "^0.34.6", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/user-event": "^14.5.1", "happy-dom": "^12.10.3", "jsdom": "^22.1.0", "@playwright/test": "^1.36.2", "k6": "^0.46.0", "svelte-check": "^3.4.6", "prettier": "^3.0.0", "prettier-plugin-svelte": "^3.0.3", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-svelte": "^2.32.4", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1"}, "type": "module", "engines": {"node": ">=18.17.0"}, "browser": {"crypto": false}}