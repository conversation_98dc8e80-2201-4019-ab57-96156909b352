@echo off

:: ClassroomIO Environment Diagnostic Script
:: Helps identify issues with the development environment

echo ========================================
echo   ClassroomIO Environment Diagnostics
echo ========================================
echo.

echo Running diagnostics...
echo Timestamp: %DATE% %TIME%
echo.

:: Basic system information
echo [SYSTEM INFO]
echo OS: %OS%
echo Processor: %PROCESSOR_ARCHITECTURE%
echo Computer: %COMPUTERNAME%
echo User: %USERNAME%
echo Current Directory: %CD%
echo.

:: Check PATH
echo [PATH CHECK]
echo PATH contains:
echo %PATH%
echo.

:: Check Node.js
echo [NODE.JS CHECK]
where node >nul 2>&1
if errorlevel 1 (
    echo ERROR: node.exe not found in PATH
) else (
    echo Node.js location:
    where node
    echo Node.js version:
    node --version 2>&1
)
echo.

:: Check npm
echo [NPM CHECK]
where npm >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm.exe not found in PATH
) else (
    echo npm location:
    where npm
    echo npm version:
    npm --version 2>&1
)
echo.

:: Check pnpm
echo [PNPM CHECK]
where pnpm >nul 2>&1
if errorlevel 1 (
    echo WARNING: pnpm.exe not found in PATH (optional)
) else (
    echo pnpm location:
    where pnpm
    echo pnpm version:
    pnpm --version 2>&1
)
echo.

:: Check project files
echo [PROJECT FILES CHECK]
if exist "package.json" (
    echo SUCCESS: package.json found
) else (
    echo ERROR: package.json not found
)

if exist ".env.example" (
    echo SUCCESS: .env.example found
) else (
    echo WARNING: .env.example not found
)

if exist ".env.local" (
    echo SUCCESS: .env.local found
) else (
    echo INFO: .env.local not found (will be created)
)

if exist "apps\dashboard\package.json" (
    echo SUCCESS: apps\dashboard\package.json found
) else (
    echo ERROR: apps\dashboard\package.json not found
)

if exist "node_modules" (
    echo INFO: node_modules directory exists
) else (
    echo INFO: node_modules directory not found (dependencies need installation)
)
echo.

:: Check ports
echo [PORT CHECK]
netstat -an | findstr ":5173" >nul 2>&1
if errorlevel 1 (
    echo SUCCESS: Port 5173 is available
) else (
    echo WARNING: Port 5173 is in use
    echo Processes using port 5173:
    netstat -ano | findstr ":5173"
)

netstat -an | findstr ":54321" >nul 2>&1
if errorlevel 1 (
    echo INFO: Port 54321 is available (Supabase)
) else (
    echo INFO: Port 54321 is in use (Supabase may be running)
)
echo.

:: Check permissions
echo [PERMISSIONS CHECK]
echo Testing file creation...
echo test > test_file.tmp 2>nul
if exist "test_file.tmp" (
    echo SUCCESS: Can create files in current directory
    del test_file.tmp >nul 2>&1
) else (
    echo ERROR: Cannot create files in current directory
    echo This may be a permissions issue
)
echo.

:: Check execution policy (PowerShell)
echo [POWERSHELL CHECK]
powershell -Command "Get-ExecutionPolicy" >nul 2>&1
if errorlevel 1 (
    echo WARNING: PowerShell not available or restricted
) else (
    echo PowerShell execution policy:
    powershell -Command "Get-ExecutionPolicy"
)
echo.

:: Test basic commands
echo [COMMAND TEST]
echo Testing basic commands...

echo Testing 'echo':
echo test_echo_command

echo Testing 'set':
set TEST_VAR=test_value
echo TEST_VAR is set to: %TEST_VAR%

echo Testing 'if exist':
if exist "package.json" (
    echo if exist works correctly
) else (
    echo if exist may have issues
)

echo Testing 'for /f':
for /f %%i in ('echo test_for_command') do echo for /f result: %%i
echo.

:: Summary
echo [DIAGNOSTIC SUMMARY]
echo ========================================

:: Check critical requirements
set ISSUES_FOUND=0

where node >nul 2>&1
if errorlevel 1 (
    echo CRITICAL: Node.js not found
    set /a ISSUES_FOUND+=1
)

where npm >nul 2>&1
if errorlevel 1 (
    echo CRITICAL: npm not found
    set /a ISSUES_FOUND+=1
)

if not exist "package.json" (
    echo CRITICAL: package.json not found
    set /a ISSUES_FOUND+=1
)

if %ISSUES_FOUND% equ 0 (
    echo SUCCESS: All critical requirements met
    echo The development environment should work correctly
) else (
    echo ERROR: %ISSUES_FOUND% critical issues found
    echo Please resolve these issues before running start-dev.bat
)

echo.
echo Diagnostic complete. Press any key to exit...
pause >nul

exit /b 0
