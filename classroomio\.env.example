# ClassroomIO Environment Configuration
# Copy this file to .env.local for development or .env.production for production

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
APP_NAME=ClassroomIO
APP_URL=http://localhost:5173
API_URL=http://localhost:5173/api

# Server Configuration
PORT=3000
HOST=0.0.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Supabase Database URL
DATABASE_URL=postgresql://postgres:password@localhost:54321/postgres

# Supabase Configuration
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
SESSION_SECRET=your-session-secret-change-this-in-production

# OAuth Configuration (Optional)
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email Templates
FROM_EMAIL=<EMAIL>
FROM_NAME=ClassroomIO

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=classroomio-uploads

# Cloudflare R2 (Alternative to S3)
CLOUDFLARE_R2_ACCOUNT_ID=
CLOUDFLARE_R2_ACCESS_KEY_ID=
CLOUDFLARE_R2_SECRET_ACCESS_KEY=
CLOUDFLARE_R2_BUCKET=

# Local Storage (Development)
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100MB

# =============================================================================
# VIDEO STREAMING CONFIGURATION
# =============================================================================
# Cloudflare Stream
CLOUDFLARE_STREAM_API_TOKEN=your-cloudflare-stream-token
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id

# Video Processing
VIDEO_PROCESSING_ENABLED=true
VIDEO_TRANSCODING_ENABLED=true
VIDEO_THUMBNAIL_ENABLED=true

# Video Security
VIDEO_WATERMARK_ENABLED=true
VIDEO_DRM_ENABLED=false
VIDEO_DOWNLOAD_PROTECTION=true

# =============================================================================
# LIVE STREAMING CONFIGURATION
# =============================================================================
# Daily.co Configuration
DAILY_API_KEY=your-daily-api-key
DAILY_DOMAIN=your-daily-domain

# BigBlueButton Configuration
BBB_URL=https://your-bbb-server.com/bigbluebutton/
BBB_SECRET=your-bbb-secret

# Jitsi Meet Configuration
JITSI_DOMAIN=meet.jit.si
JITSI_APP_ID=your-jitsi-app-id

# =============================================================================
# AI INTEGRATION
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# AI Features
AI_DOUBT_CATEGORIZATION=true
AI_CONTENT_GENERATION=true
AI_ANALYTICS_INSIGHTS=true

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================
# Metabase Configuration
METABASE_URL=http://localhost:3001
METABASE_API_KEY=your-metabase-api-key
METABASE_USERNAME=<EMAIL>
METABASE_PASSWORD=your-metabase-password

# Google Analytics
GA_TRACKING_ID=
GA_MEASUREMENT_ID=

# Custom Analytics
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Prometheus Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Grafana Configuration
GRAFANA_URL=http://localhost:3002
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=./logs/app.log

# Error Tracking (Sentry)
SENTRY_DSN=
SENTRY_ENVIRONMENT=development

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================
# Slack Integration
SLACK_WEBHOOK_URL=
SLACK_BOT_TOKEN=

# Discord Integration
DISCORD_WEBHOOK_URL=
DISCORD_BOT_TOKEN=

# Push Notifications
VAPID_PUBLIC_KEY=
VAPID_PRIVATE_KEY=
VAPID_SUBJECT=mailto:<EMAIL>

# =============================================================================
# PAYMENT INTEGRATION
# =============================================================================
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# PayPal Configuration
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=
PAYPAL_MODE=sandbox

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS Settings
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Headers
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true

# Device Security
DEVICE_LOCKING_ENABLED=false
MAX_DEVICES_PER_USER=3

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Backup Settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=classroomio-backups

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Debug Settings
DEBUG=false
VERBOSE_LOGGING=false

# Development Tools
HOT_RELOAD=true
SOURCE_MAPS=true

# Testing
TEST_DATABASE_URL=postgresql://postgres:password@localhost:54321/classroomio_test
TEST_REDIS_URL=redis://localhost:6379/1

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Feature Toggles
FEATURE_LIVE_STREAMING=true
FEATURE_AI_INTEGRATION=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_VIDEO_SECURITY=true
FEATURE_MOBILE_APP=false
FEATURE_BLOCKCHAIN_CERTIFICATES=false

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
# Caching
CACHE_TTL=3600
CACHE_MAX_SIZE=100MB

# Database Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=20

# Worker Configuration
WORKER_CONCURRENCY=5
WORKER_TIMEOUT=300000

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
TIMEZONE=UTC
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,hi

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================
# Organization Settings
DEFAULT_ORG_NAME=ClassroomIO Academy
DEFAULT_ORG_LOGO=
DEFAULT_ORG_THEME=blue

# Branding
CUSTOM_BRANDING_ENABLED=false
CUSTOM_LOGO_URL=
CUSTOM_FAVICON_URL=
CUSTOM_PRIMARY_COLOR=#3B82F6
