-- Migration: Communication Systems
-- Date: 2025-06-30
-- Description: Comprehensive communication system with doubt clearing, forums, messaging, and notifications

-- Create doubt_submissions table for AI-powered doubt clearing
CREATE TABLE IF NOT EXISTS "public"."doubt_submissions" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "student_id" uuid NOT NULL,
    "batch_id" uuid NOT NULL,
    "subject_id" uuid,
    "chapter_id" uuid,
    "lesson_id" uuid,
    "title" character varying NOT NULL,
    "description" text NOT NULL,
    "doubt_type" character varying DEFAULT 'general', -- 'general', 'concept', 'homework', 'exam', 'technical'
    "priority" character varying DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    "status" character varying DEFAULT 'pending', -- 'pending', 'assigned', 'in_progress', 'resolved', 'closed'
    "assigned_to" uuid, -- instructor ID
    "assigned_at" timestamp with time zone,
    "resolved_at" timestamp with time zone,
    "resolution_time_minutes" integer,
    "ai_category" character varying, -- AI-suggested category
    "ai_confidence" numeric, -- AI confidence score 0-1
    "ai_suggested_faqs" jsonb DEFAULT '[]'::jsonb,
    "tags" jsonb DEFAULT '[]'::jsonb,
    "attachments" jsonb DEFAULT '[]'::jsonb,
    "is_anonymous" boolean DEFAULT false,
    "upvotes" integer DEFAULT 0,
    "views" integer DEFAULT 0,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."doubt_submissions" ENABLE ROW LEVEL SECURITY;

-- Create doubt_responses table for instructor responses
CREATE TABLE IF NOT EXISTS "public"."doubt_responses" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "doubt_id" uuid NOT NULL,
    "responder_id" uuid NOT NULL,
    "response_text" text,
    "response_type" character varying DEFAULT 'text', -- 'text', 'voice', 'video', 'file'
    "attachments" jsonb DEFAULT '[]'::jsonb,
    "is_solution" boolean DEFAULT false,
    "is_helpful" boolean DEFAULT false,
    "helpful_votes" integer DEFAULT 0,
    "parent_response_id" uuid, -- for threaded responses
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."doubt_responses" ENABLE ROW LEVEL SECURITY;

-- Create forum_categories table for discussion organization
CREATE TABLE IF NOT EXISTS "public"."forum_categories" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "organization_id" uuid NOT NULL,
    "batch_id" uuid,
    "subject_id" uuid,
    "name" character varying NOT NULL,
    "description" text,
    "icon" character varying,
    "color" character varying DEFAULT '#3B82F6',
    "is_active" boolean DEFAULT true,
    "is_public" boolean DEFAULT false,
    "sort_order" integer DEFAULT 0,
    "post_count" integer DEFAULT 0,
    "last_post_at" timestamp with time zone,
    "moderators" jsonb DEFAULT '[]'::jsonb, -- array of user IDs
    "permissions" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."forum_categories" ENABLE ROW LEVEL SECURITY;

-- Create forum_posts table for discussion threads
CREATE TABLE IF NOT EXISTS "public"."forum_posts" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "category_id" uuid NOT NULL,
    "author_id" uuid NOT NULL,
    "title" character varying NOT NULL,
    "content" text NOT NULL,
    "post_type" character varying DEFAULT 'discussion', -- 'discussion', 'question', 'announcement', 'poll'
    "is_pinned" boolean DEFAULT false,
    "is_locked" boolean DEFAULT false,
    "is_solved" boolean DEFAULT false,
    "tags" jsonb DEFAULT '[]'::jsonb,
    "attachments" jsonb DEFAULT '[]'::jsonb,
    "upvotes" integer DEFAULT 0,
    "downvotes" integer DEFAULT 0,
    "views" integer DEFAULT 0,
    "reply_count" integer DEFAULT 0,
    "last_reply_at" timestamp with time zone,
    "last_reply_by" uuid,
    "moderation_status" character varying DEFAULT 'approved', -- 'pending', 'approved', 'rejected', 'flagged'
    "moderated_by" uuid,
    "moderated_at" timestamp with time zone,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."forum_posts" ENABLE ROW LEVEL SECURITY;

-- Create forum_replies table for post responses
CREATE TABLE IF NOT EXISTS "public"."forum_replies" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "post_id" uuid NOT NULL,
    "author_id" uuid NOT NULL,
    "content" text NOT NULL,
    "parent_reply_id" uuid, -- for nested replies
    "is_solution" boolean DEFAULT false,
    "upvotes" integer DEFAULT 0,
    "downvotes" integer DEFAULT 0,
    "attachments" jsonb DEFAULT '[]'::jsonb,
    "moderation_status" character varying DEFAULT 'approved',
    "moderated_by" uuid,
    "moderated_at" timestamp with time zone,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."forum_replies" ENABLE ROW LEVEL SECURITY;

-- Create user_reputation table for gamification
CREATE TABLE IF NOT EXISTS "public"."user_reputation" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "user_id" uuid NOT NULL,
    "organization_id" uuid NOT NULL,
    "total_points" integer DEFAULT 0,
    "level" integer DEFAULT 1,
    "badges" jsonb DEFAULT '[]'::jsonb,
    "achievements" jsonb DEFAULT '[]'::jsonb,
    "activity_streak" integer DEFAULT 0,
    "last_activity" timestamp with time zone DEFAULT now(),
    "points_breakdown" jsonb DEFAULT '{}'::jsonb, -- points by category
    "monthly_points" integer DEFAULT 0,
    "weekly_points" integer DEFAULT 0
);

ALTER TABLE "public"."user_reputation" ENABLE ROW LEVEL SECURITY;

-- Create messaging_channels table for group communications
CREATE TABLE IF NOT EXISTS "public"."messaging_channels" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "name" character varying NOT NULL,
    "description" text,
    "channel_type" character varying NOT NULL, -- 'batch', 'subject', 'private', 'announcement', 'support'
    "batch_id" uuid,
    "subject_id" uuid,
    "created_by" uuid NOT NULL,
    "is_active" boolean DEFAULT true,
    "is_archived" boolean DEFAULT false,
    "member_count" integer DEFAULT 0,
    "last_message_at" timestamp with time zone,
    "last_message_by" uuid,
    "permissions" jsonb DEFAULT '{}'::jsonb,
    "settings" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."messaging_channels" ENABLE ROW LEVEL SECURITY;

-- Create messaging_channel_members table for channel membership
CREATE TABLE IF NOT EXISTS "public"."messaging_channel_members" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "channel_id" uuid NOT NULL,
    "user_id" uuid NOT NULL,
    "role" character varying DEFAULT 'member', -- 'admin', 'moderator', 'member'
    "joined_at" timestamp with time zone DEFAULT now(),
    "last_read_at" timestamp with time zone DEFAULT now(),
    "is_muted" boolean DEFAULT false,
    "notification_settings" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."messaging_channel_members" ENABLE ROW LEVEL SECURITY;

-- Create messages table for real-time messaging
CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "channel_id" uuid,
    "sender_id" uuid NOT NULL,
    "recipient_id" uuid, -- for direct messages
    "message_type" character varying DEFAULT 'text', -- 'text', 'voice', 'file', 'image', 'video', 'system'
    "content" text,
    "attachments" jsonb DEFAULT '[]'::jsonb,
    "reply_to_id" uuid, -- for threaded messages
    "is_edited" boolean DEFAULT false,
    "edited_at" timestamp with time zone,
    "is_deleted" boolean DEFAULT false,
    "deleted_at" timestamp with time zone,
    "reactions" jsonb DEFAULT '{}'::jsonb,
    "read_by" jsonb DEFAULT '[]'::jsonb, -- array of user IDs who read the message
    "delivery_status" character varying DEFAULT 'sent', -- 'sent', 'delivered', 'read'
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;

-- Create notification_templates table for automated notifications
CREATE TABLE IF NOT EXISTS "public"."notification_templates" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "organization_id" uuid NOT NULL,
    "name" character varying NOT NULL,
    "template_type" character varying NOT NULL, -- 'email', 'sms', 'push', 'whatsapp', 'telegram'
    "event_trigger" character varying NOT NULL, -- 'doubt_submitted', 'assignment_due', 'class_reminder', etc.
    "subject" character varying,
    "content" text NOT NULL,
    "variables" jsonb DEFAULT '[]'::jsonb, -- available template variables
    "is_active" boolean DEFAULT true,
    "send_delay_minutes" integer DEFAULT 0,
    "conditions" jsonb DEFAULT '{}'::jsonb, -- conditions for sending
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."notification_templates" ENABLE ROW LEVEL SECURITY;

-- Create notifications table for tracking sent notifications
CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "user_id" uuid NOT NULL,
    "notification_type" character varying NOT NULL,
    "channel" character varying NOT NULL, -- 'in_app', 'email', 'sms', 'push', 'whatsapp', 'telegram'
    "title" character varying NOT NULL,
    "content" text NOT NULL,
    "action_url" text,
    "is_read" boolean DEFAULT false,
    "read_at" timestamp with time zone,
    "is_sent" boolean DEFAULT false,
    "sent_at" timestamp with time zone,
    "delivery_status" character varying DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed', 'bounced'
    "error_message" text,
    "retry_count" integer DEFAULT 0,
    "scheduled_for" timestamp with time zone,
    "template_id" uuid,
    "related_entity_type" character varying, -- 'doubt', 'assignment', 'live_session', etc.
    "related_entity_id" uuid,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."notifications" ENABLE ROW LEVEL SECURITY;

-- Create external_integrations table for third-party messaging
CREATE TABLE IF NOT EXISTS "public"."external_integrations" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "organization_id" uuid NOT NULL,
    "integration_type" character varying NOT NULL, -- 'whatsapp', 'telegram', 'slack', 'discord'
    "integration_name" character varying NOT NULL,
    "config" jsonb NOT NULL DEFAULT '{}'::jsonb, -- API keys, webhooks, etc.
    "is_active" boolean DEFAULT true,
    "last_sync" timestamp with time zone,
    "sync_status" character varying DEFAULT 'connected', -- 'connected', 'disconnected', 'error'
    "error_message" text,
    "usage_stats" jsonb DEFAULT '{}'::jsonb,
    "rate_limits" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."external_integrations" ENABLE ROW LEVEL SECURITY;

-- Add Primary Keys
ALTER TABLE "public"."doubt_submissions" ADD CONSTRAINT "doubt_submissions_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."doubt_responses" ADD CONSTRAINT "doubt_responses_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."forum_categories" ADD CONSTRAINT "forum_categories_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."forum_posts" ADD CONSTRAINT "forum_posts_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."forum_replies" ADD CONSTRAINT "forum_replies_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."user_reputation" ADD CONSTRAINT "user_reputation_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."messaging_channels" ADD CONSTRAINT "messaging_channels_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."messaging_channel_members" ADD CONSTRAINT "messaging_channel_members_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."messages" ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."notification_templates" ADD CONSTRAINT "notification_templates_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."notifications" ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."external_integrations" ADD CONSTRAINT "external_integrations_pkey" PRIMARY KEY ("id");

-- Add Foreign Key Constraints
ALTER TABLE "public"."doubt_submissions" ADD CONSTRAINT "doubt_submissions_student_id_fkey"
    FOREIGN KEY ("student_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_submissions" ADD CONSTRAINT "doubt_submissions_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_submissions" ADD CONSTRAINT "doubt_submissions_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."doubt_submissions" ADD CONSTRAINT "doubt_submissions_chapter_id_fkey"
    FOREIGN KEY ("chapter_id") REFERENCES "chapter"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."doubt_submissions" ADD CONSTRAINT "doubt_submissions_lesson_id_fkey"
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."doubt_submissions" ADD CONSTRAINT "doubt_submissions_assigned_to_fkey"
    FOREIGN KEY ("assigned_to") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."doubt_responses" ADD CONSTRAINT "doubt_responses_doubt_id_fkey"
    FOREIGN KEY ("doubt_id") REFERENCES "doubt_submissions"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_responses" ADD CONSTRAINT "doubt_responses_responder_id_fkey"
    FOREIGN KEY ("responder_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_responses" ADD CONSTRAINT "doubt_responses_parent_response_id_fkey"
    FOREIGN KEY ("parent_response_id") REFERENCES "doubt_responses"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."forum_categories" ADD CONSTRAINT "forum_categories_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."forum_categories" ADD CONSTRAINT "forum_categories_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."forum_categories" ADD CONSTRAINT "forum_categories_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."forum_posts" ADD CONSTRAINT "forum_posts_category_id_fkey"
    FOREIGN KEY ("category_id") REFERENCES "forum_categories"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."forum_posts" ADD CONSTRAINT "forum_posts_author_id_fkey"
    FOREIGN KEY ("author_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."forum_posts" ADD CONSTRAINT "forum_posts_last_reply_by_fkey"
    FOREIGN KEY ("last_reply_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."forum_posts" ADD CONSTRAINT "forum_posts_moderated_by_fkey"
    FOREIGN KEY ("moderated_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."forum_replies" ADD CONSTRAINT "forum_replies_post_id_fkey"
    FOREIGN KEY ("post_id") REFERENCES "forum_posts"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."forum_replies" ADD CONSTRAINT "forum_replies_author_id_fkey"
    FOREIGN KEY ("author_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."forum_replies" ADD CONSTRAINT "forum_replies_parent_reply_id_fkey"
    FOREIGN KEY ("parent_reply_id") REFERENCES "forum_replies"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."forum_replies" ADD CONSTRAINT "forum_replies_moderated_by_fkey"
    FOREIGN KEY ("moderated_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."user_reputation" ADD CONSTRAINT "user_reputation_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."user_reputation" ADD CONSTRAINT "user_reputation_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."messaging_channels" ADD CONSTRAINT "messaging_channels_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."messaging_channels" ADD CONSTRAINT "messaging_channels_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."messaging_channels" ADD CONSTRAINT "messaging_channels_created_by_fkey"
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."messaging_channels" ADD CONSTRAINT "messaging_channels_last_message_by_fkey"
    FOREIGN KEY ("last_message_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."messaging_channel_members" ADD CONSTRAINT "messaging_channel_members_channel_id_fkey"
    FOREIGN KEY ("channel_id") REFERENCES "messaging_channels"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."messaging_channel_members" ADD CONSTRAINT "messaging_channel_members_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."messages" ADD CONSTRAINT "messages_channel_id_fkey"
    FOREIGN KEY ("channel_id") REFERENCES "messaging_channels"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."messages" ADD CONSTRAINT "messages_sender_id_fkey"
    FOREIGN KEY ("sender_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."messages" ADD CONSTRAINT "messages_recipient_id_fkey"
    FOREIGN KEY ("recipient_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."messages" ADD CONSTRAINT "messages_reply_to_id_fkey"
    FOREIGN KEY ("reply_to_id") REFERENCES "messages"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."notification_templates" ADD CONSTRAINT "notification_templates_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."notifications" ADD CONSTRAINT "notifications_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."notifications" ADD CONSTRAINT "notifications_template_id_fkey"
    FOREIGN KEY ("template_id") REFERENCES "notification_templates"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."external_integrations" ADD CONSTRAINT "external_integrations_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

-- Add Indexes for Performance
CREATE INDEX IF NOT EXISTS "idx_doubt_submissions_student" ON "public"."doubt_submissions"("student_id");
CREATE INDEX IF NOT EXISTS "idx_doubt_submissions_batch" ON "public"."doubt_submissions"("batch_id");
CREATE INDEX IF NOT EXISTS "idx_doubt_submissions_status" ON "public"."doubt_submissions"("status");
CREATE INDEX IF NOT EXISTS "idx_doubt_submissions_assigned" ON "public"."doubt_submissions"("assigned_to");
CREATE INDEX IF NOT EXISTS "idx_doubt_submissions_created" ON "public"."doubt_submissions"("created_at");
CREATE INDEX IF NOT EXISTS "idx_doubt_submissions_priority" ON "public"."doubt_submissions"("priority");

CREATE INDEX IF NOT EXISTS "idx_doubt_responses_doubt" ON "public"."doubt_responses"("doubt_id");
CREATE INDEX IF NOT EXISTS "idx_doubt_responses_responder" ON "public"."doubt_responses"("responder_id");
CREATE INDEX IF NOT EXISTS "idx_doubt_responses_created" ON "public"."doubt_responses"("created_at");

CREATE INDEX IF NOT EXISTS "idx_forum_categories_org" ON "public"."forum_categories"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_forum_categories_batch" ON "public"."forum_categories"("batch_id");
CREATE INDEX IF NOT EXISTS "idx_forum_categories_active" ON "public"."forum_categories"("is_active");

CREATE INDEX IF NOT EXISTS "idx_forum_posts_category" ON "public"."forum_posts"("category_id");
CREATE INDEX IF NOT EXISTS "idx_forum_posts_author" ON "public"."forum_posts"("author_id");
CREATE INDEX IF NOT EXISTS "idx_forum_posts_created" ON "public"."forum_posts"("created_at");
CREATE INDEX IF NOT EXISTS "idx_forum_posts_pinned" ON "public"."forum_posts"("is_pinned");
CREATE INDEX IF NOT EXISTS "idx_forum_posts_moderation" ON "public"."forum_posts"("moderation_status");

CREATE INDEX IF NOT EXISTS "idx_forum_replies_post" ON "public"."forum_replies"("post_id");
CREATE INDEX IF NOT EXISTS "idx_forum_replies_author" ON "public"."forum_replies"("author_id");
CREATE INDEX IF NOT EXISTS "idx_forum_replies_created" ON "public"."forum_replies"("created_at");

CREATE INDEX IF NOT EXISTS "idx_user_reputation_user" ON "public"."user_reputation"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_reputation_org" ON "public"."user_reputation"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_user_reputation_points" ON "public"."user_reputation"("total_points");

CREATE INDEX IF NOT EXISTS "idx_messaging_channels_batch" ON "public"."messaging_channels"("batch_id");
CREATE INDEX IF NOT EXISTS "idx_messaging_channels_type" ON "public"."messaging_channels"("channel_type");
CREATE INDEX IF NOT EXISTS "idx_messaging_channels_active" ON "public"."messaging_channels"("is_active");

CREATE INDEX IF NOT EXISTS "idx_messaging_channel_members_channel" ON "public"."messaging_channel_members"("channel_id");
CREATE INDEX IF NOT EXISTS "idx_messaging_channel_members_user" ON "public"."messaging_channel_members"("user_id");

CREATE INDEX IF NOT EXISTS "idx_messages_channel" ON "public"."messages"("channel_id");
CREATE INDEX IF NOT EXISTS "idx_messages_sender" ON "public"."messages"("sender_id");
CREATE INDEX IF NOT EXISTS "idx_messages_recipient" ON "public"."messages"("recipient_id");
CREATE INDEX IF NOT EXISTS "idx_messages_created" ON "public"."messages"("created_at");
CREATE INDEX IF NOT EXISTS "idx_messages_deleted" ON "public"."messages"("is_deleted");

CREATE INDEX IF NOT EXISTS "idx_notifications_user" ON "public"."notifications"("user_id");
CREATE INDEX IF NOT EXISTS "idx_notifications_type" ON "public"."notifications"("notification_type");
CREATE INDEX IF NOT EXISTS "idx_notifications_channel" ON "public"."notifications"("channel");
CREATE INDEX IF NOT EXISTS "idx_notifications_read" ON "public"."notifications"("is_read");
CREATE INDEX IF NOT EXISTS "idx_notifications_sent" ON "public"."notifications"("is_sent");
CREATE INDEX IF NOT EXISTS "idx_notifications_scheduled" ON "public"."notifications"("scheduled_for");

CREATE INDEX IF NOT EXISTS "idx_notification_templates_org" ON "public"."notification_templates"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_notification_templates_type" ON "public"."notification_templates"("template_type");
CREATE INDEX IF NOT EXISTS "idx_notification_templates_trigger" ON "public"."notification_templates"("event_trigger");

CREATE INDEX IF NOT EXISTS "idx_external_integrations_org" ON "public"."external_integrations"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_external_integrations_type" ON "public"."external_integrations"("integration_type");
CREATE INDEX IF NOT EXISTS "idx_external_integrations_active" ON "public"."external_integrations"("is_active");

-- Add Unique Constraints
ALTER TABLE "public"."user_reputation" ADD CONSTRAINT "unique_user_org_reputation"
    UNIQUE ("user_id", "organization_id");

ALTER TABLE "public"."messaging_channel_members" ADD CONSTRAINT "unique_channel_user_member"
    UNIQUE ("channel_id", "user_id");

ALTER TABLE "public"."external_integrations" ADD CONSTRAINT "unique_org_integration_type"
    UNIQUE ("organization_id", "integration_type", "integration_name");

-- Create Functions for Communication Management

-- Function to submit a doubt with AI categorization
CREATE OR REPLACE FUNCTION submit_doubt(
    p_student_id uuid,
    p_batch_id uuid,
    p_title character varying,
    p_description text,
    p_subject_id uuid DEFAULT NULL,
    p_chapter_id uuid DEFAULT NULL,
    p_lesson_id uuid DEFAULT NULL,
    p_doubt_type character varying DEFAULT 'general',
    p_tags jsonb DEFAULT NULL,
    p_attachments jsonb DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    doubt_id uuid;
    ai_category character varying;
    ai_confidence numeric;
BEGIN
    -- Simple AI categorization (in production, this would call an AI service)
    IF p_description ILIKE '%homework%' OR p_description ILIKE '%assignment%' THEN
        ai_category := 'homework';
        ai_confidence := 0.8;
    ELSIF p_description ILIKE '%exam%' OR p_description ILIKE '%test%' THEN
        ai_category := 'exam';
        ai_confidence := 0.85;
    ELSIF p_description ILIKE '%concept%' OR p_description ILIKE '%understand%' THEN
        ai_category := 'concept';
        ai_confidence := 0.75;
    ELSE
        ai_category := 'general';
        ai_confidence := 0.6;
    END IF;

    -- Insert doubt submission
    INSERT INTO doubt_submissions (
        student_id, batch_id, subject_id, chapter_id, lesson_id,
        title, description, doubt_type, ai_category, ai_confidence,
        tags, attachments
    ) VALUES (
        p_student_id, p_batch_id, p_subject_id, p_chapter_id, p_lesson_id,
        p_title, p_description, p_doubt_type, ai_category, ai_confidence,
        COALESCE(p_tags, '[]'::jsonb), COALESCE(p_attachments, '[]'::jsonb)
    ) RETURNING id INTO doubt_id;

    -- Update user reputation for asking a question
    PERFORM update_user_reputation(p_student_id, 'doubt_submitted', 5);

    RETURN doubt_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update user reputation
CREATE OR REPLACE FUNCTION update_user_reputation(
    p_user_id uuid,
    p_action character varying,
    p_points integer
)
RETURNS void AS $$
DECLARE
    org_id uuid;
    current_points integer;
    new_level integer;
BEGIN
    -- Get user's organization
    SELECT organization_id INTO org_id
    FROM profile
    WHERE id = p_user_id;

    -- Insert or update reputation record
    INSERT INTO user_reputation (user_id, organization_id, total_points)
    VALUES (p_user_id, org_id, p_points)
    ON CONFLICT (user_id, organization_id)
    DO UPDATE SET
        total_points = user_reputation.total_points + p_points,
        weekly_points = user_reputation.weekly_points + p_points,
        monthly_points = user_reputation.monthly_points + p_points,
        last_activity = now(),
        updated_at = now();

    -- Calculate new level (every 100 points = 1 level)
    SELECT total_points INTO current_points
    FROM user_reputation
    WHERE user_id = p_user_id AND organization_id = org_id;

    new_level := GREATEST(1, current_points / 100);

    UPDATE user_reputation
    SET level = new_level
    WHERE user_id = p_user_id AND organization_id = org_id;

    -- Update points breakdown
    UPDATE user_reputation
    SET points_breakdown = jsonb_set(
        COALESCE(points_breakdown, '{}'::jsonb),
        ARRAY[p_action],
        to_jsonb(COALESCE((points_breakdown->>p_action)::integer, 0) + p_points)
    )
    WHERE user_id = p_user_id AND organization_id = org_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create a forum post
CREATE OR REPLACE FUNCTION create_forum_post(
    p_category_id uuid,
    p_author_id uuid,
    p_title character varying,
    p_content text,
    p_post_type character varying DEFAULT 'discussion',
    p_tags jsonb DEFAULT NULL,
    p_attachments jsonb DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    post_id uuid;
BEGIN
    -- Insert forum post
    INSERT INTO forum_posts (
        category_id, author_id, title, content, post_type, tags, attachments
    ) VALUES (
        p_category_id, p_author_id, p_title, p_content, p_post_type,
        COALESCE(p_tags, '[]'::jsonb), COALESCE(p_attachments, '[]'::jsonb)
    ) RETURNING id INTO post_id;

    -- Update category post count
    UPDATE forum_categories
    SET post_count = post_count + 1,
        last_post_at = now()
    WHERE id = p_category_id;

    -- Update user reputation for creating a post
    PERFORM update_user_reputation(p_author_id, 'forum_post_created', 10);

    RETURN post_id;
END;
$$ LANGUAGE plpgsql;

-- Function to send a notification
CREATE OR REPLACE FUNCTION send_notification(
    p_user_id uuid,
    p_notification_type character varying,
    p_channel character varying,
    p_title character varying,
    p_content text,
    p_action_url text DEFAULT NULL,
    p_template_id uuid DEFAULT NULL,
    p_related_entity_type character varying DEFAULT NULL,
    p_related_entity_id uuid DEFAULT NULL,
    p_scheduled_for timestamp with time zone DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    notification_id uuid;
BEGIN
    INSERT INTO notifications (
        user_id, notification_type, channel, title, content,
        action_url, template_id, related_entity_type, related_entity_id,
        scheduled_for
    ) VALUES (
        p_user_id, p_notification_type, p_channel, p_title, p_content,
        p_action_url, p_template_id, p_related_entity_type, p_related_entity_id,
        COALESCE(p_scheduled_for, now())
    ) RETURNING id INTO notification_id;

    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create a messaging channel
CREATE OR REPLACE FUNCTION create_messaging_channel(
    p_name character varying,
    p_channel_type character varying,
    p_created_by uuid,
    p_batch_id uuid DEFAULT NULL,
    p_subject_id uuid DEFAULT NULL,
    p_description text DEFAULT NULL,
    p_member_ids jsonb DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    channel_id uuid;
    member_id uuid;
BEGIN
    -- Insert messaging channel
    INSERT INTO messaging_channels (
        name, description, channel_type, batch_id, subject_id, created_by
    ) VALUES (
        p_name, p_description, p_channel_type, p_batch_id, p_subject_id, p_created_by
    ) RETURNING id INTO channel_id;

    -- Add creator as admin
    INSERT INTO messaging_channel_members (
        channel_id, user_id, role
    ) VALUES (
        channel_id, p_created_by, 'admin'
    );

    -- Add other members if provided
    IF p_member_ids IS NOT NULL THEN
        FOR member_id IN SELECT jsonb_array_elements_text(p_member_ids)::uuid
        LOOP
            INSERT INTO messaging_channel_members (
                channel_id, user_id, role
            ) VALUES (
                channel_id, member_id, 'member'
            ) ON CONFLICT (channel_id, user_id) DO NOTHING;
        END LOOP;
    END IF;

    -- Update member count
    UPDATE messaging_channels
    SET member_count = (
        SELECT COUNT(*)
        FROM messaging_channel_members
        WHERE channel_id = messaging_channels.id
    )
    WHERE id = channel_id;

    RETURN channel_id;
END;
$$ LANGUAGE plpgsql;
