// Dashboard Service
// Service for managing custom dashboards and widgets

import { supabase } from '$lib/utils/functions/supabase';
import type {
  DashboardConfig,
  DashboardWidget,
  ReportTemplate,
  ScheduledReport,
  RealTimeMetric,
  LiveDashboardData
} from '$lib/utils/types/analytics';

export class DashboardService {
  // Dashboard Configuration Management
  async getDashboards(
    organizationId: string,
    dashboardType?: string,
    userId?: string
  ): Promise<DashboardConfig[]> {
    let query = supabase
      .from('dashboard_configs')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (dashboardType) {
      query = query.eq('dashboard_type', dashboardType);
    }

    if (userId) {
      query = query.or(`is_public.eq.true,created_by.eq.${userId}`);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async getDashboard(dashboardId: string): Promise<DashboardConfig | null> {
    const { data, error } = await supabase
      .from('dashboard_configs')
      .select('*')
      .eq('id', dashboardId)
      .single();

    if (error) throw error;
    return data;
  }

  async createDashboard(dashboardData: Partial<DashboardConfig>): Promise<DashboardConfig> {
    const { data, error } = await supabase
      .from('dashboard_configs')
      .insert({
        name: dashboardData.name,
        description: dashboardData.description,
        dashboard_type: dashboardData.dashboard_type,
        organization_id: dashboardData.organization_id,
        created_by: dashboardData.created_by,
        is_public: dashboardData.is_public || false,
        is_default: dashboardData.is_default || false,
        config: dashboardData.config || { widgets: [], layout: {}, theme: {} },
        filters: dashboardData.filters || {},
        permissions: dashboardData.permissions || {}
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateDashboard(
    dashboardId: string,
    updates: Partial<DashboardConfig>
  ): Promise<DashboardConfig> {
    const { data, error } = await supabase
      .from('dashboard_configs')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', dashboardId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteDashboard(dashboardId: string): Promise<void> {
    const { error } = await supabase
      .from('dashboard_configs')
      .delete()
      .eq('id', dashboardId);

    if (error) throw error;
  }

  async incrementDashboardUsage(dashboardId: string): Promise<void> {
    const { error } = await supabase
      .from('dashboard_configs')
      .update({
        usage_count: supabase.raw('usage_count + 1'),
        last_used_at: new Date().toISOString()
      })
      .eq('id', dashboardId);

    if (error) throw error;
  }

  // Widget Data Fetching
  async getWidgetData(
    widget: DashboardWidget,
    filters: Record<string, any> = {}
  ): Promise<any> {
    const { data_source } = widget;

    switch (data_source.type) {
      case 'sql':
        return this.executeSQLQuery(data_source.query!, { ...data_source.parameters, ...filters });
      
      case 'function':
        return this.callDatabaseFunction(data_source.function_name!, { ...data_source.parameters, ...filters });
      
      case 'api':
        return this.callAPIEndpoint(data_source.endpoint!, { ...data_source.parameters, ...filters });
      
      default:
        throw new Error(`Unsupported data source type: ${data_source.type}`);
    }
  }

  private async executeSQLQuery(query: string, parameters: Record<string, any>): Promise<any> {
    // Replace parameters in query
    let processedQuery = query;
    Object.entries(parameters).forEach(([key, value]) => {
      processedQuery = processedQuery.replace(new RegExp(`\\$${key}`, 'g'), value);
    });

    const { data, error } = await supabase.rpc('execute_analytics_query', {
      query: processedQuery
    });

    if (error) throw error;
    return data;
  }

  private async callDatabaseFunction(functionName: string, parameters: Record<string, any>): Promise<any> {
    const { data, error } = await supabase.rpc(functionName, parameters);
    if (error) throw error;
    return data;
  }

  private async callAPIEndpoint(endpoint: string, parameters: Record<string, any>): Promise<any> {
    const url = new URL(endpoint);
    Object.entries(parameters).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    const response = await fetch(url.toString());
    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  // Real-time Dashboard Data
  async getLiveDashboardData(organizationId: string): Promise<LiveDashboardData> {
    const [activeUsers, liveSessions, systemHealth, recentActivities, alerts] = await Promise.all([
      this.getActiveUsersCount(organizationId),
      this.getLiveSessionsCount(organizationId),
      this.getSystemHealth(organizationId),
      this.getRecentActivities(organizationId, 10),
      this.getActiveAlerts(organizationId)
    ]);

    return {
      active_users: activeUsers,
      live_sessions: liveSessions,
      system_health: systemHealth,
      recent_activities: recentActivities,
      alerts: alerts
    };
  }

  private async getActiveUsersCount(organizationId: string): Promise<number> {
    const { data, error } = await supabase
      .from('analytics_events')
      .select('user_id')
      .eq('organization_id', organizationId)
      .gte('created_at', new Date(Date.now() - 15 * 60 * 1000).toISOString()) // Last 15 minutes
      .not('user_id', 'is', null);

    if (error) throw error;
    
    const uniqueUsers = new Set(data?.map(event => event.user_id));
    return uniqueUsers.size;
  }

  private async getLiveSessionsCount(organizationId: string): Promise<number> {
    const { data, error } = await supabase
      .from('live_session')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('status', 'active');

    if (error) throw error;
    return data?.length || 0;
  }

  private async getSystemHealth(organizationId: string): Promise<any> {
    // This would typically fetch from system monitoring APIs
    // For now, return mock data
    return {
      cpu_usage: 45.2,
      memory_usage: 67.8,
      disk_usage: 23.1,
      response_time: 120,
      error_rate: 0.02,
      status: 'healthy'
    };
  }

  private async getRecentActivities(organizationId: string, limit: number): Promise<any[]> {
    const { data, error } = await supabase
      .from('analytics_events')
      .select(`
        id,
        user_id,
        event_type,
        event_action,
        created_at,
        profile:user_id(fullname)
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return data?.map(event => ({
      id: event.id,
      user_id: event.user_id,
      activity_type: event.event_type,
      description: `${event.event_action} - ${event.event_type}`,
      timestamp: event.created_at
    })) || [];
  }

  private async getActiveAlerts(organizationId: string): Promise<any[]> {
    // This would typically fetch from alerting system
    // For now, return mock data
    return [];
  }

  // Real-time Metrics
  async getRealTimeMetrics(
    organizationId: string,
    metricNames: string[]
  ): Promise<RealTimeMetric[]> {
    const metrics: RealTimeMetric[] = [];

    for (const metricName of metricNames) {
      const currentValue = await this.getCurrentMetricValue(organizationId, metricName);
      const previousValue = await this.getPreviousMetricValue(organizationId, metricName);
      
      const changePercentage = previousValue > 0 
        ? ((currentValue - previousValue) / previousValue) * 100 
        : 0;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (Math.abs(changePercentage) > 1) {
        trend = changePercentage > 0 ? 'up' : 'down';
      }

      metrics.push({
        metric_name: metricName,
        current_value: currentValue,
        previous_value: previousValue,
        change_percentage: changePercentage,
        trend: trend,
        timestamp: new Date().toISOString()
      });
    }

    return metrics;
  }

  private async getCurrentMetricValue(organizationId: string, metricName: string): Promise<number> {
    const { data, error } = await supabase
      .from('system_analytics')
      .select('metric_value')
      .eq('organization_id', organizationId)
      .eq('metric_name', metricName)
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) return 0;
    return data?.metric_value || 0;
  }

  private async getPreviousMetricValue(organizationId: string, metricName: string): Promise<number> {
    const { data, error } = await supabase
      .from('system_analytics')
      .select('metric_value')
      .eq('organization_id', organizationId)
      .eq('metric_name', metricName)
      .gte('created_at', new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()) // 2 hours ago
      .lt('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // 1 hour ago
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) return 0;
    return data?.metric_value || 0;
  }

  // Dashboard Templates
  async getDefaultDashboardTemplate(dashboardType: string): Promise<Partial<DashboardConfig>> {
    const templates = {
      student: {
        name: 'Student Dashboard',
        dashboard_type: 'student',
        config: {
          widgets: [
            {
              id: 'engagement-score',
              type: 'metric',
              title: 'Engagement Score',
              position: { x: 0, y: 0 },
              size: { width: 4, height: 2 },
              config: { chart_type: 'gauge' },
              data_source: {
                type: 'function',
                function_name: 'get_student_engagement_metrics',
                parameters: {}
              },
              filters: []
            },
            {
              id: 'study-time',
              type: 'chart',
              title: 'Study Time Trend',
              position: { x: 4, y: 0 },
              size: { width: 8, height: 4 },
              config: { chart_type: 'line' },
              data_source: {
                type: 'sql',
                query: 'SELECT date, total_study_time_minutes FROM learning_analytics WHERE user_id = $user_id ORDER BY date',
                parameters: {}
              },
              filters: []
            }
          ],
          layout: {
            columns: 12,
            row_height: 60,
            margin: [10, 10],
            container_padding: [20, 20]
          },
          theme: {
            primary_color: '#3B82F6',
            secondary_color: '#10B981',
            background_color: '#F9FAFB',
            text_color: '#111827',
            font_family: 'Inter'
          }
        }
      },
      instructor: {
        name: 'Instructor Dashboard',
        dashboard_type: 'instructor',
        config: {
          widgets: [
            {
              id: 'batch-performance',
              type: 'chart',
              title: 'Batch Performance Overview',
              position: { x: 0, y: 0 },
              size: { width: 12, height: 6 },
              config: { chart_type: 'bar' },
              data_source: {
                type: 'function',
                function_name: 'get_batch_performance_analytics',
                parameters: {}
              },
              filters: []
            }
          ],
          layout: {
            columns: 12,
            row_height: 60,
            margin: [10, 10],
            container_padding: [20, 20]
          },
          theme: {
            primary_color: '#3B82F6',
            secondary_color: '#10B981',
            background_color: '#F9FAFB',
            text_color: '#111827',
            font_family: 'Inter'
          }
        }
      },
      admin: {
        name: 'Admin Dashboard',
        dashboard_type: 'admin',
        config: {
          widgets: [
            {
              id: 'organization-overview',
              type: 'metric',
              title: 'Organization Overview',
              position: { x: 0, y: 0 },
              size: { width: 12, height: 8 },
              config: {},
              data_source: {
                type: 'function',
                function_name: 'get_organization_analytics',
                parameters: {}
              },
              filters: []
            }
          ],
          layout: {
            columns: 12,
            row_height: 60,
            margin: [10, 10],
            container_padding: [20, 20]
          },
          theme: {
            primary_color: '#3B82F6',
            secondary_color: '#10B981',
            background_color: '#F9FAFB',
            text_color: '#111827',
            font_family: 'Inter'
          }
        }
      }
    };

    return templates[dashboardType] || templates.student;
  }
}

export const dashboardService = new DashboardService();
