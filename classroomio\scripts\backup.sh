#!/bin/bash

# ClassroomIO Backup Script
# Comprehensive backup solution for database, files, and configuration

set -euo pipefail

# Configuration
BACKUP_BASE_DIR="${BACKUP_DIR:-/backups}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/$BACKUP_TIMESTAMP"
COMPRESS_BACKUPS="${COMPRESS_BACKUPS:-true}"
UPLOAD_TO_S3="${UPLOAD_TO_S3:-false}"
S3_BUCKET="${BACKUP_S3_BUCKET:-}"
NOTIFICATION_WEBHOOK="${BACKUP_NOTIFICATION_WEBHOOK:-}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[BACKUP-INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[BACKUP-SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[BACKUP-WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[BACKUP-ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Error handling
cleanup_on_error() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Backup failed with exit code $exit_code"
        
        # Clean up partial backup
        if [ -d "$BACKUP_DIR" ]; then
            log_info "Cleaning up partial backup directory..."
            rm -rf "$BACKUP_DIR"
        fi
        
        # Send failure notification
        send_notification "FAILED" "Backup failed with exit code $exit_code"
    fi
    exit $exit_code
}

trap cleanup_on_error EXIT

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$NOTIFICATION_WEBHOOK" ]; then
        local color="good"
        if [ "$status" = "FAILED" ]; then
            color="danger"
        elif [ "$status" = "WARNING" ]; then
            color="warning"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"title\":\"ClassroomIO Backup $status\",\"text\":\"$message\",\"ts\":$(date +%s)}]}" \
            "$NOTIFICATION_WEBHOOK" 2>/dev/null || true
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking backup prerequisites..."
    
    # Check if backup directory exists
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        log_info "Creating backup directory: $BACKUP_BASE_DIR"
        mkdir -p "$BACKUP_BASE_DIR"
    fi
    
    # Check disk space
    local available_space=$(df "$BACKUP_BASE_DIR" | awk 'NR==2 {print $4}')
    local required_space=1048576  # 1GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        log_error "Insufficient disk space. Available: ${available_space}KB, Required: ${required_space}KB"
        exit 1
    fi
    
    # Check required tools
    local required_tools=("pg_dump" "tar" "gzip")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Check AWS CLI if S3 upload is enabled
    if [ "$UPLOAD_TO_S3" = "true" ]; then
        if ! command -v "aws" &> /dev/null; then
            log_error "AWS CLI not found but S3 upload is enabled"
            exit 1
        fi
        
        if [ -z "$S3_BUCKET" ]; then
            log_error "S3_BUCKET not specified but S3 upload is enabled"
            exit 1
        fi
    fi
    
    log_success "Prerequisites check completed"
}

# Create backup directory
create_backup_directory() {
    log_info "Creating backup directory: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    
    # Create subdirectories
    mkdir -p "$BACKUP_DIR/database"
    mkdir -p "$BACKUP_DIR/files"
    mkdir -p "$BACKUP_DIR/config"
    mkdir -p "$BACKUP_DIR/logs"
}

# Backup database
backup_database() {
    log_info "Starting database backup..."
    
    local db_backup_file="$BACKUP_DIR/database/classroomio_${BACKUP_TIMESTAMP}.sql"
    
    # Extract database connection details
    if [ -z "${DATABASE_URL:-}" ]; then
        log_error "DATABASE_URL not set"
        return 1
    fi
    
    # Parse DATABASE_URL
    local db_url="$DATABASE_URL"
    local db_host=$(echo "$db_url" | sed -n 's/.*@\([^:]*\):.*/\1/p')
    local db_port=$(echo "$db_url" | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    local db_name=$(echo "$db_url" | sed -n 's/.*\/\([^?]*\).*/\1/p')
    local db_user=$(echo "$db_url" | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
    local db_pass=$(echo "$db_url" | sed -n 's/.*\/\/[^:]*:\([^@]*\)@.*/\1/p')
    
    # Set PostgreSQL password
    export PGPASSWORD="$db_pass"
    
    # Create database dump
    if pg_dump -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" \
        --verbose --clean --if-exists --create \
        --format=custom --compress=9 \
        --file="$db_backup_file.custom" 2>/dev/null; then
        
        # Also create plain SQL dump for easier inspection
        pg_dump -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" \
            --verbose --clean --if-exists --create \
            --format=plain > "$db_backup_file" 2>/dev/null
        
        local db_size=$(du -h "$db_backup_file" | cut -f1)
        log_success "Database backup completed: $db_size"
        
        # Create backup metadata
        cat > "$BACKUP_DIR/database/metadata.json" << EOF
{
  "timestamp": "$BACKUP_TIMESTAMP",
  "database": "$db_name",
  "host": "$db_host",
  "port": "$db_port",
  "format": "custom",
  "size": "$db_size",
  "compressed": true
}
EOF
        
    else
        log_error "Database backup failed"
        return 1
    fi
    
    # Clean up password
    unset PGPASSWORD
}

# Backup uploaded files
backup_files() {
    log_info "Starting file backup..."
    
    local files_to_backup=(
        "uploads"
        "public/uploads"
        "storage"
        "assets"
    )
    
    local backed_up_files=()
    
    for file_path in "${files_to_backup[@]}"; do
        if [ -d "$file_path" ]; then
            log_info "Backing up directory: $file_path"
            
            local backup_name=$(echo "$file_path" | tr '/' '_')
            local backup_file="$BACKUP_DIR/files/${backup_name}_${BACKUP_TIMESTAMP}.tar"
            
            if [ "$COMPRESS_BACKUPS" = "true" ]; then
                tar -czf "${backup_file}.gz" -C "$(dirname "$file_path")" "$(basename "$file_path")" 2>/dev/null
                backup_file="${backup_file}.gz"
            else
                tar -cf "$backup_file" -C "$(dirname "$file_path")" "$(basename "$file_path")" 2>/dev/null
            fi
            
            local file_size=$(du -h "$backup_file" | cut -f1)
            log_success "File backup completed: $file_path ($file_size)"
            backed_up_files+=("$file_path:$file_size")
        fi
    done
    
    # Create file backup metadata
    cat > "$BACKUP_DIR/files/metadata.json" << EOF
{
  "timestamp": "$BACKUP_TIMESTAMP",
  "files": [
$(printf '    "%s",' "${backed_up_files[@]}" | sed 's/,$//')
  ],
  "compressed": $COMPRESS_BACKUPS
}
EOF
}

# Backup configuration files
backup_configuration() {
    log_info "Starting configuration backup..."
    
    local config_files=(
        ".env.production"
        ".env.local"
        "docker-compose.production.yml"
        "docker-compose.yml"
        "nginx/nginx.conf"
        "monitoring/prometheus.yml"
        "supabase/config.toml"
        "package.json"
        "tsconfig.json"
    )
    
    for config_file in "${config_files[@]}"; do
        if [ -f "$config_file" ]; then
            log_info "Backing up config: $config_file"
            
            # Create directory structure in backup
            local backup_path="$BACKUP_DIR/config/$config_file"
            mkdir -p "$(dirname "$backup_path")"
            
            # Copy file (mask sensitive data in .env files)
            if [[ "$config_file" == *.env* ]]; then
                # Mask sensitive environment variables
                sed 's/\(PASSWORD\|SECRET\|KEY\|TOKEN\)=.*/\1=***MASKED***/g' "$config_file" > "$backup_path"
            else
                cp "$config_file" "$backup_path"
            fi
        fi
    done
    
    # Create system info
    cat > "$BACKUP_DIR/config/system_info.txt" << EOF
Backup Timestamp: $BACKUP_TIMESTAMP
System: $(uname -a)
Node Version: $(node --version 2>/dev/null || echo "Not available")
Docker Version: $(docker --version 2>/dev/null || echo "Not available")
Disk Usage: $(df -h / | tail -1)
Memory Usage: $(free -h | grep Mem)
EOF
    
    log_success "Configuration backup completed"
}

# Backup logs
backup_logs() {
    log_info "Starting log backup..."
    
    local log_dirs=(
        "logs"
        "/var/log/nginx"
        "/var/log/classroomio"
    )
    
    for log_dir in "${log_dirs[@]}"; do
        if [ -d "$log_dir" ]; then
            log_info "Backing up logs: $log_dir"
            
            local backup_name=$(echo "$log_dir" | tr '/' '_' | sed 's/^_//')
            local backup_file="$BACKUP_DIR/logs/${backup_name}_${BACKUP_TIMESTAMP}.tar.gz"
            
            # Only backup recent logs (last 7 days)
            find "$log_dir" -name "*.log" -mtime -7 -print0 | \
                tar -czf "$backup_file" --null -T - 2>/dev/null || true
            
            if [ -f "$backup_file" ]; then
                local log_size=$(du -h "$backup_file" | cut -f1)
                log_success "Log backup completed: $log_dir ($log_size)"
            fi
        fi
    done
}

# Upload to S3
upload_to_s3() {
    if [ "$UPLOAD_TO_S3" = "true" ]; then
        log_info "Uploading backup to S3: $S3_BUCKET"
        
        local s3_path="s3://$S3_BUCKET/classroomio-backups/$BACKUP_TIMESTAMP/"
        
        if aws s3 sync "$BACKUP_DIR" "$s3_path" --delete 2>/dev/null; then
            log_success "Backup uploaded to S3: $s3_path"
        else
            log_error "Failed to upload backup to S3"
            return 1
        fi
    fi
}

# Cleanup old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups (keeping last $BACKUP_RETENTION_DAYS days)..."
    
    # Local cleanup
    find "$BACKUP_BASE_DIR" -type d -name "20*" -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    
    # S3 cleanup
    if [ "$UPLOAD_TO_S3" = "true" ]; then
        local cutoff_date=$(date -d "$BACKUP_RETENTION_DAYS days ago" +%Y%m%d)
        aws s3 ls "s3://$S3_BUCKET/classroomio-backups/" | \
            awk '{print $2}' | \
            sed 's/\///g' | \
            while read -r backup_date; do
                if [[ "$backup_date" < "$cutoff_date" ]]; then
                    log_info "Removing old S3 backup: $backup_date"
                    aws s3 rm "s3://$S3_BUCKET/classroomio-backups/$backup_date/" --recursive 2>/dev/null || true
                fi
            done
    fi
    
    log_success "Old backup cleanup completed"
}

# Generate backup report
generate_backup_report() {
    local backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    local end_time=$(date '+%Y-%m-%d %H:%M:%S')
    
    cat > "$BACKUP_DIR/backup_report.json" << EOF
{
  "timestamp": "$BACKUP_TIMESTAMP",
  "start_time": "$(date '+%Y-%m-%d %H:%M:%S')",
  "end_time": "$end_time",
  "backup_size": "$backup_size",
  "backup_path": "$BACKUP_DIR",
  "s3_upload": $UPLOAD_TO_S3,
  "s3_bucket": "$S3_BUCKET",
  "retention_days": $BACKUP_RETENTION_DAYS,
  "components": {
    "database": true,
    "files": true,
    "configuration": true,
    "logs": true
  },
  "status": "completed"
}
EOF
    
    log_success "Backup completed successfully"
    log_info "Backup size: $backup_size"
    log_info "Backup location: $BACKUP_DIR"
    
    # Send success notification
    send_notification "SUCCESS" "Backup completed successfully. Size: $backup_size, Location: $BACKUP_DIR"
}

# Main execution
main() {
    local start_time=$(date '+%Y-%m-%d %H:%M:%S')
    log_info "Starting ClassroomIO backup at $start_time"
    
    # Run backup steps
    check_prerequisites
    create_backup_directory
    backup_database
    backup_files
    backup_configuration
    backup_logs
    upload_to_s3
    cleanup_old_backups
    generate_backup_report
    
    log_success "Backup process completed successfully"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
