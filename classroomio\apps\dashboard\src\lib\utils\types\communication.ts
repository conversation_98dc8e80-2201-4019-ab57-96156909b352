// Communication Types for Educational Platform
// Date: 2025-06-30

export interface DoubtSubmission {
  id: string;
  created_at: string;
  updated_at: string;
  student_id: string;
  batch_id: string;
  subject_id?: string;
  chapter_id?: string;
  lesson_id?: string;
  title: string;
  description: string;
  doubt_type: 'general' | 'concept' | 'homework' | 'exam' | 'technical';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'assigned' | 'in_progress' | 'resolved' | 'closed';
  assigned_to?: string;
  assigned_at?: string;
  resolved_at?: string;
  resolution_time_minutes?: number;
  ai_category?: string;
  ai_confidence?: number;
  ai_suggested_faqs: Array<{
    question: string;
    answer: string;
    confidence: number;
  }>;
  tags: string[];
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  is_anonymous: boolean;
  upvotes: number;
  views: number;
  metadata: Record<string, any>;
}

export interface DoubtResponse {
  id: string;
  created_at: string;
  updated_at: string;
  doubt_id: string;
  responder_id: string;
  response_text?: string;
  response_type: 'text' | 'voice' | 'video' | 'file';
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  is_solution: boolean;
  is_helpful: boolean;
  helpful_votes: number;
  parent_response_id?: string;
  metadata: Record<string, any>;
}

export interface ForumCategory {
  id: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  batch_id?: string;
  subject_id?: string;
  name: string;
  description?: string;
  icon?: string;
  color: string;
  is_active: boolean;
  is_public: boolean;
  sort_order: number;
  post_count: number;
  last_post_at?: string;
  moderators: string[]; // user IDs
  permissions: {
    can_create_posts: string[]; // roles that can create posts
    can_reply: string[]; // roles that can reply
    can_moderate: string[]; // roles that can moderate
  };
}

export interface ForumPost {
  id: string;
  created_at: string;
  updated_at: string;
  category_id: string;
  author_id: string;
  title: string;
  content: string;
  post_type: 'discussion' | 'question' | 'announcement' | 'poll';
  is_pinned: boolean;
  is_locked: boolean;
  is_solved: boolean;
  tags: string[];
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  upvotes: number;
  downvotes: number;
  views: number;
  reply_count: number;
  last_reply_at?: string;
  last_reply_by?: string;
  moderation_status: 'pending' | 'approved' | 'rejected' | 'flagged';
  moderated_by?: string;
  moderated_at?: string;
  metadata: Record<string, any>;
}

export interface ForumReply {
  id: string;
  created_at: string;
  updated_at: string;
  post_id: string;
  author_id: string;
  content: string;
  parent_reply_id?: string;
  is_solution: boolean;
  upvotes: number;
  downvotes: number;
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  moderation_status: 'pending' | 'approved' | 'rejected';
  moderated_by?: string;
  moderated_at?: string;
  metadata: Record<string, any>;
}

export interface UserReputation {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  organization_id: string;
  total_points: number;
  level: number;
  badges: Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    earned_at: string;
  }>;
  achievements: Array<{
    id: string;
    name: string;
    description: string;
    progress: number;
    target: number;
    completed: boolean;
    completed_at?: string;
  }>;
  activity_streak: number;
  last_activity: string;
  points_breakdown: Record<string, number>;
  monthly_points: number;
  weekly_points: number;
}

export interface MessagingChannel {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  channel_type: 'batch' | 'subject' | 'private' | 'announcement' | 'support';
  batch_id?: string;
  subject_id?: string;
  created_by: string;
  is_active: boolean;
  is_archived: boolean;
  member_count: number;
  last_message_at?: string;
  last_message_by?: string;
  permissions: {
    can_send_messages: string[]; // roles
    can_add_members: string[]; // roles
    can_remove_members: string[]; // roles
    can_edit_channel: string[]; // roles
  };
  settings: {
    allow_file_sharing: boolean;
    allow_voice_messages: boolean;
    message_retention_days: number;
    auto_delete_messages: boolean;
  };
}

export interface MessagingChannelMember {
  id: string;
  created_at: string;
  channel_id: string;
  user_id: string;
  role: 'admin' | 'moderator' | 'member';
  joined_at: string;
  last_read_at: string;
  is_muted: boolean;
  notification_settings: {
    push_notifications: boolean;
    email_notifications: boolean;
    mention_notifications: boolean;
  };
}

export interface Message {
  id: string;
  created_at: string;
  updated_at: string;
  channel_id?: string;
  sender_id: string;
  recipient_id?: string; // for direct messages
  message_type: 'text' | 'voice' | 'file' | 'image' | 'video' | 'system';
  content?: string;
  attachments: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
    size: number;
    thumbnail_url?: string;
    duration?: number; // for voice/video
  }>;
  reply_to_id?: string;
  is_edited: boolean;
  edited_at?: string;
  is_deleted: boolean;
  deleted_at?: string;
  reactions: Record<string, string[]>; // emoji -> user IDs
  read_by: string[]; // user IDs who read the message
  delivery_status: 'sent' | 'delivered' | 'read';
  metadata: Record<string, any>;
}

export interface NotificationTemplate {
  id: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  name: string;
  template_type: 'email' | 'sms' | 'push' | 'whatsapp' | 'telegram';
  event_trigger: string;
  subject?: string;
  content: string;
  variables: Array<{
    name: string;
    description: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    required: boolean;
  }>;
  is_active: boolean;
  send_delay_minutes: number;
  conditions: {
    user_roles?: string[];
    batch_ids?: string[];
    time_restrictions?: {
      start_time: string;
      end_time: string;
      timezone: string;
    };
  };
  metadata: Record<string, any>;
}

export interface Notification {
  id: string;
  created_at: string;
  user_id: string;
  notification_type: string;
  channel: 'in_app' | 'email' | 'sms' | 'push' | 'whatsapp' | 'telegram';
  title: string;
  content: string;
  action_url?: string;
  is_read: boolean;
  read_at?: string;
  is_sent: boolean;
  sent_at?: string;
  delivery_status: 'pending' | 'sent' | 'delivered' | 'failed' | 'bounced';
  error_message?: string;
  retry_count: number;
  scheduled_for?: string;
  template_id?: string;
  related_entity_type?: string;
  related_entity_id?: string;
  metadata: Record<string, any>;
}

export interface ExternalIntegration {
  id: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  integration_type: 'whatsapp' | 'telegram' | 'slack' | 'discord';
  integration_name: string;
  config: {
    api_key?: string;
    webhook_url?: string;
    bot_token?: string;
    phone_number?: string;
    business_account_id?: string;
  };
  is_active: boolean;
  last_sync?: string;
  sync_status: 'connected' | 'disconnected' | 'error';
  error_message?: string;
  usage_stats: {
    messages_sent: number;
    messages_received: number;
    last_message_at?: string;
  };
  rate_limits: {
    messages_per_minute: number;
    messages_per_hour: number;
    messages_per_day: number;
  };
}

export interface CommunicationAnalytics {
  doubt_metrics: {
    total_doubts: number;
    resolved_doubts: number;
    average_resolution_time: number;
    doubts_by_subject: Record<string, number>;
    doubts_by_priority: Record<string, number>;
    instructor_response_rate: number;
  };
  forum_metrics: {
    total_posts: number;
    total_replies: number;
    active_users: number;
    posts_by_category: Record<string, number>;
    engagement_rate: number;
  };
  messaging_metrics: {
    total_messages: number;
    active_channels: number;
    average_response_time: number;
    messages_by_type: Record<string, number>;
  };
  notification_metrics: {
    total_sent: number;
    delivery_rate: number;
    open_rate: number;
    click_rate: number;
    notifications_by_channel: Record<string, number>;
  };
}

export interface DoubtDashboard {
  pending_doubts: DoubtSubmission[];
  assigned_doubts: DoubtSubmission[];
  recent_doubts: DoubtSubmission[];
  doubt_stats: {
    total_pending: number;
    total_assigned: number;
    total_resolved_today: number;
    average_resolution_time: number;
  };
  ai_insights: {
    common_topics: Array<{
      topic: string;
      count: number;
      trend: 'up' | 'down' | 'stable';
    }>;
    suggested_faqs: Array<{
      question: string;
      frequency: number;
    }>;
  };
}
