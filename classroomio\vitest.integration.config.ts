// Vitest Integration Test Configuration
// Specialized configuration for integration tests

import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';
import { resolve } from 'path';

export default defineConfig({
  plugins: [sveltekit()],
  
  test: {
    // Test environment for integration tests
    environment: 'node',
    
    // Global setup and teardown
    globalSetup: ['./src/lib/utils/testing/global-setup.ts'],
    setupFiles: ['./src/lib/utils/testing/test-setup.ts'],
    
    // Integration test patterns
    include: [
      'src/**/*.integration.test.{js,ts}',
      'tests/integration/**/*.{test,spec}.{js,ts}'
    ],
    exclude: [
      'node_modules/**',
      'dist/**',
      'build/**',
      '.svelte-kit/**',
      'src/**/*.test.{js,ts}', // Exclude unit tests
      'src/**/*.spec.{js,ts}'  // Exclude unit tests
    ],
    
    // Test execution for integration tests
    globals: true,
    testTimeout: 30000, // Longer timeout for integration tests
    hookTimeout: 15000,
    teardownTimeout: 10000,
    
    // Sequential execution for integration tests to avoid conflicts
    threads: false,
    
    // Coverage configuration for integration tests
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage/integration',
      exclude: [
        'node_modules/**',
        'src/lib/utils/testing/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/dist/**',
        '**/build/**',
        '**/.svelte-kit/**'
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    },
    
    // Reporters for integration tests
    reporter: [
      'default',
      'json'
    ],
    outputFile: {
      json: './test-results/integration-results.json'
    },
    
    // Watch mode disabled for integration tests
    watch: false
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '$lib': resolve('./src/lib'),
      '$app': resolve('./node_modules/@sveltejs/kit/src/runtime/app'),
      '$env': resolve('./src/env'),
      '$service-worker': resolve('./src/service-worker')
    }
  },
  
  // Define configuration for integration tests
  define: {
    // Integration test environment variables
    'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(process.env.TEST_SUPABASE_URL || 'http://localhost:54321'),
    'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(process.env.TEST_SUPABASE_ANON_KEY || 'test-anon-key'),
    'import.meta.env.VITE_APP_ENV': JSON.stringify('integration-test')
  }
});
