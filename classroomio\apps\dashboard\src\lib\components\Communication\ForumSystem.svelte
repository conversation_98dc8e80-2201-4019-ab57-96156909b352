<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { ForumCategory, ForumPost, ForumReply, UserReputation } from '$lib/utils/types/communication';
  import { forumService } from '$lib/utils/services/communication';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import {
    Add,
    Send,
    Search,
    ThumbsUp,
    ThumbsDown,
    Reply,
    Pin,
    Lock,
    CheckmarkFilled,
    View,
    Time,
    User,
    Trophy,
    Star,
    Close,
    Filter
  } from 'carbon-icons-svelte';

  export let organizationId: string;
  export let batchId: string | null = null;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    postCreated: { post: ForumPost };
    replyCreated: { reply: ForumReply };
  }>();

  let categories = writable<ForumCategory[]>([]);
  let selectedCategory = writable<ForumCategory | null>(null);
  let posts = writable<ForumPost[]>([]);
  let selectedPost = writable<ForumPost | null>(null);
  let replies = writable<ForumReply[]>([]);
  let userReputation = writable<UserReputation | null>(null);

  let loading = true;
  let error: string | null = null;
  let showCreatePost = false;
  let showCreateReply = false;
  let searchQuery = '';
  let postTypeFilter = 'all';

  // Form data
  let postTitle = '';
  let postContent = '';
  let postType: 'discussion' | 'question' | 'announcement' | 'poll' = 'discussion';
  let postTags: string[] = [];
  let replyContent = '';

  $: userId = $globalStore.user?.id;
  $: isInstructor = $globalStore.role === 'teacher' || $globalStore.isOrgAdmin;
  $: canModerate = isInstructor;

  onMount(async () => {
    await loadCategories();
  });

  async function loadCategories() {
    try {
      loading = true;
      error = null;

      const categoriesData = await forumService.getCategories(organizationId, batchId);
      categories.set(categoriesData);

      // Auto-select first category if available
      if (categoriesData.length > 0) {
        await selectCategory(categoriesData[0]);
      }

    } catch (err) {
      console.error('Error loading categories:', err);
      error = err.message || 'Failed to load forum categories';
    } finally {
      loading = false;
    }
  }

  async function selectCategory(category: ForumCategory) {
    selectedCategory.set(category);
    selectedPost.set(null);
    await loadPosts(category.id);
  }

  async function loadPosts(categoryId: string) {
    try {
      const filters: any = {};
      if (postTypeFilter !== 'all') filters.post_type = postTypeFilter;

      const postsData = await forumService.getPosts(categoryId, filters);
      posts.set(postsData);

    } catch (err) {
      console.error('Error loading posts:', err);
      error = err.message || 'Failed to load posts';
    }
  }

  async function selectPost(post: ForumPost) {
    selectedPost.set(post);
    await loadReplies(post.id);
  }

  async function loadReplies(postId: string) {
    try {
      const repliesData = await forumService.getReplies(postId);
      replies.set(repliesData);
    } catch (err) {
      console.error('Error loading replies:', err);
      error = err.message || 'Failed to load replies';
    }
  }

  async function createPost() {
    if (!$selectedCategory || !userId || !postTitle.trim() || !postContent.trim()) return;

    try {
      const postData = {
        category_id: $selectedCategory.id,
        author_id: userId,
        title: postTitle.trim(),
        content: postContent.trim(),
        post_type: postType,
        tags: postTags,
        attachments: [],
        is_pinned: false,
        is_locked: false,
        is_solved: false,
        last_reply_at: null,
        last_reply_by: null,
        metadata: {}
      };

      const postId = await forumService.createPost(postData);

      // Reload posts
      await loadPosts($selectedCategory.id);

      // Reset form
      resetPostForm();
      showCreatePost = false;

      dispatch('postCreated', { post: { ...postData, id: postId } as ForumPost });

    } catch (err) {
      console.error('Error creating post:', err);
      error = err.message || 'Failed to create post';
    }
  }

  async function createReply() {
    if (!$selectedPost || !userId || !replyContent.trim()) return;

    try {
      const replyData = {
        post_id: $selectedPost.id,
        author_id: userId,
        content: replyContent.trim(),
        parent_reply_id: null,
        is_solution: false,
        attachments: [],
        metadata: {}
      };

      await forumService.createReply(replyData);

      // Reload replies
      await loadReplies($selectedPost.id);

      // Reset form
      replyContent = '';
      showCreateReply = false;

      dispatch('replyCreated', { reply: replyData as ForumReply });

    } catch (err) {
      console.error('Error creating reply:', err);
      error = err.message || 'Failed to create reply';
    }
  }

  async function searchPosts() {
    if (!searchQuery.trim()) {
      if ($selectedCategory) {
        await loadPosts($selectedCategory.id);
      }
      return;
    }

    try {
      const searchResults = await forumService.searchPosts(organizationId, searchQuery, {
        category_id: $selectedCategory?.id,
        batch_id: batchId
      });
      posts.set(searchResults);
    } catch (err) {
      console.error('Error searching posts:', err);
      error = err.message || 'Failed to search posts';
    }
  }

  function resetPostForm() {
    postTitle = '';
    postContent = '';
    postType = 'discussion';
    postTags = [];
  }

  function addTag(tag: string) {
    if (tag.trim() && !postTags.includes(tag.trim())) {
      postTags = [...postTags, tag.trim()];
    }
  }

  function removeTag(index: number) {
    postTags = postTags.filter((_, i) => i !== index);
  }

  function formatTimeAgo(timestamp: string): string {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  }

  function getPostTypeIcon(type: string) {
    switch (type) {
      case 'question': return '❓';
      case 'announcement': return '📢';
      case 'poll': return '📊';
      default: return '💬';
    }
  }

  function getPostTypeColor(type: string): string {
    switch (type) {
      case 'question': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'announcement': return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300';
      case 'poll': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  }

  $: filteredPosts = $posts.filter(post => {
    if (searchQuery && !post.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !post.content.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    return true;
  });
</script>

<div class="forum-system {className}">
  {#if !$selectedPost}
    <!-- Forum Categories and Posts View -->
    <div class="flex h-full">
      <!-- Categories Sidebar -->
      <div class="w-1/4 border-r border-gray-200 dark:border-gray-700 pr-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {$t('forum.categories', { default: 'Categories' })}
        </h3>

        {#if loading}
          <div class="space-y-2">
            {#each Array(3) as _}
              <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            {/each}
          </div>
        {:else}
          <div class="space-y-2">
            {#each $categories as category (category.id)}
              <button
                on:click={() => selectCategory(category)}
                class="w-full text-left p-3 rounded-lg transition-colors {$selectedCategory?.id === category.id
                  ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700'}"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    {#if category.icon}
                      <span class="text-lg mr-2">{category.icon}</span>
                    {/if}
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">
                        {category.name}
                      </p>
                      {#if category.description}
                        <p class="text-xs text-gray-600 dark:text-gray-400 line-clamp-1">
                          {category.description}
                        </p>
                      {/if}
                    </div>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                      {category.post_count}
                    </p>
                    <p class="text-xs text-gray-600 dark:text-gray-400">
                      {$t('forum.posts', { default: 'posts' })}
                    </p>
                  </div>
                </div>
              </button>
            {/each}
          </div>
        {/if}
      </div>

      <!-- Posts List -->
      <div class="flex-1 pl-4">
        {#if $selectedCategory}
          <!-- Header -->
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                {$selectedCategory.name}
              </h2>
              {#if $selectedCategory.description}
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                  {$selectedCategory.description}
                </p>
              {/if}
            </div>

            <PrimaryButton
              variant={VARIANTS.CONTAINED}
              onClick={() => showCreatePost = true}
            >
              <Add size={20} class="mr-2" />
              {$t('forum.new_post', { default: 'New Post' })}
            </PrimaryButton>
          </div>

          <!-- Search and Filters -->
          <div class="flex items-center space-x-4 mb-6">
            <div class="flex-1 relative">
              <Search size={20} class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                bind:value={searchQuery}
                on:input={searchPosts}
                placeholder={$t('forum.search_posts', { default: 'Search posts...' })}
                class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
              />
            </div>

            <select
              bind:value={postTypeFilter}
              on:change={() => $selectedCategory && loadPosts($selectedCategory.id)}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
            >
              <option value="all">{$t('forum.all_types', { default: 'All Types' })}</option>
              <option value="discussion">{$t('forum.discussions', { default: 'Discussions' })}</option>
              <option value="question">{$t('forum.questions', { default: 'Questions' })}</option>
              <option value="announcement">{$t('forum.announcements', { default: 'Announcements' })}</option>
              <option value="poll">{$t('forum.polls', { default: 'Polls' })}</option>
            </select>
          </div>

          <!-- Posts List -->
          {#if filteredPosts.length === 0}
            <Box className="text-center py-12">
              <User size={48} class="text-gray-400 mx-auto mb-4" />
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {$t('forum.no_posts', { default: 'No Posts Found' })}
              </h3>
              <p class="text-gray-600 dark:text-gray-400 mb-4">
                {$t('forum.no_posts_desc', { default: 'Be the first to start a discussion!' })}
              </p>
              <PrimaryButton
                variant={VARIANTS.CONTAINED}
                onClick={() => showCreatePost = true}
              >
                <Add size={20} class="mr-2" />
                {$t('forum.create_first_post', { default: 'Create First Post' })}
              </PrimaryButton>
            </Box>
          {:else}
            <div class="space-y-4">
              {#each filteredPosts as post (post.id)}
                <Box className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => selectPost(post)}>
                  <div class="p-4">
                    <div class="flex items-start justify-between mb-3">
                      <div class="flex items-start space-x-3 flex-1">
                        <span class="text-2xl">{getPostTypeIcon(post.post_type)}</span>
                        <div class="flex-1">
                          <div class="flex items-center space-x-2 mb-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                              {post.title}
                            </h3>
                            {#if post.is_pinned}
                              <Pin size={16} class="text-primary-600" />
                            {/if}
                            {#if post.is_locked}
                              <Lock size={16} class="text-gray-500" />
                            {/if}
                            {#if post.is_solved}
                              <CheckmarkFilled size={16} class="text-green-600" />
                            {/if}
                          </div>

                          <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex items-center">
                              <User size={16} class="mr-1" />
                              <span>{post.author?.fullname || 'Unknown'}</span>
                            </div>
                            <div class="flex items-center">
                              <Time size={16} class="mr-1" />
                              <span>{formatTimeAgo(post.created_at)}</span>
                            </div>
                            <div class="flex items-center">
                              <Reply size={16} class="mr-1" />
                              <span>{post.reply_count}</span>
                            </div>
                            <div class="flex items-center">
                              <View size={16} class="mr-1" />
                              <span>{post.views}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 text-xs rounded-full {getPostTypeColor(post.post_type)}">
                          {post.post_type.toUpperCase()}
                        </span>

                        <div class="flex items-center space-x-1">
                          <button class="flex items-center text-gray-600 dark:text-gray-400 hover:text-green-600">
                            <ThumbsUp size={16} class="mr-1" />
                            <span class="text-xs">{post.upvotes}</span>
                          </button>
                          <button class="flex items-center text-gray-600 dark:text-gray-400 hover:text-red-600">
                            <ThumbsDown size={16} class="mr-1" />
                            <span class="text-xs">{post.downvotes}</span>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Tags -->
                    {#if post.tags && post.tags.length > 0}
                      <div class="flex flex-wrap gap-2">
                        {#each post.tags as tag}
                          <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">
                            #{tag}
                          </span>
                        {/each}
                      </div>
                    {/if}
                  </div>
                </Box>
              {/each}
            </div>
          {/if}
        {:else}
          <Box className="text-center py-12">
            <User size={48} class="text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {$t('forum.select_category', { default: 'Select a Category' })}
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              {$t('forum.select_category_desc', { default: 'Choose a category to view posts' })}
            </p>
          </Box>
        {/if}
      </div>
    </div>

  {:else}
    <!-- Post Detail View -->
    <div class="mb-4">
      <PrimaryButton
        variant={VARIANTS.OUTLINED}
        onClick={() => selectedPost.set(null)}
      >
        ← {$t('forum.back_to_posts', { default: 'Back to Posts' })}
      </PrimaryButton>
    </div>

    <Box className="mb-6">
      <div class="p-6">
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-2xl">{getPostTypeIcon($selectedPost.post_type)}</span>
              <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                {$selectedPost.title}
              </h1>
              {#if $selectedPost.is_pinned}
                <Pin size={20} class="text-primary-600" />
              {/if}
              {#if $selectedPost.is_locked}
                <Lock size={20} class="text-gray-500" />
              {/if}
              {#if $selectedPost.is_solved}
                <CheckmarkFilled size={20} class="text-green-600" />
              {/if}
            </div>

            <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
              <div class="flex items-center">
                <User size={16} class="mr-1" />
                <span>{$selectedPost.author?.fullname || 'Unknown'}</span>
              </div>
              <div class="flex items-center">
                <Time size={16} class="mr-1" />
                <span>{formatTimeAgo($selectedPost.created_at)}</span>
              </div>
              <div class="flex items-center">
                <View size={16} class="mr-1" />
                <span>{$selectedPost.views}</span>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <span class="px-3 py-1 text-sm rounded-full {getPostTypeColor($selectedPost.post_type)}">
              {$selectedPost.post_type.toUpperCase()}
            </span>
          </div>
        </div>

        <div class="prose dark:prose-invert max-w-none mb-4">
          <p>{$selectedPost.content}</p>
        </div>

        <!-- Tags -->
        {#if $selectedPost.tags && $selectedPost.tags.length > 0}
          <div class="flex flex-wrap gap-2 mb-4">
            {#each $selectedPost.tags as tag}
              <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">
                #{tag}
              </span>
            {/each}
          </div>
        {/if}

        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <button class="flex items-center text-gray-600 dark:text-gray-400 hover:text-green-600">
              <ThumbsUp size={20} class="mr-1" />
              <span>{$selectedPost.upvotes}</span>
            </button>

            <button class="flex items-center text-gray-600 dark:text-gray-400 hover:text-red-600">
              <ThumbsDown size={20} class="mr-1" />
              <span>{$selectedPost.downvotes}</span>
            </button>

            <div class="flex items-center text-gray-600 dark:text-gray-400">
              <Reply size={20} class="mr-1" />
              <span>{$selectedPost.reply_count}</span>
            </div>
          </div>

          <PrimaryButton
            variant={VARIANTS.OUTLINED}
            onClick={() => showCreateReply = true}
          >
            <Reply size={20} class="mr-2" />
            {$t('forum.reply', { default: 'Reply' })}
          </PrimaryButton>
        </div>
      </div>
    </Box>

    <!-- Replies -->
    <Box>
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {$t('forum.replies', { default: 'Replies' })} ({$replies.length})
        </h3>

        <div class="space-y-4">
          {#each $replies as reply (reply.id)}
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-3">
                    <span class="text-primary-600 dark:text-primary-300 text-sm font-semibold">
                      {reply.author?.fullname?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">
                      {reply.author?.fullname || 'Unknown'}
                    </p>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      {formatTimeAgo(reply.created_at)}
                    </p>
                  </div>
                </div>

                {#if reply.is_solution}
                  <span class="px-2 py-1 text-xs bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300 rounded">
                    <CheckmarkFilled size={12} class="inline mr-1" />
                    {$t('forum.solution', { default: 'Solution' })}
                  </span>
                {/if}
              </div>

              <div class="prose dark:prose-invert max-w-none mb-3">
                <p>{reply.content}</p>
              </div>

              <div class="flex items-center space-x-4">
                <button class="flex items-center text-gray-600 dark:text-gray-400 hover:text-green-600">
                  <ThumbsUp size={16} class="mr-1" />
                  <span class="text-sm">{reply.upvotes}</span>
                </button>

                <button class="flex items-center text-gray-600 dark:text-gray-400 hover:text-red-600">
                  <ThumbsDown size={16} class="mr-1" />
                  <span class="text-sm">{reply.downvotes}</span>
                </button>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </Box>
  {/if}
</div>

<!-- Create Post Modal -->
{#if showCreatePost}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('forum.create_post', { default: 'Create Post' })}
        </h2>
        <button
          on:click={() => showCreatePost = false}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('forum.post_title', { default: 'Title' })} *
          </label>
          <input
            type="text"
            bind:value={postTitle}
            placeholder={$t('forum.title_placeholder', { default: 'Enter post title' })}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('forum.post_type', { default: 'Type' })}
          </label>
          <select
            bind:value={postType}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="discussion">{$t('forum.discussion', { default: 'Discussion' })}</option>
            <option value="question">{$t('forum.question', { default: 'Question' })}</option>
            <option value="announcement">{$t('forum.announcement', { default: 'Announcement' })}</option>
            <option value="poll">{$t('forum.poll', { default: 'Poll' })}</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('forum.content', { default: 'Content' })} *
          </label>
          <textarea
            bind:value={postContent}
            placeholder={$t('forum.content_placeholder', { default: 'Write your post content...' })}
            rows="8"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          ></textarea>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('forum.tags', { default: 'Tags' })}
          </label>
          <div class="flex flex-wrap gap-2 mb-2">
            {#each postTags as tag, index}
              <span class="px-2 py-1 text-sm bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300 rounded flex items-center">
                #{tag}
                <button
                  on:click={() => removeTag(index)}
                  class="ml-1 text-primary-500 hover:text-primary-700"
                >
                  ×
                </button>
              </span>
            {/each}
          </div>
          <input
            type="text"
            placeholder={$t('forum.add_tags', { default: 'Add tags (press Enter)' })}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            on:keydown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addTag(e.target.value);
                e.target.value = '';
              }
            }}
          />
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => showCreatePost = false}
        >
          {$t('forum.cancel', { default: 'Cancel' })}
        </PrimaryButton>

        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={createPost}
          disabled={!postTitle.trim() || !postContent.trim()}
        >
          <Send size={20} class="mr-2" />
          {$t('forum.create_post', { default: 'Create Post' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}

<!-- Create Reply Modal -->
{#if showCreateReply}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('forum.reply_to_post', { default: 'Reply to Post' })}
        </h2>
        <button
          on:click={() => showCreateReply = false}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('forum.reply_content', { default: 'Reply' })} *
          </label>
          <textarea
            bind:value={replyContent}
            placeholder={$t('forum.reply_placeholder', { default: 'Write your reply...' })}
            rows="6"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          ></textarea>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => showCreateReply = false}
        >
          {$t('forum.cancel', { default: 'Cancel' })}
        </PrimaryButton>

        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={createReply}
          disabled={!replyContent.trim()}
        >
          <Send size={20} class="mr-2" />
          {$t('forum.post_reply', { default: 'Post Reply' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}

<style>
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>