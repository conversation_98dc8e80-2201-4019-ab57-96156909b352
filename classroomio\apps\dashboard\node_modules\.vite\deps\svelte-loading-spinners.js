import "./chunk-RPU3HFCT.js";
import {
  SvelteComponentDev,
  add_location,
  append_hydration_dev,
  append_styles,
  attr_dev,
  children,
  claim_element,
  claim_space,
  destroy_each,
  detach_dev,
  dispatch_dev,
  element,
  ensure_array_like_dev,
  get_svelte_dataset,
  init,
  insert_hydration_dev,
  noop,
  safe_not_equal,
  set_style,
  space,
  toggle_class,
  validate_slots
} from "./chunk-E4ZC5ETH.js";
import "./chunk-TCF7Q4S4.js";
import "./chunk-2GTGKKMZ.js";

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Circle.svelte
var file = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Circle.svelte";
function add_css(target) {
  append_styles(target, "svelte-dqjlks", ".circle.svelte-dqjlks{height:var(--size);width:var(--size);border-color:var(--color) transparent var(--color) var(--color);border-width:calc(var(--size) / 15);border-style:solid;border-image:initial;border-radius:50%;animation:var(--duration) linear 0s infinite normal none running svelte-dqjlks-rotate}.pause-animation.svelte-dqjlks{animation-play-state:paused}@keyframes svelte-dqjlks-rotate{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQ2lyY2xlLnN2ZWx0ZSIsIm1hcHBpbmdzIjoiQUFjQyxxQkFBUSxDQUNQLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNuQixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsWUFBWSxDQUFFLElBQUksT0FBTyxDQUFDLENBQUMsV0FBVyxDQUFDLElBQUksT0FBTyxDQUFDLENBQUMsSUFBSSxPQUFPLENBQUMsQ0FDaEUsWUFBWSxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQ3BDLFlBQVksQ0FBRSxLQUFLLENBQ25CLFlBQVksQ0FBRSxPQUFPLENBQ3JCLGFBQWEsQ0FBRSxHQUFHLENBQ2xCLFNBQVMsQ0FBRSxJQUFJLFVBQVUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLG9CQUNuRSxDQUNBLDhCQUFpQixDQUNoQixvQkFBb0IsQ0FBRSxNQUN2QixDQUNBLFdBQVcsb0JBQU8sQ0FDakIsRUFBRyxDQUNGLFNBQVMsQ0FBRSxPQUFPLENBQUMsQ0FDcEIsQ0FDQSxJQUFLLENBQ0osU0FBUyxDQUFFLE9BQU8sTUFBTSxDQUN6QixDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJDaXJjbGUuc3ZlbHRlIl19 */");
}
function create_fragment(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "circle svelte-dqjlks");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file, 7, 0, 154);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Circle", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "0.75s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Circle> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var Circle = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance,
      create_fragment,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Circle",
      options,
      id: create_fragment.name
    });
  }
  get color() {
    throw new Error("<Circle>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Circle>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Circle>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Circle>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Circle>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Circle>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Circle>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Circle>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Circle>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Circle>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Circle_default = Circle;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Circle2.svelte
var file2 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Circle2.svelte";
function add_css2(target) {
  append_styles(target, "svelte-1w4sjib", ".circle.svelte-1w4sjib{width:var(--size);height:var(--size);box-sizing:border-box;position:relative;border:3px solid transparent;border-top-color:var(--colorOuter);border-radius:50%;animation:svelte-1w4sjib-circleSpin var(--durationOuter) linear infinite}.circle.svelte-1w4sjib::before,.circle.svelte-1w4sjib::after{content:'';box-sizing:border-box;position:absolute;border:3px solid transparent;border-radius:50%}.circle.svelte-1w4sjib::after{border-top-color:var(--colorInner);top:9px;left:9px;right:9px;bottom:9px;animation:svelte-1w4sjib-circleSpin var(--durationInner) linear infinite}.circle.svelte-1w4sjib::before{border-top-color:var(--colorCenter);top:3px;left:3px;right:3px;bottom:3px;animation:svelte-1w4sjib-circleSpin var(--durationCenter) linear infinite}.pause-animation.svelte-1w4sjib,.pause-animation.svelte-1w4sjib::after,.pause-animation.svelte-1w4sjib::before{animation-play-state:paused}@keyframes svelte-1w4sjib-circleSpin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQ2lyY2xlMi5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBbUJDLHNCQUFRLENBQ1AsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNuQixVQUFVLENBQUUsVUFBVSxDQUN0QixRQUFRLENBQUUsUUFBUSxDQUNsQixNQUFNLENBQUUsR0FBRyxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQzdCLGdCQUFnQixDQUFFLElBQUksWUFBWSxDQUFDLENBQ25DLGFBQWEsQ0FBRSxHQUFHLENBQ2xCLFNBQVMsQ0FBRSx5QkFBVSxDQUFDLElBQUksZUFBZSxDQUFDLENBQUMsTUFBTSxDQUFDLFFBQ25ELENBQ0Esc0JBQU8sUUFBUSxDQUNmLHNCQUFPLE9BQVEsQ0FDZCxPQUFPLENBQUUsRUFBRSxDQUNYLFVBQVUsQ0FBRSxVQUFVLENBQ3RCLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLE1BQU0sQ0FBRSxHQUFHLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FDN0IsYUFBYSxDQUFFLEdBQ2hCLENBQ0Esc0JBQU8sT0FBUSxDQUNkLGdCQUFnQixDQUFFLElBQUksWUFBWSxDQUFDLENBQ25DLEdBQUcsQ0FBRSxHQUFHLENBQ1IsSUFBSSxDQUFFLEdBQUcsQ0FDVCxLQUFLLENBQUUsR0FBRyxDQUNWLE1BQU0sQ0FBRSxHQUFHLENBQ1gsU0FBUyxDQUFFLHlCQUFVLENBQUMsSUFBSSxlQUFlLENBQUMsQ0FBQyxNQUFNLENBQUMsUUFDbkQsQ0FDQSxzQkFBTyxRQUFTLENBQ2YsZ0JBQWdCLENBQUUsSUFBSSxhQUFhLENBQUMsQ0FDcEMsR0FBRyxDQUFFLEdBQUcsQ0FDUixJQUFJLENBQUUsR0FBRyxDQUNULEtBQUssQ0FBRSxHQUFHLENBQ1YsTUFBTSxDQUFFLEdBQUcsQ0FDWCxTQUFTLENBQUUseUJBQVUsQ0FBQyxJQUFJLGdCQUFnQixDQUFDLENBQUMsTUFBTSxDQUFDLFFBQ3BELENBQ0EsK0JBQWdCLENBQ2hCLCtCQUFnQixPQUFPLENBQ3ZCLCtCQUFnQixRQUFTLENBQ3hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBRUEsV0FBVyx5QkFBVyxDQUNyQixFQUFHLENBQ0YsU0FBUyxDQUFFLE9BQU8sSUFBSSxDQUN2QixDQUNBLElBQUssQ0FDSixTQUFTLENBQUUsT0FBTyxNQUFNLENBQ3pCLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIkNpcmNsZTIuc3ZlbHRlIl19 */");
}
function create_fragment2(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "circle svelte-1w4sjib");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[0] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--colorInner",
        /*colorInner*/
        ctx[5]
      );
      set_style(
        div,
        "--colorCenter",
        /*colorCenter*/
        ctx[4]
      );
      set_style(
        div,
        "--colorOuter",
        /*colorOuter*/
        ctx[3]
      );
      set_style(
        div,
        "--durationInner",
        /*durationInner*/
        ctx[7]
      );
      set_style(
        div,
        "--durationCenter",
        /*durationCenter*/
        ctx[8]
      );
      set_style(
        div,
        "--durationOuter",
        /*durationOuter*/
        ctx[6]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[2]
      );
      add_location(div, file2, 12, 0, 408);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit*/
      3) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[0] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*colorInner*/
      32) {
        set_style(
          div,
          "--colorInner",
          /*colorInner*/
          ctx2[5]
        );
      }
      if (dirty & /*colorCenter*/
      16) {
        set_style(
          div,
          "--colorCenter",
          /*colorCenter*/
          ctx2[4]
        );
      }
      if (dirty & /*colorOuter*/
      8) {
        set_style(
          div,
          "--colorOuter",
          /*colorOuter*/
          ctx2[3]
        );
      }
      if (dirty & /*durationInner*/
      128) {
        set_style(
          div,
          "--durationInner",
          /*durationInner*/
          ctx2[7]
        );
      }
      if (dirty & /*durationCenter*/
      256) {
        set_style(
          div,
          "--durationCenter",
          /*durationCenter*/
          ctx2[8]
        );
      }
      if (dirty & /*durationOuter*/
      64) {
        set_style(
          div,
          "--durationOuter",
          /*durationOuter*/
          ctx2[6]
        );
      }
      if (dirty & /*pause*/
      4) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment2.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance2($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Circle2", slots, []);
  let { size = "60" } = $$props;
  let { unit = "px" } = $$props;
  let { pause = false } = $$props;
  let { colorOuter = "#FF3E00" } = $$props;
  let { colorCenter = "#40B3FF" } = $$props;
  let { colorInner = "#676778" } = $$props;
  let { durationMultiplier = 1 } = $$props;
  let { durationOuter = `${durationMultiplier * 2}s` } = $$props;
  let { durationInner = `${durationMultiplier * 1.5}s` } = $$props;
  let { durationCenter = `${durationMultiplier * 3}s` } = $$props;
  const writable_props = [
    "size",
    "unit",
    "pause",
    "colorOuter",
    "colorCenter",
    "colorInner",
    "durationMultiplier",
    "durationOuter",
    "durationInner",
    "durationCenter"
  ];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Circle2> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("size" in $$props2)
      $$invalidate(0, size = $$props2.size);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("pause" in $$props2)
      $$invalidate(2, pause = $$props2.pause);
    if ("colorOuter" in $$props2)
      $$invalidate(3, colorOuter = $$props2.colorOuter);
    if ("colorCenter" in $$props2)
      $$invalidate(4, colorCenter = $$props2.colorCenter);
    if ("colorInner" in $$props2)
      $$invalidate(5, colorInner = $$props2.colorInner);
    if ("durationMultiplier" in $$props2)
      $$invalidate(9, durationMultiplier = $$props2.durationMultiplier);
    if ("durationOuter" in $$props2)
      $$invalidate(6, durationOuter = $$props2.durationOuter);
    if ("durationInner" in $$props2)
      $$invalidate(7, durationInner = $$props2.durationInner);
    if ("durationCenter" in $$props2)
      $$invalidate(8, durationCenter = $$props2.durationCenter);
  };
  $$self.$capture_state = () => ({
    size,
    unit,
    pause,
    colorOuter,
    colorCenter,
    colorInner,
    durationMultiplier,
    durationOuter,
    durationInner,
    durationCenter
  });
  $$self.$inject_state = ($$props2) => {
    if ("size" in $$props2)
      $$invalidate(0, size = $$props2.size);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("pause" in $$props2)
      $$invalidate(2, pause = $$props2.pause);
    if ("colorOuter" in $$props2)
      $$invalidate(3, colorOuter = $$props2.colorOuter);
    if ("colorCenter" in $$props2)
      $$invalidate(4, colorCenter = $$props2.colorCenter);
    if ("colorInner" in $$props2)
      $$invalidate(5, colorInner = $$props2.colorInner);
    if ("durationMultiplier" in $$props2)
      $$invalidate(9, durationMultiplier = $$props2.durationMultiplier);
    if ("durationOuter" in $$props2)
      $$invalidate(6, durationOuter = $$props2.durationOuter);
    if ("durationInner" in $$props2)
      $$invalidate(7, durationInner = $$props2.durationInner);
    if ("durationCenter" in $$props2)
      $$invalidate(8, durationCenter = $$props2.durationCenter);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [
    size,
    unit,
    pause,
    colorOuter,
    colorCenter,
    colorInner,
    durationOuter,
    durationInner,
    durationCenter,
    durationMultiplier
  ];
}
var Circle2 = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance2,
      create_fragment2,
      safe_not_equal,
      {
        size: 0,
        unit: 1,
        pause: 2,
        colorOuter: 3,
        colorCenter: 4,
        colorInner: 5,
        durationMultiplier: 9,
        durationOuter: 6,
        durationInner: 7,
        durationCenter: 8
      },
      add_css2
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Circle2",
      options,
      id: create_fragment2.name
    });
  }
  get size() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get colorOuter() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set colorOuter(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get colorCenter() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set colorCenter(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get colorInner() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set colorInner(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get durationMultiplier() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set durationMultiplier(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get durationOuter() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set durationOuter(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get durationInner() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set durationInner(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get durationCenter() {
    throw new Error("<Circle2>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set durationCenter(value) {
    throw new Error("<Circle2>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Circle2_default = Circle2;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Circle3.svelte
var file3 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Circle3.svelte";
function add_css3(target) {
  append_styles(target, "svelte-7wj78d", ".wrapper.svelte-7wj78d{width:var(--size);height:var(--size);display:flex;justify-content:center;align-items:center;line-height:0;box-sizing:border-box}.inner.svelte-7wj78d{transform:scale(calc(var(--floatSize) / 52))}.ball-container.svelte-7wj78d{animation:svelte-7wj78d-ballTwo var(--duration) infinite;width:44px;height:44px;flex-shrink:0;position:relative}.single-ball.svelte-7wj78d{width:44px;height:44px;position:absolute}.ball.svelte-7wj78d{width:20px;height:20px;border-radius:50%;position:absolute;animation:svelte-7wj78d-ballOne var(--duration) infinite ease}.pause-animation.svelte-7wj78d{animation-play-state:paused}.ball-top-left.svelte-7wj78d{background-color:var(--ballTopLeftColor);top:0;left:0}.ball-top-right.svelte-7wj78d{background-color:var(--ballTopRightColor);top:0;left:24px}.ball-bottom-left.svelte-7wj78d{background-color:var(--ballBottomLeftColor);top:24px;left:0}.ball-bottom-right.svelte-7wj78d{background-color:var(--ballBottomRightColor);top:24px;left:24px}@keyframes svelte-7wj78d-ballOne{0%{position:absolute}50%{top:12px;left:12px;position:absolute;opacity:0.5}100%{position:absolute}}@keyframes svelte-7wj78d-ballTwo{0%{transform:rotate(0deg) scale(1)}50%{transform:rotate(360deg) scale(1.3)}100%{transform:rotate(720deg) scale(1)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQ2lyY2xlMy5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBaUNDLHNCQUFTLENBQ1IsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNuQixPQUFPLENBQUUsSUFBSSxDQUNiLGVBQWUsQ0FBRSxNQUFNLENBQ3ZCLFdBQVcsQ0FBRSxNQUFNLENBQ25CLFdBQVcsQ0FBRSxDQUFDLENBQ2QsVUFBVSxDQUFFLFVBQ2IsQ0FDQSxvQkFBTyxDQUNOLFNBQVMsQ0FBRSxNQUFNLEtBQUssSUFBSSxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQzdDLENBQ0EsNkJBQWdCLENBQ2YsU0FBUyxDQUFFLHFCQUFPLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxRQUFRLENBQzNDLEtBQUssQ0FBRSxJQUFJLENBQ1gsTUFBTSxDQUFFLElBQUksQ0FDWixXQUFXLENBQUUsQ0FBQyxDQUNkLFFBQVEsQ0FBRSxRQUNYLENBQ0EsMEJBQWEsQ0FDWixLQUFLLENBQUUsSUFBSSxDQUNYLE1BQU0sQ0FBRSxJQUFJLENBQ1osUUFBUSxDQUFFLFFBQ1gsQ0FDQSxtQkFBTSxDQUNMLEtBQUssQ0FBRSxJQUFJLENBQ1gsTUFBTSxDQUFFLElBQUksQ0FDWixhQUFhLENBQUUsR0FBRyxDQUNsQixRQUFRLENBQUUsUUFBUSxDQUNsQixTQUFTLENBQUUscUJBQU8sQ0FBQyxJQUFJLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxJQUM3QyxDQUNBLDhCQUFpQixDQUNoQixvQkFBb0IsQ0FBRSxNQUN2QixDQUNBLDRCQUFlLENBQ2QsZ0JBQWdCLENBQUUsSUFBSSxrQkFBa0IsQ0FBQyxDQUN6QyxHQUFHLENBQUUsQ0FBQyxDQUNOLElBQUksQ0FBRSxDQUNQLENBQ0EsNkJBQWdCLENBQ2YsZ0JBQWdCLENBQUUsSUFBSSxtQkFBbUIsQ0FBQyxDQUMxQyxHQUFHLENBQUUsQ0FBQyxDQUNOLElBQUksQ0FBRSxJQUNQLENBQ0EsK0JBQWtCLENBQ2pCLGdCQUFnQixDQUFFLElBQUkscUJBQXFCLENBQUMsQ0FDNUMsR0FBRyxDQUFFLElBQUksQ0FDVCxJQUFJLENBQUUsQ0FDUCxDQUNBLGdDQUFtQixDQUNsQixnQkFBZ0IsQ0FBRSxJQUFJLHNCQUFzQixDQUFDLENBQzdDLEdBQUcsQ0FBRSxJQUFJLENBQ1QsSUFBSSxDQUFFLElBQ1AsQ0FDQSxXQUFXLHFCQUFRLENBQ2xCLEVBQUcsQ0FDRixRQUFRLENBQUUsUUFDWCxDQUNBLEdBQUksQ0FDSCxHQUFHLENBQUUsSUFBSSxDQUNULElBQUksQ0FBRSxJQUFJLENBQ1YsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsT0FBTyxDQUFFLEdBQ1YsQ0FDQSxJQUFLLENBQ0osUUFBUSxDQUFFLFFBQ1gsQ0FDRCxDQUNBLFdBQVcscUJBQVEsQ0FDbEIsRUFBRyxDQUNGLFNBQVMsQ0FBRSxPQUFPLElBQUksQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUNoQyxDQUNBLEdBQUksQ0FDSCxTQUFTLENBQUUsT0FBTyxNQUFNLENBQUMsQ0FBQyxNQUFNLEdBQUcsQ0FDcEMsQ0FDQSxJQUFLLENBQ0osU0FBUyxDQUFFLE9BQU8sTUFBTSxDQUFDLENBQUMsTUFBTSxDQUFDLENBQ2xDLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIkNpcmNsZTMuc3ZlbHRlIl19 */");
}
function create_fragment3(ctx) {
  let div10;
  let div9;
  let div8;
  let div1;
  let div0;
  let textContent = " ";
  let t1;
  let div3;
  let div2;
  let textContent_1 = " ";
  let t3;
  let div5;
  let div4;
  let textContent_2 = " ";
  let t5;
  let div7;
  let div6;
  let textContent_3 = " ";
  const block = {
    c: function create() {
      div10 = element("div");
      div9 = element("div");
      div8 = element("div");
      div1 = element("div");
      div0 = element("div");
      div0.textContent = textContent;
      t1 = space();
      div3 = element("div");
      div2 = element("div");
      div2.textContent = textContent_1;
      t3 = space();
      div5 = element("div");
      div4 = element("div");
      div4.textContent = textContent_2;
      t5 = space();
      div7 = element("div");
      div6 = element("div");
      div6.textContent = textContent_3;
      this.h();
    },
    l: function claim(nodes) {
      div10 = claim_element(nodes, "DIV", { class: true, style: true });
      var div10_nodes = children(div10);
      div9 = claim_element(div10_nodes, "DIV", { class: true });
      var div9_nodes = children(div9);
      div8 = claim_element(div9_nodes, "DIV", { class: true });
      var div8_nodes = children(div8);
      div1 = claim_element(div8_nodes, "DIV", { class: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true, ["data-svelte-h"]: true });
      if (get_svelte_dataset(div0) !== "svelte-srzctx")
        div0.textContent = textContent;
      div1_nodes.forEach(detach_dev);
      t1 = claim_space(div8_nodes);
      div3 = claim_element(div8_nodes, "DIV", { class: true });
      var div3_nodes = children(div3);
      div2 = claim_element(div3_nodes, "DIV", { class: true, ["data-svelte-h"]: true });
      if (get_svelte_dataset(div2) !== "svelte-6iyjws")
        div2.textContent = textContent_1;
      div3_nodes.forEach(detach_dev);
      t3 = claim_space(div8_nodes);
      div5 = claim_element(div8_nodes, "DIV", { class: true });
      var div5_nodes = children(div5);
      div4 = claim_element(div5_nodes, "DIV", { class: true, ["data-svelte-h"]: true });
      if (get_svelte_dataset(div4) !== "svelte-9h2qed")
        div4.textContent = textContent_2;
      div5_nodes.forEach(detach_dev);
      t5 = claim_space(div8_nodes);
      div7 = claim_element(div8_nodes, "DIV", { class: true });
      var div7_nodes = children(div7);
      div6 = claim_element(div7_nodes, "DIV", { class: true, ["data-svelte-h"]: true });
      if (get_svelte_dataset(div6) !== "svelte-yk0z5u")
        div6.textContent = textContent_3;
      div7_nodes.forEach(detach_dev);
      div8_nodes.forEach(detach_dev);
      div9_nodes.forEach(detach_dev);
      div10_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "ball ball-top-left svelte-7wj78d");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[2]
      );
      add_location(div0, file3, 17, 4, 646);
      attr_dev(div1, "class", "single-ball svelte-7wj78d");
      add_location(div1, file3, 16, 3, 616);
      attr_dev(div2, "class", "ball ball-top-right svelte-7wj78d");
      toggle_class(
        div2,
        "pause-animation",
        /*pause*/
        ctx[2]
      );
      add_location(div2, file3, 20, 4, 767);
      attr_dev(div3, "class", "contener_mixte");
      add_location(div3, file3, 19, 3, 734);
      attr_dev(div4, "class", "ball ball-bottom-left svelte-7wj78d");
      toggle_class(
        div4,
        "pause-animation",
        /*pause*/
        ctx[2]
      );
      add_location(div4, file3, 23, 4, 889);
      attr_dev(div5, "class", "contener_mixte");
      add_location(div5, file3, 22, 3, 856);
      attr_dev(div6, "class", "ball ball-bottom-right svelte-7wj78d");
      toggle_class(
        div6,
        "pause-animation",
        /*pause*/
        ctx[2]
      );
      add_location(div6, file3, 26, 4, 1013);
      attr_dev(div7, "class", "contener_mixte");
      add_location(div7, file3, 25, 3, 980);
      attr_dev(div8, "class", "ball-container svelte-7wj78d");
      toggle_class(
        div8,
        "pause-animation",
        /*pause*/
        ctx[2]
      );
      add_location(div8, file3, 15, 2, 554);
      attr_dev(div9, "class", "inner svelte-7wj78d");
      add_location(div9, file3, 14, 1, 532);
      attr_dev(div10, "class", "wrapper svelte-7wj78d");
      set_style(
        div10,
        "--size",
        /*size*/
        ctx[0] + /*unit*/
        ctx[1]
      );
      set_style(
        div10,
        "--floatSize",
        /*size*/
        ctx[0]
      );
      set_style(
        div10,
        "--ballTopLeftColor",
        /*ballTopLeft*/
        ctx[3]
      );
      set_style(
        div10,
        "--ballTopRightColor",
        /*ballTopRight*/
        ctx[4]
      );
      set_style(
        div10,
        "--ballBottomLeftColor",
        /*ballBottomLeft*/
        ctx[5]
      );
      set_style(
        div10,
        "--ballBottomRightColor",
        /*ballBottomRight*/
        ctx[6]
      );
      set_style(
        div10,
        "--duration",
        /*duration*/
        ctx[7]
      );
      add_location(div10, file3, 10, 0, 275);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div10, anchor);
      append_hydration_dev(div10, div9);
      append_hydration_dev(div9, div8);
      append_hydration_dev(div8, div1);
      append_hydration_dev(div1, div0);
      append_hydration_dev(div8, t1);
      append_hydration_dev(div8, div3);
      append_hydration_dev(div3, div2);
      append_hydration_dev(div8, t3);
      append_hydration_dev(div8, div5);
      append_hydration_dev(div5, div4);
      append_hydration_dev(div8, t5);
      append_hydration_dev(div8, div7);
      append_hydration_dev(div7, div6);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      4) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      4) {
        toggle_class(
          div2,
          "pause-animation",
          /*pause*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      4) {
        toggle_class(
          div4,
          "pause-animation",
          /*pause*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      4) {
        toggle_class(
          div6,
          "pause-animation",
          /*pause*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      4) {
        toggle_class(
          div8,
          "pause-animation",
          /*pause*/
          ctx2[2]
        );
      }
      if (dirty & /*size, unit*/
      3) {
        set_style(
          div10,
          "--size",
          /*size*/
          ctx2[0] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*size*/
      1) {
        set_style(
          div10,
          "--floatSize",
          /*size*/
          ctx2[0]
        );
      }
      if (dirty & /*ballTopLeft*/
      8) {
        set_style(
          div10,
          "--ballTopLeftColor",
          /*ballTopLeft*/
          ctx2[3]
        );
      }
      if (dirty & /*ballTopRight*/
      16) {
        set_style(
          div10,
          "--ballTopRightColor",
          /*ballTopRight*/
          ctx2[4]
        );
      }
      if (dirty & /*ballBottomLeft*/
      32) {
        set_style(
          div10,
          "--ballBottomLeftColor",
          /*ballBottomLeft*/
          ctx2[5]
        );
      }
      if (dirty & /*ballBottomRight*/
      64) {
        set_style(
          div10,
          "--ballBottomRightColor",
          /*ballBottomRight*/
          ctx2[6]
        );
      }
      if (dirty & /*duration*/
      128) {
        set_style(
          div10,
          "--duration",
          /*duration*/
          ctx2[7]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div10);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment3.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance3($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Circle3", slots, []);
  let { size = "60" } = $$props;
  let { unit = "px" } = $$props;
  let { pause = false } = $$props;
  let { ballTopLeft = "#FF3E00" } = $$props;
  let { ballTopRight = "#F8B334" } = $$props;
  let { ballBottomLeft = "#40B3FF" } = $$props;
  let { ballBottomRight = "#676778" } = $$props;
  let { duration = "1.5s" } = $$props;
  const writable_props = [
    "size",
    "unit",
    "pause",
    "ballTopLeft",
    "ballTopRight",
    "ballBottomLeft",
    "ballBottomRight",
    "duration"
  ];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Circle3> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("size" in $$props2)
      $$invalidate(0, size = $$props2.size);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("pause" in $$props2)
      $$invalidate(2, pause = $$props2.pause);
    if ("ballTopLeft" in $$props2)
      $$invalidate(3, ballTopLeft = $$props2.ballTopLeft);
    if ("ballTopRight" in $$props2)
      $$invalidate(4, ballTopRight = $$props2.ballTopRight);
    if ("ballBottomLeft" in $$props2)
      $$invalidate(5, ballBottomLeft = $$props2.ballBottomLeft);
    if ("ballBottomRight" in $$props2)
      $$invalidate(6, ballBottomRight = $$props2.ballBottomRight);
    if ("duration" in $$props2)
      $$invalidate(7, duration = $$props2.duration);
  };
  $$self.$capture_state = () => ({
    size,
    unit,
    pause,
    ballTopLeft,
    ballTopRight,
    ballBottomLeft,
    ballBottomRight,
    duration
  });
  $$self.$inject_state = ($$props2) => {
    if ("size" in $$props2)
      $$invalidate(0, size = $$props2.size);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("pause" in $$props2)
      $$invalidate(2, pause = $$props2.pause);
    if ("ballTopLeft" in $$props2)
      $$invalidate(3, ballTopLeft = $$props2.ballTopLeft);
    if ("ballTopRight" in $$props2)
      $$invalidate(4, ballTopRight = $$props2.ballTopRight);
    if ("ballBottomLeft" in $$props2)
      $$invalidate(5, ballBottomLeft = $$props2.ballBottomLeft);
    if ("ballBottomRight" in $$props2)
      $$invalidate(6, ballBottomRight = $$props2.ballBottomRight);
    if ("duration" in $$props2)
      $$invalidate(7, duration = $$props2.duration);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [
    size,
    unit,
    pause,
    ballTopLeft,
    ballTopRight,
    ballBottomLeft,
    ballBottomRight,
    duration
  ];
}
var Circle3 = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance3,
      create_fragment3,
      safe_not_equal,
      {
        size: 0,
        unit: 1,
        pause: 2,
        ballTopLeft: 3,
        ballTopRight: 4,
        ballBottomLeft: 5,
        ballBottomRight: 6,
        duration: 7
      },
      add_css3
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Circle3",
      options,
      id: create_fragment3.name
    });
  }
  get size() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get ballTopLeft() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set ballTopLeft(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get ballTopRight() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set ballTopRight(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get ballBottomLeft() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set ballBottomLeft(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get ballBottomRight() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set ballBottomRight(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Circle3>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Circle3>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Circle3_default = Circle3;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/utils.js
var durationUnitRegex = /[a-zA-Z]/;
var calculateRgba = (color, opacity) => {
  if (color[0] === "#") {
    color = color.slice(1);
  }
  if (color.length === 3) {
    let res = "";
    color.split("").forEach((c) => {
      res += c;
      res += c;
    });
    color = res;
  }
  const rgbValues = (color.match(/.{2}/g) || []).map((hex) => parseInt(hex, 16)).join(", ");
  return `rgba(${rgbValues}, ${opacity})`;
};
var range = (size, startAt = 0) => [...Array(size).keys()].map((i) => i + startAt);

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/DoubleBounce.svelte
var file4 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\DoubleBounce.svelte";
function add_css4(target) {
  append_styles(target, "svelte-1bsg8wv", ".wrapper.svelte-1bsg8wv{position:relative;width:var(--size);height:var(--size)}.circle.svelte-1bsg8wv{position:absolute;width:var(--size);height:var(--size);background-color:var(--color);border-radius:100%;opacity:0.6;top:0;left:0;animation-fill-mode:both;animation-name:svelte-1bsg8wv-bounce !important}.pause-animation.svelte-1bsg8wv{animation-play-state:paused}@keyframes svelte-1bsg8wv-bounce{0%,100%{transform:scale(0)}50%{transform:scale(1)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiRG91YmxlQm91bmNlLnN2ZWx0ZSIsIm1hcHBpbmdzIjoiQUF1QkMsdUJBQVMsQ0FDUixRQUFRLENBQUUsUUFBUSxDQUNsQixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLElBQUksTUFBTSxDQUNuQixDQUNBLHNCQUFRLENBQ1AsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNuQixnQkFBZ0IsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUM5QixhQUFhLENBQUUsSUFBSSxDQUNuQixPQUFPLENBQUUsR0FBRyxDQUNaLEdBQUcsQ0FBRSxDQUFDLENBQ04sSUFBSSxDQUFFLENBQUMsQ0FDUCxtQkFBbUIsQ0FBRSxJQUFJLENBQ3pCLGNBQWMsQ0FBRSxxQkFBTSxDQUFDLFVBQ3hCLENBQ0EsK0JBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBQ0EsV0FBVyxxQkFBTyxDQUNqQixFQUFFLENBQ0YsSUFBSyxDQUNKLFNBQVMsQ0FBRSxNQUFNLENBQUMsQ0FDbkIsQ0FDQSxHQUFJLENBQ0gsU0FBUyxDQUFFLE1BQU0sQ0FBQyxDQUNuQixDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJEb3VibGVCb3VuY2Uuc3ZlbHRlIl19 */");
}
function get_each_context(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "circle svelte-1bsg8wv");
      set_style(
        div,
        "animation",
        /*duration*/
        ctx[2] + " " + /*version*/
        (ctx[7] === 1 ? `${(+/*durationNum*/
        ctx[6] - 0.1) / 2}${/*durationUnit*/
        ctx[5]}` : `0s`) + " infinite ease-in-out"
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file4, 12, 2, 433);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "animation",
          /*duration*/
          ctx2[2] + " " + /*version*/
          (ctx2[7] === 1 ? `${(+/*durationNum*/
          ctx2[6] - 0.1) / 2}${/*durationUnit*/
          ctx2[5]}` : `0s`) + " infinite ease-in-out"
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block.name,
    type: "each",
    source: "(12:1) {#each range(2, 1) as version}",
    ctx
  });
  return block;
}
function create_fragment4(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(2, 1));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-1bsg8wv");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      add_location(div, file4, 10, 0, 330);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*duration, durationNum, durationUnit, pause*/
      116) {
        each_value = ensure_array_like_dev(range(2, 1));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment4.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance4($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("DoubleBounce", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "2.1s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<DoubleBounce> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var DoubleBounce = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance4,
      create_fragment4,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css4
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "DoubleBounce",
      options,
      id: create_fragment4.name
    });
  }
  get color() {
    throw new Error("<DoubleBounce>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<DoubleBounce>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<DoubleBounce>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<DoubleBounce>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<DoubleBounce>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<DoubleBounce>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<DoubleBounce>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<DoubleBounce>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<DoubleBounce>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<DoubleBounce>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var DoubleBounce_default = DoubleBounce;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/GoogleSpin.svelte
var file5 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\GoogleSpin.svelte";
function add_css5(target) {
  append_styles(target, "svelte-rhgdjk", ".svelte-rhgdjk{overflow:hidden;position:relative;text-indent:-9999px;display:inline-block;background:#f86;border-radius:50%;transform:rotateZ(90deg);transform-origin:50% 50%;animation:svelte-rhgdjk-plus-loader-background var(--duration) infinite ease-in-out}.svelte-rhgdjk::after{background:#f86;border-radius:50% 0 0 50%;content:'';position:absolute;right:50%;top:0;width:50%;height:100%;transform-origin:100% 50%;animation:svelte-rhgdjk-plus-loader-top var(--duration) infinite linear}.svelte-rhgdjk::before{background:#fc6;border-radius:50% 0 0 50%;content:'';position:absolute;right:50%;top:0;width:50%;height:100%;transform-origin:100% 50%;animation:svelte-rhgdjk-plus-loader-bottom var(--duration) infinite linear}.pause-animation.svelte-rhgdjk,.pause-animation.svelte-rhgdjk::before,.pause-animation.svelte-rhgdjk::after{animation-play-state:paused}@keyframes svelte-rhgdjk-plus-loader-top{2.5%{background:#f86;transform:rotateY(0deg);animation-timing-function:ease-in}13.75%{background:#ff430d;transform:rotateY(90deg);animation-timing-function:step-start}13.76%{background:#ffae0d;transform:rotateY(90deg);animation-timing-function:ease-out}25%{background:#fc6;transform:rotateY(180deg)}27.5%{background:#fc6;transform:rotateY(180deg);animation-timing-function:ease-in}41.25%{background:#ffae0d;transform:rotateY(90deg);animation-timing-function:step-start}41.26%{background:#2cc642;transform:rotateY(90deg);animation-timing-function:ease-out}50%{background:#6d7;transform:rotateY(0deg)}52.5%{background:#6d7;transform:rotateY(0deg);animation-timing-function:ease-in}63.75%{background:#2cc642;transform:rotateY(90deg);animation-timing-function:step-start}63.76%{background:#1386d2;transform:rotateY(90deg);animation-timing-function:ease-out}75%{background:#4ae;transform:rotateY(180deg)}77.5%{background:#4ae;transform:rotateY(180deg);animation-timing-function:ease-in}91.25%{background:#1386d2;transform:rotateY(90deg);animation-timing-function:step-start}91.26%{background:#ff430d;transform:rotateY(90deg);animation-timing-function:ease-in}100%{background:#f86;transform:rotateY(0deg);animation-timing-function:step-start}}@keyframes svelte-rhgdjk-plus-loader-bottom{0%{background:#fc6;animation-timing-function:step-start}50%{background:#fc6;animation-timing-function:step-start}75%{background:#4ae;animation-timing-function:step-start}100%{background:#4ae;animation-timing-function:step-start}}@keyframes svelte-rhgdjk-plus-loader-background{0%{background:#f86;transform:rotateZ(180deg)}25%{background:#f86;transform:rotateZ(180deg);animation-timing-function:step-start}27.5%{background:#6d7;transform:rotateZ(90deg)}50%{background:#6d7;transform:rotateZ(90deg);animation-timing-function:step-start}52.5%{background:#6d7;transform:rotateZ(0deg)}75%{background:#6d7;transform:rotateZ(0deg);animation-timing-function:step-start}77.5%{background:#f86;transform:rotateZ(270deg)}100%{background:#f86;transform:rotateZ(270deg);animation-timing-function:step-start}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
}
function create_fragment5(ctx) {
  let div;
  let div_style_value;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "spinner spinner--google svelte-rhgdjk");
      attr_dev(div, "style", div_style_value = "--duration: " + /*duration*/
      ctx[0] + "; " + /*styles*/
      ctx[2]);
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[1]
      );
      add_location(div, file5, 7, 0, 165);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*duration, styles*/
      5 && div_style_value !== (div_style_value = "--duration: " + /*duration*/
      ctx2[0] + "; " + /*styles*/
      ctx2[2])) {
        attr_dev(div, "style", div_style_value);
      }
      if (dirty & /*pause*/
      2) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[1]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment5.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance5($$self, $$props, $$invalidate) {
  let styles;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("GoogleSpin", slots, []);
  let { size = "40px" } = $$props;
  let { duration = "3s" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["size", "duration", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<GoogleSpin> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("duration" in $$props2)
      $$invalidate(0, duration = $$props2.duration);
    if ("pause" in $$props2)
      $$invalidate(1, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ size, duration, pause, styles });
  $$self.$inject_state = ($$props2) => {
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("duration" in $$props2)
      $$invalidate(0, duration = $$props2.duration);
    if ("pause" in $$props2)
      $$invalidate(1, pause = $$props2.pause);
    if ("styles" in $$props2)
      $$invalidate(2, styles = $$props2.styles);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*size*/
    8) {
      $:
        $$invalidate(2, styles = [`width: ${size}`, `height: ${size}`].join(";"));
    }
  };
  return [duration, pause, styles, size];
}
var GoogleSpin = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance5, create_fragment5, safe_not_equal, { size: 3, duration: 0, pause: 1 }, add_css5);
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "GoogleSpin",
      options,
      id: create_fragment5.name
    });
  }
  get size() {
    throw new Error("<GoogleSpin>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<GoogleSpin>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<GoogleSpin>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<GoogleSpin>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<GoogleSpin>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<GoogleSpin>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var GoogleSpin_default = GoogleSpin;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/ScaleOut.svelte
var file6 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\ScaleOut.svelte";
function add_css6(target) {
  append_styles(target, "svelte-1w1ueev", ".wrapper.svelte-1w1ueev{width:var(--size);height:var(--size)}.circle.svelte-1w1ueev{width:var(--size);height:var(--size);background-color:var(--color);animation-duration:var(--duration);border-radius:100%;display:inline-block;animation:svelte-1w1ueev-scaleOut var(--duration) ease-in-out infinite}.pause-animation.svelte-1w1ueev{animation-play-state:paused}@keyframes svelte-1w1ueev-scaleOut{0%{transform:scale(0)}100%{transform:scale(1);opacity:0}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiU2NhbGVPdXQuc3ZlbHRlIiwibWFwcGluZ3MiOiJBQWVDLHVCQUFTLENBQ1IsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FDbkIsQ0FDQSxzQkFBUSxDQUNQLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixNQUFNLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbkIsZ0JBQWdCLENBQUUsSUFBSSxPQUFPLENBQUMsQ0FDOUIsa0JBQWtCLENBQUUsSUFBSSxVQUFVLENBQUMsQ0FDbkMsYUFBYSxDQUFFLElBQUksQ0FDbkIsT0FBTyxDQUFFLFlBQVksQ0FDckIsU0FBUyxDQUFFLHVCQUFRLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxXQUFXLENBQUMsUUFDakQsQ0FDQSwrQkFBaUIsQ0FDaEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FDQSxXQUFXLHVCQUFTLENBQ25CLEVBQUcsQ0FDRixTQUFTLENBQUUsTUFBTSxDQUFDLENBQ25CLENBQ0EsSUFBSyxDQUNKLFNBQVMsQ0FBRSxNQUFNLENBQUMsQ0FBQyxDQUNuQixPQUFPLENBQUUsQ0FDVixDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJTY2FsZU91dC5zdmVsdGUiXX0= */");
}
function create_fragment6(ctx) {
  let div1;
  let div0;
  const block = {
    c: function create() {
      div1 = element("div");
      div0 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div1 = claim_element(nodes, "DIV", { class: true, style: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      div1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "circle svelte-1w1ueev");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div0, file6, 11, 1, 273);
      attr_dev(div1, "class", "wrapper svelte-1w1ueev");
      set_style(
        div1,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div1,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div1,
        "--duration",
        /*duration*/
        ctx[2]
      );
      set_style(
        div1,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div1, file6, 7, 0, 151);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div1, anchor);
      append_hydration_dev(div1, div0);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div1,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div1,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div1,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div1,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment6.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance6($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("ScaleOut", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<ScaleOut> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var ScaleOut = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance6,
      create_fragment6,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css6
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "ScaleOut",
      options,
      id: create_fragment6.name
    });
  }
  get color() {
    throw new Error("<ScaleOut>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<ScaleOut>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<ScaleOut>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<ScaleOut>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<ScaleOut>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<ScaleOut>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<ScaleOut>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<ScaleOut>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<ScaleOut>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<ScaleOut>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var ScaleOut_default = ScaleOut;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/SpinLine.svelte
var file7 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\SpinLine.svelte";
function add_css7(target) {
  append_styles(target, "svelte-nfuakd", ".wrapper.svelte-nfuakd{width:var(--size);height:var(--stroke);transform:scale(calc(var(--floatSize) / 75));display:flex;justify-content:center;align-items:center}.line.svelte-nfuakd{width:var(--size);height:var(--stroke);background:var(--color);border-radius:var(--stroke);transform-origin:center center;animation:svelte-nfuakd-spineLine var(--duration) ease infinite}.pause-animation.svelte-nfuakd{animation-play-state:paused}@keyframes svelte-nfuakd-spineLine{0%{transform:rotate(-20deg);height:5px;width:75px}5%{height:5px;width:75px}30%{transform:rotate(380deg);height:5px;width:75px}40%{transform:rotate(360deg);height:5px;width:75px}55%{transform:rotate(0deg);height:5px;width:5px}65%{transform:rotate(0deg);height:5px;width:85px}68%{transform:rotate(0deg);height:5px}75%{transform:rotate(0deg);height:5px;width:1px}78%{height:5px;width:5px}90%{height:5px;width:75px;transform:rotate(0deg)}99%,100%{height:5px;width:75px;transform:rotate(-20deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiU3BpbkxpbmUuc3ZlbHRlIiwibWFwcGluZ3MiOiJBQWdCQyxzQkFBUyxDQUNSLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixNQUFNLENBQUUsSUFBSSxRQUFRLENBQUMsQ0FDckIsU0FBUyxDQUFFLE1BQU0sS0FBSyxJQUFJLFdBQVcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUM3QyxPQUFPLENBQUUsSUFBSSxDQUNiLGVBQWUsQ0FBRSxNQUFNLENBQ3ZCLFdBQVcsQ0FBRSxNQUNkLENBQ0EsbUJBQU0sQ0FDTCxLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLElBQUksUUFBUSxDQUFDLENBQ3JCLFVBQVUsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUN4QixhQUFhLENBQUUsSUFBSSxRQUFRLENBQUMsQ0FDNUIsZ0JBQWdCLENBQUUsTUFBTSxDQUFDLE1BQU0sQ0FDL0IsU0FBUyxDQUFFLHVCQUFTLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxJQUFJLENBQUMsUUFDM0MsQ0FDQSw4QkFBaUIsQ0FDaEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FDQSxXQUFXLHVCQUFVLENBQ3BCLEVBQUcsQ0FDRixTQUFTLENBQUUsT0FBTyxNQUFNLENBQUMsQ0FDekIsTUFBTSxDQUFFLEdBQUcsQ0FDWCxLQUFLLENBQUUsSUFDUixDQUNBLEVBQUcsQ0FDRixNQUFNLENBQUUsR0FBRyxDQUNYLEtBQUssQ0FBRSxJQUNSLENBQ0EsR0FBSSxDQUNILFNBQVMsQ0FBRSxPQUFPLE1BQU0sQ0FBQyxDQUN6QixNQUFNLENBQUUsR0FBRyxDQUNYLEtBQUssQ0FBRSxJQUNSLENBQ0EsR0FBSSxDQUNILFNBQVMsQ0FBRSxPQUFPLE1BQU0sQ0FBQyxDQUN6QixNQUFNLENBQUUsR0FBRyxDQUNYLEtBQUssQ0FBRSxJQUNSLENBQ0EsR0FBSSxDQUNILFNBQVMsQ0FBRSxPQUFPLElBQUksQ0FBQyxDQUN2QixNQUFNLENBQUUsR0FBRyxDQUNYLEtBQUssQ0FBRSxHQUNSLENBQ0EsR0FBSSxDQUNILFNBQVMsQ0FBRSxPQUFPLElBQUksQ0FBQyxDQUN2QixNQUFNLENBQUUsR0FBRyxDQUNYLEtBQUssQ0FBRSxJQUNSLENBQ0EsR0FBSSxDQUNILFNBQVMsQ0FBRSxPQUFPLElBQUksQ0FBQyxDQUN2QixNQUFNLENBQUUsR0FDVCxDQUNBLEdBQUksQ0FDSCxTQUFTLENBQUUsT0FBTyxJQUFJLENBQUMsQ0FDdkIsTUFBTSxDQUFFLEdBQUcsQ0FDWCxLQUFLLENBQUUsR0FDUixDQUNBLEdBQUksQ0FDSCxNQUFNLENBQUUsR0FBRyxDQUNYLEtBQUssQ0FBRSxHQUNSLENBQ0EsR0FBSSxDQUNILE1BQU0sQ0FBRSxHQUFHLENBQ1gsS0FBSyxDQUFFLElBQUksQ0FDWCxTQUFTLENBQUUsT0FBTyxJQUFJLENBQ3ZCLENBQ0EsR0FBRyxDQUNILElBQUssQ0FDSixNQUFNLENBQUUsR0FBRyxDQUNYLEtBQUssQ0FBRSxJQUFJLENBQ1gsU0FBUyxDQUFFLE9BQU8sTUFBTSxDQUN6QixDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJTcGluTGluZS5zdmVsdGUiXX0= */");
}
function create_fragment7(ctx) {
  let div1;
  let div0;
  const block = {
    c: function create() {
      div1 = element("div");
      div0 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div1 = claim_element(nodes, "DIV", { class: true, style: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      div1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "line svelte-nfuakd");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[5]
      );
      add_location(div0, file7, 12, 1, 328);
      attr_dev(div1, "class", "wrapper svelte-nfuakd");
      set_style(
        div1,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div1,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div1,
        "--stroke",
        /*stroke*/
        ctx[4]
      );
      set_style(
        div1,
        "--floatSize",
        /*size*/
        ctx[3]
      );
      set_style(
        div1,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div1, file7, 8, 0, 190);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div1, anchor);
      append_hydration_dev(div1, div0);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      32) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[5]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div1,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div1,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*stroke*/
      16) {
        set_style(
          div1,
          "--stroke",
          /*stroke*/
          ctx2[4]
        );
      }
      if (dirty & /*size*/
      8) {
        set_style(
          div1,
          "--floatSize",
          /*size*/
          ctx2[3]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div1,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment7.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance7($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("SpinLine", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "4s" } = $$props;
  let { size = "60" } = $$props;
  let { stroke = +size / 12 + unit } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "stroke", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<SpinLine> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("stroke" in $$props2)
      $$invalidate(4, stroke = $$props2.stroke);
    if ("pause" in $$props2)
      $$invalidate(5, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    color,
    unit,
    duration,
    size,
    stroke,
    pause
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("stroke" in $$props2)
      $$invalidate(4, stroke = $$props2.stroke);
    if ("pause" in $$props2)
      $$invalidate(5, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, stroke, pause];
}
var SpinLine = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance7,
      create_fragment7,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        stroke: 4,
        pause: 5
      },
      add_css7
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "SpinLine",
      options,
      id: create_fragment7.name
    });
  }
  get color() {
    throw new Error("<SpinLine>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<SpinLine>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<SpinLine>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<SpinLine>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<SpinLine>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<SpinLine>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<SpinLine>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<SpinLine>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get stroke() {
    throw new Error("<SpinLine>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set stroke(value) {
    throw new Error("<SpinLine>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<SpinLine>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<SpinLine>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var SpinLine_default = SpinLine;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Stretch.svelte
var file8 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Stretch.svelte";
function add_css8(target) {
  append_styles(target, "svelte-cihful", ".wrapper.svelte-cihful{height:var(--size);width:var(--size);display:inline-block;text-align:center;font-size:10px}.rect.svelte-cihful{height:100%;width:10%;display:inline-block;margin-right:4px;transform:scaleY(0.4);background-color:var(--color);animation:svelte-cihful-stretch var(--duration) ease-in-out infinite}.pause-animation.svelte-cihful{animation-play-state:paused}@keyframes svelte-cihful-stretch{0%,40%,100%{transform:scaleY(0.4)}20%{transform:scaleY(1)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiU3RyZXRjaC5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBcUJDLHNCQUFTLENBQ1IsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixPQUFPLENBQUUsWUFBWSxDQUNyQixVQUFVLENBQUUsTUFBTSxDQUNsQixTQUFTLENBQUUsSUFDWixDQUNBLG1CQUFNLENBQ0wsTUFBTSxDQUFFLElBQUksQ0FDWixLQUFLLENBQUUsR0FBRyxDQUNWLE9BQU8sQ0FBRSxZQUFZLENBQ3JCLFlBQVksQ0FBRSxHQUFHLENBQ2pCLFNBQVMsQ0FBRSxPQUFPLEdBQUcsQ0FBQyxDQUN0QixnQkFBZ0IsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUM5QixTQUFTLENBQUUscUJBQU8sQ0FBQyxJQUFJLFVBQVUsQ0FBQyxDQUFDLFdBQVcsQ0FBQyxRQUNoRCxDQUNBLDhCQUFpQixDQUNoQixvQkFBb0IsQ0FBRSxNQUN2QixDQUNBLFdBQVcscUJBQVEsQ0FDbEIsRUFBRSxDQUNGLEdBQUcsQ0FDSCxJQUFLLENBQ0osU0FBUyxDQUFFLE9BQU8sR0FBRyxDQUN0QixDQUNBLEdBQUksQ0FDSCxTQUFTLENBQUUsT0FBTyxDQUFDLENBQ3BCLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIlN0cmV0Y2guc3ZlbHRlIl19 */");
}
function get_each_context2(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block2(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "rect svelte-cihful");
      set_style(
        div,
        "animation-delay",
        /*version*/
        (ctx[7] - 1) * (+/*durationNum*/
        ctx[6] / 12) + /*durationUnit*/
        ctx[5]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file8, 12, 2, 457);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block2.name,
    type: "each",
    source: "(12:1) {#each range(5, 1) as version}",
    ctx
  });
  return block;
}
function create_fragment8(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(5, 1));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block2(get_each_context2(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-cihful");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div, file8, 10, 0, 330);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*durationNum, durationUnit, pause*/
      112) {
        each_value = ensure_array_like_dev(range(5, 1));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context2(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block2(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment8.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance8($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Stretch", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1.2s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Stretch> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var Stretch = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance8,
      create_fragment8,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css8
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Stretch",
      options,
      id: create_fragment8.name
    });
  }
  get color() {
    throw new Error("<Stretch>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Stretch>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Stretch>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Stretch>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Stretch>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Stretch>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Stretch>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Stretch>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Stretch>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Stretch>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Stretch_default = Stretch;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/BarLoader.svelte
var file9 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\BarLoader.svelte";
function add_css9(target) {
  append_styles(target, "svelte-bnawe9", ".wrapper.svelte-bnawe9{height:calc(var(--size) / 15);width:calc(var(--size) * 2);background-color:var(--rgba);position:relative;overflow:hidden;background-clip:padding-box}.lines.svelte-bnawe9{height:calc(var(--size) / 15);background-color:var(--color)}.small-lines.svelte-bnawe9{position:absolute;overflow:hidden;background-clip:padding-box;display:block;border-radius:2px;will-change:left, right;animation-fill-mode:forwards}.small-lines.\\31 .svelte-bnawe9{animation:var(--duration) cubic-bezier(0.65, 0.815, 0.735, 0.395) 0s infinite normal none\n			running svelte-bnawe9-long}.small-lines.\\32 .svelte-bnawe9{animation:var(--duration) cubic-bezier(0.165, 0.84, 0.44, 1) calc((var(--duration) + 0.1) / 2)\n			infinite normal none running svelte-bnawe9-short}.pause-animation.svelte-bnawe9{animation-play-state:paused}@keyframes svelte-bnawe9-long{0%{left:-35%;right:100%}60%{left:100%;right:-90%}100%{left:100%;right:-90%}}@keyframes svelte-bnawe9-short{0%{left:-200%;right:100%}60%{left:107%;right:-8%}100%{left:107%;right:-8%}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQmFyTG9hZGVyLnN2ZWx0ZSIsIm1hcHBpbmdzIjoiQUFxQkMsc0JBQVMsQ0FDUixNQUFNLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FDOUIsS0FBSyxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQzVCLGdCQUFnQixDQUFFLElBQUksTUFBTSxDQUFDLENBQzdCLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLFFBQVEsQ0FBRSxNQUFNLENBQ2hCLGVBQWUsQ0FBRSxXQUNsQixDQUNBLG9CQUFPLENBQ04sTUFBTSxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQzlCLGdCQUFnQixDQUFFLElBQUksT0FBTyxDQUM5QixDQUVBLDBCQUFhLENBQ1osUUFBUSxDQUFFLFFBQVEsQ0FDbEIsUUFBUSxDQUFFLE1BQU0sQ0FDaEIsZUFBZSxDQUFFLFdBQVcsQ0FDNUIsT0FBTyxDQUFFLEtBQUssQ0FDZCxhQUFhLENBQUUsR0FBRyxDQUNsQixXQUFXLENBQUUsSUFBSSxDQUFDLENBQUMsS0FBSyxDQUN4QixtQkFBbUIsQ0FBRSxRQUN0QixDQUNBLFlBQVksbUJBQUssQ0FDaEIsU0FBUyxDQUFFLElBQUksVUFBVSxDQUFDLENBQUMsYUFBYSxJQUFJLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQztBQUN4RixHQUFHLE9BQU8sQ0FBQyxrQkFDVixDQUNBLFlBQVksbUJBQUssQ0FDaEIsU0FBUyxDQUFFLElBQUksVUFBVSxDQUFDLENBQUMsYUFBYSxLQUFLLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7QUFDaEcsR0FBRyxRQUFRLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsbUJBQy9CLENBQ0EsOEJBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBRUEsV0FBVyxrQkFBSyxDQUNmLEVBQUcsQ0FDRixJQUFJLENBQUUsSUFBSSxDQUNWLEtBQUssQ0FBRSxJQUNSLENBQ0EsR0FBSSxDQUNILElBQUksQ0FBRSxJQUFJLENBQ1YsS0FBSyxDQUFFLElBQ1IsQ0FDQSxJQUFLLENBQ0osSUFBSSxDQUFFLElBQUksQ0FDVixLQUFLLENBQUUsSUFDUixDQUNELENBQ0EsV0FBVyxtQkFBTSxDQUNoQixFQUFHLENBQ0YsSUFBSSxDQUFFLEtBQUssQ0FDWCxLQUFLLENBQUUsSUFDUixDQUNBLEdBQUksQ0FDSCxJQUFJLENBQUUsSUFBSSxDQUNWLEtBQUssQ0FBRSxHQUNSLENBQ0EsSUFBSyxDQUNKLElBQUksQ0FBRSxJQUFJLENBQ1YsS0FBSyxDQUFFLEdBQ1IsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiQmFyTG9hZGVyLnN2ZWx0ZSJdfQ== */");
}
function get_each_context3(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[6] = list[i];
  return child_ctx;
}
function create_each_block3(ctx) {
  let div;
  let div_class_value;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", div_class_value = "lines small-lines " + /*version*/
      ctx[6] + " svelte-bnawe9");
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file9, 12, 2, 348);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block3.name,
    type: "each",
    source: "(12:1) {#each range(2, 1) as version}",
    ctx
  });
  return block;
}
function create_fragment9(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(2, 1));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block3(get_each_context3(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-bnawe9");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--rgba",
        /*rgba*/
        ctx[5]
      );
      add_location(div, file9, 10, 0, 248);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*color, duration, pause*/
      21) {
        each_value = ensure_array_like_dev(range(2, 1));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context3(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block3(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*rgba*/
      32) {
        set_style(
          div,
          "--rgba",
          /*rgba*/
          ctx2[5]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment9.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance9($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("BarLoader", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "2.1s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let rgba;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<BarLoader> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    calculateRgba,
    range,
    color,
    unit,
    duration,
    size,
    pause,
    rgba
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("rgba" in $$props2)
      $$invalidate(5, rgba = $$props2.rgba);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*color*/
    1) {
      $:
        $$invalidate(5, rgba = calculateRgba(color, 0.2));
    }
  };
  return [color, unit, duration, size, pause, rgba];
}
var BarLoader = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance9,
      create_fragment9,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css9
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "BarLoader",
      options,
      id: create_fragment9.name
    });
  }
  get color() {
    throw new Error("<BarLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<BarLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<BarLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<BarLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<BarLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<BarLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<BarLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<BarLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<BarLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<BarLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var BarLoader_default = BarLoader;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Jumper.svelte
var file10 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Jumper.svelte";
function add_css10(target) {
  append_styles(target, "svelte-c0n7a9", ".wrapper.svelte-c0n7a9{width:var(--size);height:var(--size)}.circle.svelte-c0n7a9{border-radius:100%;animation-fill-mode:both;position:absolute;opacity:0;width:var(--size);height:var(--size);background-color:var(--color);animation:svelte-c0n7a9-bounce var(--duration) linear infinite}.pause-animation.svelte-c0n7a9{animation-play-state:paused}@keyframes svelte-c0n7a9-bounce{0%{opacity:0;transform:scale(0)}5%{opacity:1}100%{opacity:0;transform:scale(1)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiSnVtcGVyLnN2ZWx0ZSIsIm1hcHBpbmdzIjoiQUFxQkMsc0JBQVMsQ0FDUixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLElBQUksTUFBTSxDQUNuQixDQUNBLHFCQUFRLENBQ1AsYUFBYSxDQUFFLElBQUksQ0FDbkIsbUJBQW1CLENBQUUsSUFBSSxDQUN6QixRQUFRLENBQUUsUUFBUSxDQUNsQixPQUFPLENBQUUsQ0FBQyxDQUNWLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixNQUFNLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbkIsZ0JBQWdCLENBQUUsSUFBSSxPQUFPLENBQUMsQ0FDOUIsU0FBUyxDQUFFLG9CQUFNLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxNQUFNLENBQUMsUUFDMUMsQ0FDQSw4QkFBaUIsQ0FDaEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FDQSxXQUFXLG9CQUFPLENBQ2pCLEVBQUcsQ0FDRixPQUFPLENBQUUsQ0FBQyxDQUNWLFNBQVMsQ0FBRSxNQUFNLENBQUMsQ0FDbkIsQ0FDQSxFQUFHLENBQ0YsT0FBTyxDQUFFLENBQ1YsQ0FDQSxJQUFLLENBQ0osT0FBTyxDQUFFLENBQUMsQ0FDVixTQUFTLENBQUUsTUFBTSxDQUFDLENBQ25CLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIkp1bXBlci5zdmVsdGUiXX0= */");
}
function get_each_context4(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block4(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "circle svelte-c0n7a9");
      set_style(div, "animation-delay", +/*durationNum*/
      ctx[6] / 3 * /*version*/
      (ctx[7] - 1) + /*durationUnit*/
      ctx[5]);
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file10, 12, 2, 456);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block4.name,
    type: "each",
    source: "(12:1) {#each range(3, 1) as version}",
    ctx
  });
  return block;
}
function create_fragment10(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(3, 1));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block4(get_each_context4(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-c0n7a9");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div, file10, 10, 0, 328);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*durationNum, durationUnit, pause*/
      112) {
        each_value = ensure_array_like_dev(range(3, 1));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context4(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block4(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment10.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance10($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Jumper", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Jumper> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var Jumper = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance10,
      create_fragment10,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css10
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Jumper",
      options,
      id: create_fragment10.name
    });
  }
  get color() {
    throw new Error("<Jumper>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Jumper>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Jumper>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Jumper>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Jumper>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Jumper>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Jumper>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Jumper>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Jumper>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Jumper>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Jumper_default = Jumper;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/RingLoader.svelte
var file11 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\RingLoader.svelte";
function add_css11(target) {
  append_styles(target, "svelte-kxapcj", ".wrapper.svelte-kxapcj{position:relative;width:var(--size);height:var(--size)}.border.svelte-kxapcj{border-color:var(--color);position:absolute;top:0px;left:0px;width:var(--size);height:var(--size);opacity:0.4;perspective:800px;border-width:6px;border-style:solid;border-image:initial;border-radius:100%}.border.\\31 .svelte-kxapcj{animation:var(--duration) linear 0s infinite normal none running svelte-kxapcj-ringOne}.border.\\32 .svelte-kxapcj{animation:var(--duration) linear 0s infinite normal none running svelte-kxapcj-ringTwo}.pause-animation.svelte-kxapcj{animation-play-state:paused}@keyframes svelte-kxapcj-ringOne{0%{transform:rotateX(0deg) rotateY(0deg) rotateZ(0deg)}100%{transform:rotateX(360deg) rotateY(180deg) rotateZ(360deg)}}@keyframes svelte-kxapcj-ringTwo{0%{transform:rotateX(0deg) rotateY(0deg) rotateZ(0deg)}100%{transform:rotateX(180deg) rotateY(360deg) rotateZ(360deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiUmluZ0xvYWRlci5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBZUMsc0JBQVMsQ0FDUixRQUFRLENBQUUsUUFBUSxDQUNsQixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLElBQUksTUFBTSxDQUNuQixDQUNBLHFCQUFRLENBQ1AsWUFBWSxDQUFFLElBQUksT0FBTyxDQUFDLENBQzFCLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLEdBQUcsQ0FBRSxHQUFHLENBQ1IsSUFBSSxDQUFFLEdBQUcsQ0FDVCxLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLE9BQU8sQ0FBRSxHQUFHLENBQ1osV0FBVyxDQUFFLEtBQUssQ0FDbEIsWUFBWSxDQUFFLEdBQUcsQ0FDakIsWUFBWSxDQUFFLEtBQUssQ0FDbkIsWUFBWSxDQUFFLE9BQU8sQ0FDckIsYUFBYSxDQUFFLElBQ2hCLENBQ0EsT0FBTyxtQkFBSyxDQUNYLFNBQVMsQ0FBRSxJQUFJLFVBQVUsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLHFCQUNuRSxDQUNBLE9BQU8sbUJBQUssQ0FDWCxTQUFTLENBQUUsSUFBSSxVQUFVLENBQUMsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxxQkFDbkUsQ0FDQSw4QkFBaUIsQ0FDaEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FFQSxXQUFXLHFCQUFRLENBQ2xCLEVBQUcsQ0FDRixTQUFTLENBQUUsUUFBUSxJQUFJLENBQUMsQ0FBQyxRQUFRLElBQUksQ0FBQyxDQUFDLFFBQVEsSUFBSSxDQUNwRCxDQUNBLElBQUssQ0FDSixTQUFTLENBQUUsUUFBUSxNQUFNLENBQUMsQ0FBQyxRQUFRLE1BQU0sQ0FBQyxDQUFDLFFBQVEsTUFBTSxDQUMxRCxDQUNELENBQ0EsV0FBVyxxQkFBUSxDQUNsQixFQUFHLENBQ0YsU0FBUyxDQUFFLFFBQVEsSUFBSSxDQUFDLENBQUMsUUFBUSxJQUFJLENBQUMsQ0FBQyxRQUFRLElBQUksQ0FDcEQsQ0FDQSxJQUFLLENBQ0osU0FBUyxDQUFFLFFBQVEsTUFBTSxDQUFDLENBQUMsUUFBUSxNQUFNLENBQUMsQ0FBQyxRQUFRLE1BQU0sQ0FDMUQsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiUmluZ0xvYWRlci5zdmVsdGUiXX0= */");
}
function get_each_context5(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[5] = list[i];
  return child_ctx;
}
function create_each_block5(ctx) {
  let div;
  let div_class_value;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", div_class_value = "border " + /*version*/
      ctx[5] + " svelte-kxapcj");
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file11, 10, 2, 312);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block5.name,
    type: "each",
    source: "(10:1) {#each range(2, 1) as version}",
    ctx
  });
  return block;
}
function create_fragment11(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(2, 1));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block5(get_each_context5(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-kxapcj");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div, file11, 8, 0, 184);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      16) {
        each_value = ensure_array_like_dev(range(2, 1));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context5(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block5(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment11.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance11($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("RingLoader", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "2s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<RingLoader> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    color,
    unit,
    duration,
    size,
    pause
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var RingLoader = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance11,
      create_fragment11,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css11
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "RingLoader",
      options,
      id: create_fragment11.name
    });
  }
  get color() {
    throw new Error("<RingLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<RingLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<RingLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<RingLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<RingLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<RingLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<RingLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<RingLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<RingLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<RingLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var RingLoader_default = RingLoader;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/SyncLoader.svelte
var file12 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\SyncLoader.svelte";
function add_css12(target) {
  append_styles(target, "svelte-14x3x60", ".wrapper.svelte-14x3x60{height:var(--size);width:var(--size);display:flex;align-items:center;justify-content:center}.dot.svelte-14x3x60{height:var(--dotSize);width:var(--dotSize);background-color:var(--color);margin:2px;display:inline-block;border-radius:100%;animation:svelte-14x3x60-sync var(--duration) ease-in-out infinite alternate both running}.pause-animation.svelte-14x3x60{animation-play-state:paused}@-webkit-keyframes svelte-14x3x60-sync{33%{-webkit-transform:translateY(10px);transform:translateY(10px)}66%{-webkit-transform:translateY(-10px);transform:translateY(-10px)}100%{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes svelte-14x3x60-sync{33%{-webkit-transform:translateY(10px);transform:translateY(10px)}66%{-webkit-transform:translateY(-10px);transform:translateY(-10px)}100%{-webkit-transform:translateY(0);transform:translateY(0)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiU3luY0xvYWRlci5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBc0JDLHVCQUFTLENBQ1IsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixPQUFPLENBQUUsSUFBSSxDQUNiLFdBQVcsQ0FBRSxNQUFNLENBQ25CLGVBQWUsQ0FBRSxNQUNsQixDQUVBLG1CQUFLLENBQ0osTUFBTSxDQUFFLElBQUksU0FBUyxDQUFDLENBQ3RCLEtBQUssQ0FBRSxJQUFJLFNBQVMsQ0FBQyxDQUNyQixnQkFBZ0IsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUM5QixNQUFNLENBQUUsR0FBRyxDQUNYLE9BQU8sQ0FBRSxZQUFZLENBQ3JCLGFBQWEsQ0FBRSxJQUFJLENBQ25CLFNBQVMsQ0FBRSxtQkFBSSxDQUFDLElBQUksVUFBVSxDQUFDLENBQUMsV0FBVyxDQUFDLFFBQVEsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLE9BQ3JFLENBQ0EsK0JBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBRUEsbUJBQW1CLG1CQUFLLENBQ3ZCLEdBQUksQ0FDSCxpQkFBaUIsQ0FBRSxXQUFXLElBQUksQ0FBQyxDQUNuQyxTQUFTLENBQUUsV0FBVyxJQUFJLENBQzNCLENBQ0EsR0FBSSxDQUNILGlCQUFpQixDQUFFLFdBQVcsS0FBSyxDQUFDLENBQ3BDLFNBQVMsQ0FBRSxXQUFXLEtBQUssQ0FDNUIsQ0FDQSxJQUFLLENBQ0osaUJBQWlCLENBQUUsV0FBVyxDQUFDLENBQUMsQ0FDaEMsU0FBUyxDQUFFLFdBQVcsQ0FBQyxDQUN4QixDQUNELENBQ0EsV0FBVyxtQkFBSyxDQUNmLEdBQUksQ0FDSCxpQkFBaUIsQ0FBRSxXQUFXLElBQUksQ0FBQyxDQUNuQyxTQUFTLENBQUUsV0FBVyxJQUFJLENBQzNCLENBQ0EsR0FBSSxDQUNILGlCQUFpQixDQUFFLFdBQVcsS0FBSyxDQUFDLENBQ3BDLFNBQVMsQ0FBRSxXQUFXLEtBQUssQ0FDNUIsQ0FDQSxJQUFLLENBQ0osaUJBQWlCLENBQUUsV0FBVyxDQUFDLENBQUMsQ0FDaEMsU0FBUyxDQUFFLFdBQVcsQ0FBQyxDQUN4QixDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJTeW5jTG9hZGVyLnN2ZWx0ZSJdfQ== */");
}
function get_each_context6(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block6(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "dot svelte-14x3x60");
      set_style(div, "--dotSize", +/*size*/
      ctx[3] * 0.25 + /*unit*/
      ctx[1]);
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "animation-delay",
        /*i*/
        ctx[7] * (+/*durationNum*/
        ctx[6] / 10) + /*durationUnit*/
        ctx[5]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file12, 12, 2, 433);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*size, unit*/
      10) {
        set_style(div, "--dotSize", +/*size*/
        ctx2[3] * 0.25 + /*unit*/
        ctx2[1]);
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block6.name,
    type: "each",
    source: "(12:1) {#each range(3, 1) as i}",
    ctx
  });
  return block;
}
function create_fragment12(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(3, 1));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block6(get_each_context6(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-14x3x60");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div, file12, 10, 0, 330);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit, color, durationNum, durationUnit, pause*/
      123) {
        each_value = ensure_array_like_dev(range(3, 1));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context6(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block6(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment12.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance12($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("SyncLoader", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "0.6s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<SyncLoader> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var SyncLoader = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance12,
      create_fragment12,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css12
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "SyncLoader",
      options,
      id: create_fragment12.name
    });
  }
  get color() {
    throw new Error("<SyncLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<SyncLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<SyncLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<SyncLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<SyncLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<SyncLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<SyncLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<SyncLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<SyncLoader>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<SyncLoader>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var SyncLoader_default = SyncLoader;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Rainbow.svelte
var file13 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Rainbow.svelte";
function add_css13(target) {
  append_styles(target, "svelte-1lgkc8y", ".wrapper.svelte-1lgkc8y{width:var(--size);height:calc(var(--size) / 2);overflow:hidden}.rainbow.svelte-1lgkc8y{width:var(--size);height:var(--size);border-left-color:transparent;border-bottom-color:transparent;border-top-color:var(--color);border-right-color:var(--color);box-sizing:border-box;transform:rotate(-200deg);border-radius:50%;border-style:solid;animation:var(--duration) ease-in-out 0s infinite normal none running svelte-1lgkc8y-rotate}.pause-animation.svelte-1lgkc8y{animation-play-state:paused}@keyframes svelte-1lgkc8y-rotate{0%{border-width:10px}25%{border-width:3px}50%{transform:rotate(115deg);border-width:10px}75%{border-width:3px}100%{border-width:10px}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiUmFpbmJvdy5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBWUMsdUJBQVMsQ0FDUixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQzdCLFFBQVEsQ0FBRSxNQUNYLENBQ0EsdUJBQVMsQ0FDUixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLGlCQUFpQixDQUFFLFdBQVcsQ0FDOUIsbUJBQW1CLENBQUUsV0FBVyxDQUNoQyxnQkFBZ0IsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUM5QixrQkFBa0IsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUNoQyxVQUFVLENBQUUsVUFBVSxDQUN0QixTQUFTLENBQUUsT0FBTyxPQUFPLENBQUMsQ0FDMUIsYUFBYSxDQUFFLEdBQUcsQ0FDbEIsWUFBWSxDQUFFLEtBQUssQ0FDbkIsU0FBUyxDQUFFLElBQUksVUFBVSxDQUFDLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMscUJBQ3hFLENBQ0EsK0JBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBQ0EsV0FBVyxxQkFBTyxDQUNqQixFQUFHLENBQ0YsWUFBWSxDQUFFLElBQ2YsQ0FDQSxHQUFJLENBQ0gsWUFBWSxDQUFFLEdBQ2YsQ0FDQSxHQUFJLENBQ0gsU0FBUyxDQUFFLE9BQU8sTUFBTSxDQUFDLENBQ3pCLFlBQVksQ0FBRSxJQUNmLENBQ0EsR0FBSSxDQUNILFlBQVksQ0FBRSxHQUNmLENBQ0EsSUFBSyxDQUNKLFlBQVksQ0FBRSxJQUNmLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIlJhaW5ib3cuc3ZlbHRlIl19 */");
}
function create_fragment13(ctx) {
  let div1;
  let div0;
  const block = {
    c: function create() {
      div1 = element("div");
      div0 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div1 = claim_element(nodes, "DIV", { class: true, style: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      div1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "rainbow svelte-1lgkc8y");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div0, file13, 8, 1, 246);
      attr_dev(div1, "class", "wrapper svelte-1lgkc8y");
      set_style(
        div1,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div1,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div1,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div1, file13, 7, 0, 151);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div1, anchor);
      append_hydration_dev(div1, div0);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div1,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div1,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div1,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment13.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance13($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Rainbow", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "3s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Rainbow> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var Rainbow = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance13,
      create_fragment13,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css13
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Rainbow",
      options,
      id: create_fragment13.name
    });
  }
  get color() {
    throw new Error("<Rainbow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Rainbow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Rainbow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Rainbow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Rainbow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Rainbow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Rainbow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Rainbow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Rainbow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Rainbow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Rainbow_default = Rainbow;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Firework.svelte
var file14 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Firework.svelte";
function add_css14(target) {
  append_styles(target, "svelte-x7zza7", ".wrapper.svelte-x7zza7{width:calc(var(--size) * 1.3);height:calc(var(--size) * 1.3);display:flex;justify-content:center;align-items:center}.firework.svelte-x7zza7{border:calc(var(--size) / 10) dotted var(--color);width:var(--size);height:var(--size);border-radius:50%;animation:svelte-x7zza7-fire var(--duration) cubic-bezier(0.165, 0.84, 0.44, 1) infinite}.pause-animation.svelte-x7zza7{animation-play-state:paused}@keyframes svelte-x7zza7-fire{0%{opacity:1;transform:scale(0.1)}25%{opacity:0.85}100%{transform:scale(1);opacity:0}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiRmlyZXdvcmsuc3ZlbHRlIiwibWFwcGluZ3MiOiJBQVlDLHNCQUFTLENBQ1IsS0FBSyxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQzlCLE1BQU0sQ0FBRSxLQUFLLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUMvQixPQUFPLENBQUUsSUFBSSxDQUNiLGVBQWUsQ0FBRSxNQUFNLENBQ3ZCLFdBQVcsQ0FBRSxNQUNkLENBQ0EsdUJBQVUsQ0FDVCxNQUFNLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxNQUFNLENBQUMsSUFBSSxPQUFPLENBQUMsQ0FDbEQsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNuQixhQUFhLENBQUUsR0FBRyxDQUNsQixTQUFTLENBQUUsa0JBQUksQ0FBQyxJQUFJLFVBQVUsQ0FBQyxDQUFDLGFBQWEsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsUUFDcEUsQ0FDQSw4QkFBaUIsQ0FDaEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FFQSxXQUFXLGtCQUFLLENBQ2YsRUFBRyxDQUNGLE9BQU8sQ0FBRSxDQUFDLENBQ1YsU0FBUyxDQUFFLE1BQU0sR0FBRyxDQUNyQixDQUNBLEdBQUksQ0FDSCxPQUFPLENBQUUsSUFDVixDQUNBLElBQUssQ0FDSixTQUFTLENBQUUsTUFBTSxDQUFDLENBQUMsQ0FDbkIsT0FBTyxDQUFFLENBQ1YsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiRmlyZXdvcmsuc3ZlbHRlIl19 */");
}
function create_fragment14(ctx) {
  let div1;
  let div0;
  const block = {
    c: function create() {
      div1 = element("div");
      div0 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div1 = claim_element(nodes, "DIV", { class: true, style: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      div1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "firework svelte-x7zza7");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div0, file14, 8, 1, 249);
      attr_dev(div1, "class", "wrapper svelte-x7zza7");
      set_style(
        div1,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div1,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div1,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div1, file14, 7, 0, 154);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div1, anchor);
      append_hydration_dev(div1, div0);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div1,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div1,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div1,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment14.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance14($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Firework", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1.25s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Firework> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var Firework = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance14,
      create_fragment14,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css14
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Firework",
      options,
      id: create_fragment14.name
    });
  }
  get color() {
    throw new Error("<Firework>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Firework>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Firework>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Firework>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Firework>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Firework>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Firework>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Firework>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Firework>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Firework>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Firework_default = Firework;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Pulse.svelte
var file15 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Pulse.svelte";
function add_css15(target) {
  append_styles(target, "svelte-1w8rpx6", ".wrapper.svelte-1w8rpx6{position:relative;display:flex;justify-content:center;align-items:center;width:var(--size);height:calc(var(--size) / 2.5)}.cube.svelte-1w8rpx6{position:absolute;top:0px;width:calc(var(--size) / 5);height:calc(var(--size) / 2.5);background-color:var(--color);animation:svelte-1w8rpx6-motion var(--duration) cubic-bezier(0.895, 0.03, 0.685, 0.22) infinite}.pause-animation.svelte-1w8rpx6{animation-play-state:paused}@keyframes svelte-1w8rpx6-motion{0%{opacity:1}50%{opacity:0}100%{opacity:1}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiUHVsc2Uuc3ZlbHRlIiwibWFwcGluZ3MiOiJBQXVCQyx1QkFBUyxDQUNSLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLE9BQU8sQ0FBRSxJQUFJLENBQ2IsZUFBZSxDQUFFLE1BQU0sQ0FDdkIsV0FBVyxDQUFFLE1BQU0sQ0FDbkIsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxLQUFLLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FDL0IsQ0FDQSxvQkFBTSxDQUNMLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLEdBQUcsQ0FBRSxHQUFHLENBQ1IsS0FBSyxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQzVCLE1BQU0sQ0FBRSxLQUFLLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUMvQixnQkFBZ0IsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUM5QixTQUFTLENBQUUscUJBQU0sQ0FBQyxJQUFJLFVBQVUsQ0FBQyxDQUFDLGFBQWEsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsUUFDMUUsQ0FDQSwrQkFBaUIsQ0FDaEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FDQSxXQUFXLHFCQUFPLENBQ2pCLEVBQUcsQ0FDRixPQUFPLENBQUUsQ0FDVixDQUNBLEdBQUksQ0FDSCxPQUFPLENBQUUsQ0FDVixDQUNBLElBQUssQ0FDSixPQUFPLENBQUUsQ0FDVixDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJQdWxzZS5zdmVsdGUiXX0= */");
}
function get_each_context7(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block7(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "cube svelte-1w8rpx6");
      set_style(
        div,
        "animation-delay",
        /*version*/
        ctx[7] * (+/*durationNum*/
        ctx[6] / 10) + /*durationUnit*/
        ctx[5]
      );
      set_style(
        div,
        "left",
        /*version*/
        ctx[7] * (+/*size*/
        ctx[3] / 3 + +/*size*/
        ctx[3] / 15) + /*unit*/
        ctx[1]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file15, 12, 2, 457);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "left",
          /*version*/
          ctx2[7] * (+/*size*/
          ctx2[3] / 3 + +/*size*/
          ctx2[3] / 15) + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block7.name,
    type: "each",
    source: "(12:1) {#each range(3, 0) as version}",
    ctx
  });
  return block;
}
function create_fragment15(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(3, 0));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block7(get_each_context7(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-1w8rpx6");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div, file15, 10, 0, 330);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*durationNum, durationUnit, size, unit, pause*/
      122) {
        each_value = ensure_array_like_dev(range(3, 0));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context7(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block7(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment15.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance15($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Pulse", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1.5s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Pulse> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var Pulse = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance15,
      create_fragment15,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css15
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Pulse",
      options,
      id: create_fragment15.name
    });
  }
  get color() {
    throw new Error("<Pulse>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Pulse>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Pulse>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Pulse>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Pulse>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Pulse>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Pulse>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Pulse>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Pulse>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Pulse>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Pulse_default = Pulse;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Jellyfish.svelte
var file16 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Jellyfish.svelte";
function add_css16(target) {
  append_styles(target, "svelte-1rvptk", ".wrapper.svelte-1rvptk{position:relative;display:flex;justify-content:center;align-items:center;width:var(--size);height:var(--size)}.ring.svelte-1rvptk{position:absolute;border:2px solid var(--color);border-radius:50%;background-color:transparent;animation:svelte-1rvptk-motion var(--duration) ease infinite}.pause-animation.svelte-1rvptk{animation-play-state:paused}@keyframes svelte-1rvptk-motion{0%{transform:translateY(var(--motionOne))}50%{transform:translateY(var(--motionTwo))}100%{transform:translateY(var(--motionThree))}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiSmVsbHlmaXNoLnN2ZWx0ZSIsIm1hcHBpbmdzIjoiQUE0QkMsc0JBQVMsQ0FDUixRQUFRLENBQUUsUUFBUSxDQUNsQixPQUFPLENBQUUsSUFBSSxDQUNiLGVBQWUsQ0FBRSxNQUFNLENBQ3ZCLFdBQVcsQ0FBRSxNQUFNLENBQ25CLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixNQUFNLENBQUUsSUFBSSxNQUFNLENBQ25CLENBQ0EsbUJBQU0sQ0FDTCxRQUFRLENBQUUsUUFBUSxDQUNsQixNQUFNLENBQUUsR0FBRyxDQUFDLEtBQUssQ0FBQyxJQUFJLE9BQU8sQ0FBQyxDQUM5QixhQUFhLENBQUUsR0FBRyxDQUNsQixnQkFBZ0IsQ0FBRSxXQUFXLENBQzdCLFNBQVMsQ0FBRSxvQkFBTSxDQUFDLElBQUksVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLFFBQ3hDLENBQ0EsOEJBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBQ0EsV0FBVyxvQkFBTyxDQUNqQixFQUFHLENBQ0YsU0FBUyxDQUFFLFdBQVcsSUFBSSxXQUFXLENBQUMsQ0FDdkMsQ0FDQSxHQUFJLENBQ0gsU0FBUyxDQUFFLFdBQVcsSUFBSSxXQUFXLENBQUMsQ0FDdkMsQ0FDQSxJQUFLLENBQ0osU0FBUyxDQUFFLFdBQVcsSUFBSSxhQUFhLENBQUMsQ0FDekMsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiSmVsbHlmaXNoLnN2ZWx0ZSJdfQ== */");
}
function get_each_context8(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block8(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "ring svelte-1rvptk");
      set_style(
        div,
        "animation-delay",
        /*version*/
        ctx[7] * (+/*durationNum*/
        ctx[6] / 25) + /*durationUnit*/
        ctx[5]
      );
      set_style(
        div,
        "width",
        /*version*/
        ctx[7] * (+/*size*/
        ctx[3] / 6) + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "height",
        /*version*/
        ctx[7] * (+/*size*/
        ctx[3] / 6) / 2 + /*unit*/
        ctx[1]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file16, 17, 2, 563);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "width",
          /*version*/
          ctx2[7] * (+/*size*/
          ctx2[3] / 6) + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "height",
          /*version*/
          ctx2[7] * (+/*size*/
          ctx2[3] / 6) / 2 + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block8.name,
    type: "each",
    source: "(17:1) {#each range(6, 0) as version}",
    ctx
  });
  return block;
}
function create_fragment16(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(6, 0));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block8(get_each_context8(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-1rvptk");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(div, "--motionOne", -/*size*/
      ctx[3] / 5 + /*unit*/
      ctx[1]);
      set_style(div, "--motionTwo", +/*size*/
      ctx[3] / 4 + /*unit*/
      ctx[1]);
      set_style(div, "--motionThree", -/*size*/
      ctx[3] / 5 + /*unit*/
      ctx[1]);
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div, file16, 10, 0, 330);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*durationNum, durationUnit, size, unit, pause*/
      122) {
        each_value = ensure_array_like_dev(range(6, 0));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context8(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block8(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(div, "--motionOne", -/*size*/
        ctx2[3] / 5 + /*unit*/
        ctx2[1]);
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(div, "--motionTwo", +/*size*/
        ctx2[3] / 4 + /*unit*/
        ctx2[1]);
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(div, "--motionThree", -/*size*/
        ctx2[3] / 5 + /*unit*/
        ctx2[1]);
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment16.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance16($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Jellyfish", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "2.5s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Jellyfish> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var Jellyfish = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance16,
      create_fragment16,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css16
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Jellyfish",
      options,
      id: create_fragment16.name
    });
  }
  get color() {
    throw new Error("<Jellyfish>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Jellyfish>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Jellyfish>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Jellyfish>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Jellyfish>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Jellyfish>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Jellyfish>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Jellyfish>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Jellyfish>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Jellyfish>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Jellyfish_default = Jellyfish;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Chasing.svelte
var file17 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Chasing.svelte";
function add_css17(target) {
  append_styles(target, "svelte-1uhddr4", ".wrapper.svelte-1uhddr4{height:var(--size);width:var(--size);display:flex;justify-content:center;align-items:center}.spinner.svelte-1uhddr4{height:var(--size);width:var(--size);animation:svelte-1uhddr4-rotate var(--duration) infinite linear}.dot.svelte-1uhddr4{width:60%;height:60%;display:inline-block;position:absolute;top:0;background-color:var(--color);border-radius:100%;animation:svelte-1uhddr4-bounce var(--duration) infinite ease-in-out}.pause-animation.svelte-1uhddr4{animation-play-state:paused}@keyframes svelte-1uhddr4-rotate{100%{transform:rotate(360deg)}}@keyframes svelte-1uhddr4-bounce{0%,100%{transform:scale(0)}50%{transform:scale(1)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQ2hhc2luZy5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBeUJDLHVCQUFTLENBQ1IsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixPQUFPLENBQUUsSUFBSSxDQUNiLGVBQWUsQ0FBRSxNQUFNLENBQ3ZCLFdBQVcsQ0FBRSxNQUNkLENBQ0EsdUJBQVMsQ0FDUixNQUFNLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbkIsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLFNBQVMsQ0FBRSxxQkFBTSxDQUFDLElBQUksVUFBVSxDQUFDLENBQUMsUUFBUSxDQUFDLE1BQzVDLENBQ0EsbUJBQUssQ0FDSixLQUFLLENBQUUsR0FBRyxDQUNWLE1BQU0sQ0FBRSxHQUFHLENBQ1gsT0FBTyxDQUFFLFlBQVksQ0FDckIsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsR0FBRyxDQUFFLENBQUMsQ0FDTixnQkFBZ0IsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUM5QixhQUFhLENBQUUsSUFBSSxDQUNuQixTQUFTLENBQUUscUJBQU0sQ0FBQyxJQUFJLFVBQVUsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxXQUM1QyxDQUNBLCtCQUFpQixDQUNoQixvQkFBb0IsQ0FBRSxNQUN2QixDQUVBLFdBQVcscUJBQU8sQ0FDakIsSUFBSyxDQUNKLFNBQVMsQ0FBRSxPQUFPLE1BQU0sQ0FDekIsQ0FDRCxDQUNBLFdBQVcscUJBQU8sQ0FDakIsRUFBRSxDQUNGLElBQUssQ0FDSixTQUFTLENBQUUsTUFBTSxDQUFDLENBQ25CLENBQ0EsR0FBSSxDQUNILFNBQVMsQ0FBRSxNQUFNLENBQUMsQ0FDbkIsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiQ2hhc2luZy5zdmVsdGUiXX0= */");
}
function get_each_context9(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block9(ctx) {
  let div;
  let div_style_value;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "dot svelte-1uhddr4");
      attr_dev(div, "style", div_style_value = "animation-delay: " + /*version*/
      (ctx[7] === 1 ? `${+/*durationNum*/
      ctx[6] / 2}${/*durationUnit*/
      ctx[5]}` : "0s") + "; " + /*version*/
      (ctx[7] === 1 ? "bottom: 0;" : "") + " " + /*version*/
      (ctx[7] === 1 ? "top: auto;" : ""));
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file17, 13, 3, 511);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block9.name,
    type: "each",
    source: "(13:2) {#each range(2, 0) as version}",
    ctx
  });
  return block;
}
function create_fragment17(ctx) {
  let div1;
  let div0;
  let each_value = ensure_array_like_dev(range(2, 0));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block9(get_each_context9(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div1 = element("div");
      div0 = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div1 = claim_element(nodes, "DIV", { class: true, style: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true });
      var div0_nodes = children(div0);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div0_nodes);
      }
      div0_nodes.forEach(detach_dev);
      div1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "spinner svelte-1uhddr4");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div0, file17, 11, 1, 423);
      attr_dev(div1, "class", "wrapper svelte-1uhddr4");
      set_style(
        div1,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div1,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div1,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div1, file17, 10, 0, 328);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div1, anchor);
      append_hydration_dev(div1, div0);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div0, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*durationNum, durationUnit, pause*/
      112) {
        each_value = ensure_array_like_dev(range(2, 0));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context9(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block9(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div0, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div1,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div1,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div1,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div1);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment17.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance17($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Chasing", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "2s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Chasing> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    durationUnitRegex,
    range,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var Chasing = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance17,
      create_fragment17,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css17
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Chasing",
      options,
      id: create_fragment17.name
    });
  }
  get color() {
    throw new Error("<Chasing>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Chasing>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Chasing>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Chasing>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Chasing>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Chasing>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Chasing>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Chasing>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Chasing>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Chasing>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Chasing_default = Chasing;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Square.svelte
var file18 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Square.svelte";
function add_css18(target) {
  append_styles(target, "svelte-x90y", ".square.svelte-x90y{height:var(--size);width:var(--size);background-color:var(--color);animation:svelte-x90y-squareDelay var(--duration) 0s infinite cubic-bezier(0.09, 0.57, 0.49, 0.9);animation-fill-mode:both;perspective:100px;display:inline-block}.pause-animation.svelte-x90y{animation-play-state:paused}@keyframes svelte-x90y-squareDelay{25%{-webkit-transform:rotateX(180deg) rotateY(0);transform:rotateX(180deg) rotateY(0)}50%{-webkit-transform:rotateX(180deg) rotateY(180deg);transform:rotateX(180deg) rotateY(180deg)}75%{-webkit-transform:rotateX(0) rotateY(180deg);transform:rotateX(0) rotateY(180deg)}100%{-webkit-transform:rotateX(0) rotateY(0);transform:rotateX(0) rotateY(0)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiU3F1YXJlLnN2ZWx0ZSIsIm1hcHBpbmdzIjoiQUFjQyxtQkFBUSxDQUNQLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNuQixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsZ0JBQWdCLENBQUUsSUFBSSxPQUFPLENBQUMsQ0FDOUIsU0FBUyxDQUFFLHVCQUFXLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLGFBQWEsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDLENBQ3RGLG1CQUFtQixDQUFFLElBQUksQ0FDekIsV0FBVyxDQUFFLEtBQUssQ0FDbEIsT0FBTyxDQUFFLFlBQ1YsQ0FDQSw0QkFBaUIsQ0FDaEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FDQSxXQUFXLHVCQUFZLENBQ3RCLEdBQUksQ0FDSCxpQkFBaUIsQ0FBRSxRQUFRLE1BQU0sQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQzdDLFNBQVMsQ0FBRSxRQUFRLE1BQU0sQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUNyQyxDQUNBLEdBQUksQ0FDSCxpQkFBaUIsQ0FBRSxRQUFRLE1BQU0sQ0FBQyxDQUFDLFFBQVEsTUFBTSxDQUFDLENBQ2xELFNBQVMsQ0FBRSxRQUFRLE1BQU0sQ0FBQyxDQUFDLFFBQVEsTUFBTSxDQUMxQyxDQUNBLEdBQUksQ0FDSCxpQkFBaUIsQ0FBRSxRQUFRLENBQUMsQ0FBQyxDQUFDLFFBQVEsTUFBTSxDQUFDLENBQzdDLFNBQVMsQ0FBRSxRQUFRLENBQUMsQ0FBQyxDQUFDLFFBQVEsTUFBTSxDQUNyQyxDQUNBLElBQUssQ0FDSixpQkFBaUIsQ0FBRSxRQUFRLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUFDLENBQ3hDLFNBQVMsQ0FBRSxRQUFRLENBQUMsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxDQUNoQyxDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJTcXVhcmUuc3ZlbHRlIl19 */");
}
function create_fragment18(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "square svelte-x90y");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file18, 7, 0, 151);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment18.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance18($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Square", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "3s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Square> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var Square = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance18,
      create_fragment18,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css18
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Square",
      options,
      id: create_fragment18.name
    });
  }
  get color() {
    throw new Error("<Square>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Square>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Square>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Square>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Square>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Square>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Square>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Square>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Square>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Square>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Square_default = Square;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Shadow.svelte
var file19 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Shadow.svelte";
function add_css19(target) {
  append_styles(target, "svelte-5bpnhx", ".wrapper.svelte-5bpnhx{position:relative;display:flex;justify-content:center;align-items:center;width:var(--size);height:var(--size)}.shadow.svelte-5bpnhx{color:var(--color);font-size:var(--size);overflow:hidden;width:var(--size);height:var(--size);border-radius:50%;margin:28px auto;position:relative;transform:translateZ(0);animation:svelte-5bpnhx-load var(--duration) infinite ease, svelte-5bpnhx-round var(--duration) infinite ease}.pause-animation.svelte-5bpnhx{animation-play-state:paused}@keyframes svelte-5bpnhx-load{0%{box-shadow:0 -0.83em 0 -0.4em, 0 -0.83em 0 -0.42em, 0 -0.83em 0 -0.44em, 0 -0.83em 0 -0.46em,\n				0 -0.83em 0 -0.477em}5%,95%{box-shadow:0 -0.83em 0 -0.4em, 0 -0.83em 0 -0.42em, 0 -0.83em 0 -0.44em, 0 -0.83em 0 -0.46em,\n				0 -0.83em 0 -0.477em}10%,59%{box-shadow:0 -0.83em 0 -0.4em, -0.087em -0.825em 0 -0.42em, -0.173em -0.812em 0 -0.44em,\n				-0.256em -0.789em 0 -0.46em, -0.297em -0.775em 0 -0.477em}20%{box-shadow:0 -0.83em 0 -0.4em, -0.338em -0.758em 0 -0.42em, -0.555em -0.617em 0 -0.44em,\n				-0.671em -0.488em 0 -0.46em, -0.749em -0.34em 0 -0.477em}38%{box-shadow:0 -0.83em 0 -0.4em, -0.377em -0.74em 0 -0.42em, -0.645em -0.522em 0 -0.44em,\n				-0.775em -0.297em 0 -0.46em, -0.82em -0.09em 0 -0.477em}100%{box-shadow:0 -0.83em 0 -0.4em, 0 -0.83em 0 -0.42em, 0 -0.83em 0 -0.44em, 0 -0.83em 0 -0.46em,\n				0 -0.83em 0 -0.477em}}@keyframes svelte-5bpnhx-round{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
}
function create_fragment19(ctx) {
  let div1;
  let div0;
  const block = {
    c: function create() {
      div1 = element("div");
      div0 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div1 = claim_element(nodes, "DIV", { class: true, style: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      div1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "shadow svelte-5bpnhx");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div0, file19, 8, 1, 248);
      attr_dev(div1, "class", "wrapper svelte-5bpnhx");
      set_style(
        div1,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div1,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div1,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div1, file19, 7, 0, 153);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div1, anchor);
      append_hydration_dev(div1, div0);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div1,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div1,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div1,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment19.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance19($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Shadow", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1.7s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Shadow> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var Shadow = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance19,
      create_fragment19,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css19
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Shadow",
      options,
      id: create_fragment19.name
    });
  }
  get color() {
    throw new Error("<Shadow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Shadow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Shadow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Shadow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Shadow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Shadow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Shadow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Shadow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Shadow>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Shadow>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Shadow_default = Shadow;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Moon.svelte
var file20 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Moon.svelte";
function add_css20(target) {
  append_styles(target, "svelte-e653jg", ".wrapper.svelte-e653jg{height:var(--size);width:var(--size);border-radius:100%;animation:svelte-e653jg-moonStretchDelay var(--duration) 0s infinite linear;animation-fill-mode:forwards;position:relative}.circle-one.svelte-e653jg{top:var(--moonSize);background-color:var(--color);width:calc(var(--size) / 7);height:calc(var(--size) / 7);border-radius:100%;animation:svelte-e653jg-moonStretchDelay var(--duration) 0s infinite linear;animation-fill-mode:forwards;opacity:0.8;position:absolute}.circle-two.svelte-e653jg{opacity:0.1;border:calc(var(--size) / 7) solid var(--color);height:var(--size);width:var(--size);border-radius:100%;box-sizing:border-box}.pause-animation.svelte-e653jg{animation-play-state:paused}@keyframes svelte-e653jg-moonStretchDelay{100%{transform:rotate(360deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiTW9vbi5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBbUJDLHNCQUFTLENBQ1IsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixhQUFhLENBQUUsSUFBSSxDQUNuQixTQUFTLENBQUUsOEJBQWdCLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLE1BQU0sQ0FDOUQsbUJBQW1CLENBQUUsUUFBUSxDQUM3QixRQUFRLENBQUUsUUFDWCxDQUNBLHlCQUFZLENBQ1gsR0FBRyxDQUFFLElBQUksVUFBVSxDQUFDLENBQ3BCLGdCQUFnQixDQUFFLElBQUksT0FBTyxDQUFDLENBQzlCLEtBQUssQ0FBRSxLQUFLLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUM1QixNQUFNLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FDN0IsYUFBYSxDQUFFLElBQUksQ0FDbkIsU0FBUyxDQUFFLDhCQUFnQixDQUFDLElBQUksVUFBVSxDQUFDLENBQUMsRUFBRSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQzlELG1CQUFtQixDQUFFLFFBQVEsQ0FDN0IsT0FBTyxDQUFFLEdBQUcsQ0FDWixRQUFRLENBQUUsUUFDWCxDQUNBLHlCQUFZLENBQ1gsT0FBTyxDQUFFLEdBQUcsQ0FDWixNQUFNLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsSUFBSSxPQUFPLENBQUMsQ0FDaEQsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixhQUFhLENBQUUsSUFBSSxDQUNuQixVQUFVLENBQUUsVUFDYixDQUNBLDhCQUFpQixDQUNoQixvQkFBb0IsQ0FBRSxNQUN2QixDQUNBLFdBQVcsOEJBQWlCLENBQzNCLElBQUssQ0FDSixTQUFTLENBQUUsT0FBTyxNQUFNLENBQ3pCLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIk1vb24uc3ZlbHRlIl19 */");
}
function create_fragment20(ctx) {
  let div2;
  let div0;
  let t;
  let div1;
  const block = {
    c: function create() {
      div2 = element("div");
      div0 = element("div");
      t = space();
      div1 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div2 = claim_element(nodes, "DIV", { class: true, style: true });
      var div2_nodes = children(div2);
      div0 = claim_element(div2_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      t = claim_space(div2_nodes);
      div1 = claim_element(div2_nodes, "DIV", { class: true });
      children(div1).forEach(detach_dev);
      div2_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "circle-one svelte-e653jg");
      toggle_class(
        div0,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div0, file20, 14, 1, 369);
      attr_dev(div1, "class", "circle-two svelte-e653jg");
      toggle_class(
        div1,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div1, file20, 15, 1, 427);
      attr_dev(div2, "class", "wrapper svelte-e653jg");
      set_style(
        div2,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div2,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div2,
        "--moonSize",
        /*top*/
        ctx[5] + /*unit*/
        ctx[1]
      );
      set_style(
        div2,
        "--duration",
        /*duration*/
        ctx[2]
      );
      toggle_class(
        div2,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div2, file20, 9, 0, 215);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div2, anchor);
      append_hydration_dev(div2, div0);
      append_hydration_dev(div2, t);
      append_hydration_dev(div2, div1);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div0,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div1,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div2,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div2,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*unit*/
      2) {
        set_style(
          div2,
          "--moonSize",
          /*top*/
          ctx2[5] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div2,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div2,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div2);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment20.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance20($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Moon", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "0.6s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let moonSize = +size / 7;
  let top = +size / 2 - moonSize / 2;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Moon> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    color,
    unit,
    duration,
    size,
    pause,
    moonSize,
    top
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("moonSize" in $$props2)
      moonSize = $$props2.moonSize;
    if ("top" in $$props2)
      $$invalidate(5, top = $$props2.top);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, top];
}
var Moon = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance20,
      create_fragment20,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css20
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Moon",
      options,
      id: create_fragment20.name
    });
  }
  get color() {
    throw new Error("<Moon>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Moon>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Moon>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Moon>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Moon>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Moon>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Moon>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Moon>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Moon>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Moon>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Moon_default = Moon;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Plane.svelte
var file21 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Plane.svelte";
function add_css21(target) {
  append_styles(target, "svelte-1cx3779", ".wrapper.svelte-1cx3779.svelte-1cx3779{height:var(--size);width:var(--size);position:relative;display:flex;justify-content:center;align-items:center}.wrapper.svelte-1cx3779 .svelte-1cx3779{line-height:0;box-sizing:border-box}.spinner-inner.svelte-1cx3779.svelte-1cx3779{height:var(--size);width:var(--size);transform:scale(calc(var(--size) / 70))}.mask.svelte-1cx3779.svelte-1cx3779{position:absolute;border-radius:2px;overflow:hidden;perspective:1000;backface-visibility:hidden}.plane.svelte-1cx3779.svelte-1cx3779{background:var(--color);width:400%;height:100%;position:absolute;z-index:100;perspective:1000;backface-visibility:hidden}#top.svelte-1cx3779 .plane.svelte-1cx3779{z-index:2000;animation:svelte-1cx3779-trans1 var(--duration) ease-in infinite 0s backwards}#middle.svelte-1cx3779 .plane.svelte-1cx3779{transform:translate3d(0px, 0, 0);background:var(--rgba);animation:svelte-1cx3779-trans2 var(--duration) linear infinite calc(var(--duration) / 4) backwards}#bottom.svelte-1cx3779 .plane.svelte-1cx3779{z-index:2000;animation:svelte-1cx3779-trans3 var(--duration) ease-out infinite calc(var(--duration) / 2) backwards}#top.svelte-1cx3779.svelte-1cx3779{width:53px;height:20px;left:20px;top:5px;transform:skew(-15deg, 0);z-index:100}#middle.svelte-1cx3779.svelte-1cx3779{width:33px;height:20px;left:20px;top:21px;transform:skew(-15deg, 40deg)}#bottom.svelte-1cx3779.svelte-1cx3779{width:53px;height:20px;top:35px;transform:skew(-15deg, 0)}.pause-animation.svelte-1cx3779 .plane.svelte-1cx3779{animation-play-state:paused}@keyframes svelte-1cx3779-trans1{from{transform:translate3d(53px, 0, 0)}to{transform:translate3d(-250px, 0, 0)}}@keyframes svelte-1cx3779-trans2{from{transform:translate3d(-160px, 0, 0)}to{transform:translate3d(53px, 0, 0)}}@keyframes svelte-1cx3779-trans3{from{transform:translate3d(53px, 0, 0)}to{transform:translate3d(-220px, 0, 0)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */");
}
function create_fragment21(ctx) {
  let div7;
  let div6;
  let div1;
  let div0;
  let t0;
  let div3;
  let div2;
  let t1;
  let div5;
  let div4;
  const block = {
    c: function create() {
      div7 = element("div");
      div6 = element("div");
      div1 = element("div");
      div0 = element("div");
      t0 = space();
      div3 = element("div");
      div2 = element("div");
      t1 = space();
      div5 = element("div");
      div4 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div7 = claim_element(nodes, "DIV", { class: true, style: true });
      var div7_nodes = children(div7);
      div6 = claim_element(div7_nodes, "DIV", { class: true });
      var div6_nodes = children(div6);
      div1 = claim_element(div6_nodes, "DIV", { id: true, class: true });
      var div1_nodes = children(div1);
      div0 = claim_element(div1_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      div1_nodes.forEach(detach_dev);
      t0 = claim_space(div6_nodes);
      div3 = claim_element(div6_nodes, "DIV", { id: true, class: true });
      var div3_nodes = children(div3);
      div2 = claim_element(div3_nodes, "DIV", { class: true });
      children(div2).forEach(detach_dev);
      div3_nodes.forEach(detach_dev);
      t1 = claim_space(div6_nodes);
      div5 = claim_element(div6_nodes, "DIV", { id: true, class: true });
      var div5_nodes = children(div5);
      div4 = claim_element(div5_nodes, "DIV", { class: true });
      children(div4).forEach(detach_dev);
      div5_nodes.forEach(detach_dev);
      div6_nodes.forEach(detach_dev);
      div7_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "plane svelte-1cx3779");
      add_location(div0, file21, 16, 3, 446);
      attr_dev(div1, "id", "top");
      attr_dev(div1, "class", "mask svelte-1cx3779");
      add_location(div1, file21, 15, 2, 415);
      attr_dev(div2, "class", "plane svelte-1cx3779");
      add_location(div2, file21, 19, 3, 513);
      attr_dev(div3, "id", "middle");
      attr_dev(div3, "class", "mask svelte-1cx3779");
      add_location(div3, file21, 18, 2, 479);
      attr_dev(div4, "class", "plane svelte-1cx3779");
      add_location(div4, file21, 22, 3, 580);
      attr_dev(div5, "id", "bottom");
      attr_dev(div5, "class", "mask svelte-1cx3779");
      add_location(div5, file21, 21, 2, 546);
      attr_dev(div6, "class", "spinner-inner svelte-1cx3779");
      toggle_class(
        div6,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div6, file21, 14, 1, 355);
      attr_dev(div7, "class", "wrapper svelte-1cx3779");
      set_style(
        div7,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div7,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div7,
        "--rgba",
        /*rgba*/
        ctx[5]
      );
      set_style(
        div7,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div7, file21, 10, 0, 241);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div7, anchor);
      append_hydration_dev(div7, div6);
      append_hydration_dev(div6, div1);
      append_hydration_dev(div1, div0);
      append_hydration_dev(div6, t0);
      append_hydration_dev(div6, div3);
      append_hydration_dev(div3, div2);
      append_hydration_dev(div6, t1);
      append_hydration_dev(div6, div5);
      append_hydration_dev(div5, div4);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div6,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div7,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div7,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*rgba*/
      32) {
        set_style(
          div7,
          "--rgba",
          /*rgba*/
          ctx2[5]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div7,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div7);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment21.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance21($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Plane", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1.3s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let rgba;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Plane> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    calculateRgba,
    color,
    unit,
    duration,
    size,
    pause,
    rgba
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("rgba" in $$props2)
      $$invalidate(5, rgba = $$props2.rgba);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*color*/
    1) {
      $:
        $$invalidate(5, rgba = calculateRgba(color, 0.6));
    }
  };
  return [color, unit, duration, size, pause, rgba];
}
var Plane = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance21,
      create_fragment21,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css21
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Plane",
      options,
      id: create_fragment21.name
    });
  }
  get color() {
    throw new Error("<Plane>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Plane>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Plane>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Plane>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Plane>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Plane>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Plane>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Plane>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Plane>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Plane>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Plane_default = Plane;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Diamonds.svelte
var file22 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Diamonds.svelte";
function add_css22(target) {
  append_styles(target, "svelte-1jnfmql", "span.svelte-1jnfmql.svelte-1jnfmql{width:var(--size);height:calc(var(--size) / 4);position:relative;display:block}div.svelte-1jnfmql.svelte-1jnfmql{width:calc(var(--size) / 4);height:calc(var(--size) / 4);position:absolute;left:0%;top:0;border-radius:2px;background:var(--color);transform:translateX(-50%) rotate(45deg) scale(0);animation:svelte-1jnfmql-diamonds var(--duration) linear infinite}div.svelte-1jnfmql.svelte-1jnfmql:nth-child(1){animation-delay:calc(var(--duration) * 2 / 3 * -1)}div.svelte-1jnfmql.svelte-1jnfmql:nth-child(2){animation-delay:calc(var(--duration) * 2 / 3 * -2)}div.svelte-1jnfmql.svelte-1jnfmql:nth-child(3){animation-delay:calc(var(--duration) * 2 / 3 * -3)}.pause-animation.svelte-1jnfmql div.svelte-1jnfmql{animation-play-state:paused}@keyframes svelte-1jnfmql-diamonds{50%{left:50%;transform:translateX(-50%) rotate(45deg) scale(1)}100%{left:100%;transform:translateX(-50%) rotate(45deg) scale(0)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiRGlhbW9uZHMuc3ZlbHRlIiwibWFwcGluZ3MiOiJBQWlCQyxrQ0FBSyxDQUNKLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixNQUFNLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FDN0IsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsT0FBTyxDQUFFLEtBQ1YsQ0FDQSxpQ0FBSSxDQUNILEtBQUssQ0FBRSxLQUFLLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUM1QixNQUFNLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FDN0IsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsSUFBSSxDQUFFLEVBQUUsQ0FDUixHQUFHLENBQUUsQ0FBQyxDQUNOLGFBQWEsQ0FBRSxHQUFHLENBQ2xCLFVBQVUsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUN4QixTQUFTLENBQUUsV0FBVyxJQUFJLENBQUMsQ0FBQyxPQUFPLEtBQUssQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQ2xELFNBQVMsQ0FBRSx1QkFBUSxDQUFDLElBQUksVUFBVSxDQUFDLENBQUMsTUFBTSxDQUFDLFFBQzVDLENBQ0EsaUNBQUcsV0FBVyxDQUFDLENBQUUsQ0FDaEIsZUFBZSxDQUFFLEtBQUssSUFBSSxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FDbkQsQ0FDQSxpQ0FBRyxXQUFXLENBQUMsQ0FBRSxDQUNoQixlQUFlLENBQUUsS0FBSyxJQUFJLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUNuRCxDQUNBLGlDQUFHLFdBQVcsQ0FBQyxDQUFFLENBQ2hCLGVBQWUsQ0FBRSxLQUFLLElBQUksVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQ25ELENBQ0EsK0JBQWdCLENBQUMsa0JBQUksQ0FDcEIsb0JBQW9CLENBQUUsTUFDdkIsQ0FFQSxXQUFXLHVCQUFTLENBQ25CLEdBQUksQ0FDSCxJQUFJLENBQUUsR0FBRyxDQUNULFNBQVMsQ0FBRSxXQUFXLElBQUksQ0FBQyxDQUFDLE9BQU8sS0FBSyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQ2xELENBQ0EsSUFBSyxDQUNKLElBQUksQ0FBRSxJQUFJLENBQ1YsU0FBUyxDQUFFLFdBQVcsSUFBSSxDQUFDLENBQUMsT0FBTyxLQUFLLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FDbEQsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiRGlhbW9uZHMuc3ZlbHRlIl19 */");
}
function create_fragment22(ctx) {
  let span;
  let div0;
  let t0;
  let div1;
  let t1;
  let div2;
  const block = {
    c: function create() {
      span = element("span");
      div0 = element("div");
      t0 = space();
      div1 = element("div");
      t1 = space();
      div2 = element("div");
      this.h();
    },
    l: function claim(nodes) {
      span = claim_element(nodes, "SPAN", { style: true, class: true });
      var span_nodes = children(span);
      div0 = claim_element(span_nodes, "DIV", { class: true });
      children(div0).forEach(detach_dev);
      t0 = claim_space(span_nodes);
      div1 = claim_element(span_nodes, "DIV", { class: true });
      children(div1).forEach(detach_dev);
      t1 = claim_space(span_nodes);
      div2 = claim_element(span_nodes, "DIV", { class: true });
      children(div2).forEach(detach_dev);
      span_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div0, "class", "svelte-1jnfmql");
      add_location(div0, file22, 11, 1, 265);
      attr_dev(div1, "class", "svelte-1jnfmql");
      add_location(div1, file22, 12, 1, 274);
      attr_dev(div2, "class", "svelte-1jnfmql");
      add_location(div2, file22, 13, 1, 283);
      set_style(
        span,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        span,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        span,
        "--duration",
        /*duration*/
        ctx[2]
      );
      attr_dev(span, "class", "svelte-1jnfmql");
      toggle_class(
        span,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(span, file22, 7, 0, 153);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, span, anchor);
      append_hydration_dev(span, div0);
      append_hydration_dev(span, t0);
      append_hydration_dev(span, div1);
      append_hydration_dev(span, t1);
      append_hydration_dev(span, div2);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          span,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          span,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          span,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          span,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(span);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment22.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance22($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Diamonds", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1.5s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Diamonds> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var Diamonds = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance22,
      create_fragment22,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css22
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Diamonds",
      options,
      id: create_fragment22.name
    });
  }
  get color() {
    throw new Error("<Diamonds>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Diamonds>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Diamonds>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Diamonds>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Diamonds>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Diamonds>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Diamonds>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Diamonds>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Diamonds>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Diamonds>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Diamonds_default = Diamonds;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Clock.svelte
var file23 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Clock.svelte";
function add_css23(target) {
  append_styles(target, "svelte-db2m9w", "div.svelte-db2m9w{position:relative;width:var(--size);height:var(--size);background-color:transparent;box-shadow:inset 0px 0px 0px 2px var(--color);border-radius:50%}div.svelte-db2m9w::before,div.svelte-db2m9w::after{position:absolute;content:'';background-color:var(--color)}div.svelte-db2m9w::after{width:calc(var(--size) / 2.4);height:2px;top:calc(var(--size) / 2);left:calc(var(--size) / 2);transform-origin:1px 1px;animation:svelte-db2m9w-rotate calc(var(--duration) / 4) linear infinite}div.svelte-db2m9w::before{width:calc(var(--size) / 3);height:2px;top:calc((var(--size) / 2));left:calc((var(--size) / 2));transform-origin:1px 1px;animation:svelte-db2m9w-rotate var(--duration) linear infinite}.pause-animation.svelte-db2m9w,.pause-animation.svelte-db2m9w::before,.pause-animation.svelte-db2m9w::after{animation-play-state:paused}@keyframes svelte-db2m9w-rotate{100%{transform:rotate(360deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQ2xvY2suc3ZlbHRlIiwibWFwcGluZ3MiOiJBQWFDLGlCQUFJLENBQ0gsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNuQixnQkFBZ0IsQ0FBRSxXQUFXLENBQzdCLFVBQVUsQ0FBRSxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLElBQUksT0FBTyxDQUFDLENBQzlDLGFBQWEsQ0FBRSxHQUNoQixDQUNBLGlCQUFHLFFBQVEsQ0FDWCxpQkFBRyxPQUFRLENBQ1YsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsT0FBTyxDQUFFLEVBQUUsQ0FDWCxnQkFBZ0IsQ0FBRSxJQUFJLE9BQU8sQ0FDOUIsQ0FDQSxpQkFBRyxPQUFRLENBQ1YsS0FBSyxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQzlCLE1BQU0sQ0FBRSxHQUFHLENBQ1gsR0FBRyxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQzFCLElBQUksQ0FBRSxLQUFLLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUMzQixnQkFBZ0IsQ0FBRSxHQUFHLENBQUMsR0FBRyxDQUN6QixTQUFTLENBQUUsb0JBQU0sQ0FBQyxLQUFLLElBQUksVUFBVSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxRQUNwRCxDQUNBLGlCQUFHLFFBQVMsQ0FDWCxLQUFLLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FDNUIsTUFBTSxDQUFFLEdBQUcsQ0FDWCxHQUFHLENBQUUsS0FBSyxDQUFDLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQzVCLElBQUksQ0FBRSxLQUFLLENBQUMsSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FDN0IsZ0JBQWdCLENBQUUsR0FBRyxDQUFDLEdBQUcsQ0FDekIsU0FBUyxDQUFFLG9CQUFNLENBQUMsSUFBSSxVQUFVLENBQUMsQ0FBQyxNQUFNLENBQUMsUUFDMUMsQ0FDQSw4QkFBZ0IsQ0FDaEIsOEJBQWdCLFFBQVEsQ0FDeEIsOEJBQWdCLE9BQVEsQ0FDdkIsb0JBQW9CLENBQUUsTUFDdkIsQ0FDQSxXQUFXLG9CQUFPLENBQ2pCLElBQUssQ0FDSixTQUFTLENBQUUsT0FBTyxNQUFNLENBQ3pCLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIkNsb2NrLnN2ZWx0ZSJdfQ== */");
}
function create_fragment23(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { style: true, class: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      attr_dev(div, "class", "svelte-db2m9w");
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file23, 7, 0, 151);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment23.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance23($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Clock", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "8s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Clock> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var Clock = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance23,
      create_fragment23,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css23
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Clock",
      options,
      id: create_fragment23.name
    });
  }
  get color() {
    throw new Error("<Clock>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Clock>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Clock>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Clock>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Clock>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Clock>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Clock>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Clock>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Clock>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Clock>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Clock_default = Clock;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Wave.svelte
var file24 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Wave.svelte";
function add_css24(target) {
  append_styles(target, "svelte-1vzsw15", ".wrapper.svelte-1vzsw15{position:relative;display:flex;justify-content:center;align-items:center;width:calc(var(--size) * 2.5);height:var(--size);overflow:hidden}.bar.svelte-1vzsw15{position:absolute;top:calc(var(--size) / 10);width:calc(var(--size) / 5);height:calc(var(--size) / 10);margin-top:calc(var(--size) - var(--size) / 10);transform:skewY(0deg);background-color:var(--color);animation:svelte-1vzsw15-motion var(--duration) ease-in-out infinite}.pause-animation.svelte-1vzsw15{animation-play-state:paused}@keyframes svelte-1vzsw15-motion{25%{transform:skewY(25deg)}50%{height:100%;margin-top:0}75%{transform:skewY(-25deg)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiV2F2ZS5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBc0JDLHVCQUFTLENBQ1IsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsT0FBTyxDQUFFLElBQUksQ0FDYixlQUFlLENBQUUsTUFBTSxDQUN2QixXQUFXLENBQUUsTUFBTSxDQUNuQixLQUFLLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FDOUIsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQ25CLFFBQVEsQ0FBRSxNQUNYLENBQ0EsbUJBQUssQ0FDSixRQUFRLENBQUUsUUFBUSxDQUNsQixHQUFHLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FDM0IsS0FBSyxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQzVCLE1BQU0sQ0FBRSxLQUFLLElBQUksTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUM5QixVQUFVLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FDaEQsU0FBUyxDQUFFLE1BQU0sSUFBSSxDQUFDLENBQ3RCLGdCQUFnQixDQUFFLElBQUksT0FBTyxDQUFDLENBQzlCLFNBQVMsQ0FBRSxxQkFBTSxDQUFDLElBQUksVUFBVSxDQUFDLENBQUMsV0FBVyxDQUFDLFFBQy9DLENBQ0EsK0JBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBQ0EsV0FBVyxxQkFBTyxDQUNqQixHQUFJLENBQ0gsU0FBUyxDQUFFLE1BQU0sS0FBSyxDQUN2QixDQUNBLEdBQUksQ0FDSCxNQUFNLENBQUUsSUFBSSxDQUNaLFVBQVUsQ0FBRSxDQUNiLENBQ0EsR0FBSSxDQUNILFNBQVMsQ0FBRSxNQUFNLE1BQU0sQ0FDeEIsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiV2F2ZS5zdmVsdGUiXX0= */");
}
function get_each_context10(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i];
  return child_ctx;
}
function create_each_block10(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "bar svelte-1vzsw15");
      set_style(
        div,
        "left",
        /*version*/
        ctx[7] * (+/*size*/
        ctx[3] / 5 + (+/*size*/
        ctx[3] / 15 - +/*size*/
        ctx[3] / 100)) + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "animation-delay",
        /*version*/
        ctx[7] * (+/*durationNum*/
        ctx[6] / 8.3) + /*durationUnit*/
        ctx[5]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file24, 12, 2, 460);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "left",
          /*version*/
          ctx2[7] * (+/*size*/
          ctx2[3] / 5 + (+/*size*/
          ctx2[3] / 15 - +/*size*/
          ctx2[3] / 100)) + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block10.name,
    type: "each",
    source: "(12:1) {#each range(10, 0) as version}",
    ctx
  });
  return block;
}
function create_fragment24(ctx) {
  let div;
  let each_value = ensure_array_like_dev(range(10, 0));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block10(get_each_context10(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      div = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      var div_nodes = children(div);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(div_nodes);
      }
      div_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-1vzsw15");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(div, file24, 10, 0, 331);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit, durationNum, durationUnit, pause*/
      122) {
        each_value = ensure_array_like_dev(range(10, 0));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context10(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block10(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment24.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance24($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Wave", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1.25s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Wave> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(5, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(6, durationNum = $$props2.durationNum);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause, durationUnit, durationNum];
}
var Wave = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance24,
      create_fragment24,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css24
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Wave",
      options,
      id: create_fragment24.name
    });
  }
  get color() {
    throw new Error("<Wave>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Wave>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Wave>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Wave>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Wave>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Wave>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Wave>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Wave>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Wave>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Wave>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Wave_default = Wave;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/Puff.svelte
var file25 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\Puff.svelte";
function add_css25(target) {
  append_styles(target, "svelte-bv9t2p", ".wrapper.svelte-bv9t2p{display:inherit;position:relative;width:var(--size);height:var(--size)}.circle.svelte-bv9t2p{position:absolute;width:var(--size);height:var(--size);border:thick solid var(--rgba);border-radius:50%;opacity:1;top:0px;left:0px;animation-fill-mode:both;animation-iteration-count:infinite;animation-timing-function:cubic-bezier(0.165, 0.84, 0.44, 1), cubic-bezier(0.3, 0.61, 0.355, 1);animation-direction:normal, normal;animation-fill-mode:none, none;animation-play-state:running, running;animation-name:svelte-bv9t2p-puff-1, svelte-bv9t2p-puff-2;box-sizing:border-box}.pause-animation.svelte-bv9t2p{animation-play-state:paused}@keyframes svelte-bv9t2p-puff-1{0%{transform:scale(0)}100%{transform:scale(1)}}@keyframes svelte-bv9t2p-puff-2{0%{opacity:1}100%{opacity:0}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiUHVmZi5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBNEJDLHNCQUFTLENBQ1IsT0FBTyxDQUFFLE9BQU8sQ0FDaEIsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsS0FBSyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FDbkIsQ0FDQSxxQkFBUSxDQUNQLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixNQUFNLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbkIsTUFBTSxDQUFFLEtBQUssQ0FBQyxLQUFLLENBQUMsSUFBSSxNQUFNLENBQUMsQ0FDL0IsYUFBYSxDQUFFLEdBQUcsQ0FDbEIsT0FBTyxDQUFFLENBQUMsQ0FDVixHQUFHLENBQUUsR0FBRyxDQUNSLElBQUksQ0FBRSxHQUFHLENBQ1QsbUJBQW1CLENBQUUsSUFBSSxDQUN6Qix5QkFBeUIsQ0FBRSxRQUFRLENBQ25DLHlCQUF5QixDQUFFLGFBQWEsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxhQUFhLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUNoRyxtQkFBbUIsQ0FBRSxNQUFNLENBQUMsQ0FBQyxNQUFNLENBQ25DLG1CQUFtQixDQUFFLElBQUksQ0FBQyxDQUFDLElBQUksQ0FDL0Isb0JBQW9CLENBQUUsT0FBTyxDQUFDLENBQUMsT0FBTyxDQUN0QyxjQUFjLENBQUUsb0JBQU0sQ0FBQyxDQUFDLG9CQUFNLENBQzlCLFVBQVUsQ0FBRSxVQUNiLENBQ0EsOEJBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBQ0EsV0FBVyxvQkFBTyxDQUNqQixFQUFHLENBQ0YsU0FBUyxDQUFFLE1BQU0sQ0FBQyxDQUNuQixDQUNBLElBQUssQ0FDSixTQUFTLENBQUUsTUFBTSxDQUFDLENBQ25CLENBQ0QsQ0FDQSxXQUFXLG9CQUFPLENBQ2pCLEVBQUcsQ0FDRixPQUFPLENBQUUsQ0FDVixDQUNBLElBQUssQ0FDSixPQUFPLENBQUUsQ0FDVixDQUNEIiwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlcyI6WyJQdWZmLnN2ZWx0ZSJdfQ== */");
}
function get_each_context11(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[8] = list[i];
  return child_ctx;
}
function create_each_block11(ctx) {
  let span;
  const block = {
    c: function create() {
      span = element("span");
      this.h();
    },
    l: function claim(nodes) {
      span = claim_element(nodes, "SPAN", { class: true, style: true });
      children(span).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(span, "class", "circle svelte-bv9t2p");
      set_style(
        span,
        "animation-delay",
        /*version*/
        ctx[8] === 1 ? "-1s" : "0s"
      );
      set_style(span, "animation-duration", 2 / +/*durationNum*/
      ctx[7] + /*durationUnit*/
      ctx[6]);
      toggle_class(
        span,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(span, file25, 17, 2, 535);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, span, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*pause*/
      16) {
        toggle_class(
          span,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(span);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block11.name,
    type: "each",
    source: "(17:1) {#each range(2, 1) as version}",
    ctx
  });
  return block;
}
function create_fragment25(ctx) {
  let span;
  let each_value = ensure_array_like_dev(range(2, 1));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block11(get_each_context11(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      span = element("span");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      this.h();
    },
    l: function claim(nodes) {
      span = claim_element(nodes, "SPAN", { class: true, style: true });
      var span_nodes = children(span);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(span_nodes);
      }
      span_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(span, "class", "wrapper svelte-bv9t2p");
      set_style(
        span,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        span,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        span,
        "--rgba",
        /*rgba*/
        ctx[5]
      );
      set_style(
        span,
        "--duration",
        /*duration*/
        ctx[2]
      );
      add_location(span, file25, 12, 0, 388);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, span, anchor);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(span, null);
        }
      }
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*durationNum, durationUnit, pause*/
      208) {
        each_value = ensure_array_like_dev(range(2, 1));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context11(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block11(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(span, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*size, unit*/
      10) {
        set_style(
          span,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          span,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*rgba*/
      32) {
        set_style(
          span,
          "--rgba",
          /*rgba*/
          ctx2[5]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          span,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(span);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment25.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance25($$self, $$props, $$invalidate) {
  var _a;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Puff", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "1s" } = $$props;
  let { size = "60" } = $$props;
  let { pause = false } = $$props;
  let durationUnit = ((_a = duration.match(durationUnitRegex)) == null ? void 0 : _a[0]) ?? "s";
  let durationNum = duration.replace(durationUnitRegex, "");
  let rgba;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<Puff> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({
    range,
    durationUnitRegex,
    calculateRgba,
    color,
    unit,
    duration,
    size,
    pause,
    durationUnit,
    durationNum,
    rgba
  });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
    if ("durationUnit" in $$props2)
      $$invalidate(6, durationUnit = $$props2.durationUnit);
    if ("durationNum" in $$props2)
      $$invalidate(7, durationNum = $$props2.durationNum);
    if ("rgba" in $$props2)
      $$invalidate(5, rgba = $$props2.rgba);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*color*/
    1) {
      $:
        $$invalidate(5, rgba = calculateRgba(color, 1));
    }
  };
  return [color, unit, duration, size, pause, rgba, durationUnit, durationNum];
}
var Puff = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance25,
      create_fragment25,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css25
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Puff",
      options,
      id: create_fragment25.name
    });
  }
  get color() {
    throw new Error("<Puff>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<Puff>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<Puff>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<Puff>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<Puff>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<Puff>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<Puff>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Puff>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<Puff>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<Puff>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Puff_default = Puff;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/ArrowDown.svelte
var file26 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\ArrowDown.svelte";
function add_css26(target) {
  append_styles(target, "svelte-f6hkgc", ".wrapper.svelte-f6hkgc{width:var(--size);height:calc(var(--size) * 1.5);margin-left:var(--size);background:var(--color);display:inline-block;position:relative;box-sizing:border-box;animation:svelte-f6hkgc-bump var(--duration) ease-in infinite alternate}.wrapper.svelte-f6hkgc::after{content:'';box-sizing:border-box;left:50%;top:100%;transform:translate(-50%, 0);position:absolute;border:var(--size) solid transparent;border-top-color:var(--color)}.pause-animation.svelte-f6hkgc{animation-play-state:paused}@keyframes svelte-f6hkgc-bump{0%{transform:translate(-50%, 5px)}100%{transform:translate(-50%, -5px)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQXJyb3dEb3duLnN2ZWx0ZSIsIm1hcHBpbmdzIjoiQUFjQyxzQkFBUyxDQUNSLEtBQUssQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUNsQixNQUFNLENBQUUsS0FBSyxJQUFJLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FDL0IsV0FBVyxDQUFFLElBQUksTUFBTSxDQUFDLENBQ3hCLFVBQVUsQ0FBRSxJQUFJLE9BQU8sQ0FBQyxDQUN4QixPQUFPLENBQUUsWUFBWSxDQUNyQixRQUFRLENBQUUsUUFBUSxDQUNsQixVQUFVLENBQUUsVUFBVSxDQUN0QixTQUFTLENBQUUsa0JBQUksQ0FBQyxJQUFJLFVBQVUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsU0FDbEQsQ0FDQSxzQkFBUSxPQUFRLENBQ2YsT0FBTyxDQUFFLEVBQUUsQ0FDWCxVQUFVLENBQUUsVUFBVSxDQUN0QixJQUFJLENBQUUsR0FBRyxDQUNULEdBQUcsQ0FBRSxJQUFJLENBQ1QsU0FBUyxDQUFFLFVBQVUsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQzdCLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLE1BQU0sQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUFDLEtBQUssQ0FBQyxXQUFXLENBQ3JDLGdCQUFnQixDQUFFLElBQUksT0FBTyxDQUM5QixDQUVBLDhCQUFpQixDQUNoQixvQkFBb0IsQ0FBRSxNQUN2QixDQUNBLFdBQVcsa0JBQUssQ0FDZixFQUFHLENBQ0YsU0FBUyxDQUFFLFVBQVUsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUMvQixDQUNBLElBQUssQ0FDSixTQUFTLENBQUUsVUFBVSxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQ2hDLENBQ0QiLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VzIjpbIkFycm93RG93bi5zdmVsdGUiXX0= */");
}
function create_fragment26(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-f6hkgc");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file26, 7, 0, 153);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment26.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance26($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("ArrowDown", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "0.4s" } = $$props;
  let { size = "15" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<ArrowDown> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var ArrowDown = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance26,
      create_fragment26,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css26
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "ArrowDown",
      options,
      id: create_fragment26.name
    });
  }
  get color() {
    throw new Error("<ArrowDown>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<ArrowDown>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<ArrowDown>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<ArrowDown>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<ArrowDown>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<ArrowDown>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<ArrowDown>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<ArrowDown>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<ArrowDown>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<ArrowDown>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var ArrowDown_default = ArrowDown;

// ../../node_modules/.pnpm/svelte-loading-spinners@0.3.6/node_modules/svelte-loading-spinners/ArrowUp.svelte
var file27 = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-loading-spinners@0.3.6\\node_modules\\svelte-loading-spinners\\ArrowUp.svelte";
function add_css27(target) {
  append_styles(target, "svelte-1ju64u", ".wrapper.svelte-1ju64u{width:var(--size);height:calc(var(--size) * 1.5);margin-left:var(--size);margin-top:var(--size);background:var(--color);display:inline-block;position:relative;box-sizing:border-box;animation:svelte-1ju64u-bump var(--duration) ease-in infinite alternate}.wrapper.svelte-1ju64u::after{content:'';box-sizing:border-box;left:50%;bottom:100%;transform:translate(-50%, 0);position:absolute;border:var(--size) solid transparent;border-bottom-color:var(--color)}.pause-animation.svelte-1ju64u{animation-play-state:paused}@keyframes svelte-1ju64u-bump{0%{transform:translate(-50%, 5px)}100%{transform:translate(-50%, -5px)}}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiQXJyb3dVcC5zdmVsdGUiLCJtYXBwaW5ncyI6IkFBY0Msc0JBQVMsQ0FDUixLQUFLLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDbEIsTUFBTSxDQUFFLEtBQUssSUFBSSxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQy9CLFdBQVcsQ0FBRSxJQUFJLE1BQU0sQ0FBQyxDQUN4QixVQUFVLENBQUUsSUFBSSxNQUFNLENBQUMsQ0FDdkIsVUFBVSxDQUFFLElBQUksT0FBTyxDQUFDLENBQ3hCLE9BQU8sQ0FBRSxZQUFZLENBQ3JCLFFBQVEsQ0FBRSxRQUFRLENBQ2xCLFVBQVUsQ0FBRSxVQUFVLENBQ3RCLFNBQVMsQ0FBRSxrQkFBSSxDQUFDLElBQUksVUFBVSxDQUFDLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxTQUNsRCxDQUNBLHNCQUFRLE9BQVEsQ0FDZixPQUFPLENBQUUsRUFBRSxDQUNYLFVBQVUsQ0FBRSxVQUFVLENBQ3RCLElBQUksQ0FBRSxHQUFHLENBQ1QsTUFBTSxDQUFFLElBQUksQ0FDWixTQUFTLENBQUUsVUFBVSxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FDN0IsUUFBUSxDQUFFLFFBQVEsQ0FDbEIsTUFBTSxDQUFFLElBQUksTUFBTSxDQUFDLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FDckMsbUJBQW1CLENBQUUsSUFBSSxPQUFPLENBQ2pDLENBRUEsOEJBQWlCLENBQ2hCLG9CQUFvQixDQUFFLE1BQ3ZCLENBQ0EsV0FBVyxrQkFBSyxDQUNmLEVBQUcsQ0FDRixTQUFTLENBQUUsVUFBVSxJQUFJLENBQUMsQ0FBQyxHQUFHLENBQy9CLENBQ0EsSUFBSyxDQUNKLFNBQVMsQ0FBRSxVQUFVLElBQUksQ0FBQyxDQUFDLElBQUksQ0FDaEMsQ0FDRCIsIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZXMiOlsiQXJyb3dVcC5zdmVsdGUiXX0= */");
}
function create_fragment27(ctx) {
  let div;
  const block = {
    c: function create() {
      div = element("div");
      this.h();
    },
    l: function claim(nodes) {
      div = claim_element(nodes, "DIV", { class: true, style: true });
      children(div).forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(div, "class", "wrapper svelte-1ju64u");
      set_style(
        div,
        "--size",
        /*size*/
        ctx[3] + /*unit*/
        ctx[1]
      );
      set_style(
        div,
        "--color",
        /*color*/
        ctx[0]
      );
      set_style(
        div,
        "--duration",
        /*duration*/
        ctx[2]
      );
      toggle_class(
        div,
        "pause-animation",
        /*pause*/
        ctx[4]
      );
      add_location(div, file27, 7, 0, 153);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, div, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (dirty & /*size, unit*/
      10) {
        set_style(
          div,
          "--size",
          /*size*/
          ctx2[3] + /*unit*/
          ctx2[1]
        );
      }
      if (dirty & /*color*/
      1) {
        set_style(
          div,
          "--color",
          /*color*/
          ctx2[0]
        );
      }
      if (dirty & /*duration*/
      4) {
        set_style(
          div,
          "--duration",
          /*duration*/
          ctx2[2]
        );
      }
      if (dirty & /*pause*/
      16) {
        toggle_class(
          div,
          "pause-animation",
          /*pause*/
          ctx2[4]
        );
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(div);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment27.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance27($$self, $$props, $$invalidate) {
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("ArrowUp", slots, []);
  let { color = "#FF3E00" } = $$props;
  let { unit = "px" } = $$props;
  let { duration = "0.4s" } = $$props;
  let { size = "15" } = $$props;
  let { pause = false } = $$props;
  const writable_props = ["color", "unit", "duration", "size", "pause"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<ArrowUp> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  $$self.$capture_state = () => ({ color, unit, duration, size, pause });
  $$self.$inject_state = ($$props2) => {
    if ("color" in $$props2)
      $$invalidate(0, color = $$props2.color);
    if ("unit" in $$props2)
      $$invalidate(1, unit = $$props2.unit);
    if ("duration" in $$props2)
      $$invalidate(2, duration = $$props2.duration);
    if ("size" in $$props2)
      $$invalidate(3, size = $$props2.size);
    if ("pause" in $$props2)
      $$invalidate(4, pause = $$props2.pause);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  return [color, unit, duration, size, pause];
}
var ArrowUp = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance27,
      create_fragment27,
      safe_not_equal,
      {
        color: 0,
        unit: 1,
        duration: 2,
        size: 3,
        pause: 4
      },
      add_css27
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "ArrowUp",
      options,
      id: create_fragment27.name
    });
  }
  get color() {
    throw new Error("<ArrowUp>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set color(value) {
    throw new Error("<ArrowUp>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get unit() {
    throw new Error("<ArrowUp>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set unit(value) {
    throw new Error("<ArrowUp>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get duration() {
    throw new Error("<ArrowUp>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set duration(value) {
    throw new Error("<ArrowUp>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get size() {
    throw new Error("<ArrowUp>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<ArrowUp>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get pause() {
    throw new Error("<ArrowUp>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set pause(value) {
    throw new Error("<ArrowUp>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var ArrowUp_default = ArrowUp;
export {
  ArrowDown_default as ArrowDown,
  ArrowUp_default as ArrowUp,
  BarLoader_default as BarLoader,
  Chasing_default as Chasing,
  Circle_default as Circle,
  Circle2_default as Circle2,
  Circle3_default as Circle3,
  Clock_default as Clock,
  Diamonds_default as Diamonds,
  DoubleBounce_default as DoubleBounce,
  Firework_default as Firework,
  GoogleSpin_default as GoogleSpin,
  Jellyfish_default as Jellyfish,
  Jumper_default as Jumper,
  Moon_default as Moon,
  Plane_default as Plane,
  Puff_default as Puff,
  Pulse_default as Pulse,
  Rainbow_default as Rainbow,
  RingLoader_default as RingLoader,
  ScaleOut_default as ScaleOut,
  Shadow_default as Shadow,
  SpinLine_default as SpinLine,
  Square_default as Square,
  Stretch_default as Stretch,
  SyncLoader_default as SyncLoader,
  Wave_default as Wave
};
//# sourceMappingURL=svelte-loading-spinners.js.map
