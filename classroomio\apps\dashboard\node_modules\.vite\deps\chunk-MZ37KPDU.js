import {
  SvelteComponentDev,
  add_location,
  append_hydration_dev,
  assign,
  attr_dev,
  children,
  claim_svg_element,
  claim_text,
  compute_rest_props,
  detach_dev,
  dispatch_dev,
  exclude_internal_props,
  get_spread_update,
  init,
  insert_hydration_dev,
  noop,
  safe_not_equal,
  set_data_dev,
  set_svg_attributes,
  svg_element,
  text,
  validate_slots
} from "./chunk-E4ZC5ETH.js";

// ../../node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/Settings.svelte
var file = "C:/Users/<USER>/Downloads/New folder (2)/classroomio/node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/Settings.svelte";
function create_if_block(ctx) {
  let title_1;
  let t;
  const block = {
    c: function create() {
      title_1 = svg_element("title");
      t = text(
        /*title*/
        ctx[1]
      );
      this.h();
    },
    l: function claim(nodes) {
      title_1 = claim_svg_element(nodes, "title", {});
      var title_1_nodes = children(title_1);
      t = claim_text(
        title_1_nodes,
        /*title*/
        ctx[1]
      );
      title_1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      add_location(title_1, file, 22, 13, 543);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, title_1, anchor);
      append_hydration_dev(title_1, t);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*title*/
      2)
        set_data_dev(
          t,
          /*title*/
          ctx2[1]
        );
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(title_1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block.name,
    type: "if",
    source: "(23:2) {#if title}",
    ctx
  });
  return block;
}
function create_fragment(ctx) {
  let svg;
  let path0;
  let path1;
  let if_block = (
    /*title*/
    ctx[1] && create_if_block(ctx)
  );
  let svg_levels = [
    { xmlns: "http://www.w3.org/2000/svg" },
    { viewBox: "0 0 32 32" },
    { fill: "currentColor" },
    { preserveAspectRatio: "xMidYMid meet" },
    { width: (
      /*size*/
      ctx[0]
    ) },
    { height: (
      /*size*/
      ctx[0]
    ) },
    /*attributes*/
    ctx[2],
    /*$$restProps*/
    ctx[3]
  ];
  let svg_data = {};
  for (let i = 0; i < svg_levels.length; i += 1) {
    svg_data = assign(svg_data, svg_levels[i]);
  }
  const block = {
    c: function create() {
      svg = svg_element("svg");
      if (if_block)
        if_block.c();
      path0 = svg_element("path");
      path1 = svg_element("path");
      this.h();
    },
    l: function claim(nodes) {
      svg = claim_svg_element(nodes, "svg", {
        xmlns: true,
        viewBox: true,
        fill: true,
        preserveAspectRatio: true,
        width: true,
        height: true
      });
      var svg_nodes = children(svg);
      if (if_block)
        if_block.l(svg_nodes);
      path0 = claim_svg_element(svg_nodes, "path", { d: true });
      children(path0).forEach(detach_dev);
      path1 = claim_svg_element(svg_nodes, "path", { d: true });
      children(path1).forEach(detach_dev);
      svg_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(path0, "d", "M27,16.76c0-.25,0-.5,0-.76s0-.51,0-.77l1.92-1.68A2,2,0,0,0,29.3,11L26.94,7a2,2,0,0,0-1.73-1,2,2,0,0,0-.64.1l-2.43.82a11.35,11.35,0,0,0-1.31-.75l-.51-2.52a2,2,0,0,0-2-1.61H13.64a2,2,0,0,0-2,1.61l-.51,2.52a11.48,11.48,0,0,0-1.32.75L7.43,6.06A2,2,0,0,0,6.79,6,2,2,0,0,0,5.06,7L2.7,11a2,2,0,0,0,.41,2.51L5,15.24c0,.25,0,.5,0,.76s0,.51,0,.77L3.11,18.45A2,2,0,0,0,2.7,21L5.06,25a2,2,0,0,0,1.73,1,2,2,0,0,0,.64-.1l2.43-.82a11.35,11.35,0,0,0,1.31.75l.51,2.52a2,2,0,0,0,2,1.61h4.72a2,2,0,0,0,2-1.61l.51-2.52a11.48,11.48,0,0,0,1.32-.75l2.42.82a2,2,0,0,0,.64.1,2,2,0,0,0,1.73-1L29.3,21a2,2,0,0,0-.41-2.51ZM25.21,24l-3.43-1.16a8.86,8.86,0,0,1-2.71,1.57L18.36,28H13.64l-.71-3.55a9.36,9.36,0,0,1-2.7-1.57L6.79,24,4.43,20l2.72-2.4a8.9,8.9,0,0,1,0-3.13L4.43,12,6.79,8l3.43,1.16a8.86,8.86,0,0,1,2.71-1.57L13.64,4h4.72l.71,3.55a9.36,9.36,0,0,1,2.7,1.57L25.21,8,27.57,12l-2.72,2.4a8.9,8.9,0,0,1,0,3.13L27.57,20Z");
      add_location(path0, file, 23, 2, 573);
      attr_dev(path1, "d", "M16,22a6,6,0,1,1,6-6A5.94,5.94,0,0,1,16,22Zm0-10a3.91,3.91,0,0,0-4,4,3.91,3.91,0,0,0,4,4,3.91,3.91,0,0,0,4-4A3.91,3.91,0,0,0,16,12Z");
      add_location(path1, file, 23, 912, 1483);
      set_svg_attributes(svg, svg_data);
      add_location(svg, file, 13, 0, 337);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, svg, anchor);
      if (if_block)
        if_block.m(svg, null);
      append_hydration_dev(svg, path0);
      append_hydration_dev(svg, path1);
    },
    p: function update(ctx2, [dirty]) {
      if (
        /*title*/
        ctx2[1]
      ) {
        if (if_block) {
          if_block.p(ctx2, dirty);
        } else {
          if_block = create_if_block(ctx2);
          if_block.c();
          if_block.m(svg, path0);
        }
      } else if (if_block) {
        if_block.d(1);
        if_block = null;
      }
      set_svg_attributes(svg, svg_data = get_spread_update(svg_levels, [
        { xmlns: "http://www.w3.org/2000/svg" },
        { viewBox: "0 0 32 32" },
        { fill: "currentColor" },
        { preserveAspectRatio: "xMidYMid meet" },
        dirty & /*size*/
        1 && { width: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*size*/
        1 && { height: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*attributes*/
        4 && /*attributes*/
        ctx2[2],
        dirty & /*$$restProps*/
        8 && /*$$restProps*/
        ctx2[3]
      ]));
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(svg);
      }
      if (if_block)
        if_block.d();
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance($$self, $$props, $$invalidate) {
  let labelled;
  let attributes;
  const omit_props_names = ["size", "title"];
  let $$restProps = compute_rest_props($$props, omit_props_names);
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Settings", slots, []);
  let { size = 16 } = $$props;
  let { title = void 0 } = $$props;
  $$self.$$set = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    $$invalidate(3, $$restProps = compute_rest_props($$props, omit_props_names));
    if ("size" in $$new_props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$new_props)
      $$invalidate(1, title = $$new_props.title);
  };
  $$self.$capture_state = () => ({ size, title, labelled, attributes });
  $$self.$inject_state = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), $$new_props));
    if ("size" in $$props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$props)
      $$invalidate(1, title = $$new_props.title);
    if ("labelled" in $$props)
      $$invalidate(4, labelled = $$new_props.labelled);
    if ("attributes" in $$props)
      $$invalidate(2, attributes = $$new_props.attributes);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    $:
      $$invalidate(4, labelled = $$props["aria-label"] || $$props["aria-labelledby"] || title);
    $:
      $$invalidate(2, attributes = {
        "aria-hidden": labelled ? void 0 : true,
        role: labelled ? "img" : void 0,
        focusable: Number($$props["tabindex"]) === 0 ? true : void 0
      });
  };
  $$props = exclude_internal_props($$props);
  return [size, title, attributes, $$restProps, labelled];
}
var Settings = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance, create_fragment, safe_not_equal, { size: 0, title: 1 });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Settings",
      options,
      id: create_fragment.name
    });
  }
  get size() {
    throw new Error("<Settings>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Settings>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get title() {
    throw new Error("<Settings>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set title(value) {
    throw new Error("<Settings>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Settings_default = Settings;

export {
  Settings_default
};
//# sourceMappingURL=chunk-MZ37KPDU.js.map
