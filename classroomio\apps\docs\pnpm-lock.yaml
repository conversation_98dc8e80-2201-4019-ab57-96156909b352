lockfileVersion: 5.3

specifiers:
  '@types/node': 18.11.10
  next: ^13.0.6
  nextra: latest
  nextra-theme-docs: latest
  react: ^18.2.0
  react-dom: ^18.2.0
  typescript: ^4.9.3

dependencies:
  next: 13.0.6_react-dom@18.2.0+react@18.2.0
  nextra: 2.2.14_f26ff3bd08f1cd28b0f73422c76f5ffd
  nextra-theme-docs: 2.2.14_d8d66b9d2170cddb63c39dddec8541b9
  react: 18.2.0
  react-dom: 18.2.0_react@18.2.0

devDependencies:
  '@types/node': 18.11.10
  typescript: 4.9.3

packages:

  /@babel/runtime/7.20.6:
    resolution: {integrity: sha512-Q+8MqP7TiHMWzSfwiJwXCjyf4GYA4Dgw3emg/7xmwsdLJOZUp+nMqcOwOzzYheuM1rhDu8FSj2l0aoMygEuXuA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.13.11
    dev: false

  /@headlessui/react/1.7.10_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-1m66h/5eayTEZVT2PI13/2PG3EVC7a9XalmUtVSC8X76pcyKYMuyX1XAL2RUtCr8WhoMa/KrDEyoeU5v+kSQOw==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16 || ^17 || ^18
      react-dom: ^16 || ^17 || ^18
    dependencies:
      client-only: 0.0.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@mdx-js/mdx/2.2.1:
    resolution: {integrity: sha512-hZ3ex7exYLJn6FfReq8yTvA6TE53uW9UHJQM9IlSauOuS55J9y8RtA7W+dzp6Yrzr00/U1sd7q+Wf61q6SfiTQ==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/mdx': 2.0.3
      estree-util-build-jsx: 2.2.0
      estree-util-is-identifier-name: 2.0.1
      estree-util-to-js: 1.1.0
      estree-walker: 3.0.1
      hast-util-to-estree: 2.1.0
      markdown-extensions: 1.1.1
      periscopic: 3.0.4
      remark-mdx: 2.1.5
      remark-parse: 10.0.1
      remark-rehype: 10.1.0
      unified: 10.1.2
      unist-util-position-from-estree: 1.1.1
      unist-util-stringify-position: 3.0.2
      unist-util-visit: 4.1.1
      vfile: 5.3.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@mdx-js/react/2.2.1_react@18.2.0:
    resolution: {integrity: sha512-YdXcMcEnqZhzql98RNrqYo9cEhTTesBiCclEtoiQUbJwx87q9453GTapYU6kJ8ZZ2ek1Vp25SiAXEFy5O/eAPw==}
    peerDependencies:
      react: '>=16'
    dependencies:
      '@types/mdx': 2.0.3
      '@types/react': 18.0.25
      react: 18.2.0
    dev: false

  /@napi-rs/simple-git-android-arm-eabi/0.1.8:
    resolution: {integrity: sha512-JJCejHBB1G6O8nxjQLT4quWCcvLpC3oRdJJ9G3MFYSCoYS8i1bWCWeU+K7Br+xT+D6s1t9q8kNJAwJv9Ygpi0g==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-android-arm64/0.1.8:
    resolution: {integrity: sha512-mraHzwWBw3tdRetNOS5KnFSjvdAbNBnjFLA8I4PwTCPJj3Q4txrigcPp2d59cJ0TC51xpnPXnZjYdNwwSI9g6g==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-darwin-arm64/0.1.8:
    resolution: {integrity: sha512-ufy/36eI/j4UskEuvqSH7uXtp3oXeLDmjQCfKJz3u5Vx98KmOMKrqAm2H81AB2WOtCo5mqS6PbBeUXR8BJX8lQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-darwin-x64/0.1.8:
    resolution: {integrity: sha512-Vb21U+v3tPJNl+8JtIHHT8HGe6WZ8o1Tq3f6p+Jx9Cz71zEbcIiB9FCEMY1knS/jwQEOuhhlI9Qk7d4HY+rprA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-linux-arm-gnueabihf/0.1.8:
    resolution: {integrity: sha512-6BPTJ7CzpSm2t54mRLVaUr3S7ORJfVJoCk2rQ8v8oDg0XAMKvmQQxOsAgqKBo9gYNHJnqrOx3AEuEgvB586BuQ==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-linux-arm64-gnu/0.1.8:
    resolution: {integrity: sha512-qfESqUCAA/XoQpRXHptSQ8gIFnETCQt1zY9VOkplx6tgYk9PCeaX4B1Xuzrh3eZamSCMJFn+1YB9Ut8NwyGgAA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-linux-arm64-musl/0.1.8:
    resolution: {integrity: sha512-G80BQPpaRmQpn8dJGHp4I2/YVhWDUNJwcCrJAtAdbKFDCMyCHJBln2ERL/+IEUlIAT05zK/c1Z5WEprvXEdXow==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-linux-x64-gnu/0.1.8:
    resolution: {integrity: sha512-NI6o1sZYEf6vPtNWJAm9w8BxJt+LlSFW0liSjYe3lc3e4dhMfV240f0ALeqlwdIldRPaDFwZSJX5/QbS7nMzhw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-linux-x64-musl/0.1.8:
    resolution: {integrity: sha512-wljGAEOW41er45VTiU8kXJmO480pQKzsgRCvPlJJSCaEVBbmo6XXbFIXnZy1a2J3Zyy2IOsRB4PVkUZaNuPkZQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-win32-arm64-msvc/0.1.8:
    resolution: {integrity: sha512-QuV4QILyKPfbWHoQKrhXqjiCClx0SxbCTVogkR89BwivekqJMd9UlMxZdoCmwLWutRx4z9KmzQqokvYI5QeepA==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git-win32-x64-msvc/0.1.8:
    resolution: {integrity: sha512-UzNS4JtjhZhZ5hRLq7BIUq+4JOwt1ThIKv11CsF1ag2l99f0123XvfEpjczKTaa94nHtjXYc2Mv9TjccBqYOew==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@napi-rs/simple-git/0.1.8:
    resolution: {integrity: sha512-BvOMdkkofTz6lEE35itJ/laUokPhr/5ToMGlOH25YnhLD2yN1KpRAT4blW9tT8281/1aZjW3xyi73bs//IrDKA==}
    engines: {node: '>= 10'}
    optionalDependencies:
      '@napi-rs/simple-git-android-arm-eabi': 0.1.8
      '@napi-rs/simple-git-android-arm64': 0.1.8
      '@napi-rs/simple-git-darwin-arm64': 0.1.8
      '@napi-rs/simple-git-darwin-x64': 0.1.8
      '@napi-rs/simple-git-linux-arm-gnueabihf': 0.1.8
      '@napi-rs/simple-git-linux-arm64-gnu': 0.1.8
      '@napi-rs/simple-git-linux-arm64-musl': 0.1.8
      '@napi-rs/simple-git-linux-x64-gnu': 0.1.8
      '@napi-rs/simple-git-linux-x64-musl': 0.1.8
      '@napi-rs/simple-git-win32-arm64-msvc': 0.1.8
      '@napi-rs/simple-git-win32-x64-msvc': 0.1.8
    dev: false

  /@next/env/13.0.6:
    resolution: {integrity: sha512-yceT6DCHKqPRS1cAm8DHvDvK74DLIkDQdm5iV+GnIts8h0QbdHvkUIkdOvQoOODgpr6018skbmSQp12z5OWIQQ==}
    dev: false

  /@next/swc-android-arm-eabi/13.0.6:
    resolution: {integrity: sha512-FGFSj3v2Bluw8fD/X+1eXIEB0PhoJE0zfutsAauRhmNpjjZshLDgoXMWm1jTRL/04K/o9gwwO2+A8+sPVCH1uw==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-android-arm64/13.0.6:
    resolution: {integrity: sha512-7MgbtU7kimxuovVsd7jSJWMkIHBDBUsNLmmlkrBRHTvgzx5nDBXogP0hzZm7EImdOPwVMPpUHRQMBP9mbsiJYQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-darwin-arm64/13.0.6:
    resolution: {integrity: sha512-AUVEpVTxbP/fxdFsjVI9d5a0CFn6NVV7A/RXOb0Y+pXKIIZ1V5rFjPwpYfIfyOo2lrqgehMNQcyMRoTrhq04xg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-darwin-x64/13.0.6:
    resolution: {integrity: sha512-SasCDJlshglsPnbzhWaIF6VEGkQy2NECcAOxPwaPr0cwbbt4aUlZ7QmskNzgolr5eAjFS/xTr7CEeKJtZpAAtQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-freebsd-x64/13.0.6:
    resolution: {integrity: sha512-6Lbxd9gAdXneTkwHyYW/qtX1Tdw7ND9UbiGsGz/SP43ZInNWnW6q0au4hEVPZ9bOWWRKzcVoeTBdoMpQk9Hx9w==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-arm-gnueabihf/13.0.6:
    resolution: {integrity: sha512-wNdi5A519e1P+ozEuYOhWPzzE6m1y7mkO6NFwn6watUwO0X9nZs7fT9THmnekvmFQpaZ6U+xf2MQ9poQoCh6jQ==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-arm64-gnu/13.0.6:
    resolution: {integrity: sha512-e8KTRnleQY1KLk5PwGV5hrmvKksCc74QRpHl5ffWnEEAtL2FE0ave5aIkXqErsPdXkiKuA/owp3LjQrP+/AH7Q==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-arm64-musl/13.0.6:
    resolution: {integrity: sha512-/7RF03C3mhjYpHN+pqOolgME3guiHU5T3TsejuyteqyEyzdEyLHod+jcYH6ft7UZ71a6TdOewvmbLOtzHW2O8A==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-x64-gnu/13.0.6:
    resolution: {integrity: sha512-kxyEXnYHpOEkFnmrlwB1QlzJtjC6sAJytKcceIyFUHbCaD3W/Qb5tnclcnHKTaFccizZRePXvV25Ok/eUSpKTw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-linux-x64-musl/13.0.6:
    resolution: {integrity: sha512-N0c6gubS3WW1oYYgo02xzZnNatfVQP/CiJq2ax+DJ55ePV62IACbRCU99TZNXXg+Kos6vNW4k+/qgvkvpGDeyA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-arm64-msvc/13.0.6:
    resolution: {integrity: sha512-QjeMB2EBqBFPb/ac0CYr7GytbhUkrG4EwFWbcE0vsRp4H8grt25kYpFQckL4Jak3SUrp7vKfDwZ/SwO7QdO8vw==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-ia32-msvc/13.0.6:
    resolution: {integrity: sha512-EQzXtdqRTcmhT/tCq81rIwE36Y3fNHPInaCuJzM/kftdXfa0F+64y7FAoMO13npX8EG1+SamXgp/emSusKrCXg==}
    engines: {node: '>= 10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@next/swc-win32-x64-msvc/13.0.6:
    resolution: {integrity: sha512-pSkqZ//UP/f2sS9T7IvHLfEWDPTX0vRyXJnAUNisKvO3eF3e1xdhDX7dix/X3Z3lnN4UjSwOzclAI87JFbOwmQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: false
    optional: true

  /@popperjs/core/2.11.6:
    resolution: {integrity: sha512-50/17A98tWUfQ176raKiOGXuYpLyyVMkxxG6oylzL3BPOlA6ADGdK7EYunSa4I064xerltq9TGXs8HmOk5E+vw==}
    dev: false

  /@swc/helpers/0.4.14:
    resolution: {integrity: sha512-4C7nX/dvpzB7za4Ql9K81xK3HPxCpHMgwTZVyf+9JQ6VUbn9jjZVN7/Nkdz/Ugzs2CSjqnL/UPXroiVBVHUWUw==}
    dependencies:
      tslib: 2.4.1
    dev: false

  /@types/acorn/4.0.6:
    resolution: {integrity: sha512-veQTnWP+1D/xbxVrPC3zHnCZRjSrKfhbMUlEA43iMZLu7EsnTtkJklIuwrCPbOi8YkvDQAiW05VQQFvvz9oieQ==}
    dependencies:
      '@types/estree': 1.0.0
    dev: false

  /@types/debug/4.1.7:
    resolution: {integrity: sha512-9AonUzyTjXXhEOa0DnqpzZi6VHlqKMswga9EXjpXnnqxwLtdvPPtlO8evrI5D9S6asFRCQ6v+wpiUKbw+vKqyg==}
    dependencies:
      '@types/ms': 0.7.31
    dev: false

  /@types/estree-jsx/1.0.0:
    resolution: {integrity: sha512-3qvGd0z8F2ENTGr/GG1yViqfiKmRfrXVx5sJyHGFu3z7m5g5utCQtGp/g29JnjflhtQJBv1WDQukHiT58xPcYQ==}
    dependencies:
      '@types/estree': 1.0.0
    dev: false

  /@types/estree/1.0.0:
    resolution: {integrity: sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==}
    dev: false

  /@types/hast/2.3.4:
    resolution: {integrity: sha512-wLEm0QvaoawEDoTRwzTXp4b4jpwiJDvR5KMnFnVodm3scufTlBOWRD6N1OBf9TZMhjlNsSfcO5V+7AF4+Vy+9g==}
    dependencies:
      '@types/unist': 2.0.6
    dev: false

  /@types/js-yaml/4.0.5:
    resolution: {integrity: sha512-FhpRzf927MNQdRZP0J5DLIdTXhjLYzeUTmLAu69mnVksLH9CJY3IuSeEgbKUki7GQZm0WqDkGzyxju2EZGD2wA==}
    dev: false

  /@types/katex/0.11.1:
    resolution: {integrity: sha512-DUlIj2nk0YnJdlWgsFuVKcX27MLW0KbKmGVoUHmFr+74FYYNUDAaj9ZqTADvsbE8rfxuVmSFc7KczYn5Y09ozg==}
    dev: false

  /@types/mdast/3.0.10:
    resolution: {integrity: sha512-W864tg/Osz1+9f4lrGTZpCSO5/z4608eUp19tbozkq2HJK6i3z1kT0H9tlADXuYIb1YYOBByU4Jsqkk75q48qA==}
    dependencies:
      '@types/unist': 2.0.6
    dev: false

  /@types/mdx/2.0.3:
    resolution: {integrity: sha512-IgHxcT3RC8LzFLhKwP3gbMPeaK7BM9eBH46OdapPA7yvuIUJ8H6zHZV53J8hGZcTSnt95jANt+rTBNUUc22ACQ==}
    dev: false

  /@types/ms/0.7.31:
    resolution: {integrity: sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==}
    dev: false

  /@types/node/18.11.10:
    resolution: {integrity: sha512-juG3RWMBOqcOuXC643OAdSA525V44cVgGV6dUDuiFtss+8Fk5x1hI93Rsld43VeJVIeqlP9I7Fn9/qaVqoEAuQ==}
    dev: true

  /@types/prop-types/15.7.5:
    resolution: {integrity: sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w==}
    dev: false

  /@types/react/18.0.25:
    resolution: {integrity: sha512-xD6c0KDT4m7n9uD4ZHi02lzskaiqcBxf4zi+tXZY98a04wvc0hi/TcCPC2FOESZi51Nd7tlUeOJY8RofL799/g==}
    dependencies:
      '@types/prop-types': 15.7.5
      '@types/scheduler': 0.16.2
      csstype: 3.1.1
    dev: false

  /@types/scheduler/0.16.2:
    resolution: {integrity: sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew==}
    dev: false

  /@types/unist/2.0.6:
    resolution: {integrity: sha512-PBjIUxZHOuj0R15/xuwJYjFi+KZdNFrehocChv4g5hu6aFroHue8m0lBP0POdK2nKzbw0cgV1mws8+V/JAcEkQ==}
    dev: false

  /acorn-jsx/5.3.2_acorn@8.8.1:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.8.1
    dev: false

  /acorn/8.8.1:
    resolution: {integrity: sha512-7zFpHzhnqYKrkYdUjF1HI1bzd0VygEGX8lFk4k5zVMqHEoES+P+7TKI+EvLO9WVMJ8eekdO0aDEK044xTXwPPA==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: false

  /ansi-styles/3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: false

  /arch/2.2.0:
    resolution: {integrity: sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ==}
    dev: false

  /arg/1.0.0:
    resolution: {integrity: sha512-Wk7TEzl1KqvTGs/uyhmHO/3XLd3t1UeU4IstvPXVzGPM522cTjqjNZ99esCkcL52sjqjo8e8CTBcWhkxvGzoAw==}
    dev: false

  /argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: false

  /argparse/2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: false

  /astring/1.8.3:
    resolution: {integrity: sha512-sRpyiNrx2dEYIMmUXprS8nlpRg2Drs8m9ElX9vVEXaCB4XEAJhKfs7IcX0IwShjuOAjLR6wzIrgoptz1n19i1A==}
    hasBin: true
    dev: false

  /bail/2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}
    dev: false

  /caniuse-lite/1.0.30001435:
    resolution: {integrity: sha512-kdCkUTjR+v4YAJelyiDTqiu82BDr4W4CP5sgTA0ZBmqn30XfS2ZghPLMowik9TPhS+psWJiUNxsqLyurDbmutA==}
    dev: false

  /ccount/2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}
    dev: false

  /chalk/2.3.0:
    resolution: {integrity: sha512-Az5zJR2CBujap2rqXGaJKaPHyJ0IrUimvYNX+ncCy8PJP4ltOGTrHUIo097ZaL2zMeKYpiCdqDvS6zdrTFok3Q==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 4.5.0
    dev: false

  /character-entities-html4/2.1.0:
    resolution: {integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==}
    dev: false

  /character-entities-legacy/3.0.0:
    resolution: {integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==}
    dev: false

  /character-entities/2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}
    dev: false

  /character-reference-invalid/2.0.1:
    resolution: {integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==}
    dev: false

  /client-only/0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}
    dev: false

  /clipboardy/1.2.2:
    resolution: {integrity: sha512-16KrBOV7bHmHdxcQiCvfUFYVFyEah4FI8vYT1Fr7CGSA4G+xBWMEfUEQJS1hxeHGtI9ju1Bzs9uXSbj5HZKArw==}
    engines: {node: '>=4'}
    dependencies:
      arch: 2.2.0
      execa: 0.8.0
    dev: false

  /clsx/1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==}
    engines: {node: '>=6'}
    dev: false

  /color-convert/1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3
    dev: false

  /color-name/1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}
    dev: false

  /comma-separated-tokens/2.0.3:
    resolution: {integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==}
    dev: false

  /commander/8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}
    dev: false

  /compute-scroll-into-view/2.0.4:
    resolution: {integrity: sha512-y/ZA3BGnxoM/QHHQ2Uy49CLtnWPbt4tTPpEEZiEmmiWBFKjej7nEyH8Ryz54jH0MLXflUYA3Er2zUxPSJu5R+g==}
    dev: false

  /cross-spawn/5.1.0:
    resolution: {integrity: sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==}
    dependencies:
      lru-cache: 4.1.5
      shebang-command: 1.2.0
      which: 1.3.1
    dev: false

  /csstype/3.1.1:
    resolution: {integrity: sha512-DJR/VvkAvSZW9bTouZue2sSxDwdTN92uHjqeKVm+0dAqdfNykRzQ95tay8aXMBAAPpUiq4Qcug2L7neoRh2Egw==}
    dev: false

  /debug/4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2
    dev: false

  /decode-named-character-reference/1.0.2:
    resolution: {integrity: sha512-O8x12RzrUF8xyVcY0KJowWsmaJxQbmy0/EtnNtHRpsOcT7dFk5W598coHqBVpmWo1oQQfsCqfCmkZN5DJrZVdg==}
    dependencies:
      character-entities: 2.0.2
    dev: false

  /dequal/2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}
    dev: false

  /diff/5.1.0:
    resolution: {integrity: sha512-D+mk+qE8VC/PAUrlAU34N+VfXev0ghe5ywmpqrawphmVZc1bEfn56uo9qpyGp1p4xpzOHkSW4ztBd6L7Xx4ACw==}
    engines: {node: '>=0.3.1'}
    dev: false

  /escape-string-regexp/1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}
    dev: false

  /escape-string-regexp/5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}
    dev: false

  /esprima/4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /estree-util-attach-comments/2.1.0:
    resolution: {integrity: sha512-rJz6I4L0GaXYtHpoMScgDIwM0/Vwbu5shbMeER596rB2D1EWF6+Gj0e0UKzJPZrpoOc87+Q2kgVFHfjAymIqmw==}
    dependencies:
      '@types/estree': 1.0.0
    dev: false

  /estree-util-build-jsx/2.2.0:
    resolution: {integrity: sha512-apsfRxF9uLrqosApvHVtYZjISPvTJ+lBiIydpC+9wE6cF6ssbhnjyQLqaIjgzGxvC2Hbmec1M7g91PoBayYoQQ==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      estree-util-is-identifier-name: 2.0.1
      estree-walker: 3.0.1
    dev: false

  /estree-util-is-identifier-name/2.0.1:
    resolution: {integrity: sha512-rxZj1GkQhY4x1j/CSnybK9cGuMFQYFPLq0iNyopqf14aOVLFtMv7Esika+ObJWPWiOHuMOAHz3YkWoLYYRnzWQ==}
    dev: false

  /estree-util-to-js/1.1.0:
    resolution: {integrity: sha512-490lbfCcpLk+ofK6HCgqDfYs4KAfq6QVvDw3+Bm1YoKRgiOjKiKYGAVQE1uwh7zVxBgWhqp4FDtp5SqunpUk1A==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      astring: 1.8.3
      source-map: 0.7.4
    dev: false

  /estree-util-value-to-estree/1.3.0:
    resolution: {integrity: sha512-Y+ughcF9jSUJvncXwqRageavjrNPAI+1M/L3BI3PyLp1nmgYTGUXU6t5z1Y7OWuThoDdhPME07bQU+d5LxdJqw==}
    engines: {node: '>=12.0.0'}
    dependencies:
      is-plain-obj: 3.0.0
    dev: false

  /estree-util-visit/1.2.0:
    resolution: {integrity: sha512-wdsoqhWueuJKsh5hqLw3j8lwFqNStm92VcwtAOAny8g/KS/l5Y8RISjR4k5W6skCj3Nirag/WUCMS0Nfy3sgsg==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/unist': 2.0.6
    dev: false

  /estree-walker/3.0.1:
    resolution: {integrity: sha512-woY0RUD87WzMBUiZLx8NsYr23N5BKsOMZHhu2hoNRVh6NXGfoiT1KOL8G3UHlJAnEDGmfa5ubNA/AacfG+Kb0g==}
    dev: false

  /execa/0.8.0:
    resolution: {integrity: sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA==}
    engines: {node: '>=4'}
    dependencies:
      cross-spawn: 5.1.0
      get-stream: 3.0.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0
    dev: false

  /extend-shallow/2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
    dev: false

  /extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}
    dev: false

  /flexsearch/0.7.31:
    resolution: {integrity: sha512-XGozTsMPYkm+6b5QL3Z9wQcJjNYxp0CYn3U1gO7dwD6PAqU1SVWZxI9CCg3z+ml3YfqdPnrBehaBrnH2AGKbNA==}
    dev: false

  /focus-visible/5.2.0:
    resolution: {integrity: sha512-Rwix9pBtC1Nuy5wysTmKy+UjbDJpIfg8eHjw0rjZ1mX4GNLz1Bmd16uDpI3Gk1i70Fgcs8Csg2lPm8HULFg9DQ==}
    dev: false

  /get-stream/3.0.0:
    resolution: {integrity: sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ==}
    engines: {node: '>=4'}
    dev: false

  /git-up/7.0.0:
    resolution: {integrity: sha512-ONdIrbBCFusq1Oy0sC71F5azx8bVkvtZtMJAsv+a6lz5YAmbNnLD6HAB4gptHZVLPR8S2/kVN6Gab7lryq5+lQ==}
    dependencies:
      is-ssh: 1.4.0
      parse-url: 8.1.0
    dev: false

  /git-url-parse/13.1.0:
    resolution: {integrity: sha512-5FvPJP/70WkIprlUZ33bm4UAaFdjcLkJLpWft1BeZKqwR0uhhNGoKwlUaPtVb4LxCSQ++erHapRak9kWGj+FCA==}
    dependencies:
      git-up: 7.0.0
    dev: false

  /github-slugger/2.0.0:
    resolution: {integrity: sha512-IaOQ9puYtjrkq7Y0Ygl9KDZnrf/aiUJYUpVf89y8kyaxbRG7Y1SrX/jaumrv81vc61+kiMempujsM3Yw7w5qcw==}
    dev: false

  /graceful-fs/4.2.10:
    resolution: {integrity: sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==}
    dev: false

  /gray-matter/4.0.3:
    resolution: {integrity: sha512-5v6yZd4JK3eMI3FqqCouswVqwugaA9r4dNZB1wwcmrD02QkV5H0y7XBQW8QwQqEaZY1pM9aqORSORhJRdNK44Q==}
    engines: {node: '>=6.0'}
    dependencies:
      js-yaml: 3.14.1
      kind-of: 6.0.3
      section-matter: 1.0.0
      strip-bom-string: 1.0.0
    dev: false

  /has-flag/2.0.0:
    resolution: {integrity: sha512-P+1n3MnwjR/Epg9BBo1KT8qbye2g2Ou4sFumihwt6I4tsUX7jnLcX4BTOSKg/B1ZrIYMN9FcEnG4x5a7NB8Eng==}
    engines: {node: '>=0.10.0'}
    dev: false

  /hash-obj/4.0.0:
    resolution: {integrity: sha512-FwO1BUVWkyHasWDW4S8o0ssQXjvyghLV2rfVhnN36b2bbcj45eGiuzdn9XOvOpjV3TKQD7Gm2BWNXdE9V4KKYg==}
    engines: {node: '>=12'}
    dependencies:
      is-obj: 3.0.0
      sort-keys: 5.0.0
      type-fest: 1.4.0
    dev: false

  /hast-util-from-parse5/7.1.1:
    resolution: {integrity: sha512-R6PoNcUs89ZxLJmMWsVbwSWuz95/9OriyQZ3e2ybwqGsRXzhA6gv49rgGmQvLbZuSNDv9fCg7vV7gXUsvtUFaA==}
    dependencies:
      '@types/hast': 2.3.4
      '@types/unist': 2.0.6
      hastscript: 7.2.0
      property-information: 6.2.0
      vfile: 5.3.6
      vfile-location: 4.0.1
      web-namespaces: 2.0.1
    dev: false

  /hast-util-is-element/2.1.3:
    resolution: {integrity: sha512-O1bKah6mhgEq2WtVMk+Ta5K7pPMqsBBlmzysLdcwKVrqzZQ0CHqUPiIVspNhAG1rvxpvJjtGee17XfauZYKqVA==}
    dependencies:
      '@types/hast': 2.3.4
      '@types/unist': 2.0.6
    dev: false

  /hast-util-parse-selector/3.1.1:
    resolution: {integrity: sha512-jdlwBjEexy1oGz0aJ2f4GKMaVKkA9jwjr4MjAAI22E5fM/TXVZHuS5OpONtdeIkRKqAaryQ2E9xNQxijoThSZA==}
    dependencies:
      '@types/hast': 2.3.4
    dev: false

  /hast-util-to-estree/2.1.0:
    resolution: {integrity: sha512-Vwch1etMRmm89xGgz+voWXvVHba2iiMdGMKmaMfYt35rbVtFDq8JNwwAIvi8zHMkO6Gvqo9oTMwJTmzVRfXh4g==}
    dependencies:
      '@types/estree': 1.0.0
      '@types/estree-jsx': 1.0.0
      '@types/hast': 2.3.4
      '@types/unist': 2.0.6
      comma-separated-tokens: 2.0.3
      estree-util-attach-comments: 2.1.0
      estree-util-is-identifier-name: 2.0.1
      hast-util-whitespace: 2.0.0
      mdast-util-mdx-expression: 1.3.1
      mdast-util-mdxjs-esm: 1.3.0
      property-information: 6.2.0
      space-separated-tokens: 2.0.2
      style-to-object: 0.3.0
      unist-util-position: 4.0.3
      zwitch: 2.0.4
    transitivePeerDependencies:
      - supports-color
    dev: false

  /hast-util-to-text/3.1.2:
    resolution: {integrity: sha512-tcllLfp23dJJ+ju5wCCZHVpzsQQ43+moJbqVX3jNWPB7z/KFC4FyZD6R7y94cHL6MQ33YtMZL8Z0aIXXI4XFTw==}
    dependencies:
      '@types/hast': 2.3.4
      '@types/unist': 2.0.6
      hast-util-is-element: 2.1.3
      unist-util-find-after: 4.0.1
    dev: false

  /hast-util-whitespace/2.0.0:
    resolution: {integrity: sha512-Pkw+xBHuV6xFeJprJe2BBEoDV+AvQySaz3pPDRUs5PNZEMQjpXJJueqrpcHIXxnWTcAGi/UOCgVShlkY6kLoqg==}
    dev: false

  /hastscript/7.2.0:
    resolution: {integrity: sha512-TtYPq24IldU8iKoJQqvZOuhi5CyCQRAbvDOX0x1eW6rsHSxa/1i2CCiptNTotGHJ3VoHRGmqiv6/D3q113ikkw==}
    dependencies:
      '@types/hast': 2.3.4
      comma-separated-tokens: 2.0.3
      hast-util-parse-selector: 3.1.1
      property-information: 6.2.0
      space-separated-tokens: 2.0.2
    dev: false

  /inline-style-parser/0.1.1:
    resolution: {integrity: sha512-7NXolsK4CAS5+xvdj5OMMbI962hU/wvwoxk+LWR9Ek9bVtyuuYScDN6eS0rUm6TxApFpw7CX1o4uJzcd4AyD3Q==}
    dev: false

  /intersection-observer/0.12.2:
    resolution: {integrity: sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==}
    dev: false

  /is-alphabetical/2.0.1:
    resolution: {integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==}
    dev: false

  /is-alphanumerical/2.0.1:
    resolution: {integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==}
    dependencies:
      is-alphabetical: 2.0.1
      is-decimal: 2.0.1
    dev: false

  /is-buffer/2.0.5:
    resolution: {integrity: sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==}
    engines: {node: '>=4'}
    dev: false

  /is-decimal/2.0.1:
    resolution: {integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==}
    dev: false

  /is-extendable/0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-hexadecimal/2.0.1:
    resolution: {integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==}
    dev: false

  /is-obj/3.0.0:
    resolution: {integrity: sha512-IlsXEHOjtKhpN8r/tRFj2nDyTmHvcfNeu/nrRIcXE17ROeatXchkojffa1SpdqW4cr/Fj6QkEf/Gn4zf6KKvEQ==}
    engines: {node: '>=12'}
    dev: false

  /is-plain-obj/3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==}
    engines: {node: '>=10'}
    dev: false

  /is-plain-obj/4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}
    dev: false

  /is-reference/3.0.0:
    resolution: {integrity: sha512-Eo1W3wUoHWoCoVM4GVl/a+K0IgiqE5aIo4kJABFyMum1ZORlPkC+UC357sSQUL5w5QCE5kCC9upl75b7+7CY/Q==}
    dependencies:
      '@types/estree': 1.0.0
    dev: false

  /is-ssh/1.4.0:
    resolution: {integrity: sha512-x7+VxdxOdlV3CYpjvRLBv5Lo9OJerlYanjwFrPR9fuGPjCiNiCzFgAWpiLAohSbsnH4ZAys3SBh+hq5rJosxUQ==}
    dependencies:
      protocols: 2.0.1
    dev: false

  /is-stream/1.1.0:
    resolution: {integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}
    dev: false

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: false

  /js-yaml/3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: false

  /js-yaml/4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: false

  /jsonc-parser/3.2.0:
    resolution: {integrity: sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==}
    dev: false

  /katex/0.13.24:
    resolution: {integrity: sha512-jZxYuKCma3VS5UuxOx/rFV1QyGSl3Uy/i0kTJF3HgQ5xMinCQVF8Zd4bMY/9aI9b9A2pjIBOsjSSm68ykTAr8w==}
    hasBin: true
    dependencies:
      commander: 8.3.0
    dev: false

  /katex/0.15.6:
    resolution: {integrity: sha512-UpzJy4yrnqnhXvRPhjEuLA4lcPn6eRngixW7Q3TJErjg3Aw2PuLFBzTkdUb89UtumxjhHTqL3a5GDGETMSwgJA==}
    hasBin: true
    dependencies:
      commander: 8.3.0
    dev: false

  /katex/0.16.4:
    resolution: {integrity: sha512-WudRKUj8yyBeVDI4aYMNxhx5Vhh2PjpzQw1GRu/LVGqL4m1AxwD1GcUp0IMbdJaf5zsjtj8ghP0DOQRYhroNkw==}
    hasBin: true
    dependencies:
      commander: 8.3.0
    dev: false

  /kind-of/6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /kleur/4.1.5:
    resolution: {integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==}
    engines: {node: '>=6'}
    dev: false

  /lodash.get/4.4.2:
    resolution: {integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==}
    dev: false

  /longest-streak/3.1.0:
    resolution: {integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==}
    dev: false

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /lru-cache/4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==}
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2
    dev: false

  /markdown-extensions/1.1.1:
    resolution: {integrity: sha512-WWC0ZuMzCyDHYCasEGs4IPvLyTGftYwh6wIEOULOF0HXcqZlhwRzrK0w2VUlxWA98xnvb/jszw4ZSkJ6ADpM6Q==}
    engines: {node: '>=0.10.0'}
    dev: false

  /markdown-table/3.0.3:
    resolution: {integrity: sha512-Z1NL3Tb1M9wH4XESsCDEksWoKTdlUafKc4pt0GRwjUyXaCFZ+dc3g2erqB6zm3szA2IUSi7VnPI+o/9jnxh9hw==}
    dev: false

  /match-sorter/6.3.1:
    resolution: {integrity: sha512-mxybbo3pPNuA+ZuCUhm5bwNkXrJTbsk5VWbR5wiwz/GC6LIiegBGn2w3O08UG/jdbYLinw51fSQ5xNU1U3MgBw==}
    dependencies:
      '@babel/runtime': 7.20.6
      remove-accents: 0.4.2
    dev: false

  /mdast-util-definitions/5.1.1:
    resolution: {integrity: sha512-rQ+Gv7mHttxHOBx2dkF4HWTg+EE+UR78ptQWDylzPKaQuVGdG4HIoY3SrS/pCp80nZ04greFvXbVFHT+uf0JVQ==}
    dependencies:
      '@types/mdast': 3.0.10
      '@types/unist': 2.0.6
      unist-util-visit: 4.1.1
    dev: false

  /mdast-util-find-and-replace/2.2.1:
    resolution: {integrity: sha512-SobxkQXFAdd4b5WmEakmkVoh18icjQRxGy5OWTCzgsLRm1Fu/KCtwD1HIQSsmq5ZRjVH0Ehwg6/Fn3xIUk+nKw==}
    dependencies:
      escape-string-regexp: 5.0.0
      unist-util-is: 5.1.1
      unist-util-visit-parents: 5.1.1
    dev: false

  /mdast-util-from-markdown/1.2.0:
    resolution: {integrity: sha512-iZJyyvKD1+K7QX1b5jXdE7Sc5dtoTry1vzV28UZZe8Z1xVnB/czKntJ7ZAkG0tANqRnBF6p3p7GpU1y19DTf2Q==}
    dependencies:
      '@types/mdast': 3.0.10
      '@types/unist': 2.0.6
      decode-named-character-reference: 1.0.2
      mdast-util-to-string: 3.1.0
      micromark: 3.1.0
      micromark-util-decode-numeric-character-reference: 1.0.0
      micromark-util-decode-string: 1.0.2
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      unist-util-stringify-position: 3.0.2
      uvu: 0.5.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm-autolink-literal/1.0.2:
    resolution: {integrity: sha512-FzopkOd4xTTBeGXhXSBU0OCDDh5lUj2rd+HQqG92Ld+jL4lpUfgX2AT2OHAVP9aEeDKp7G92fuooSZcYJA3cRg==}
    dependencies:
      '@types/mdast': 3.0.10
      ccount: 2.0.1
      mdast-util-find-and-replace: 2.2.1
      micromark-util-character: 1.1.0
    dev: false

  /mdast-util-gfm-footnote/1.0.1:
    resolution: {integrity: sha512-p+PrYlkw9DeCRkTVw1duWqPRHX6Ywh2BNKJQcZbCwAuP/59B0Lk9kakuAd7KbQprVO4GzdW8eS5++A9PUSqIyw==}
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-to-markdown: 1.3.0
      micromark-util-normalize-identifier: 1.0.0
    dev: false

  /mdast-util-gfm-strikethrough/1.0.2:
    resolution: {integrity: sha512-T/4DVHXcujH6jx1yqpcAYYwd+z5lAYMw4Ls6yhTfbMMtCt0PHY4gEfhW9+lKsLBtyhUGKRIzcUA2FATVqnvPDA==}
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-to-markdown: 1.3.0
    dev: false

  /mdast-util-gfm-table/1.0.6:
    resolution: {integrity: sha512-uHR+fqFq3IvB3Rd4+kzXW8dmpxUhvgCQZep6KdjsLK4O6meK5dYZEayLtIxNus1XO3gfjfcIFe8a7L0HZRGgag==}
    dependencies:
      '@types/mdast': 3.0.10
      markdown-table: 3.0.3
      mdast-util-from-markdown: 1.2.0
      mdast-util-to-markdown: 1.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm-task-list-item/1.0.1:
    resolution: {integrity: sha512-KZ4KLmPdABXOsfnM6JHUIjxEvcx2ulk656Z/4Balw071/5qgnhz+H1uGtf2zIGnrnvDC8xR4Fj9uKbjAFGNIeA==}
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-to-markdown: 1.3.0
    dev: false

  /mdast-util-gfm/2.0.1:
    resolution: {integrity: sha512-42yHBbfWIFisaAfV1eixlabbsa6q7vHeSPY+cg+BBjX51M8xhgMacqH9g6TftB/9+YkcI0ooV4ncfrJslzm/RQ==}
    dependencies:
      mdast-util-from-markdown: 1.2.0
      mdast-util-gfm-autolink-literal: 1.0.2
      mdast-util-gfm-footnote: 1.0.1
      mdast-util-gfm-strikethrough: 1.0.2
      mdast-util-gfm-table: 1.0.6
      mdast-util-gfm-task-list-item: 1.0.1
      mdast-util-to-markdown: 1.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-math/2.0.2:
    resolution: {integrity: sha512-8gmkKVp9v6+Tgjtq6SYx9kGPpTf6FVYRa53/DLh479aldR9AyP48qeVOgNZ5X7QUK7nOy4yw7vg6mbiGcs9jWQ==}
    dependencies:
      '@types/mdast': 3.0.10
      longest-streak: 3.1.0
      mdast-util-to-markdown: 1.3.0
    dev: false

  /mdast-util-mdx-expression/1.3.1:
    resolution: {integrity: sha512-TTb6cKyTA1RD+1su1iStZ5PAv3rFfOUKcoU5EstUpv/IZo63uDX03R8+jXjMEhcobXnNOiG6/ccekvVl4eV1zQ==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/hast': 2.3.4
      '@types/mdast': 3.0.10
      mdast-util-from-markdown: 1.2.0
      mdast-util-to-markdown: 1.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-mdx-jsx/2.1.0:
    resolution: {integrity: sha512-KzgzfWMhdteDkrY4mQtyvTU5bc/W4ppxhe9SzelO6QUUiwLAM+Et2Dnjjprik74a336kHdo0zKm7Tp+n6FFeRg==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/hast': 2.3.4
      '@types/mdast': 3.0.10
      ccount: 2.0.1
      mdast-util-to-markdown: 1.3.0
      parse-entities: 4.0.0
      stringify-entities: 4.0.3
      unist-util-remove-position: 4.0.1
      unist-util-stringify-position: 3.0.2
      vfile-message: 3.1.3
    dev: false

  /mdast-util-mdx/2.0.0:
    resolution: {integrity: sha512-M09lW0CcBT1VrJUaF/PYxemxxHa7SLDHdSn94Q9FhxjCQfuW7nMAWKWimTmA3OyDMSTH981NN1csW1X+HPSluw==}
    dependencies:
      mdast-util-mdx-expression: 1.3.1
      mdast-util-mdx-jsx: 2.1.0
      mdast-util-mdxjs-esm: 1.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-mdxjs-esm/1.3.0:
    resolution: {integrity: sha512-7N5ihsOkAEGjFotIX9p/YPdl4TqUoMxL4ajNz7PbT89BqsdWJuBC9rvgt6wpbwTZqWWR0jKWqQbwsOWDBUZv4g==}
    dependencies:
      '@types/estree-jsx': 1.0.0
      '@types/hast': 2.3.4
      '@types/mdast': 3.0.10
      mdast-util-from-markdown: 1.2.0
      mdast-util-to-markdown: 1.3.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-to-hast/12.2.4:
    resolution: {integrity: sha512-a21xoxSef1l8VhHxS1Dnyioz6grrJkoaCUgGzMD/7dWHvboYX3VW53esRUfB5tgTyz4Yos1n25SPcj35dJqmAg==}
    dependencies:
      '@types/hast': 2.3.4
      '@types/mdast': 3.0.10
      mdast-util-definitions: 5.1.1
      micromark-util-sanitize-uri: 1.1.0
      trim-lines: 3.0.1
      unist-builder: 3.0.0
      unist-util-generated: 2.0.0
      unist-util-position: 4.0.3
      unist-util-visit: 4.1.1
    dev: false

  /mdast-util-to-markdown/1.3.0:
    resolution: {integrity: sha512-6tUSs4r+KK4JGTTiQ7FfHmVOaDrLQJPmpjD6wPMlHGUVXoG9Vjc3jIeP+uyBWRf8clwB2blM+W7+KrlMYQnftA==}
    dependencies:
      '@types/mdast': 3.0.10
      '@types/unist': 2.0.6
      longest-streak: 3.1.0
      mdast-util-to-string: 3.1.0
      micromark-util-decode-string: 1.0.2
      unist-util-visit: 4.1.1
      zwitch: 2.0.4
    dev: false

  /mdast-util-to-string/3.1.0:
    resolution: {integrity: sha512-n4Vypz/DZgwo0iMHLQL49dJzlp7YtAJP+N07MZHpjPf/5XJuHUWstviF4Mn2jEiR/GNmtnRRqnwsXExk3igfFA==}
    dev: false

  /micromark-core-commonmark/1.0.6:
    resolution: {integrity: sha512-K+PkJTxqjFfSNkfAhp4GB+cZPfQd6dxtTXnf+RjZOV7T4EEXnvgzOcnp+eSTmpGk9d1S9sL6/lqrgSNn/s0HZA==}
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-factory-destination: 1.0.0
      micromark-factory-label: 1.0.2
      micromark-factory-space: 1.0.0
      micromark-factory-title: 1.0.2
      micromark-factory-whitespace: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-chunked: 1.0.0
      micromark-util-classify-character: 1.0.0
      micromark-util-html-tag-name: 1.1.0
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-resolve-all: 1.0.0
      micromark-util-subtokenize: 1.0.2
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-autolink-literal/1.0.3:
    resolution: {integrity: sha512-i3dmvU0htawfWED8aHMMAzAVp/F0Z+0bPh3YrbTPPL1v4YAlCZpy5rBO5p0LPYiZo0zFVkoYh7vDU7yQSiCMjg==}
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-sanitize-uri: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-footnote/1.0.4:
    resolution: {integrity: sha512-E/fmPmDqLiMUP8mLJ8NbJWJ4bTw6tS+FEQS8CcuDtZpILuOb2kjLqPEeAePF1djXROHXChM/wPJw0iS4kHCcIg==}
    dependencies:
      micromark-core-commonmark: 1.0.6
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-sanitize-uri: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-strikethrough/1.0.4:
    resolution: {integrity: sha512-/vjHU/lalmjZCT5xt7CcHVJGq8sYRm80z24qAKXzaHzem/xsDYb2yLL+NNVbYvmpLx3O7SYPuGL5pzusL9CLIQ==}
    dependencies:
      micromark-util-chunked: 1.0.0
      micromark-util-classify-character: 1.0.0
      micromark-util-resolve-all: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-table/1.0.5:
    resolution: {integrity: sha512-xAZ8J1X9W9K3JTJTUL7G6wSKhp2ZYHrFk5qJgY/4B33scJzE2kpfRL6oiw/veJTbt7jiM/1rngLlOKPWr1G+vg==}
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm-tagfilter/1.0.1:
    resolution: {integrity: sha512-Ty6psLAcAjboRa/UKUbbUcwjVAv5plxmpUTy2XC/3nJFL37eHej8jrHrRzkqcpipJliuBH30DTs7+3wqNcQUVA==}
    dependencies:
      micromark-util-types: 1.0.2
    dev: false

  /micromark-extension-gfm-task-list-item/1.0.3:
    resolution: {integrity: sha512-PpysK2S1Q/5VXi72IIapbi/jliaiOFzv7THH4amwXeYXLq3l1uo8/2Be0Ac1rEwK20MQEsGH2ltAZLNY2KI/0Q==}
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-gfm/2.0.1:
    resolution: {integrity: sha512-p2sGjajLa0iYiGQdT0oelahRYtMWvLjy8J9LOCxzIQsllMCGLbsLW+Nc+N4vi02jcRJvedVJ68cjelKIO6bpDA==}
    dependencies:
      micromark-extension-gfm-autolink-literal: 1.0.3
      micromark-extension-gfm-footnote: 1.0.4
      micromark-extension-gfm-strikethrough: 1.0.4
      micromark-extension-gfm-table: 1.0.5
      micromark-extension-gfm-tagfilter: 1.0.1
      micromark-extension-gfm-task-list-item: 1.0.3
      micromark-util-combine-extensions: 1.0.0
      micromark-util-types: 1.0.2
    dev: false

  /micromark-extension-math/2.0.2:
    resolution: {integrity: sha512-cFv2B/E4pFPBBFuGgLHkkNiFAIQv08iDgPH2HCuR2z3AUgMLecES5Cq7AVtwOtZeRrbA80QgMUk8VVW0Z+D2FA==}
    dependencies:
      '@types/katex': 0.11.1
      katex: 0.13.24
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-mdx-expression/1.0.3:
    resolution: {integrity: sha512-TjYtjEMszWze51NJCZmhv7MEBcgYRgb3tJeMAJ+HQCAaZHHRBaDCccqQzGizR/H4ODefP44wRTgOn2vE5I6nZA==}
    dependencies:
      micromark-factory-mdx-expression: 1.0.6
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-events-to-acorn: 1.2.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-extension-mdx-jsx/1.0.3:
    resolution: {integrity: sha512-VfA369RdqUISF0qGgv2FfV7gGjHDfn9+Qfiv5hEwpyr1xscRj/CiVRkU7rywGFCO7JwJ5L0e7CJz60lY52+qOA==}
    dependencies:
      '@types/acorn': 4.0.6
      estree-util-is-identifier-name: 2.0.1
      micromark-factory-mdx-expression: 1.0.6
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
      vfile-message: 3.1.3
    dev: false

  /micromark-extension-mdx-md/1.0.0:
    resolution: {integrity: sha512-xaRAMoSkKdqZXDAoSgp20Azm0aRQKGOl0RrS81yGu8Hr/JhMsBmfs4wR7m9kgVUIO36cMUQjNyiyDKPrsv8gOw==}
    dependencies:
      micromark-util-types: 1.0.2
    dev: false

  /micromark-extension-mdxjs-esm/1.0.3:
    resolution: {integrity: sha512-2N13ol4KMoxb85rdDwTAC6uzs8lMX0zeqpcyx7FhS7PxXomOnLactu8WI8iBNXW8AVyea3KIJd/1CKnUmwrK9A==}
    dependencies:
      micromark-core-commonmark: 1.0.6
      micromark-util-character: 1.1.0
      micromark-util-events-to-acorn: 1.2.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      unist-util-position-from-estree: 1.1.1
      uvu: 0.5.6
      vfile-message: 3.1.3
    dev: false

  /micromark-extension-mdxjs/1.0.0:
    resolution: {integrity: sha512-TZZRZgeHvtgm+IhtgC2+uDMR7h8eTKF0QUX9YsgoL9+bADBpBY6SiLvWqnBlLbCEevITmTqmEuY3FoxMKVs1rQ==}
    dependencies:
      acorn: 8.8.1
      acorn-jsx: 5.3.2_acorn@8.8.1
      micromark-extension-mdx-expression: 1.0.3
      micromark-extension-mdx-jsx: 1.0.3
      micromark-extension-mdx-md: 1.0.0
      micromark-extension-mdxjs-esm: 1.0.3
      micromark-util-combine-extensions: 1.0.0
      micromark-util-types: 1.0.2
    dev: false

  /micromark-factory-destination/1.0.0:
    resolution: {integrity: sha512-eUBA7Rs1/xtTVun9TmV3gjfPz2wEwgK5R5xcbIM5ZYAtvGF6JkyaDsj0agx8urXnO31tEO6Ug83iVH3tdedLnw==}
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
    dev: false

  /micromark-factory-label/1.0.2:
    resolution: {integrity: sha512-CTIwxlOnU7dEshXDQ+dsr2n+yxpP0+fn271pu0bwDIS8uqfFcumXpj5mLn3hSC8iw2MUr6Gx8EcKng1dD7i6hg==}
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-factory-mdx-expression/1.0.6:
    resolution: {integrity: sha512-WRQIc78FV7KrCfjsEf/sETopbYjElh3xAmNpLkd1ODPqxEngP42eVRGbiPEQWpRV27LzqW+XVTvQAMIIRLPnNA==}
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-events-to-acorn: 1.2.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      unist-util-position-from-estree: 1.1.1
      uvu: 0.5.6
      vfile-message: 3.1.3
    dev: false

  /micromark-factory-space/1.0.0:
    resolution: {integrity: sha512-qUmqs4kj9a5yBnk3JMLyjtWYN6Mzfcx8uJfi5XAveBniDevmZasdGBba5b4QsvRcAkmvGo5ACmSUmyGiKTLZew==}
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-types: 1.0.2
    dev: false

  /micromark-factory-title/1.0.2:
    resolution: {integrity: sha512-zily+Nr4yFqgMGRKLpTVsNl5L4PMu485fGFDOQJQBl2NFpjGte1e86zC0da93wf97jrc4+2G2GQudFMHn3IX+A==}
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-factory-whitespace/1.0.0:
    resolution: {integrity: sha512-Qx7uEyahU1lt1RnsECBiuEbfr9INjQTGa6Err+gF3g0Tx4YEviPbqqGKNv/NrBaE7dVHdn1bVZKM/n5I/Bak7A==}
    dependencies:
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
    dev: false

  /micromark-util-character/1.1.0:
    resolution: {integrity: sha512-agJ5B3unGNJ9rJvADMJ5ZiYjBRyDpzKAOk01Kpi1TKhlT1APx3XZk6eN7RtSz1erbWHC2L8T3xLZ81wdtGRZzg==}
    dependencies:
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
    dev: false

  /micromark-util-chunked/1.0.0:
    resolution: {integrity: sha512-5e8xTis5tEZKgesfbQMKRCyzvffRRUX+lK/y+DvsMFdabAicPkkZV6gO+FEWi9RfuKKoxxPwNL+dFF0SMImc1g==}
    dependencies:
      micromark-util-symbol: 1.0.1
    dev: false

  /micromark-util-classify-character/1.0.0:
    resolution: {integrity: sha512-F8oW2KKrQRb3vS5ud5HIqBVkCqQi224Nm55o5wYLzY/9PwHGXC01tr3d7+TqHHz6zrKQ72Okwtvm/xQm6OVNZA==}
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
    dev: false

  /micromark-util-combine-extensions/1.0.0:
    resolution: {integrity: sha512-J8H058vFBdo/6+AsjHp2NF7AJ02SZtWaVUjsayNFeAiydTxUwViQPxN0Hf8dp4FmCQi0UUFovFsEyRSUmFH3MA==}
    dependencies:
      micromark-util-chunked: 1.0.0
      micromark-util-types: 1.0.2
    dev: false

  /micromark-util-decode-numeric-character-reference/1.0.0:
    resolution: {integrity: sha512-OzO9AI5VUtrTD7KSdagf4MWgHMtET17Ua1fIpXTpuhclCqD8egFWo85GxSGvxgkGS74bEahvtM0WP0HjvV0e4w==}
    dependencies:
      micromark-util-symbol: 1.0.1
    dev: false

  /micromark-util-decode-string/1.0.2:
    resolution: {integrity: sha512-DLT5Ho02qr6QWVNYbRZ3RYOSSWWFuH3tJexd3dgN1odEuPNxCngTCXJum7+ViRAd9BbdxCvMToPOD/IvVhzG6Q==}
    dependencies:
      decode-named-character-reference: 1.0.2
      micromark-util-character: 1.1.0
      micromark-util-decode-numeric-character-reference: 1.0.0
      micromark-util-symbol: 1.0.1
    dev: false

  /micromark-util-encode/1.0.1:
    resolution: {integrity: sha512-U2s5YdnAYexjKDel31SVMPbfi+eF8y1U4pfiRW/Y8EFVCy/vgxk/2wWTxzcqE71LHtCuCzlBDRU2a5CQ5j+mQA==}
    dev: false

  /micromark-util-events-to-acorn/1.2.0:
    resolution: {integrity: sha512-WWp3bf7xT9MppNuw3yPjpnOxa8cj5ACivEzXJKu0WwnjBYfzaBvIAT9KfeyI0Qkll+bfQtfftSwdgTH6QhTOKw==}
    dependencies:
      '@types/acorn': 4.0.6
      '@types/estree': 1.0.0
      estree-util-visit: 1.2.0
      micromark-util-types: 1.0.2
      uvu: 0.5.6
      vfile-location: 4.0.1
      vfile-message: 3.1.3
    dev: false

  /micromark-util-html-tag-name/1.1.0:
    resolution: {integrity: sha512-BKlClMmYROy9UiV03SwNmckkjn8QHVaWkqoAqzivabvdGcwNGMMMH/5szAnywmsTBUzDsU57/mFi0sp4BQO6dA==}
    dev: false

  /micromark-util-normalize-identifier/1.0.0:
    resolution: {integrity: sha512-yg+zrL14bBTFrQ7n35CmByWUTFsgst5JhA4gJYoty4Dqzj4Z4Fr/DHekSS5aLfH9bdlfnSvKAWsAgJhIbogyBg==}
    dependencies:
      micromark-util-symbol: 1.0.1
    dev: false

  /micromark-util-resolve-all/1.0.0:
    resolution: {integrity: sha512-CB/AGk98u50k42kvgaMM94wzBqozSzDDaonKU7P7jwQIuH2RU0TeBqGYJz2WY1UdihhjweivStrJ2JdkdEmcfw==}
    dependencies:
      micromark-util-types: 1.0.2
    dev: false

  /micromark-util-sanitize-uri/1.1.0:
    resolution: {integrity: sha512-RoxtuSCX6sUNtxhbmsEFQfWzs8VN7cTctmBPvYivo98xb/kDEoTCtJQX5wyzIYEmk/lvNFTat4hL8oW0KndFpg==}
    dependencies:
      micromark-util-character: 1.1.0
      micromark-util-encode: 1.0.1
      micromark-util-symbol: 1.0.1
    dev: false

  /micromark-util-subtokenize/1.0.2:
    resolution: {integrity: sha512-d90uqCnXp/cy4G881Ub4psE57Sf8YD0pim9QdjCRNjfas2M1u6Lbt+XZK9gnHL2XFhnozZiEdCa9CNfXSfQ6xA==}
    dependencies:
      micromark-util-chunked: 1.0.0
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    dev: false

  /micromark-util-symbol/1.0.1:
    resolution: {integrity: sha512-oKDEMK2u5qqAptasDAwWDXq0tG9AssVwAx3E9bBF3t/shRIGsWIRG+cGafs2p/SnDSOecnt6hZPCE2o6lHfFmQ==}
    dev: false

  /micromark-util-types/1.0.2:
    resolution: {integrity: sha512-DCfg/T8fcrhrRKTPjRrw/5LLvdGV7BHySf/1LOZx7TzWZdYRjogNtyNq885z3nNallwr3QUKARjqvHqX1/7t+w==}
    dev: false

  /micromark/3.1.0:
    resolution: {integrity: sha512-6Mj0yHLdUZjHnOPgr5xfWIMqMWS12zDN6iws9SLuSz76W8jTtAv24MN4/CL7gJrl5vtxGInkkqDv/JIoRsQOvA==}
    dependencies:
      '@types/debug': 4.1.7
      debug: 4.3.4
      decode-named-character-reference: 1.0.2
      micromark-core-commonmark: 1.0.6
      micromark-factory-space: 1.0.0
      micromark-util-character: 1.1.0
      micromark-util-chunked: 1.0.0
      micromark-util-combine-extensions: 1.0.0
      micromark-util-decode-numeric-character-reference: 1.0.0
      micromark-util-encode: 1.0.1
      micromark-util-normalize-identifier: 1.0.0
      micromark-util-resolve-all: 1.0.0
      micromark-util-sanitize-uri: 1.1.0
      micromark-util-subtokenize: 1.0.2
      micromark-util-symbol: 1.0.1
      micromark-util-types: 1.0.2
      uvu: 0.5.6
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mri/1.2.0:
    resolution: {integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==}
    engines: {node: '>=4'}
    dev: false

  /ms/2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}
    dev: false

  /nanoid/3.3.4:
    resolution: {integrity: sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: false

  /nanoid/4.0.1:
    resolution: {integrity: sha512-udKGtCCUafD3nQtJg9wBhRP3KMbPglUsgV5JVsXhvyBs/oefqb4sqMEhKBBgqZncYowu58p1prsZQBYvAj/Gww==}
    engines: {node: ^14 || ^16 || >=18}
    hasBin: true
    dev: false

  /next-mdx-remote/4.3.0_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-fbxkY03pM2Wx5bDNTVKpYD5Hx3QVZGH+6xDtVIxlxXz4HTifP1yI2DrkDvxXbTz0SYGIbluRMIW81IOOa8pigA==}
    engines: {node: '>=14', npm: '>=7'}
    peerDependencies:
      react: '>=16.x <=18.x'
      react-dom: '>=16.x <=18.x'
    dependencies:
      '@mdx-js/mdx': 2.2.1
      '@mdx-js/react': 2.2.1_react@18.2.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      vfile: 5.3.6
      vfile-matter: 3.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /next-seo/5.14.1_f26ff3bd08f1cd28b0f73422c76f5ffd:
    resolution: {integrity: sha512-NiJeQbxYP3z+EMp52q8k3Q+OfX2+Yv2WehERDj98r2wjXxL+woKpRBdsSVYolTD0Hm8IWs42SzaISE93RoQdOw==}
    peerDependencies:
      next: ^8.1.1-canary.54 || >=9.0.0
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      next: 13.0.6_react-dom@18.2.0+react@18.2.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /next-themes/0.2.1_f26ff3bd08f1cd28b0f73422c76f5ffd:
    resolution: {integrity: sha512-B+AKNfYNIzh0vqQQKqQItTS8evEouKD7H5Hj3kmuPERwddR2TxvDSFZuTj6T7Jfn1oyeUyJMydPl1Bkxkh0W7A==}
    peerDependencies:
      next: '*'
      react: '*'
      react-dom: '*'
    dependencies:
      next: 13.0.6_react-dom@18.2.0+react@18.2.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /next/13.0.6_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-COvigvms2LRt1rrzfBQcMQ2GZd86Mvk1z+LOLY5pniFtL4VrTmhZ9salrbKfSiXbhsD01TrDdD68ec3ABDyscA==}
    engines: {node: '>=14.6.0'}
    hasBin: true
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^6.0.0 || ^7.0.0
      react: ^18.2.0
      react-dom: ^18.2.0
      sass: ^1.3.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
    dependencies:
      '@next/env': 13.0.6
      '@swc/helpers': 0.4.14
      caniuse-lite: 1.0.30001435
      postcss: 8.4.14
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      styled-jsx: 5.1.0_react@18.2.0
    optionalDependencies:
      '@next/swc-android-arm-eabi': 13.0.6
      '@next/swc-android-arm64': 13.0.6
      '@next/swc-darwin-arm64': 13.0.6
      '@next/swc-darwin-x64': 13.0.6
      '@next/swc-freebsd-x64': 13.0.6
      '@next/swc-linux-arm-gnueabihf': 13.0.6
      '@next/swc-linux-arm64-gnu': 13.0.6
      '@next/swc-linux-arm64-musl': 13.0.6
      '@next/swc-linux-x64-gnu': 13.0.6
      '@next/swc-linux-x64-musl': 13.0.6
      '@next/swc-win32-arm64-msvc': 13.0.6
      '@next/swc-win32-ia32-msvc': 13.0.6
      '@next/swc-win32-x64-msvc': 13.0.6
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros
    dev: false

  /nextra-theme-docs/2.2.14_d8d66b9d2170cddb63c39dddec8541b9:
    resolution: {integrity: sha512-QQcHOcAXSfrpbSX3FqXgcQ2favKLnBAczqKWbSDVEtgHiUG6s7pVpxclpKm5F1c/fP47v19USRq3BL/SZ4JEIQ==}
    peerDependencies:
      next: '>=9.5.3'
      nextra: 2.2.14
      react: '>=16.13.1'
      react-dom: '>=16.13.1'
    dependencies:
      '@headlessui/react': 1.7.10_react-dom@18.2.0+react@18.2.0
      '@popperjs/core': 2.11.6
      clsx: 1.2.1
      flexsearch: 0.7.31
      focus-visible: 5.2.0
      git-url-parse: 13.1.0
      intersection-observer: 0.12.2
      match-sorter: 6.3.1
      next: 13.0.6_react-dom@18.2.0+react@18.2.0
      next-seo: 5.14.1_f26ff3bd08f1cd28b0f73422c76f5ffd
      next-themes: 0.2.1_f26ff3bd08f1cd28b0f73422c76f5ffd
      nextra: 2.2.14_f26ff3bd08f1cd28b0f73422c76f5ffd
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      scroll-into-view-if-needed: 3.0.4
      zod: 3.20.2
    dev: false

  /nextra/2.2.14_f26ff3bd08f1cd28b0f73422c76f5ffd:
    resolution: {integrity: sha512-kToTiTiE4qrQsQ9snFRqCGLLSjKSFgFV/BJm3yp/SRmkmCr1WaWrlmUTAuXlxM7PREbNaZouNSOJ0hGS92rM8A==}
    peerDependencies:
      next: '>=9.5.3'
      react: '>=16.13.1'
      react-dom: '>=16.13.1'
    dependencies:
      '@mdx-js/mdx': 2.2.1
      '@mdx-js/react': 2.2.1_react@18.2.0
      '@napi-rs/simple-git': 0.1.8
      github-slugger: 2.0.0
      graceful-fs: 4.2.10
      gray-matter: 4.0.3
      katex: 0.16.4
      lodash.get: 4.4.2
      next: 13.0.6_react-dom@18.2.0+react@18.2.0
      next-mdx-remote: 4.3.0_react-dom@18.2.0+react@18.2.0
      p-limit: 3.1.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      rehype-katex: 6.0.2
      rehype-pretty-code: 0.9.2_shiki@0.12.1
      remark-gfm: 3.0.1
      remark-math: 5.1.1
      remark-reading-time: 2.0.1
      shiki: 0.12.1
      slash: 3.0.0
      title: 3.5.3
      unist-util-remove: 3.1.1
      unist-util-visit: 4.1.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /npm-run-path/2.0.2:
    resolution: {integrity: sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==}
    engines: {node: '>=4'}
    dependencies:
      path-key: 2.0.1
    dev: false

  /p-finally/1.0.0:
    resolution: {integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==}
    engines: {node: '>=4'}
    dev: false

  /p-limit/3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: false

  /parse-entities/4.0.0:
    resolution: {integrity: sha512-5nk9Fn03x3rEhGaX1FU6IDwG/k+GxLXlFAkgrbM1asuAFl3BhdQWvASaIsmwWypRNcZKHPYnIuOSfIWEyEQnPQ==}
    dependencies:
      '@types/unist': 2.0.6
      character-entities: 2.0.2
      character-entities-legacy: 3.0.0
      character-reference-invalid: 2.0.1
      decode-named-character-reference: 1.0.2
      is-alphanumerical: 2.0.1
      is-decimal: 2.0.1
      is-hexadecimal: 2.0.1
    dev: false

  /parse-numeric-range/1.3.0:
    resolution: {integrity: sha512-twN+njEipszzlMJd4ONUYgSfZPDxgHhT9Ahed5uTigpQn90FggW4SA/AIPq/6a149fTbE9qBEcSwE3FAEp6wQQ==}
    dev: false

  /parse-path/7.0.0:
    resolution: {integrity: sha512-Euf9GG8WT9CdqwuWJGdf3RkUcTBArppHABkO7Lm8IzRQp0e2r/kkFnmhu4TSK30Wcu5rVAZLmfPKSBBi9tWFog==}
    dependencies:
      protocols: 2.0.1
    dev: false

  /parse-url/8.1.0:
    resolution: {integrity: sha512-xDvOoLU5XRrcOZvnI6b8zA6n9O9ejNk/GExuz1yBuWUGn9KA97GI6HTs6u02wKara1CeVmZhH+0TZFdWScR89w==}
    dependencies:
      parse-path: 7.0.0
    dev: false

  /parse5/6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}
    dev: false

  /path-key/2.0.1:
    resolution: {integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==}
    engines: {node: '>=4'}
    dev: false

  /periscopic/3.0.4:
    resolution: {integrity: sha512-SFx68DxCv0Iyo6APZuw/AKewkkThGwssmU0QWtTlvov3VAtPX+QJ4CadwSaz8nrT5jPIuxdvJWB4PnD2KNDxQg==}
    dependencies:
      estree-walker: 3.0.1
      is-reference: 3.0.0
    dev: false

  /picocolors/1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}
    dev: false

  /postcss/8.4.14:
    resolution: {integrity: sha512-E398TUmfAYFPBSdzgeieK2Y1+1cpdxJx8yXbK/m57nRhKSmk1GB2tO4lbLBtlkfPQTDKfe4Xqv1ASWPpayPEig==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.4
      picocolors: 1.0.0
      source-map-js: 1.0.2
    dev: false

  /property-information/6.2.0:
    resolution: {integrity: sha512-kma4U7AFCTwpqq5twzC1YVIDXSqg6qQK6JN0smOw8fgRy1OkMi0CYSzFmsy6dnqSenamAtj0CyXMUJ1Mf6oROg==}
    dev: false

  /protocols/2.0.1:
    resolution: {integrity: sha512-/XJ368cyBJ7fzLMwLKv1e4vLxOju2MNAIokcr7meSaNcVbWz/CPcW22cP04mwxOErdA5mwjA8Q6w/cdAQxVn7Q==}
    dev: false

  /pseudomap/1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==}
    dev: false

  /react-dom/18.2.0_react@18.2.0:
    resolution: {integrity: sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==}
    peerDependencies:
      react: ^18.2.0
    dependencies:
      loose-envify: 1.4.0
      react: 18.2.0
      scheduler: 0.23.0
    dev: false

  /react/18.2.0:
    resolution: {integrity: sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /reading-time/1.5.0:
    resolution: {integrity: sha512-onYyVhBNr4CmAxFsKS7bz+uTLRakypIe4R+5A824vBSkQy/hB3fZepoVEf8OVAxzLvK+H/jm9TzpI3ETSm64Kg==}
    dev: false

  /regenerator-runtime/0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}
    dev: false

  /rehype-katex/6.0.2:
    resolution: {integrity: sha512-C4gDAlS1+l0hJqctyiU64f9CvT00S03qV1T6HiMzbSuLBgWUtcqydWHY9OpKrm0SpkK16FNd62CDKyWLwV2ppg==}
    dependencies:
      '@types/hast': 2.3.4
      '@types/katex': 0.11.1
      hast-util-to-text: 3.1.2
      katex: 0.15.6
      rehype-parse: 8.0.4
      unified: 10.1.2
      unist-util-remove-position: 4.0.1
      unist-util-visit: 4.1.1
    dev: false

  /rehype-parse/8.0.4:
    resolution: {integrity: sha512-MJJKONunHjoTh4kc3dsM1v3C9kGrrxvA3U8PxZlP2SjH8RNUSrb+lF7Y0KVaUDnGH2QZ5vAn7ulkiajM9ifuqg==}
    dependencies:
      '@types/hast': 2.3.4
      hast-util-from-parse5: 7.1.1
      parse5: 6.0.1
      unified: 10.1.2
    dev: false

  /rehype-pretty-code/0.9.2_shiki@0.12.1:
    resolution: {integrity: sha512-l369pvBK6ihBEuy2+VDpHU+zbbY8I+Z4LiyIOunHAt3xyw6selaOFKc/DnX94jI5OJb3+NgjbOxXx2yaAypjZw==}
    engines: {node: ^12.16.0 || >=13.2.0}
    peerDependencies:
      shiki: '*'
    dependencies:
      hash-obj: 4.0.0
      nanoid: 4.0.1
      parse-numeric-range: 1.3.0
      shiki: 0.12.1
    dev: false

  /remark-gfm/3.0.1:
    resolution: {integrity: sha512-lEFDoi2PICJyNrACFOfDD3JlLkuSbOa5Wd8EPt06HUdptv8Gn0bxYTdbU/XXQ3swAPkEaGxxPN9cbnMHvVu1Ig==}
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-gfm: 2.0.1
      micromark-extension-gfm: 2.0.1
      unified: 10.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /remark-math/5.1.1:
    resolution: {integrity: sha512-cE5T2R/xLVtfFI4cCePtiRn+e6jKMtFDR3P8V3qpv8wpKjwvHoBA4eJzvX+nVrnlNy0911bdGmuspCSwetfYHw==}
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-math: 2.0.2
      micromark-extension-math: 2.0.2
      unified: 10.1.2
    dev: false

  /remark-mdx/2.1.5:
    resolution: {integrity: sha512-A8vw5s+BgOa968Irt8BO7DfWJTE0Fe7Ge3hX8zzDB1DnwMZTNdK6qF2IcFao+/7nzk1vSysKcFp+3ku4vhMpaQ==}
    dependencies:
      mdast-util-mdx: 2.0.0
      micromark-extension-mdxjs: 1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /remark-parse/10.0.1:
    resolution: {integrity: sha512-1fUyHr2jLsVOkhbvPRBJ5zTKZZyD6yZzYaWCS6BPBdQ8vEMBCH+9zNCDA6tET/zHCi/jLqjCWtlJZUPk+DbnFw==}
    dependencies:
      '@types/mdast': 3.0.10
      mdast-util-from-markdown: 1.2.0
      unified: 10.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /remark-reading-time/2.0.1:
    resolution: {integrity: sha512-fy4BKy9SRhtYbEHvp6AItbRTnrhiDGbqLQTSYVbQPGuRCncU1ubSsh9p/W5QZSxtYcUXv8KGL0xBgPLyNJA1xw==}
    dependencies:
      estree-util-is-identifier-name: 2.0.1
      estree-util-value-to-estree: 1.3.0
      reading-time: 1.5.0
      unist-util-visit: 3.1.0
    dev: false

  /remark-rehype/10.1.0:
    resolution: {integrity: sha512-EFmR5zppdBp0WQeDVZ/b66CWJipB2q2VLNFMabzDSGR66Z2fQii83G5gTBbgGEnEEA0QRussvrFHxk1HWGJskw==}
    dependencies:
      '@types/hast': 2.3.4
      '@types/mdast': 3.0.10
      mdast-util-to-hast: 12.2.4
      unified: 10.1.2
    dev: false

  /remove-accents/0.4.2:
    resolution: {integrity: sha1-CkPTqq4egNuRngeuJUsoXZ4ce7U=}
    dev: false

  /sade/1.8.1:
    resolution: {integrity: sha512-xal3CZX1Xlo/k4ApwCFrHVACi9fBqJ7V+mwhBsuf/1IOKbBy098Fex+Wa/5QMubw09pSZ/u8EY8PWgevJsXp1A==}
    engines: {node: '>=6'}
    dependencies:
      mri: 1.2.0
    dev: false

  /scheduler/0.23.0:
    resolution: {integrity: sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /scroll-into-view-if-needed/3.0.4:
    resolution: {integrity: sha512-s+/F50jwTOUt+u5oEIAzum9MN2lUQNvWBe/zfEsVQcbaERjGkKLq1s+2wCHkahMLC8nMLbzMVKivx9JhunXaZg==}
    dependencies:
      compute-scroll-into-view: 2.0.4
    dev: false

  /section-matter/1.0.0:
    resolution: {integrity: sha512-vfD3pmTzGpufjScBh50YHKzEu2lxBWhVEHsNGoEXmCmn2hKGfeNLYMzCJpe8cD7gqX7TJluOVpBkAequ6dgMmA==}
    engines: {node: '>=4'}
    dependencies:
      extend-shallow: 2.0.1
      kind-of: 6.0.3
    dev: false

  /shebang-command/1.2.0:
    resolution: {integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: 1.0.0
    dev: false

  /shebang-regex/1.0.0:
    resolution: {integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /shiki/0.12.1:
    resolution: {integrity: sha512-aieaV1m349rZINEBkjxh2QbBvFFQOlgqYTNtCal82hHj4dDZ76oMlQIX+C7ryerBTDiga3e5NfH6smjdJ02BbQ==}
    dependencies:
      jsonc-parser: 3.2.0
      vscode-oniguruma: 1.7.0
      vscode-textmate: 8.0.0
    dev: false

  /signal-exit/3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: false

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: false

  /sort-keys/5.0.0:
    resolution: {integrity: sha512-Pdz01AvCAottHTPQGzndktFNdbRA75BgOfeT1hH+AMnJFv8lynkPi42rfeEhpx1saTEI3YNMWxfqu0sFD1G8pw==}
    engines: {node: '>=12'}
    dependencies:
      is-plain-obj: 4.1.0
    dev: false

  /source-map-js/1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map/0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}
    dev: false

  /space-separated-tokens/2.0.2:
    resolution: {integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==}
    dev: false

  /sprintf-js/1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}
    dev: false

  /stringify-entities/4.0.3:
    resolution: {integrity: sha512-BP9nNHMhhfcMbiuQKCqMjhDP5yBCAxsPu4pHFFzJ6Alo9dZgY4VLDPutXqIjpRiMoKdp7Av85Gr73Q5uH9k7+g==}
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0
    dev: false

  /strip-bom-string/1.0.0:
    resolution: {integrity: sha512-uCC2VHvQRYu+lMh4My/sFNmF2klFymLX1wHJeXnbEJERpV/ZsVuonzerjfrGpIGF7LBVa1O7i9kjiWvJiFck8g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /strip-eof/1.0.0:
    resolution: {integrity: sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==}
    engines: {node: '>=0.10.0'}
    dev: false

  /style-to-object/0.3.0:
    resolution: {integrity: sha512-CzFnRRXhzWIdItT3OmF8SQfWyahHhjq3HwcMNCNLn+N7klOOqPjMeG/4JSu77D7ypZdGvSzvkrbyeTMizz2VrA==}
    dependencies:
      inline-style-parser: 0.1.1
    dev: false

  /styled-jsx/5.1.0_react@18.2.0:
    resolution: {integrity: sha512-/iHaRJt9U7T+5tp6TRelLnqBqiaIT0HsO0+vgyj8hK2KUk7aejFqRrumqPUlAqDwAj8IbS/1hk3IhBAAK/FCUQ==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true
    dependencies:
      client-only: 0.0.1
      react: 18.2.0
    dev: false

  /supports-color/4.5.0:
    resolution: {integrity: sha512-ycQR/UbvI9xIlEdQT1TQqwoXtEldExbCEAJgRo5YXlmSKjv6ThHnP9/vwGa1gr19Gfw+LkFd7KqYMhzrRC5JYw==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 2.0.0
    dev: false

  /title/3.5.3:
    resolution: {integrity: sha512-20JyowYglSEeCvZv3EZ0nZ046vLarO37prvV0mbtQV7C8DJPGgN967r8SJkqd3XK3K3lD3/Iyfp3avjfil8Q2Q==}
    hasBin: true
    dependencies:
      arg: 1.0.0
      chalk: 2.3.0
      clipboardy: 1.2.2
      titleize: 1.0.0
    dev: false

  /titleize/1.0.0:
    resolution: {integrity: sha1-fTUHIgYYMLpmF2MeDP0+oIOY2Vo=}
    engines: {node: '>=0.10.0'}
    dev: false

  /trim-lines/3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}
    dev: false

  /trough/2.1.0:
    resolution: {integrity: sha512-AqTiAOLcj85xS7vQ8QkAV41hPDIJ71XJB4RCUrzo/1GM2CQwhkJGaf9Hgr7BOugMRpgGUrqRg/DrBDl4H40+8g==}
    dev: false

  /tslib/2.4.1:
    resolution: {integrity: sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==}
    dev: false

  /type-fest/1.4.0:
    resolution: {integrity: sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==}
    engines: {node: '>=10'}
    dev: false

  /typescript/4.9.3:
    resolution: {integrity: sha512-CIfGzTelbKNEnLpLdGFgdyKhG23CKdKgQPOBc+OUNrkJ2vr+KSzsSV5kq5iWhEQbok+quxgGzrAtGWCyU7tHnA==}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: true

  /unified/10.1.2:
    resolution: {integrity: sha512-pUSWAi/RAnVy1Pif2kAoeWNBa3JVrx0MId2LASj8G+7AiHWoKZNTomq6LG326T68U7/e263X6fTdcXIy7XnF7Q==}
    dependencies:
      '@types/unist': 2.0.6
      bail: 2.0.2
      extend: 3.0.2
      is-buffer: 2.0.5
      is-plain-obj: 4.1.0
      trough: 2.1.0
      vfile: 5.3.6
    dev: false

  /unist-builder/3.0.0:
    resolution: {integrity: sha512-GFxmfEAa0vi9i5sd0R2kcrI9ks0r82NasRq5QHh2ysGngrc6GiqD5CDf1FjPenY4vApmFASBIIlk/jj5J5YbmQ==}
    dependencies:
      '@types/unist': 2.0.6
    dev: false

  /unist-util-find-after/4.0.1:
    resolution: {integrity: sha512-QO/PuPMm2ERxC6vFXEPtmAutOopy5PknD+Oq64gGwxKtk4xwo9Z97t9Av1obPmGU0IyTa6EKYUfTrK2QJS3Ozw==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1
    dev: false

  /unist-util-generated/2.0.0:
    resolution: {integrity: sha512-TiWE6DVtVe7Ye2QxOVW9kqybs6cZexNwTwSMVgkfjEReqy/xwGpAXb99OxktoWwmL+Z+Epb0Dn8/GNDYP1wnUw==}
    dev: false

  /unist-util-is/5.1.1:
    resolution: {integrity: sha512-F5CZ68eYzuSvJjGhCLPL3cYx45IxkqXSetCcRgUXtbcm50X2L9oOWQlfUfDdAf+6Pd27YDblBfdtmsThXmwpbQ==}
    dev: false

  /unist-util-position-from-estree/1.1.1:
    resolution: {integrity: sha512-xtoY50b5+7IH8tFbkw64gisG9tMSpxDjhX9TmaJJae/XuxQ9R/Kc8Nv1eOsf43Gt4KV/LkriMy9mptDr7XLcaw==}
    dependencies:
      '@types/unist': 2.0.6
    dev: false

  /unist-util-position/4.0.3:
    resolution: {integrity: sha512-p/5EMGIa1qwbXjA+QgcBXaPWjSnZfQ2Sc3yBEEfgPwsEmJd8Qh+DSk3LGnmOM4S1bY2C0AjmMnB8RuEYxpPwXQ==}
    dependencies:
      '@types/unist': 2.0.6
    dev: false

  /unist-util-remove-position/4.0.1:
    resolution: {integrity: sha512-0yDkppiIhDlPrfHELgB+NLQD5mfjup3a8UYclHruTJWmY74je8g+CIFr79x5f6AkmzSwlvKLbs63hC0meOMowQ==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-visit: 4.1.1
    dev: false

  /unist-util-remove/3.1.1:
    resolution: {integrity: sha512-kfCqZK5YVY5yEa89tvpl7KnBBHu2c6CzMkqHUrlOqaRgGOMp0sMvwWOVrbAtj03KhovQB7i96Gda72v/EFE0vw==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1
      unist-util-visit-parents: 5.1.1
    dev: false

  /unist-util-stringify-position/3.0.2:
    resolution: {integrity: sha512-7A6eiDCs9UtjcwZOcCpM4aPII3bAAGv13E96IkawkOAW0OhH+yRxtY0lzo8KiHpzEMfH7Q+FizUmwp8Iqy5EWg==}
    dependencies:
      '@types/unist': 2.0.6
    dev: false

  /unist-util-visit-parents/4.1.1:
    resolution: {integrity: sha512-1xAFJXAKpnnJl8G7K5KgU7FY55y3GcLIXqkzUj5QF/QVP7biUm0K0O2oqVkYsdjzJKifYeWn9+o6piAK2hGSHw==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1
    dev: false

  /unist-util-visit-parents/5.1.1:
    resolution: {integrity: sha512-gks4baapT/kNRaWxuGkl5BIhoanZo7sC/cUT/JToSRNL1dYoXRFl75d++NkjYk4TAu2uv2Px+l8guMajogeuiw==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1
    dev: false

  /unist-util-visit/3.1.0:
    resolution: {integrity: sha512-Szoh+R/Ll68QWAyQyZZpQzZQm2UPbxibDvaY8Xc9SUtYgPsDzx5AWSk++UUt2hJuow8mvwR+rG+LQLw+KsuAKA==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1
      unist-util-visit-parents: 4.1.1
    dev: false

  /unist-util-visit/4.1.1:
    resolution: {integrity: sha512-n9KN3WV9k4h1DxYR1LoajgN93wpEi/7ZplVe02IoB4gH5ctI1AaF2670BLHQYbwj+pY83gFtyeySFiyMHJklrg==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-is: 5.1.1
      unist-util-visit-parents: 5.1.1
    dev: false

  /uvu/0.5.6:
    resolution: {integrity: sha512-+g8ENReyr8YsOc6fv/NVJs2vFdHBnBNdfE49rshrTzDWOlUx4Gq7KOS2GD8eqhy2j+Ejq29+SbKH8yjkAqXqoA==}
    engines: {node: '>=8'}
    hasBin: true
    dependencies:
      dequal: 2.0.3
      diff: 5.1.0
      kleur: 4.1.5
      sade: 1.8.1
    dev: false

  /vfile-location/4.0.1:
    resolution: {integrity: sha512-JDxPlTbZrZCQXogGheBHjbRWjESSPEak770XwWPfw5mTc1v1nWGLB/apzZxsx8a0SJVfF8HK8ql8RD308vXRUw==}
    dependencies:
      '@types/unist': 2.0.6
      vfile: 5.3.6
    dev: false

  /vfile-matter/3.0.1:
    resolution: {integrity: sha512-CAAIDwnh6ZdtrqAuxdElUqQRQDQgbbIrYtDYI8gCjXS1qQ+1XdLoK8FIZWxJwn0/I+BkSSZpar3SOgjemQz4fg==}
    dependencies:
      '@types/js-yaml': 4.0.5
      is-buffer: 2.0.5
      js-yaml: 4.1.0
    dev: false

  /vfile-message/3.1.3:
    resolution: {integrity: sha512-0yaU+rj2gKAyEk12ffdSbBfjnnj+b1zqTBv3OQCTn8yEB02bsPizwdBPrLJjHnK+cU9EMMcUnNv938XcZIkmdA==}
    dependencies:
      '@types/unist': 2.0.6
      unist-util-stringify-position: 3.0.2
    dev: false

  /vfile/5.3.6:
    resolution: {integrity: sha512-ADBsmerdGBs2WYckrLBEmuETSPyTD4TuLxTrw0DvjirxW1ra4ZwkbzG8ndsv3Q57smvHxo677MHaQrY9yxH8cA==}
    dependencies:
      '@types/unist': 2.0.6
      is-buffer: 2.0.5
      unist-util-stringify-position: 3.0.2
      vfile-message: 3.1.3
    dev: false

  /vscode-oniguruma/1.7.0:
    resolution: {integrity: sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==}
    dev: false

  /vscode-textmate/8.0.0:
    resolution: {integrity: sha512-AFbieoL7a5LMqcnOF04ji+rpXadgOXnZsxQr//r83kLPr7biP7am3g9zbaZIaBGwBRWeSvoMD4mgPdX3e4NWBg==}
    dev: false

  /web-namespaces/2.0.1:
    resolution: {integrity: sha512-bKr1DkiNa2krS7qxNtdrtHAmzuYGFQLiQ13TsorsdT6ULTkPLKuu5+GsFpDlg6JFjUTwX2DyhMPG2be8uPrqsQ==}
    dev: false

  /which/1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: false

  /yallist/2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==}
    dev: false

  /yocto-queue/0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    dev: false

  /zod/3.20.2:
    resolution: {integrity: sha512-1MzNQdAvO+54H+EaK5YpyEy0T+Ejo/7YLHS93G3RnYWh5gaotGHwGeN/ZO687qEDU2y4CdStQYXVHIgrUl5UVQ==}
    dev: false

  /zwitch/2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}
    dev: false
