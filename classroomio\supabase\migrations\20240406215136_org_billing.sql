create type "public"."PLAN" as enum ('EARLY_ADOPTER', 'ENTERPRISE', 'BASIC');

create table "public"."organization_plan" (
    "id" bigint generated by default as identity not null,
    "activated_at" timestamp with time zone not null default now(),
    "org_id" uuid,
    "plan_name" "PLAN",
    "is_active" boolean,
    "deactivated_at" timestamp with time zone,
    "updated_at" timestamp with time zone default now(),
    "payload" jsonb,
    "triggered_by" bigint
);


CREATE UNIQUE INDEX organization_plan_pkey ON public.organization_plan USING btree (id);

alter table "public"."organization_plan" add constraint "organization_plan_pkey" PRIMARY KEY using index "organization_plan_pkey";

alter table "public"."organization_plan" add constraint "organization_plan_org_id_fkey" FOREIGN KEY (org_id) REFERENCES organization(id) not valid;

alter table "public"."organization_plan" validate constraint "organization_plan_org_id_fkey";

alter table "public"."organization_plan" add constraint "organization_plan_triggered_by_fkey" FOREIGN KEY (triggered_by) REFERENCES organizationmember(id) not valid;

alter table "public"."organization_plan" validate constraint "organization_plan_triggered_by_fkey";
