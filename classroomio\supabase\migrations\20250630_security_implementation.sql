-- Migration: Security Implementation
-- Date: 2025-06-30
-- Description: Comprehensive security features for video DRM, device management, and anti-piracy

-- Enhanced device_session table for advanced device management
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "hardware_fingerprint" text;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "browser_fingerprint" text;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "screen_resolution" text;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "timezone" text;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "language" text;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "platform" text;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "device_memory" bigint;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "cpu_cores" integer;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "is_approved" boolean DEFAULT false;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "approved_by" uuid;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "approved_at" timestamp with time zone;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "device_name" text;
ALTER TABLE "public"."device_session" ADD COLUMN IF NOT EXISTS "trust_score" numeric DEFAULT 0;

-- Create security_events table for comprehensive logging
CREATE TABLE IF NOT EXISTS "public"."security_events" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "event_type" character varying NOT NULL, -- 'login', 'logout', 'device_change', 'suspicious_activity', 'screenshot_attempt', 'recording_attempt', 'tab_switch', 'developer_tools', 'copy_attempt'
    "severity" character varying NOT NULL DEFAULT 'low', -- 'low', 'medium', 'high', 'critical'
    "user_id" uuid,
    "session_id" uuid,
    "device_fingerprint" text,
    "ip_address" inet,
    "user_agent" text,
    "event_data" jsonb DEFAULT '{}'::jsonb,
    "location" jsonb DEFAULT '{}'::jsonb, -- geolocation data
    "is_blocked" boolean DEFAULT false,
    "response_action" text, -- action taken in response to event
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."security_events" ENABLE ROW LEVEL SECURITY;

-- Create video_access_tokens table for secure video streaming
CREATE TABLE IF NOT EXISTS "public"."video_access_tokens" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "video_id" uuid NOT NULL,
    "user_id" uuid NOT NULL,
    "session_id" uuid,
    "access_token" text NOT NULL UNIQUE,
    "expires_at" timestamp with time zone NOT NULL,
    "max_uses" integer DEFAULT 1,
    "current_uses" integer DEFAULT 0,
    "ip_restrictions" jsonb DEFAULT '[]'::jsonb, -- allowed IP addresses
    "device_restrictions" jsonb DEFAULT '[]'::jsonb, -- allowed device fingerprints
    "is_revoked" boolean DEFAULT false,
    "revoked_at" timestamp with time zone,
    "revoked_reason" text,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."video_access_tokens" ENABLE ROW LEVEL SECURITY;

-- Create drm_keys table for video encryption
CREATE TABLE IF NOT EXISTS "public"."drm_keys" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "video_id" uuid NOT NULL,
    "key_id" text NOT NULL,
    "encryption_key" text NOT NULL, -- encrypted key
    "drm_provider" character varying NOT NULL DEFAULT 'vdocipher', -- 'vdocipher', 'aws_elemental', 'custom'
    "provider_video_id" text,
    "provider_otp" text,
    "provider_playback_info" jsonb DEFAULT '{}'::jsonb,
    "expires_at" timestamp with time zone,
    "is_active" boolean DEFAULT true,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."drm_keys" ENABLE ROW LEVEL SECURITY;

-- Create security_policies table for organization-level security settings
CREATE TABLE IF NOT EXISTS "public"."security_policies" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "organization_id" uuid NOT NULL,
    "policy_name" character varying NOT NULL,
    "policy_type" character varying NOT NULL, -- 'device_management', 'video_protection', 'exam_security', 'general'
    "settings" jsonb NOT NULL DEFAULT '{}'::jsonb,
    "is_active" boolean DEFAULT true,
    "created_by" uuid,
    "updated_by" uuid
);

ALTER TABLE "public"."security_policies" ENABLE ROW LEVEL SECURITY;

-- Create exam_sessions table for secure exam environment
CREATE TABLE IF NOT EXISTS "public"."exam_sessions" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "student_id" uuid NOT NULL,
    "exam_id" uuid, -- reference to quiz/assignment
    "lesson_id" uuid,
    "batch_id" uuid,
    "session_token" text NOT NULL UNIQUE,
    "device_fingerprint" text NOT NULL,
    "start_time" timestamp with time zone NOT NULL,
    "end_time" timestamp with time zone,
    "expected_duration" integer, -- in minutes
    "is_active" boolean DEFAULT true,
    "is_lockdown_active" boolean DEFAULT false,
    "lockdown_violations" jsonb DEFAULT '[]'::jsonb,
    "proctoring_data" jsonb DEFAULT '{}'::jsonb,
    "security_events" jsonb DEFAULT '[]'::jsonb,
    "final_score" numeric,
    "is_completed" boolean DEFAULT false,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."exam_sessions" ENABLE ROW LEVEL SECURITY;

-- Create watermark_templates table for dynamic watermarking
CREATE TABLE IF NOT EXISTS "public"."watermark_templates" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "organization_id" uuid NOT NULL,
    "name" character varying NOT NULL,
    "template_type" character varying NOT NULL DEFAULT 'text', -- 'text', 'image', 'qr_code'
    "template_data" jsonb NOT NULL DEFAULT '{}'::jsonb,
    "position_rules" jsonb NOT NULL DEFAULT '{}'::jsonb,
    "style_settings" jsonb NOT NULL DEFAULT '{}'::jsonb,
    "is_active" boolean DEFAULT true,
    "created_by" uuid
);

ALTER TABLE "public"."watermark_templates" ENABLE ROW LEVEL SECURITY;

-- Add Primary Keys
ALTER TABLE "public"."security_events" ADD CONSTRAINT "security_events_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."video_access_tokens" ADD CONSTRAINT "video_access_tokens_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."drm_keys" ADD CONSTRAINT "drm_keys_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."security_policies" ADD CONSTRAINT "security_policies_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."exam_sessions" ADD CONSTRAINT "exam_sessions_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."watermark_templates" ADD CONSTRAINT "watermark_templates_pkey" PRIMARY KEY ("id");

-- Add Foreign Key Constraints
ALTER TABLE "public"."device_session" ADD CONSTRAINT "device_session_approved_by_fkey" 
    FOREIGN KEY ("approved_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."security_events" ADD CONSTRAINT "security_events_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."video_access_tokens" ADD CONSTRAINT "video_access_tokens_video_id_fkey" 
    FOREIGN KEY ("video_id") REFERENCES "video_content"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."video_access_tokens" ADD CONSTRAINT "video_access_tokens_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."drm_keys" ADD CONSTRAINT "drm_keys_video_id_fkey" 
    FOREIGN KEY ("video_id") REFERENCES "video_content"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."security_policies" ADD CONSTRAINT "security_policies_organization_id_fkey" 
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."security_policies" ADD CONSTRAINT "security_policies_created_by_fkey" 
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."security_policies" ADD CONSTRAINT "security_policies_updated_by_fkey" 
    FOREIGN KEY ("updated_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."exam_sessions" ADD CONSTRAINT "exam_sessions_student_id_fkey" 
    FOREIGN KEY ("student_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."exam_sessions" ADD CONSTRAINT "exam_sessions_lesson_id_fkey" 
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."exam_sessions" ADD CONSTRAINT "exam_sessions_batch_id_fkey" 
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."watermark_templates" ADD CONSTRAINT "watermark_templates_organization_id_fkey" 
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."watermark_templates" ADD CONSTRAINT "watermark_templates_created_by_fkey"
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

-- Add Indexes for Performance
CREATE INDEX IF NOT EXISTS "idx_device_session_hardware_fingerprint" ON "public"."device_session"("hardware_fingerprint");
CREATE INDEX IF NOT EXISTS "idx_device_session_approved" ON "public"."device_session"("is_approved");
CREATE INDEX IF NOT EXISTS "idx_device_session_trust_score" ON "public"."device_session"("trust_score");

CREATE INDEX IF NOT EXISTS "idx_security_events_type" ON "public"."security_events"("event_type");
CREATE INDEX IF NOT EXISTS "idx_security_events_severity" ON "public"."security_events"("severity");
CREATE INDEX IF NOT EXISTS "idx_security_events_user" ON "public"."security_events"("user_id");
CREATE INDEX IF NOT EXISTS "idx_security_events_created" ON "public"."security_events"("created_at");
CREATE INDEX IF NOT EXISTS "idx_security_events_blocked" ON "public"."security_events"("is_blocked");

CREATE INDEX IF NOT EXISTS "idx_video_access_tokens_video" ON "public"."video_access_tokens"("video_id");
CREATE INDEX IF NOT EXISTS "idx_video_access_tokens_user" ON "public"."video_access_tokens"("user_id");
CREATE INDEX IF NOT EXISTS "idx_video_access_tokens_expires" ON "public"."video_access_tokens"("expires_at");
CREATE INDEX IF NOT EXISTS "idx_video_access_tokens_revoked" ON "public"."video_access_tokens"("is_revoked");

CREATE INDEX IF NOT EXISTS "idx_drm_keys_video" ON "public"."drm_keys"("video_id");
CREATE INDEX IF NOT EXISTS "idx_drm_keys_provider" ON "public"."drm_keys"("drm_provider");
CREATE INDEX IF NOT EXISTS "idx_drm_keys_active" ON "public"."drm_keys"("is_active");

CREATE INDEX IF NOT EXISTS "idx_security_policies_org" ON "public"."security_policies"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_security_policies_type" ON "public"."security_policies"("policy_type");
CREATE INDEX IF NOT EXISTS "idx_security_policies_active" ON "public"."security_policies"("is_active");

CREATE INDEX IF NOT EXISTS "idx_exam_sessions_student" ON "public"."exam_sessions"("student_id");
CREATE INDEX IF NOT EXISTS "idx_exam_sessions_active" ON "public"."exam_sessions"("is_active");
CREATE INDEX IF NOT EXISTS "idx_exam_sessions_lockdown" ON "public"."exam_sessions"("is_lockdown_active");
CREATE INDEX IF NOT EXISTS "idx_exam_sessions_start_time" ON "public"."exam_sessions"("start_time");

CREATE INDEX IF NOT EXISTS "idx_watermark_templates_org" ON "public"."watermark_templates"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_watermark_templates_type" ON "public"."watermark_templates"("template_type");
CREATE INDEX IF NOT EXISTS "idx_watermark_templates_active" ON "public"."watermark_templates"("is_active");

-- Add Unique Constraints
ALTER TABLE "public"."security_policies" ADD CONSTRAINT "unique_org_policy_name"
    UNIQUE ("organization_id", "policy_name");

ALTER TABLE "public"."drm_keys" ADD CONSTRAINT "unique_video_drm_key"
    UNIQUE ("video_id", "key_id");

-- Create Functions for Security Management

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_event_type character varying,
    p_severity character varying,
    p_user_id uuid,
    p_session_id uuid DEFAULT NULL,
    p_device_fingerprint text DEFAULT NULL,
    p_ip_address inet DEFAULT NULL,
    p_user_agent text DEFAULT NULL,
    p_event_data jsonb DEFAULT NULL,
    p_location jsonb DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    event_id uuid;
BEGIN
    INSERT INTO security_events (
        event_type, severity, user_id, session_id, device_fingerprint,
        ip_address, user_agent, event_data, location
    ) VALUES (
        p_event_type, p_severity, p_user_id, p_session_id, p_device_fingerprint,
        p_ip_address, p_user_agent, COALESCE(p_event_data, '{}'::jsonb), COALESCE(p_location, '{}'::jsonb)
    ) RETURNING id INTO event_id;

    RETURN event_id;
END;
$$ LANGUAGE plpgsql;

-- Function to generate secure video access token
CREATE OR REPLACE FUNCTION generate_video_access_token(
    p_video_id uuid,
    p_user_id uuid,
    p_session_id uuid DEFAULT NULL,
    p_expires_minutes integer DEFAULT 60,
    p_max_uses integer DEFAULT 1
)
RETURNS text AS $$
DECLARE
    access_token text;
    expires_at timestamp with time zone;
BEGIN
    -- Generate secure token
    access_token := encode(gen_random_bytes(32), 'base64');
    expires_at := now() + (p_expires_minutes || ' minutes')::interval;

    -- Insert token record
    INSERT INTO video_access_tokens (
        video_id, user_id, session_id, access_token, expires_at, max_uses
    ) VALUES (
        p_video_id, p_user_id, p_session_id, access_token, expires_at, p_max_uses
    );

    RETURN access_token;
END;
$$ LANGUAGE plpgsql;

-- Function to validate video access token
CREATE OR REPLACE FUNCTION validate_video_access_token(
    p_access_token text,
    p_user_id uuid,
    p_ip_address inet DEFAULT NULL,
    p_device_fingerprint text DEFAULT NULL
)
RETURNS boolean AS $$
DECLARE
    token_record record;
    is_valid boolean := false;
BEGIN
    -- Get token record
    SELECT * INTO token_record
    FROM video_access_tokens
    WHERE access_token = p_access_token
    AND user_id = p_user_id
    AND expires_at > now()
    AND is_revoked = false
    AND current_uses < max_uses;

    IF FOUND THEN
        -- Check IP restrictions if any
        IF jsonb_array_length(token_record.ip_restrictions) > 0 THEN
            IF NOT (token_record.ip_restrictions ? p_ip_address::text) THEN
                RETURN false;
            END IF;
        END IF;

        -- Check device restrictions if any
        IF jsonb_array_length(token_record.device_restrictions) > 0 THEN
            IF NOT (token_record.device_restrictions ? p_device_fingerprint) THEN
                RETURN false;
            END IF;
        END IF;

        -- Update usage count
        UPDATE video_access_tokens
        SET current_uses = current_uses + 1
        WHERE id = token_record.id;

        is_valid := true;
    END IF;

    RETURN is_valid;
END;
$$ LANGUAGE plpgsql;
