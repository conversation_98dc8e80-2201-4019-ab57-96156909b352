-- Migration: Video Player Enhancement
-- Date: 2025-06-30
-- Description: Enhanced video player functionality with progress tracking and download management

-- Create video_progress table for detailed progress tracking
CREATE TABLE IF NOT EXISTS "public"."video_progress" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "video_id" uuid NOT NULL,
    "student_id" uuid NOT NULL,
    "lesson_id" uuid,
    "current_time" numeric DEFAULT 0, -- in seconds
    "duration" numeric DEFAULT 0, -- in seconds
    "completion_percentage" numeric DEFAULT 0, -- 0-100
    "watch_time" numeric DEFAULT 0, -- total watch time in seconds
    "last_watched_at" timestamp with time zone DEFAULT now(),
    "is_completed" boolean DEFAULT false,
    "engagement_data" jsonb DEFAULT '{
        "play_count": 0,
        "pause_count": 0,
        "seek_count": 0,
        "replay_count": 0,
        "quality_changes": 0,
        "average_playback_rate": 1.0,
        "segments_watched": []
    }'::jsonb
);

ALTER TABLE "public"."video_progress" ENABLE ROW LEVEL SECURITY;

-- Create download_queue table for offline video management
CREATE TABLE IF NOT EXISTS "public"."download_queue" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "video_id" uuid NOT NULL,
    "student_id" uuid NOT NULL,
    "quality" character varying NOT NULL DEFAULT 'auto',
    "file_size" bigint DEFAULT 0,
    "download_url" text,
    "download_token" text,
    "expires_at" timestamp with time zone,
    "status" character varying DEFAULT 'pending', -- 'pending', 'downloading', 'completed', 'failed', 'expired'
    "progress" numeric DEFAULT 0, -- 0-100
    "downloaded_at" timestamp with time zone,
    "local_path" text,
    "error_message" text
);

ALTER TABLE "public"."download_queue" ENABLE ROW LEVEL SECURITY;

-- Create video_upload table for upload management
CREATE TABLE IF NOT EXISTS "public"."video_upload" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "original_filename" character varying NOT NULL,
    "file_size" bigint NOT NULL,
    "mime_type" character varying NOT NULL,
    "upload_url" text,
    "upload_status" character varying DEFAULT 'pending', -- 'pending', 'uploading', 'processing', 'completed', 'failed'
    "upload_progress" numeric DEFAULT 0, -- 0-100
    "processing_progress" numeric DEFAULT 0, -- 0-100
    "error_message" text,
    "video_content_id" uuid,
    "uploaded_by" uuid NOT NULL,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."video_upload" ENABLE ROW LEVEL SECURITY;

-- Create video_analytics table for analytics data
CREATE TABLE IF NOT EXISTS "public"."video_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "video_id" uuid NOT NULL,
    "total_views" bigint DEFAULT 0,
    "unique_viewers" bigint DEFAULT 0,
    "total_watch_time" numeric DEFAULT 0, -- in seconds
    "average_watch_time" numeric DEFAULT 0,
    "completion_rate" numeric DEFAULT 0, -- percentage
    "engagement_rate" numeric DEFAULT 0, -- percentage
    "analytics_data" jsonb DEFAULT '{
        "popular_segments": [],
        "drop_off_points": [],
        "quality_distribution": {},
        "device_distribution": {}
    }'::jsonb,
    "last_updated" timestamp with time zone DEFAULT now()
);

ALTER TABLE "public"."video_analytics" ENABLE ROW LEVEL SECURITY;

-- Add Primary Keys
ALTER TABLE "public"."video_progress" ADD CONSTRAINT "video_progress_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."download_queue" ADD CONSTRAINT "download_queue_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."video_upload" ADD CONSTRAINT "video_upload_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."video_analytics" ADD CONSTRAINT "video_analytics_pkey" PRIMARY KEY ("id");

-- Add Foreign Key Constraints
ALTER TABLE "public"."video_progress" ADD CONSTRAINT "video_progress_video_id_fkey" 
    FOREIGN KEY ("video_id") REFERENCES "video_content"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."video_progress" ADD CONSTRAINT "video_progress_student_id_fkey" 
    FOREIGN KEY ("student_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."video_progress" ADD CONSTRAINT "video_progress_lesson_id_fkey" 
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."download_queue" ADD CONSTRAINT "download_queue_video_id_fkey" 
    FOREIGN KEY ("video_id") REFERENCES "video_content"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."download_queue" ADD CONSTRAINT "download_queue_student_id_fkey" 
    FOREIGN KEY ("student_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."video_upload" ADD CONSTRAINT "video_upload_video_content_id_fkey" 
    FOREIGN KEY ("video_content_id") REFERENCES "video_content"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."video_upload" ADD CONSTRAINT "video_upload_uploaded_by_fkey" 
    FOREIGN KEY ("uploaded_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."video_analytics" ADD CONSTRAINT "video_analytics_video_id_fkey" 
    FOREIGN KEY ("video_id") REFERENCES "video_content"("id") ON UPDATE CASCADE ON DELETE CASCADE;

-- Add Indexes for Performance
CREATE INDEX IF NOT EXISTS "idx_video_progress_video_student" ON "public"."video_progress"("video_id", "student_id");
CREATE INDEX IF NOT EXISTS "idx_video_progress_student" ON "public"."video_progress"("student_id");
CREATE INDEX IF NOT EXISTS "idx_video_progress_lesson" ON "public"."video_progress"("lesson_id");
CREATE INDEX IF NOT EXISTS "idx_video_progress_completed" ON "public"."video_progress"("is_completed");
CREATE INDEX IF NOT EXISTS "idx_video_progress_last_watched" ON "public"."video_progress"("last_watched_at");

CREATE INDEX IF NOT EXISTS "idx_download_queue_student" ON "public"."download_queue"("student_id");
CREATE INDEX IF NOT EXISTS "idx_download_queue_status" ON "public"."download_queue"("status");
CREATE INDEX IF NOT EXISTS "idx_download_queue_expires" ON "public"."download_queue"("expires_at");

CREATE INDEX IF NOT EXISTS "idx_video_upload_status" ON "public"."video_upload"("upload_status");
CREATE INDEX IF NOT EXISTS "idx_video_upload_uploaded_by" ON "public"."video_upload"("uploaded_by");

CREATE INDEX IF NOT EXISTS "idx_video_analytics_video" ON "public"."video_analytics"("video_id");

-- Add Unique Constraints
ALTER TABLE "public"."video_progress" ADD CONSTRAINT "unique_video_progress_per_student" 
    UNIQUE ("video_id", "student_id");

ALTER TABLE "public"."video_analytics" ADD CONSTRAINT "unique_video_analytics" 
    UNIQUE ("video_id");

-- Create Functions for Video Progress Management
CREATE OR REPLACE FUNCTION update_video_progress(
    p_video_id uuid,
    p_student_id uuid,
    p_lesson_id uuid,
    p_current_time numeric,
    p_duration numeric,
    p_engagement_data jsonb DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    completion_pct numeric;
    is_complete boolean;
BEGIN
    -- Calculate completion percentage
    completion_pct := CASE 
        WHEN p_duration > 0 THEN LEAST(100, (p_current_time / p_duration) * 100)
        ELSE 0 
    END;
    
    -- Determine if video is completed (80% threshold)
    is_complete := completion_pct >= 80;
    
    -- Insert or update progress
    INSERT INTO video_progress (
        video_id, student_id, lesson_id, current_time, duration, 
        completion_percentage, is_completed, last_watched_at, engagement_data
    ) VALUES (
        p_video_id, p_student_id, p_lesson_id, p_current_time, p_duration,
        completion_pct, is_complete, now(), COALESCE(p_engagement_data, '{}'::jsonb)
    )
    ON CONFLICT (video_id, student_id) 
    DO UPDATE SET
        current_time = p_current_time,
        duration = p_duration,
        completion_percentage = completion_pct,
        is_completed = is_complete,
        last_watched_at = now(),
        engagement_data = CASE 
            WHEN p_engagement_data IS NOT NULL THEN p_engagement_data
            ELSE video_progress.engagement_data
        END,
        updated_at = now();
        
    -- Update progress_tracking table for overall analytics
    INSERT INTO progress_tracking (
        student_id, lesson_id, video_id, progress_type, progress_value, time_spent
    ) VALUES (
        p_student_id, p_lesson_id, p_video_id, 'video_watch', completion_pct, p_current_time
    )
    ON CONFLICT (student_id, video_id, progress_type) 
    DO UPDATE SET
        progress_value = completion_pct,
        time_spent = p_current_time,
        updated_at = now();
END;
$$ LANGUAGE plpgsql;
