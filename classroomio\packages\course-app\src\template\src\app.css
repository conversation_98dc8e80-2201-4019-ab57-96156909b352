@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 210 40% 98%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --posthog-primary: 36 83% 54%;
    --posthog-secondary: 19 100% 48%;
    --posthog-ring: 37 78% 39%;

    --posthog-background: 70 16% 93%;
    --posthog-border: 68 8% 80%;

    --minimal-primary: 222 95% 41%;
    --minimal-secondary: 214 33% 9%;

    --classic-primary: 300 98% 41%;
    --classic-secondary: 300 100% 95%;

    --examprep-primary: 224 98% 37%;

    --webflow-primary: 224 93% 39%;
    --webflow-secondary: 224 93% 39%;

    --bootcamp-primary: 151 100% 43%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-app;
  }
}

.prose :is(h1, h2, h3, h4, h5, h6) {
  font-weight: 700;
  margin-top: 20px;
}
.prose :is(h1) {
  font-size: 2em;
}
.prose :is(h2) {
  font-size: 1.5em;
}
.prose :is(h3) {
  font-size: 1.17em;
}
.prose :is(h4) {
  font-size: 1em;
}
.prose :is(h5) {
  font-size: 0.83em;
}
.prose :is(h6) {
  font-size: 0.67em;
}

.prose :is(p, li) {
  margin-top: 0.7em;
  margin-bottom: 0em;
  color: #374151;
}

.prose :is(ul, ol) {
  list-style-type: disc;
  padding-left: 3rem;
}

.prose pre {
  max-inline-size: 100%;
  padding: 1rem;
  border-radius: 8px;
  tab-size: 2;
}
.prose blockquote {
  border-left: 4px solid gray;
  font-style: italic;
  font-weight: 500;
  padding-left: 16px;
  margin: 20px 0;
}
.prose figure + em {
  display: block;
  font-size: 14px;
  margin-bottom: 40px;
  border: none;
  padding: none;
  text-align: center;
}
