// Anti-Piracy Protection Utilities
// Date: 2025-06-30

import { securityEventService } from '$lib/utils/services/security';
import { globalStore } from '$lib/utils/store/app';

export interface AntiPiracyConfig {
  screenshot_protection: boolean;
  recording_protection: boolean;
  right_click_disabled: boolean;
  developer_tools_blocked: boolean;
  copy_paste_disabled: boolean;
  text_selection_disabled: boolean;
  print_disabled: boolean;
  drag_drop_disabled: boolean;
}

export class AntiPiracyProtection {
  private static instance: AntiPiracyProtection;
  private config: AntiPiracyConfig;
  private isActive: boolean = false;
  private eventListeners: Array<{ element: Element | Document | Window; event: string; handler: EventListener }> = [];
  private devToolsCheckInterval: NodeJS.Timeout | null = null;
  private recordingCheckInterval: NodeJS.Timeout | null = null;

  constructor(config: AntiPiracyConfig) {
    this.config = config;
  }

  static getInstance(config?: AntiPiracyConfig): AntiPiracyProtection {
    if (!AntiPiracyProtection.instance && config) {
      AntiPiracyProtection.instance = new AntiPiracyProtection(config);
    }
    return AntiPiracyProtection.instance;
  }

  activate(): void {
    if (this.isActive) return;

    this.isActive = true;

    if (this.config.screenshot_protection) {
      this.enableScreenshotProtection();
    }

    if (this.config.recording_protection) {
      this.enableRecordingProtection();
    }

    if (this.config.right_click_disabled) {
      this.disableRightClick();
    }

    if (this.config.developer_tools_blocked) {
      this.blockDeveloperTools();
    }

    if (this.config.copy_paste_disabled) {
      this.disableCopyPaste();
    }

    if (this.config.text_selection_disabled) {
      this.disableTextSelection();
    }

    if (this.config.print_disabled) {
      this.disablePrint();
    }

    if (this.config.drag_drop_disabled) {
      this.disableDragDrop();
    }

    // Add visibility change detection
    this.addVisibilityChangeDetection();

    console.log('Anti-piracy protection activated');
  }

  deactivate(): void {
    if (!this.isActive) return;

    this.isActive = false;

    // Remove all event listeners
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];

    // Clear intervals
    if (this.devToolsCheckInterval) {
      clearInterval(this.devToolsCheckInterval);
      this.devToolsCheckInterval = null;
    }

    if (this.recordingCheckInterval) {
      clearInterval(this.recordingCheckInterval);
      this.recordingCheckInterval = null;
    }

    // Re-enable disabled features
    document.body.style.userSelect = '';
    document.body.style.webkitUserSelect = '';
    document.body.style.mozUserSelect = '';
    document.body.style.msUserSelect = '';

    console.log('Anti-piracy protection deactivated');
  }

  private enableScreenshotProtection(): void {
    // Detect screenshot attempts (keyboard shortcuts)
    const screenshotHandler = (e: KeyboardEvent) => {
      const isScreenshot = 
        (e.key === 'PrintScreen') ||
        (e.ctrlKey && e.shiftKey && e.key === 'S') || // Chrome DevTools screenshot
        (e.metaKey && e.shiftKey && e.key === '3') || // macOS screenshot
        (e.metaKey && e.shiftKey && e.key === '4') || // macOS area screenshot
        (e.metaKey && e.shiftKey && e.key === '5'); // macOS screenshot utility

      if (isScreenshot) {
        e.preventDefault();
        e.stopPropagation();
        this.logSecurityEvent('screenshot_attempt', 'high', {
          key_combination: `${e.ctrlKey ? 'Ctrl+' : ''}${e.shiftKey ? 'Shift+' : ''}${e.metaKey ? 'Meta+' : ''}${e.key}`,
          timestamp: new Date().toISOString()
        });
        this.showWarningMessage('Screenshot detected and blocked');
        return false;
      }
    };

    this.addEventListener(document, 'keydown', screenshotHandler);
    this.addEventListener(document, 'keyup', screenshotHandler);

    // Blur content when window loses focus (potential screenshot)
    const blurHandler = () => {
      document.body.style.filter = 'blur(10px)';
      this.logSecurityEvent('window_blur', 'medium', {
        reason: 'potential_screenshot',
        timestamp: new Date().toISOString()
      });
    };

    const focusHandler = () => {
      document.body.style.filter = '';
    };

    this.addEventListener(window, 'blur', blurHandler);
    this.addEventListener(window, 'focus', focusHandler);
  }

  private enableRecordingProtection(): void {
    // Check for screen recording APIs
    this.recordingCheckInterval = setInterval(() => {
      this.checkForRecording();
    }, 2000);

    // Detect media recording attempts
    const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
    navigator.mediaDevices.getUserMedia = async (constraints) => {
      if (constraints?.video || constraints?.audio) {
        this.logSecurityEvent('recording_attempt', 'critical', {
          constraints,
          timestamp: new Date().toISOString()
        });
        throw new Error('Media recording is not allowed');
      }
      return originalGetUserMedia.call(navigator.mediaDevices, constraints);
    };
  }

  private async checkForRecording(): Promise<void> {
    try {
      // Check if screen capture is active
      if ('getDisplayMedia' in navigator.mediaDevices) {
        // This is a heuristic check - actual detection is limited by browser security
        const stream = await navigator.mediaDevices.getDisplayMedia({ video: true }).catch(() => null);
        if (stream) {
          stream.getTracks().forEach(track => track.stop());
          this.logSecurityEvent('recording_attempt', 'critical', {
            type: 'screen_capture',
            timestamp: new Date().toISOString()
          });
          this.showWarningMessage('Screen recording detected and blocked');
        }
      }
    } catch (error) {
      // Expected - user denied permission or no recording active
    }
  }

  private disableRightClick(): void {
    const rightClickHandler = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      this.logSecurityEvent('right_click_attempt', 'low', {
        x: e.clientX,
        y: e.clientY,
        timestamp: new Date().toISOString()
      });
      return false;
    };

    this.addEventListener(document, 'contextmenu', rightClickHandler);
  }

  private blockDeveloperTools(): void {
    // Detect DevTools opening
    let devtools = { open: false, orientation: null };
    
    this.devToolsCheckInterval = setInterval(() => {
      const threshold = 160;
      
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools.open) {
          devtools.open = true;
          this.logSecurityEvent('developer_tools', 'critical', {
            window_dimensions: {
              outer: { width: window.outerWidth, height: window.outerHeight },
              inner: { width: window.innerWidth, height: window.innerHeight }
            },
            timestamp: new Date().toISOString()
          });
          this.handleDevToolsDetection();
        }
      } else {
        devtools.open = false;
      }
    }, 500);

    // Detect F12 key
    const f12Handler = (e: KeyboardEvent) => {
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.shiftKey && e.key === 'J') ||
          (e.ctrlKey && e.key === 'U')) {
        e.preventDefault();
        e.stopPropagation();
        this.logSecurityEvent('developer_tools', 'critical', {
          key_combination: `${e.ctrlKey ? 'Ctrl+' : ''}${e.shiftKey ? 'Shift+' : ''}${e.key}`,
          timestamp: new Date().toISOString()
        });
        this.handleDevToolsDetection();
        return false;
      }
    };

    this.addEventListener(document, 'keydown', f12Handler);
  }

  private handleDevToolsDetection(): void {
    // Blur content
    document.body.style.filter = 'blur(20px)';
    
    // Show warning
    this.showWarningMessage('Developer tools detected. Please close them to continue.');
    
    // Optional: Redirect or logout
    setTimeout(() => {
      if (confirm('Developer tools detected. You will be logged out for security reasons.')) {
        window.location.href = '/logout';
      }
    }, 3000);
  }

  private disableCopyPaste(): void {
    const copyPasteHandler = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && (e.key === 'c' || e.key === 'v' || e.key === 'x' || e.key === 'a')) {
        e.preventDefault();
        e.stopPropagation();
        this.logSecurityEvent('copy_attempt', 'medium', {
          action: e.key,
          timestamp: new Date().toISOString()
        });
        return false;
      }
    };

    this.addEventListener(document, 'keydown', copyPasteHandler);

    // Disable clipboard events
    const clipboardHandler = (e: ClipboardEvent) => {
      e.preventDefault();
      e.stopPropagation();
      return false;
    };

    this.addEventListener(document, 'copy', clipboardHandler);
    this.addEventListener(document, 'paste', clipboardHandler);
    this.addEventListener(document, 'cut', clipboardHandler);
  }

  private disableTextSelection(): void {
    // CSS-based text selection disable
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
    document.body.style.mozUserSelect = 'none';
    document.body.style.msUserSelect = 'none';

    // JavaScript-based selection disable
    const selectionHandler = (e: Event) => {
      e.preventDefault();
      return false;
    };

    this.addEventListener(document, 'selectstart', selectionHandler);
    this.addEventListener(document, 'dragstart', selectionHandler);
  }

  private disablePrint(): void {
    const printHandler = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        e.stopPropagation();
        this.logSecurityEvent('print_attempt', 'medium', {
          timestamp: new Date().toISOString()
        });
        this.showWarningMessage('Printing is disabled for security reasons');
        return false;
      }
    };

    this.addEventListener(document, 'keydown', printHandler);

    // Override window.print
    window.print = () => {
      this.logSecurityEvent('print_attempt', 'medium', {
        method: 'window.print',
        timestamp: new Date().toISOString()
      });
      this.showWarningMessage('Printing is disabled for security reasons');
    };
  }

  private disableDragDrop(): void {
    const dragDropHandler = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      return false;
    };

    this.addEventListener(document, 'dragstart', dragDropHandler);
    this.addEventListener(document, 'drag', dragDropHandler);
    this.addEventListener(document, 'dragenter', dragDropHandler);
    this.addEventListener(document, 'dragover', dragDropHandler);
    this.addEventListener(document, 'dragleave', dragDropHandler);
    this.addEventListener(document, 'drop', dragDropHandler);
  }

  private addVisibilityChangeDetection(): void {
    const visibilityHandler = () => {
      if (document.hidden) {
        this.logSecurityEvent('tab_switch', 'medium', {
          visibility_state: document.visibilityState,
          timestamp: new Date().toISOString()
        });
      }
    };

    this.addEventListener(document, 'visibilitychange', visibilityHandler);
  }

  private addEventListener(element: Element | Document | Window, event: string, handler: EventListener): void {
    element.addEventListener(event, handler);
    this.eventListeners.push({ element, event, handler });
  }

  private async logSecurityEvent(eventType: string, severity: string, eventData: any): Promise<void> {
    try {
      const userId = globalStore.user?.id;
      await securityEventService.logEvent({
        event_type: eventType as any,
        severity: severity as any,
        user_id: userId,
        event_data: eventData,
        location: {},
        is_blocked: true,
        response_action: 'blocked',
        metadata: {}
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  private showWarningMessage(message: string): void {
    // Create warning overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 999999;
      font-family: Arial, sans-serif;
      font-size: 18px;
      text-align: center;
    `;
    
    overlay.innerHTML = `
      <div style="background: #dc2626; padding: 20px; border-radius: 8px; max-width: 400px;">
        <h3 style="margin: 0 0 10px 0;">⚠️ Security Warning</h3>
        <p style="margin: 0;">${message}</p>
      </div>
    `;

    document.body.appendChild(overlay);

    // Remove after 3 seconds
    setTimeout(() => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    }, 3000);
  }

  updateConfig(newConfig: Partial<AntiPiracyConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.isActive) {
      this.deactivate();
      this.activate();
    }
  }

  getConfig(): AntiPiracyConfig {
    return { ...this.config };
  }

  isProtectionActive(): boolean {
    return this.isActive;
  }
}
