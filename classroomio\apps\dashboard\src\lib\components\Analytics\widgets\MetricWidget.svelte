<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { DashboardWidget, ThemeConfig } from '$lib/utils/types/analytics';
  import { t } from '$lib/utils/functions/translations';
  import { 
    TrendUp, 
    TrendDown, 
    TrendFlat,
    View,
    Time,
    User,
    CheckmarkFilled
  } from 'carbon-icons-svelte';

  export let widget: DashboardWidget;
  export let data: any;
  export let theme: ThemeConfig;

  const dispatch = createEventDispatcher<{
    click: { widget: DashboardWidget };
  }>();

  $: metricValue = getMetricValue(data);
  $: previousValue = getPreviousValue(data);
  $: changePercentage = calculateChangePercentage(metricValue, previousValue);
  $: trend = getTrend(changePercentage);
  $: formattedValue = formatValue(metricValue);
  $: formattedChange = formatChange(changePercentage);

  function getMetricValue(data: any): number {
    if (typeof data === 'number') return data;
    if (data?.value !== undefined) return data.value;
    if (data?.current_value !== undefined) return data.current_value;
    if (Array.isArray(data) && data.length > 0) {
      return data[data.length - 1]?.value || 0;
    }
    return 0;
  }

  function getPreviousValue(data: any): number {
    if (data?.previous_value !== undefined) return data.previous_value;
    if (Array.isArray(data) && data.length > 1) {
      return data[data.length - 2]?.value || 0;
    }
    return 0;
  }

  function calculateChangePercentage(current: number, previous: number): number {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  function getTrend(changePercentage: number): 'up' | 'down' | 'flat' {
    if (Math.abs(changePercentage) < 1) return 'flat';
    return changePercentage > 0 ? 'up' : 'down';
  }

  function formatValue(value: number): string {
    const config = widget.config;
    
    // Check for custom formatting in widget config
    if (config.format) {
      switch (config.format) {
        case 'currency':
          return new Intl.NumberFormat('en-US', { 
            style: 'currency', 
            currency: config.currency || 'USD' 
          }).format(value);
        case 'percentage':
          return `${value.toFixed(1)}%`;
        case 'duration':
          return formatDuration(value);
        case 'compact':
          return formatCompactNumber(value);
        default:
          return value.toLocaleString();
      }
    }

    // Auto-detect format based on value
    if (value >= 1000000) {
      return formatCompactNumber(value);
    } else if (value >= 1000) {
      return value.toLocaleString();
    } else if (value % 1 !== 0) {
      return value.toFixed(2);
    }
    
    return value.toString();
  }

  function formatChange(changePercentage: number): string {
    const abs = Math.abs(changePercentage);
    if (abs < 0.1) return '0%';
    return `${changePercentage > 0 ? '+' : ''}${changePercentage.toFixed(1)}%`;
  }

  function formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${Math.round(minutes)}m`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      const mins = Math.round(minutes % 60);
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    } else {
      const days = Math.floor(minutes / 1440);
      const hours = Math.floor((minutes % 1440) / 60);
      return hours > 0 ? `${days}d ${hours}h` : `${days}d`;
    }
  }

  function formatCompactNumber(value: number): string {
    if (value >= 1000000000) {
      return `${(value / 1000000000).toFixed(1)}B`;
    } else if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  }

  function getTrendIcon(trend: string) {
    switch (trend) {
      case 'up': return TrendUp;
      case 'down': return TrendDown;
      default: return TrendFlat;
    }
  }

  function getTrendColor(trend: string): string {
    // Check if this is a "good" or "bad" metric based on widget config
    const isPositiveTrend = widget.config.positive_trend !== false;
    
    switch (trend) {
      case 'up':
        return isPositiveTrend ? 'text-green-600' : 'text-red-600';
      case 'down':
        return isPositiveTrend ? 'text-red-600' : 'text-green-600';
      default:
        return 'text-gray-500';
    }
  }

  function getMetricIcon() {
    if (widget.config.icon) {
      switch (widget.config.icon) {
        case 'user': return User;
        case 'time': return Time;
        case 'view': return View;
        case 'check': return CheckmarkFilled;
        default: return View;
      }
    }
    return View;
  }

  function handleClick() {
    dispatch('click', { widget });
  }
</script>

<div 
  class="metric-widget h-full flex flex-col justify-center p-6 cursor-pointer"
  on:click={handleClick}
  on:keydown={(e) => e.key === 'Enter' && handleClick()}
  role="button"
  tabindex="0"
  style="color: {theme?.text_color || '#111827'};"
>
  <!-- Metric Icon (if configured) -->
  {#if widget.config.show_icon !== false}
    <div class="flex justify-center mb-4">
      <div 
        class="w-12 h-12 rounded-full flex items-center justify-center"
        style="background-color: {theme?.primary_color || '#3B82F6'}20;"
      >
        <svelte:component 
          this={getMetricIcon()} 
          size={24} 
          class="text-primary-600"
          style="color: {theme?.primary_color || '#3B82F6'};"
        />
      </div>
    </div>
  {/if}

  <!-- Main Metric Value -->
  <div class="text-center mb-4">
    <div 
      class="text-4xl font-bold mb-2"
      style="color: {theme?.primary_color || '#3B82F6'};"
    >
      {formattedValue}
    </div>
    
    {#if data?.label || widget.config.label}
      <div class="text-sm text-gray-600 dark:text-gray-400">
        {data?.label || widget.config.label}
      </div>
    {/if}
  </div>

  <!-- Trend Indicator -->
  {#if previousValue > 0 && widget.config.show_trend !== false}
    <div class="flex items-center justify-center space-x-2">
      <svelte:component 
        this={getTrendIcon(trend)} 
        size={16} 
        class={getTrendColor(trend)}
      />
      <span class="text-sm font-medium {getTrendColor(trend)}">
        {formattedChange}
      </span>
      {#if data?.period}
        <span class="text-xs text-gray-500 dark:text-gray-400">
          vs {data.period}
        </span>
      {:else}
        <span class="text-xs text-gray-500 dark:text-gray-400">
          {$t('analytics.vs_previous', { default: 'vs previous' })}
        </span>
      {/if}
    </div>
  {/if}

  <!-- Additional Metrics (if provided) -->
  {#if data?.additional_metrics && Array.isArray(data.additional_metrics)}
    <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
      <div class="grid grid-cols-2 gap-4">
        {#each data.additional_metrics as metric}
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">
              {formatValue(metric.value)}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {metric.label}
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Target/Goal Indicator (if configured) -->
  {#if data?.target && widget.config.show_target !== false}
    <div class="mt-4">
      <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
        <span>{$t('analytics.target', { default: 'Target' })}</span>
        <span>{formatValue(data.target)}</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          class="h-2 rounded-full transition-all duration-300"
          style="
            width: {Math.min(100, (metricValue / data.target) * 100)}%;
            background-color: {theme?.secondary_color || '#10B981'};
          "
        ></div>
      </div>
      <div class="text-xs text-gray-600 dark:text-gray-400 mt-1 text-center">
        {((metricValue / data.target) * 100).toFixed(1)}% {$t('analytics.of_target', { default: 'of target' })}
      </div>
    </div>
  {/if}

  <!-- Sparkline (if data provided) -->
  {#if data?.sparkline && Array.isArray(data.sparkline) && data.sparkline.length > 1}
    <div class="mt-4">
      <svg class="w-full h-8" viewBox="0 0 100 20">
        {#each data.sparkline as point, index}
          {#if index > 0}
            <line
              x1={(index - 1) * (100 / (data.sparkline.length - 1))}
              y1={20 - ((data.sparkline[index - 1] / Math.max(...data.sparkline)) * 18)}
              x2={index * (100 / (data.sparkline.length - 1))}
              y2={20 - ((point / Math.max(...data.sparkline)) * 18)}
              stroke={theme?.primary_color || '#3B82F6'}
              stroke-width="1"
              fill="none"
            />
          {/if}
        {/each}
      </svg>
    </div>
  {/if}
</div>

<style>
  .metric-widget {
    transition: transform 0.2s ease-in-out;
  }

  .metric-widget:hover {
    transform: scale(1.02);
  }

  .metric-widget:focus {
    outline: 2px solid var(--primary-color, #3B82F6);
    outline-offset: 2px;
  }
</style>
