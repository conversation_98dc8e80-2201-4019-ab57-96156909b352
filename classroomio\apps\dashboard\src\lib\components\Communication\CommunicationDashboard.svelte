<script lang="ts">
  import { onMount } from 'svelte';
  import { writable } from 'svelte/store';
  import type { CommunicationAnalytics } from '$lib/utils/types/communication';
  import { doubtService, forumService, messagingService, notificationService } from '$lib/utils/services/communication';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Dashboard,
    User,
    Chat,
    Forum,
    Notification,
    Email,
    TrendUp,
    TrendDown,
    Time,
    CheckmarkFilled,
    Warning,
    View
  } from 'carbon-icons-svelte';

  export let organizationId: string;
  export let batchId: string | null = null;
  export let className: string = '';

  let analytics = writable<CommunicationAnalytics | null>(null);
  let loading = true;
  let error: string | null = null;
  let selectedTimeRange = '7d';

  $: isInstructor = $globalStore.role === 'teacher' || $globalStore.isOrgAdmin;

  onMount(async () => {
    await loadAnalytics();
  });

  async function loadAnalytics() {
    try {
      loading = true;
      error = null;

      // In a real implementation, this would be a single API call
      // For now, we'll simulate the analytics data
      const analyticsData: CommunicationAnalytics = {
        doubt_metrics: {
          total_doubts: 156,
          resolved_doubts: 142,
          average_resolution_time: 45, // minutes
          doubts_by_subject: {
            'Mathematics': 45,
            'Physics': 38,
            'Chemistry': 32,
            'Biology': 25,
            'English': 16
          },
          doubts_by_priority: {
            'urgent': 12,
            'high': 34,
            'medium': 78,
            'low': 32
          },
          instructor_response_rate: 94.2
        },
        forum_metrics: {
          total_posts: 89,
          total_replies: 267,
          active_users: 78,
          posts_by_category: {
            'General Discussion': 32,
            'Study Help': 28,
            'Announcements': 15,
            'Q&A': 14
          },
          engagement_rate: 67.8
        },
        messaging_metrics: {
          total_messages: 1247,
          active_channels: 12,
          average_response_time: 8, // minutes
          messages_by_type: {
            'text': 1089,
            'voice': 98,
            'file': 45,
            'image': 15
          }
        },
        notification_metrics: {
          total_sent: 2341,
          delivery_rate: 98.7,
          open_rate: 76.3,
          click_rate: 34.2,
          notifications_by_channel: {
            'in_app': 1245,
            'email': 678,
            'push': 234,
            'whatsapp': 123,
            'sms': 61
          }
        }
      };

      analytics.set(analyticsData);

    } catch (err) {
      console.error('Error loading analytics:', err);
      error = err.message || 'Failed to load analytics';
    } finally {
      loading = false;
    }
  }

  function formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  function formatPercentage(num: number): string {
    return num.toFixed(1) + '%';
  }

  function getMetricTrend(current: number, previous: number): { trend: 'up' | 'down' | 'stable'; percentage: number } {
    if (previous === 0) return { trend: 'stable', percentage: 0 };
    
    const change = ((current - previous) / previous) * 100;
    
    if (Math.abs(change) < 1) return { trend: 'stable', percentage: 0 };
    
    return {
      trend: change > 0 ? 'up' : 'down',
      percentage: Math.abs(change)
    };
  }

  function getTrendIcon(trend: 'up' | 'down' | 'stable') {
    switch (trend) {
      case 'up': return TrendUp;
      case 'down': return TrendDown;
      default: return null;
    }
  }

  function getTrendColor(trend: 'up' | 'down' | 'stable', isPositive: boolean = true) {
    if (trend === 'stable') return 'text-gray-500';
    
    const isGood = (trend === 'up' && isPositive) || (trend === 'down' && !isPositive);
    return isGood ? 'text-green-500' : 'text-red-500';
  }
</script>

<div class="communication-dashboard {className}">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center">
      <Dashboard size={24} class="text-primary-600 mr-3" />
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          {$t('communication.dashboard', { default: 'Communication Dashboard' })}
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          {$t('communication.dashboard_desc', { default: 'Monitor and manage all communication channels' })}
        </p>
      </div>
    </div>

    <div class="flex items-center space-x-4">
      <select 
        bind:value={selectedTimeRange}
        on:change={loadAnalytics}
        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
      >
        <option value="24h">{$t('communication.last_24h', { default: 'Last 24 Hours' })}</option>
        <option value="7d">{$t('communication.last_7d', { default: 'Last 7 Days' })}</option>
        <option value="30d">{$t('communication.last_30d', { default: 'Last 30 Days' })}</option>
        <option value="90d">{$t('communication.last_90d', { default: 'Last 90 Days' })}</option>
      </select>
    </div>
  </div>

  {#if loading}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {#each Array(4) as _}
        <Box className="animate-pulse">
          <div class="p-6">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
            <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          </div>
        </Box>
      {/each}
    </div>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('communication.error', { default: 'Error Loading Dashboard' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadAnalytics}>
        {$t('communication.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if $analytics}
    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Doubts Metric -->
      <Box>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <User size={20} class="text-blue-500 mr-2" />
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                {$t('communication.total_doubts', { default: 'Total Doubts' })}
              </span>
            </div>
          </div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">
            {formatNumber($analytics.doubt_metrics.total_doubts)}
          </div>
          <div class="flex items-center text-sm">
            <span class="text-green-600">
              {formatPercentage($analytics.doubt_metrics.resolved_doubts / $analytics.doubt_metrics.total_doubts * 100)} resolved
            </span>
          </div>
        </div>
      </Box>

      <!-- Forum Posts Metric -->
      <Box>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <Forum size={20} class="text-purple-500 mr-2" />
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                {$t('communication.forum_posts', { default: 'Forum Posts' })}
              </span>
            </div>
          </div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">
            {formatNumber($analytics.forum_metrics.total_posts)}
          </div>
          <div class="flex items-center text-sm">
            <span class="text-blue-600">
              {formatNumber($analytics.forum_metrics.total_replies)} replies
            </span>
          </div>
        </div>
      </Box>

      <!-- Messages Metric -->
      <Box>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <Chat size={20} class="text-green-500 mr-2" />
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                {$t('communication.messages', { default: 'Messages' })}
              </span>
            </div>
          </div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">
            {formatNumber($analytics.messaging_metrics.total_messages)}
          </div>
          <div class="flex items-center text-sm">
            <span class="text-gray-600 dark:text-gray-400">
              {$analytics.messaging_metrics.active_channels} channels
            </span>
          </div>
        </div>
      </Box>

      <!-- Notifications Metric -->
      <Box>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <Notification size={20} class="text-orange-500 mr-2" />
              <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                {$t('communication.notifications', { default: 'Notifications' })}
              </span>
            </div>
          </div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">
            {formatNumber($analytics.notification_metrics.total_sent)}
          </div>
          <div class="flex items-center text-sm">
            <span class="text-green-600">
              {formatPercentage($analytics.notification_metrics.delivery_rate)} delivered
            </span>
          </div>
        </div>
      </Box>
    </div>

    <!-- Detailed Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Doubt Analytics -->
      <Box>
        <div class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {$t('communication.doubt_analytics', { default: 'Doubt Analytics' })}
          </h3>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {$t('communication.avg_resolution_time', { default: 'Avg Resolution Time' })}
              </span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {$analytics.doubt_metrics.average_resolution_time} min
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {$t('communication.response_rate', { default: 'Response Rate' })}
              </span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {formatPercentage($analytics.doubt_metrics.instructor_response_rate)}
              </span>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                {$t('communication.doubts_by_subject', { default: 'Doubts by Subject' })}
              </h4>
              <div class="space-y-2">
                {#each Object.entries($analytics.doubt_metrics.doubts_by_subject) as [subject, count]}
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-gray-600 dark:text-gray-400">{subject}</span>
                    <span class="text-xs font-medium text-gray-900 dark:text-white">{count}</span>
                  </div>
                {/each}
              </div>
            </div>
          </div>
        </div>
      </Box>

      <!-- Forum Analytics -->
      <Box>
        <div class="p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {$t('communication.forum_analytics', { default: 'Forum Analytics' })}
          </h3>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {$t('communication.active_users', { default: 'Active Users' })}
              </span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {$analytics.forum_metrics.active_users}
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {$t('communication.engagement_rate', { default: 'Engagement Rate' })}
              </span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {formatPercentage($analytics.forum_metrics.engagement_rate)}
              </span>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                {$t('communication.posts_by_category', { default: 'Posts by Category' })}
              </h4>
              <div class="space-y-2">
                {#each Object.entries($analytics.forum_metrics.posts_by_category) as [category, count]}
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-gray-600 dark:text-gray-400">{category}</span>
                    <span class="text-xs font-medium text-gray-900 dark:text-white">{count}</span>
                  </div>
                {/each}
              </div>
            </div>
          </div>
        </div>
      </Box>
    </div>

    <!-- Notification Performance -->
    <Box>
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {$t('communication.notification_performance', { default: 'Notification Performance' })}
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600 mb-1">
              {formatPercentage($analytics.notification_metrics.delivery_rate)}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('communication.delivery_rate', { default: 'Delivery Rate' })}
            </div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600 mb-1">
              {formatPercentage($analytics.notification_metrics.open_rate)}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('communication.open_rate', { default: 'Open Rate' })}
            </div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600 mb-1">
              {formatPercentage($analytics.notification_metrics.click_rate)}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('communication.click_rate', { default: 'Click Rate' })}
            </div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600 mb-1">
              {formatNumber($analytics.notification_metrics.total_sent)}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('communication.total_sent', { default: 'Total Sent' })}
            </div>
          </div>
        </div>
      </div>
    </Box>
  {/if}
</div>
