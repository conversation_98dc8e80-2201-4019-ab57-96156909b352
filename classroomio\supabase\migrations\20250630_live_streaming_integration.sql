-- Migration: Live Streaming Integration
-- Date: 2025-06-30
-- Description: Comprehensive live streaming system with BigBlueButton/Jitsi integration

-- Create live_sessions table for managing live classes
CREATE TABLE IF NOT EXISTS "public"."live_sessions" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "title" character varying NOT NULL,
    "description" text,
    "batch_id" uuid NOT NULL,
    "subject_id" uuid,
    "chapter_id" uuid,
    "lesson_id" uuid,
    "instructor_id" uuid NOT NULL,
    "scheduled_start" timestamp with time zone NOT NULL,
    "scheduled_end" timestamp with time zone NOT NULL,
    "actual_start" timestamp with time zone,
    "actual_end" timestamp with time zone,
    "status" character varying DEFAULT 'scheduled', -- 'scheduled', 'live', 'ended', 'cancelled'
    "session_type" character varying DEFAULT 'class', -- 'class', 'exam', 'meeting', 'workshop'
    "max_participants" integer DEFAULT 100,
    "is_recorded" boolean DEFAULT true,
    "recording_url" text,
    "recording_duration" integer, -- in seconds
    "meeting_config" jsonb DEFAULT '{}'::jsonb,
    "security_settings" jsonb DEFAULT '{}'::jsonb,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."live_sessions" ENABLE ROW LEVEL SECURITY;

-- Create live_session_participants table for tracking attendance
CREATE TABLE IF NOT EXISTS "public"."live_session_participants" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "session_id" uuid NOT NULL,
    "user_id" uuid NOT NULL,
    "role" character varying NOT NULL DEFAULT 'student', -- 'instructor', 'student', 'moderator', 'observer'
    "joined_at" timestamp with time zone,
    "left_at" timestamp with time zone,
    "total_duration" integer DEFAULT 0, -- in seconds
    "device_fingerprint" text,
    "ip_address" inet,
    "user_agent" text,
    "connection_quality" jsonb DEFAULT '{}'::jsonb,
    "participation_data" jsonb DEFAULT '{}'::jsonb, -- chat messages, polls, etc.
    "is_approved" boolean DEFAULT false,
    "approval_status" character varying DEFAULT 'pending' -- 'pending', 'approved', 'rejected', 'auto_approved'
);

ALTER TABLE "public"."live_session_participants" ENABLE ROW LEVEL SECURITY;

-- Create breakout_rooms table for group activities
CREATE TABLE IF NOT EXISTS "public"."breakout_rooms" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "session_id" uuid NOT NULL,
    "room_name" character varying NOT NULL,
    "room_number" integer NOT NULL,
    "max_participants" integer DEFAULT 10,
    "duration_minutes" integer DEFAULT 15,
    "is_active" boolean DEFAULT true,
    "started_at" timestamp with time zone,
    "ended_at" timestamp with time zone,
    "room_config" jsonb DEFAULT '{}'::jsonb,
    "assigned_participants" jsonb DEFAULT '[]'::jsonb -- array of user IDs
);

ALTER TABLE "public"."breakout_rooms" ENABLE ROW LEVEL SECURITY;

-- Create live_polls table for interactive polling
CREATE TABLE IF NOT EXISTS "public"."live_polls" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "session_id" uuid NOT NULL,
    "created_by" uuid NOT NULL,
    "question" text NOT NULL,
    "poll_type" character varying NOT NULL DEFAULT 'multiple_choice', -- 'multiple_choice', 'single_choice', 'text', 'rating', 'yes_no'
    "options" jsonb DEFAULT '[]'::jsonb,
    "is_active" boolean DEFAULT false,
    "is_anonymous" boolean DEFAULT true,
    "allow_multiple_answers" boolean DEFAULT false,
    "started_at" timestamp with time zone,
    "ended_at" timestamp with time zone,
    "duration_seconds" integer DEFAULT 60,
    "results" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."live_polls" ENABLE ROW LEVEL SECURITY;

-- Create live_poll_responses table for poll answers
CREATE TABLE IF NOT EXISTS "public"."live_poll_responses" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "poll_id" uuid NOT NULL,
    "user_id" uuid,
    "response_data" jsonb NOT NULL,
    "response_time" timestamp with time zone DEFAULT now(),
    "is_anonymous" boolean DEFAULT true
);

ALTER TABLE "public"."live_poll_responses" ENABLE ROW LEVEL SECURITY;

-- Create live_chat_messages table for session chat
CREATE TABLE IF NOT EXISTS "public"."live_chat_messages" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "session_id" uuid NOT NULL,
    "user_id" uuid NOT NULL,
    "message" text NOT NULL,
    "message_type" character varying DEFAULT 'text', -- 'text', 'emoji', 'file', 'poll', 'announcement'
    "is_private" boolean DEFAULT false,
    "recipient_id" uuid, -- for private messages
    "is_moderated" boolean DEFAULT false,
    "moderation_status" character varying DEFAULT 'approved', -- 'pending', 'approved', 'rejected'
    "moderated_by" uuid,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."live_chat_messages" ENABLE ROW LEVEL SECURITY;

-- Create whiteboard_sessions table for collaborative whiteboard
CREATE TABLE IF NOT EXISTS "public"."whiteboard_sessions" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "session_id" uuid NOT NULL,
    "title" character varying NOT NULL,
    "whiteboard_data" jsonb DEFAULT '{}'::jsonb,
    "is_active" boolean DEFAULT true,
    "created_by" uuid NOT NULL,
    "collaborators" jsonb DEFAULT '[]'::jsonb, -- array of user IDs with permissions
    "version" integer DEFAULT 1,
    "last_modified_by" uuid
);

ALTER TABLE "public"."whiteboard_sessions" ENABLE ROW LEVEL SECURITY;

-- Create live_session_analytics table for detailed analytics
CREATE TABLE IF NOT EXISTS "public"."live_session_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "session_id" uuid NOT NULL,
    "analytics_type" character varying NOT NULL, -- 'attendance', 'engagement', 'participation', 'technical'
    "data_point" character varying NOT NULL,
    "value" numeric,
    "metadata" jsonb DEFAULT '{}'::jsonb,
    "timestamp" timestamp with time zone DEFAULT now()
);

ALTER TABLE "public"."live_session_analytics" ENABLE ROW LEVEL SECURITY;

-- Create meeting_providers table for external meeting service configuration
CREATE TABLE IF NOT EXISTS "public"."meeting_providers" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "organization_id" uuid NOT NULL,
    "provider_name" character varying NOT NULL, -- 'bigbluebutton', 'jitsi', 'zoom', 'teams'
    "provider_config" jsonb NOT NULL DEFAULT '{}'::jsonb,
    "is_active" boolean DEFAULT true,
    "is_default" boolean DEFAULT false,
    "api_credentials" jsonb DEFAULT '{}'::jsonb, -- encrypted credentials
    "webhook_url" text,
    "rate_limits" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."meeting_providers" ENABLE ROW LEVEL SECURITY;

-- Add Primary Keys
ALTER TABLE "public"."live_sessions" ADD CONSTRAINT "live_sessions_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."live_session_participants" ADD CONSTRAINT "live_session_participants_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."breakout_rooms" ADD CONSTRAINT "breakout_rooms_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."live_polls" ADD CONSTRAINT "live_polls_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."live_poll_responses" ADD CONSTRAINT "live_poll_responses_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."live_chat_messages" ADD CONSTRAINT "live_chat_messages_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."whiteboard_sessions" ADD CONSTRAINT "whiteboard_sessions_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."live_session_analytics" ADD CONSTRAINT "live_session_analytics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."meeting_providers" ADD CONSTRAINT "meeting_providers_pkey" PRIMARY KEY ("id");

-- Add Foreign Key Constraints
ALTER TABLE "public"."live_sessions" ADD CONSTRAINT "live_sessions_batch_id_fkey" 
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_sessions" ADD CONSTRAINT "live_sessions_subject_id_fkey" 
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."live_sessions" ADD CONSTRAINT "live_sessions_chapter_id_fkey" 
    FOREIGN KEY ("chapter_id") REFERENCES "chapter"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."live_sessions" ADD CONSTRAINT "live_sessions_lesson_id_fkey" 
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."live_sessions" ADD CONSTRAINT "live_sessions_instructor_id_fkey" 
    FOREIGN KEY ("instructor_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_session_participants" ADD CONSTRAINT "live_session_participants_session_id_fkey" 
    FOREIGN KEY ("session_id") REFERENCES "live_sessions"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_session_participants" ADD CONSTRAINT "live_session_participants_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."breakout_rooms" ADD CONSTRAINT "breakout_rooms_session_id_fkey" 
    FOREIGN KEY ("session_id") REFERENCES "live_sessions"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_polls" ADD CONSTRAINT "live_polls_session_id_fkey" 
    FOREIGN KEY ("session_id") REFERENCES "live_sessions"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_polls" ADD CONSTRAINT "live_polls_created_by_fkey" 
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_poll_responses" ADD CONSTRAINT "live_poll_responses_poll_id_fkey" 
    FOREIGN KEY ("poll_id") REFERENCES "live_polls"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_poll_responses" ADD CONSTRAINT "live_poll_responses_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."live_chat_messages" ADD CONSTRAINT "live_chat_messages_session_id_fkey" 
    FOREIGN KEY ("session_id") REFERENCES "live_sessions"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_chat_messages" ADD CONSTRAINT "live_chat_messages_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_chat_messages" ADD CONSTRAINT "live_chat_messages_recipient_id_fkey" 
    FOREIGN KEY ("recipient_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."live_chat_messages" ADD CONSTRAINT "live_chat_messages_moderated_by_fkey" 
    FOREIGN KEY ("moderated_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."whiteboard_sessions" ADD CONSTRAINT "whiteboard_sessions_session_id_fkey" 
    FOREIGN KEY ("session_id") REFERENCES "live_sessions"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."whiteboard_sessions" ADD CONSTRAINT "whiteboard_sessions_created_by_fkey" 
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."whiteboard_sessions" ADD CONSTRAINT "whiteboard_sessions_last_modified_by_fkey" 
    FOREIGN KEY ("last_modified_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."live_session_analytics" ADD CONSTRAINT "live_session_analytics_session_id_fkey" 
    FOREIGN KEY ("session_id") REFERENCES "live_sessions"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."meeting_providers" ADD CONSTRAINT "meeting_providers_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

-- Add Indexes for Performance
CREATE INDEX IF NOT EXISTS "idx_live_sessions_batch" ON "public"."live_sessions"("batch_id");
CREATE INDEX IF NOT EXISTS "idx_live_sessions_instructor" ON "public"."live_sessions"("instructor_id");
CREATE INDEX IF NOT EXISTS "idx_live_sessions_status" ON "public"."live_sessions"("status");
CREATE INDEX IF NOT EXISTS "idx_live_sessions_scheduled_start" ON "public"."live_sessions"("scheduled_start");
CREATE INDEX IF NOT EXISTS "idx_live_sessions_type" ON "public"."live_sessions"("session_type");

CREATE INDEX IF NOT EXISTS "idx_live_session_participants_session" ON "public"."live_session_participants"("session_id");
CREATE INDEX IF NOT EXISTS "idx_live_session_participants_user" ON "public"."live_session_participants"("user_id");
CREATE INDEX IF NOT EXISTS "idx_live_session_participants_role" ON "public"."live_session_participants"("role");
CREATE INDEX IF NOT EXISTS "idx_live_session_participants_approval" ON "public"."live_session_participants"("approval_status");

CREATE INDEX IF NOT EXISTS "idx_breakout_rooms_session" ON "public"."breakout_rooms"("session_id");
CREATE INDEX IF NOT EXISTS "idx_breakout_rooms_active" ON "public"."breakout_rooms"("is_active");

CREATE INDEX IF NOT EXISTS "idx_live_polls_session" ON "public"."live_polls"("session_id");
CREATE INDEX IF NOT EXISTS "idx_live_polls_active" ON "public"."live_polls"("is_active");
CREATE INDEX IF NOT EXISTS "idx_live_polls_created_by" ON "public"."live_polls"("created_by");

CREATE INDEX IF NOT EXISTS "idx_live_poll_responses_poll" ON "public"."live_poll_responses"("poll_id");
CREATE INDEX IF NOT EXISTS "idx_live_poll_responses_user" ON "public"."live_poll_responses"("user_id");

CREATE INDEX IF NOT EXISTS "idx_live_chat_messages_session" ON "public"."live_chat_messages"("session_id");
CREATE INDEX IF NOT EXISTS "idx_live_chat_messages_user" ON "public"."live_chat_messages"("user_id");
CREATE INDEX IF NOT EXISTS "idx_live_chat_messages_created" ON "public"."live_chat_messages"("created_at");
CREATE INDEX IF NOT EXISTS "idx_live_chat_messages_moderation" ON "public"."live_chat_messages"("moderation_status");

CREATE INDEX IF NOT EXISTS "idx_whiteboard_sessions_session" ON "public"."whiteboard_sessions"("session_id");
CREATE INDEX IF NOT EXISTS "idx_whiteboard_sessions_active" ON "public"."whiteboard_sessions"("is_active");

CREATE INDEX IF NOT EXISTS "idx_live_session_analytics_session" ON "public"."live_session_analytics"("session_id");
CREATE INDEX IF NOT EXISTS "idx_live_session_analytics_type" ON "public"."live_session_analytics"("analytics_type");
CREATE INDEX IF NOT EXISTS "idx_live_session_analytics_timestamp" ON "public"."live_session_analytics"("timestamp");

CREATE INDEX IF NOT EXISTS "idx_meeting_providers_org" ON "public"."meeting_providers"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_meeting_providers_active" ON "public"."meeting_providers"("is_active");
CREATE INDEX IF NOT EXISTS "idx_meeting_providers_default" ON "public"."meeting_providers"("is_default");

-- Add Unique Constraints
ALTER TABLE "public"."live_session_participants" ADD CONSTRAINT "unique_session_user_participant"
    UNIQUE ("session_id", "user_id");

ALTER TABLE "public"."breakout_rooms" ADD CONSTRAINT "unique_session_room_number"
    UNIQUE ("session_id", "room_number");

ALTER TABLE "public"."meeting_providers" ADD CONSTRAINT "unique_org_provider_name"
    UNIQUE ("organization_id", "provider_name");

-- Create Functions for Live Session Management

-- Function to create a live session with automatic participant enrollment
CREATE OR REPLACE FUNCTION create_live_session(
    p_title character varying,
    p_description text,
    p_batch_id uuid,
    p_instructor_id uuid,
    p_scheduled_start timestamp with time zone,
    p_scheduled_end timestamp with time zone,
    p_session_type character varying DEFAULT 'class',
    p_auto_enroll_batch boolean DEFAULT true
)
RETURNS uuid AS $$
DECLARE
    session_id uuid;
    student_record record;
BEGIN
    -- Create the live session
    INSERT INTO live_sessions (
        title, description, batch_id, instructor_id,
        scheduled_start, scheduled_end, session_type
    ) VALUES (
        p_title, p_description, p_batch_id, p_instructor_id,
        p_scheduled_start, p_scheduled_end, p_session_type
    ) RETURNING id INTO session_id;

    -- Auto-enroll instructor
    INSERT INTO live_session_participants (
        session_id, user_id, role, approval_status
    ) VALUES (
        session_id, p_instructor_id, 'instructor', 'auto_approved'
    );

    -- Auto-enroll batch students if requested
    IF p_auto_enroll_batch THEN
        FOR student_record IN
            SELECT bs.student_id
            FROM batch_student bs
            WHERE bs.batch_id = p_batch_id
            AND bs.is_active = true
        LOOP
            INSERT INTO live_session_participants (
                session_id, user_id, role, approval_status
            ) VALUES (
                session_id, student_record.student_id, 'student', 'auto_approved'
            ) ON CONFLICT (session_id, user_id) DO NOTHING;
        END LOOP;
    END IF;

    RETURN session_id;
END;
$$ LANGUAGE plpgsql;

-- Function to join a live session with security checks
CREATE OR REPLACE FUNCTION join_live_session(
    p_session_id uuid,
    p_user_id uuid,
    p_device_fingerprint text DEFAULT NULL,
    p_ip_address inet DEFAULT NULL,
    p_user_agent text DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
    session_record record;
    participant_record record;
    result jsonb;
BEGIN
    -- Get session details
    SELECT * INTO session_record
    FROM live_sessions
    WHERE id = p_session_id;

    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Session not found');
    END IF;

    -- Check if session is live or scheduled to start soon
    IF session_record.status NOT IN ('live', 'scheduled') THEN
        RETURN jsonb_build_object('success', false, 'error', 'Session is not available');
    END IF;

    -- Get participant record
    SELECT * INTO participant_record
    FROM live_session_participants
    WHERE session_id = p_session_id AND user_id = p_user_id;

    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'error', 'Not enrolled in this session');
    END IF;

    -- Check approval status
    IF participant_record.approval_status NOT IN ('approved', 'auto_approved') THEN
        RETURN jsonb_build_object('success', false, 'error', 'Waiting for approval');
    END IF;

    -- Update participant join time and device info
    UPDATE live_session_participants
    SET
        joined_at = COALESCE(joined_at, now()),
        device_fingerprint = COALESCE(p_device_fingerprint, device_fingerprint),
        ip_address = COALESCE(p_ip_address, ip_address),
        user_agent = COALESCE(p_user_agent, user_agent),
        updated_at = now()
    WHERE session_id = p_session_id AND user_id = p_user_id;

    -- Update session status to live if instructor joins
    IF participant_record.role = 'instructor' AND session_record.status = 'scheduled' THEN
        UPDATE live_sessions
        SET status = 'live', actual_start = now()
        WHERE id = p_session_id;
    END IF;

    result := jsonb_build_object(
        'success', true,
        'session_id', p_session_id,
        'role', participant_record.role,
        'meeting_config', session_record.meeting_config
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to track live session analytics
CREATE OR REPLACE FUNCTION track_session_analytics(
    p_session_id uuid,
    p_analytics_type character varying,
    p_data_point character varying,
    p_value numeric,
    p_metadata jsonb DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    INSERT INTO live_session_analytics (
        session_id, analytics_type, data_point, value, metadata
    ) VALUES (
        p_session_id, p_analytics_type, p_data_point, p_value, COALESCE(p_metadata, '{}'::jsonb)
    );
END;
$$ LANGUAGE plpgsql;
