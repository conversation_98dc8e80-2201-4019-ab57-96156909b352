<script lang="ts">
  import { onMount } from 'svelte';
  import { writable } from 'svelte/store';
  import type { SecurityPolicy } from '$lib/utils/types/security';
  import { securityPolicyService } from '$lib/utils/services/security';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import { PageBody, PageNav } from '$lib/components/Page';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Security, 
    Add, 
    Edit, 
    Save, 
    Reset,
    CheckmarkFilled,
    Warning
  } from 'carbon-icons-svelte';
  import { Tabs, Tab, TabContent } from 'carbon-components-svelte';

  let policies = writable<SecurityPolicy[]>([]);
  let loading = true;
  let saving = false;
  let error: string | null = null;
  let selectedTab = 0;

  // Policy settings
  let devicePolicy: Partial<SecurityPolicy> = {
    policy_type: 'device_management',
    settings: {
      max_devices_per_user: 3,
      device_approval_required: true,
      auto_approve_trusted_devices: false,
      device_trust_threshold: 80,
      session_timeout_minutes: 480,
      concurrent_session_limit: 1
    }
  };

  let videoPolicy: Partial<SecurityPolicy> = {
    policy_type: 'video_protection',
    settings: {
      drm_enabled: true,
      watermark_enabled: true,
      screenshot_protection: true,
      recording_protection: true,
      right_click_disabled: true,
      developer_tools_blocked: true
    }
  };

  let examPolicy: Partial<SecurityPolicy> = {
    policy_type: 'exam_security',
    settings: {
      lockdown_browser_required: true,
      fullscreen_enforcement: true,
      tab_switching_blocked: true,
      copy_paste_disabled: true,
      proctoring_enabled: false,
      webcam_required: false,
      microphone_required: false
    }
  };

  let generalPolicy: Partial<SecurityPolicy> = {
    policy_type: 'general',
    settings: {
      ip_whitelist: [],
      geo_restrictions: [],
      time_restrictions: {
        start_time: '00:00',
        end_time: '23:59',
        timezone: 'UTC'
      },
      suspicious_activity_threshold: 5,
      auto_block_suspicious_users: false
    }
  };

  const tabs = [
    { label: $t('security.device_management', { default: 'Device Management' }), value: 'device' },
    { label: $t('security.video_protection', { default: 'Video Protection' }), value: 'video' },
    { label: $t('security.exam_security', { default: 'Exam Security' }), value: 'exam' },
    { label: $t('security.general_settings', { default: 'General Settings' }), value: 'general' }
  ];

  $: organizationId = $globalStore.org?.id;
  $: isAdmin = $globalStore.isOrgAdmin;

  onMount(async () => {
    if (isAdmin && organizationId) {
      await loadPolicies();
    }
  });

  async function loadPolicies() {
    try {
      loading = true;
      error = null;

      const orgPolicies = await securityPolicyService.getOrganizationPolicies(organizationId);
      policies.set(orgPolicies);

      // Load existing policies into forms
      for (const policy of orgPolicies) {
        switch (policy.policy_type) {
          case 'device_management':
            devicePolicy = { ...devicePolicy, ...policy };
            break;
          case 'video_protection':
            videoPolicy = { ...videoPolicy, ...policy };
            break;
          case 'exam_security':
            examPolicy = { ...examPolicy, ...policy };
            break;
          case 'general':
            generalPolicy = { ...generalPolicy, ...policy };
            break;
        }
      }

    } catch (err) {
      console.error('Error loading security policies:', err);
      error = err.message || 'Failed to load security policies';
    } finally {
      loading = false;
    }
  }

  async function savePolicy(policyType: string) {
    if (!organizationId || !isAdmin) return;

    try {
      saving = true;
      error = null;

      let policyToSave: Partial<SecurityPolicy>;
      switch (policyType) {
        case 'device_management':
          policyToSave = devicePolicy;
          break;
        case 'video_protection':
          policyToSave = videoPolicy;
          break;
        case 'exam_security':
          policyToSave = examPolicy;
          break;
        case 'general':
          policyToSave = generalPolicy;
          break;
        default:
          throw new Error('Invalid policy type');
      }

      const existingPolicy = $policies.find(p => p.policy_type === policyType);
      
      if (existingPolicy) {
        // Update existing policy
        await securityPolicyService.updatePolicy(existingPolicy.id, {
          settings: policyToSave.settings,
          updated_by: $globalStore.user?.id
        });
      } else {
        // Create new policy
        await securityPolicyService.createPolicy({
          organization_id: organizationId,
          policy_name: `${policyType}_policy`,
          policy_type: policyType as any,
          settings: policyToSave.settings || {},
          is_active: true,
          created_by: $globalStore.user?.id
        });
      }

      await loadPolicies(); // Refresh policies

    } catch (err) {
      console.error('Error saving security policy:', err);
      error = err.message || 'Failed to save security policy';
    } finally {
      saving = false;
    }
  }

  function resetPolicy(policyType: string) {
    switch (policyType) {
      case 'device_management':
        devicePolicy = {
          policy_type: 'device_management',
          settings: {
            max_devices_per_user: 3,
            device_approval_required: true,
            auto_approve_trusted_devices: false,
            device_trust_threshold: 80,
            session_timeout_minutes: 480,
            concurrent_session_limit: 1
          }
        };
        break;
      case 'video_protection':
        videoPolicy = {
          policy_type: 'video_protection',
          settings: {
            drm_enabled: true,
            watermark_enabled: true,
            screenshot_protection: true,
            recording_protection: true,
            right_click_disabled: true,
            developer_tools_blocked: true
          }
        };
        break;
      case 'exam_security':
        examPolicy = {
          policy_type: 'exam_security',
          settings: {
            lockdown_browser_required: true,
            fullscreen_enforcement: true,
            tab_switching_blocked: true,
            copy_paste_disabled: true,
            proctoring_enabled: false,
            webcam_required: false,
            microphone_required: false
          }
        };
        break;
      case 'general':
        generalPolicy = {
          policy_type: 'general',
          settings: {
            ip_whitelist: [],
            geo_restrictions: [],
            time_restrictions: {
              start_time: '00:00',
              end_time: '23:59',
              timezone: 'UTC'
            },
            suspicious_activity_threshold: 5,
            auto_block_suspicious_users: false
          }
        };
        break;
    }
  }
</script>

<svelte:head>
  <title>{$t('security.policies', { default: 'Security Policies' })} - ClassroomIO</title>
</svelte:head>

{#if !isAdmin}
  <PageBody>
    <Box className="text-center py-12">
      <Security size={48} class="text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {$t('security.access_denied', { default: 'Access Denied' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {$t('security.admin_required', { default: 'Administrator privileges required' })}
      </p>
    </Box>
  </PageBody>

{:else}
  <PageNav title={$t('security.policies', { default: 'Security Policies' })}>
    <div class="flex items-center space-x-4">
      <!-- Add any navigation items here -->
    </div>
  </PageNav>

  <PageBody>
    {#if loading}
      <Box className="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {$t('security.loading_policies', { default: 'Loading Security Policies' })}
        </h3>
      </Box>

    {:else if error}
      <Box className="text-center py-12">
        <Warning size={48} class="text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {$t('security.error', { default: 'Error Loading Policies' })}
        </h3>
        <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
        <PrimaryButton onClick={loadPolicies}>
          {$t('security.retry', { default: 'Retry' })}
        </PrimaryButton>
      </Box>

    {:else}
      <div class="mb-6">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {$t('security.configure_policies', { default: 'Configure Security Policies' })}
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          {$t('security.policies_desc', { default: 'Manage security settings for your organization' })}
        </p>
      </div>

      <Tabs bind:selected={selectedTab}>
        {#each tabs as tab, index}
          <Tab label={tab.label} />
        {/each}
        
        <svelte:fragment slot="content">
          <!-- Device Management Tab -->
          <TabContent>
            {#if selectedTab === 0}
              <Box>
                <div class="space-y-6">
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {$t('security.device_settings', { default: 'Device Management Settings' })}
                    </h3>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {$t('security.max_devices', { default: 'Max Devices per User' })}
                      </label>
                      <input
                        type="number"
                        bind:value={devicePolicy.settings.max_devices_per_user}
                        min="1"
                        max="10"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {$t('security.session_timeout', { default: 'Session Timeout (minutes)' })}
                      </label>
                      <input
                        type="number"
                        bind:value={devicePolicy.settings.session_timeout_minutes}
                        min="30"
                        max="1440"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {$t('security.trust_threshold', { default: 'Device Trust Threshold (%)' })}
                      </label>
                      <input
                        type="number"
                        bind:value={devicePolicy.settings.device_trust_threshold}
                        min="0"
                        max="100"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {$t('security.concurrent_sessions', { default: 'Concurrent Session Limit' })}
                      </label>
                      <input
                        type="number"
                        bind:value={devicePolicy.settings.concurrent_session_limit}
                        min="1"
                        max="5"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    </div>
                  </div>

                  <div class="space-y-4">
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={devicePolicy.settings.device_approval_required}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.require_approval', { default: 'Require device approval' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={devicePolicy.settings.auto_approve_trusted_devices}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.auto_approve_trusted', { default: 'Auto-approve trusted devices' })}
                      </span>
                    </label>
                  </div>

                  <div class="flex items-center space-x-4">
                    <PrimaryButton
                      variant={VARIANTS.CONTAINED}
                      onClick={() => savePolicy('device_management')}
                      disabled={saving}
                    >
                      <Save size={20} class="mr-2" />
                      {$t('security.save_policy', { default: 'Save Policy' })}
                    </PrimaryButton>

                    <PrimaryButton
                      variant={VARIANTS.OUTLINED}
                      onClick={() => resetPolicy('device_management')}
                    >
                      <Reset size={20} class="mr-2" />
                      {$t('security.reset', { default: 'Reset' })}
                    </PrimaryButton>
                  </div>
                </div>
              </Box>
            {/if}
          </TabContent>

          <!-- Video Protection Tab -->
          <TabContent>
            {#if selectedTab === 1}
              <Box>
                <div class="space-y-6">
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {$t('security.video_protection_settings', { default: 'Video Protection Settings' })}
                    </h3>
                  </div>

                  <div class="space-y-4">
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={videoPolicy.settings.drm_enabled}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.enable_drm', { default: 'Enable DRM protection' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={videoPolicy.settings.watermark_enabled}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.enable_watermark', { default: 'Enable video watermarking' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={videoPolicy.settings.screenshot_protection}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.screenshot_protection', { default: 'Block screenshot attempts' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={videoPolicy.settings.recording_protection}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.recording_protection', { default: 'Block screen recording' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={videoPolicy.settings.right_click_disabled}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.disable_right_click', { default: 'Disable right-click menu' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={videoPolicy.settings.developer_tools_blocked}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.block_dev_tools', { default: 'Block developer tools' })}
                      </span>
                    </label>
                  </div>

                  <div class="flex items-center space-x-4">
                    <PrimaryButton
                      variant={VARIANTS.CONTAINED}
                      onClick={() => savePolicy('video_protection')}
                      disabled={saving}
                    >
                      <Save size={20} class="mr-2" />
                      {$t('security.save_policy', { default: 'Save Policy' })}
                    </PrimaryButton>

                    <PrimaryButton
                      variant={VARIANTS.OUTLINED}
                      onClick={() => resetPolicy('video_protection')}
                    >
                      <Reset size={20} class="mr-2" />
                      {$t('security.reset', { default: 'Reset' })}
                    </PrimaryButton>
                  </div>
                </div>
              </Box>
            {/if}
          </TabContent>

          <!-- Exam Security Tab -->
          <TabContent>
            {#if selectedTab === 2}
              <Box>
                <div class="space-y-6">
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {$t('security.exam_security_settings', { default: 'Exam Security Settings' })}
                    </h3>
                  </div>

                  <div class="space-y-4">
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={examPolicy.settings.lockdown_browser_required}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.lockdown_browser', { default: 'Require lockdown browser mode' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={examPolicy.settings.fullscreen_enforcement}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.fullscreen_enforcement', { default: 'Enforce fullscreen mode' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={examPolicy.settings.tab_switching_blocked}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.block_tab_switching', { default: 'Block tab switching' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={examPolicy.settings.copy_paste_disabled}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.disable_copy_paste', { default: 'Disable copy/paste' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={examPolicy.settings.proctoring_enabled}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.enable_proctoring', { default: 'Enable proctoring' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={examPolicy.settings.webcam_required}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.require_webcam', { default: 'Require webcam access' })}
                      </span>
                    </label>

                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={examPolicy.settings.microphone_required}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.require_microphone', { default: 'Require microphone access' })}
                      </span>
                    </label>
                  </div>

                  <div class="flex items-center space-x-4">
                    <PrimaryButton
                      variant={VARIANTS.CONTAINED}
                      onClick={() => savePolicy('exam_security')}
                      disabled={saving}
                    >
                      <Save size={20} class="mr-2" />
                      {$t('security.save_policy', { default: 'Save Policy' })}
                    </PrimaryButton>

                    <PrimaryButton
                      variant={VARIANTS.OUTLINED}
                      onClick={() => resetPolicy('exam_security')}
                    >
                      <Reset size={20} class="mr-2" />
                      {$t('security.reset', { default: 'Reset' })}
                    </PrimaryButton>
                  </div>
                </div>
              </Box>
            {/if}
          </TabContent>

          <!-- General Settings Tab -->
          <TabContent>
            {#if selectedTab === 3}
              <Box>
                <div class="space-y-6">
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {$t('security.general_settings', { default: 'General Security Settings' })}
                    </h3>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {$t('security.suspicious_threshold', { default: 'Suspicious Activity Threshold' })}
                      </label>
                      <input
                        type="number"
                        bind:value={generalPolicy.settings.suspicious_activity_threshold}
                        min="1"
                        max="20"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    </div>
                  </div>

                  <div class="space-y-4">
                    <label class="flex items-center">
                      <input
                        type="checkbox"
                        bind:checked={generalPolicy.settings.auto_block_suspicious_users}
                        class="mr-3"
                      />
                      <span class="text-gray-900 dark:text-white">
                        {$t('security.auto_block_suspicious', { default: 'Auto-block suspicious users' })}
                      </span>
                    </label>
                  </div>

                  <div class="flex items-center space-x-4">
                    <PrimaryButton
                      variant={VARIANTS.CONTAINED}
                      onClick={() => savePolicy('general')}
                      disabled={saving}
                    >
                      <Save size={20} class="mr-2" />
                      {$t('security.save_policy', { default: 'Save Policy' })}
                    </PrimaryButton>

                    <PrimaryButton
                      variant={VARIANTS.OUTLINED}
                      onClick={() => resetPolicy('general')}
                    >
                      <Reset size={20} class="mr-2" />
                      {$t('security.reset', { default: 'Reset' })}
                    </PrimaryButton>
                  </div>
                </div>
              </Box>
            {/if}
          </TabContent>
        </svelte:fragment>
      </Tabs>
    {/if}
  </PageBody>
{/if}
