// Global Teardown for Playwright E2E Tests
import { chromium, FullConfig } from '@playwright/test';
import { promises as fs } from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test global teardown...');
  
  const { baseURL } = config.projects[0].use;
  
  try {
    // Clean up test data
    await cleanupTestData(baseURL);
    
    // Clean up authentication files
    await cleanupAuthFiles();
    
    // Clean up temporary files
    await cleanupTempFiles();
    
    console.log('✅ E2E test global teardown completed');
    
  } catch (error) {
    console.error('❌ E2E test global teardown failed:', error);
    // Don't throw error to avoid failing the test run
  }
}

async function cleanupTestData(baseURL?: string) {
  console.log('🗑️ Cleaning up test data...');
  
  // Launch browser for cleanup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    await page.goto(baseURL || 'http://localhost:5173');
    
    // Remove test data from localStorage
    await page.evaluate(() => {
      localStorage.removeItem('e2e-test-data');
      localStorage.clear();
      sessionStorage.clear();
    });
    
    // In a real implementation, this would make API calls to clean up test data
    // For example:
    // - Delete test users
    // - Delete test organizations
    // - Delete test courses and batches
    // - Clean up uploaded files
    
    console.log('✅ Test data cleanup completed');
    
  } catch (error) {
    console.error('❌ Failed to cleanup test data:', error);
  } finally {
    await browser.close();
  }
}

async function cleanupAuthFiles() {
  console.log('🔐 Cleaning up authentication files...');
  
  const authDir = path.join(__dirname, 'auth');
  
  try {
    // Check if auth directory exists
    await fs.access(authDir);
    
    // Remove all auth files
    const authFiles = await fs.readdir(authDir);
    for (const file of authFiles) {
      if (file.endsWith('.json')) {
        await fs.unlink(path.join(authDir, file));
        console.log(`🗑️ Removed auth file: ${file}`);
      }
    }
    
    console.log('✅ Authentication files cleanup completed');
    
  } catch (error) {
    // Auth directory might not exist, which is fine
    console.log('ℹ️ No authentication files to clean up');
  }
}

async function cleanupTempFiles() {
  console.log('🗑️ Cleaning up temporary files...');
  
  const tempDirs = [
    'test-results/e2e-artifacts',
    'playwright-report',
    'test-results/screenshots',
    'test-results/videos'
  ];
  
  for (const dir of tempDirs) {
    try {
      await fs.access(dir);
      
      // Remove old files (keep recent ones for debugging)
      const files = await fs.readdir(dir);
      const now = Date.now();
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          console.log(`🗑️ Removed old temp file: ${file}`);
        }
      }
      
    } catch (error) {
      // Directory might not exist, which is fine
      continue;
    }
  }
  
  console.log('✅ Temporary files cleanup completed');
}

export default globalTeardown;
