import { Steps, Callout } from 'nextra/components';

# Creating Your First Course

Below is a video guiding you through the process of creating your inaugural course. Should you have any inquiries, feel free to drop a comment in the YouTube comment section, reach out to us on **[Discord](https://discord.gg/BSaEzcfu)**, or send a DM on **[Twitter](https://x.com/rotimi_best?s=20)**.

<div className="nx-w-full nx-flex nx-items-center nx-justify-center">
  <iframe
    width="560"
    height="315"
    src="https://www.youtube.com/embed/ztiZ6vo2gg0?si=v_UUL1YUPyDQ4_pp"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    allowfullscreen
    className="nx-my-4 nx-rounded-md"
  ></iframe>
</div>

## Procedure

Initiating a course on ClassroomIO is a straightforward process, involving the following steps:

1. **Accessing the Course Section:** Navigate to the admin dashboard and select "Courses" from the sidebar menu.

2. **Initiating Course Creation:** Once on the Courses page, locate and click the "Create Course" button situated in the top right corner of the interface.

3. **Providing Course Details:** Complete the necessary fields, including the course title and description. Optionally, utilize the wand icon next to the description field for AI-assisted generation.

Upon completion, click **Finish** to save and automatically redirect to the course page.

## Next Steps

Following course creation, consider the following enhancements:

- Adding Lessons
- Including Notes
- Generating Notes with AI
- Embedding Slides
- Embedding YouTube Videos

## Adding Lessons

Adding lessons to a course is achieved through the following steps:

<Steps>
 ### Click the Lesson Link

On the course page, click "Lessons" in the sidebar to access the lessons page. Click the "Add" button in the top right corner to open the Add Lesson modal.

> Alternatively, add a lesson on-the-go by clicking the '+' icon next to the lesson link in the sidebar.

### Fill in the Lesson Details

Provide the lesson title, select a tutor, date, and optionally lock the lesson or notes.

<Callout type="info" emoji="💡">
  Locking the lesson renders it inaccessible to students upon course publication.
</Callout>

Save your changes to be redirected to the lesson page for material addition.

</Steps>

For each lesson, you may furnish materials such as notes, slides, and videos by clicking the "Edit" button on the top right corner of the **Materials** tab.

## Adding Notes

Notes can be added to a lesson manually or via AI assistance:

1. Click "Edit" while on the materials tab of the lesson page.
2. Navigate to the notes tab if not already selected.
3. Input your lesson content using the provided text editor or utilize AI assistance by clicking the **magic wand** icon.
4. Click "Done" in the top right corner to save your note.

### Generating Notes with AI

The assistive AI can generate notes based on your lesson topic, offering options such as generating an outline, notes, or activities, streamlining the note creation process.

## Embedding Slides

To embed slides, navigate to the slides tab and input the link to the slides. Click "Done" to save your slides.

## Embedding Videos

Embedding videos is simple: select the "Videos" tab to open the modal for adding videos. From the "Add By" options in the modal, select the "YouTube Link" tab. Input the link to the YouTube video and click the "Add Video" button. Once added, the video is automatically included in your materials. You can delete the video using the trash icon.

<Callout emoji="🚨">
  Please note that for now, we do not support adding videos from local sources.
</Callout>

## Publishing a course

To publish a course, navigate to the settings section in the sidebar of the course page. Scroll down to find the "Publish Course" option and toggle it to "Publish". Finally, click the "Save Changes" button to ensure your modifications are saved.
