{"compilerOptions": {"css": "external", "dev": true, "hydratable": true}, "configFile": false, "extensions": [".svelte", ".md"], "preprocess": [{"markup": "async markup({ content, filename }) {\n            var _a, _b, _c;\n            const dependencies = [];\n            let code = content;\n            let map;\n            let attributes;\n            let toString;\n            for (const pp of preprocessors) {\n                const processed = await (0, compiler_1.preprocess)(code, pp, { filename });\n                if (processed && processed.dependencies) {\n                    dependencies.push(...processed.dependencies);\n                }\n                code = processed ? processed.code : code;\n                map = (_a = processed.map) !== null && _a !== void 0 ? _a : map;\n                attributes = (_b = processed.attributes) !== null && _b !== void 0 ? _b : attributes;\n                toString = (_c = processed.toString) !== null && _c !== void 0 ? _c : toString;\n            }\n            return {\n                code,\n                dependencies,\n                map,\n                attributes,\n                toString,\n            };\n        }"}, {"script": "({ content, filename }) => {\n\t\tif (!filename) return;\n\n\t\tconst basename = path.basename(filename);\n\t\tif (basename.startsWith('+page.') || basename.startsWith('+layout.')) {\n\t\t\tconst match = content.match(options_regex);\n\t\t\tif (match) {\n\t\t\t\tconst fixed = basename.replace('.svelte', '(.server).js/ts');\n\n\t\t\t\tconst message =\n\t\t\t\t\t`\\n${colors.bold().red(path.relative('.', filename))}\\n` +\n\t\t\t\t\t`\\`${match[1]}\\` will be ignored — move it to ${fixed} instead. See https://svelte.dev/docs/kit/page-options for more information.`;\n\n\t\t\t\tif (!warned.has(message)) {\n\t\t\t\t\tconsole.log(message);\n\t\t\t\t\twarned.add(message);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}", "markup": "({ content, filename }) => {\n\t\tif (!filename) return;\n\n\t\tconst basename = path.basename(filename);\n\t\tconst has_children =\n\t\t\tcontent.includes('<slot') || (isSvelte5Plus() && content.includes('{@render'));\n\n\t\tif (basename.startsWith('+layout.') && !has_children) {\n\t\t\tconst message =\n\t\t\t\t`\\n${colors.bold().red(path.relative('.', filename))}\\n` +\n\t\t\t\t`\\`<slot />\\`${isSvelte5Plus() ? ' or `{@render ...}` tag' : ''}` +\n\t\t\t\t' missing — inner content will not be rendered';\n\n\t\t\tif (!warned.has(message)) {\n\t\t\t\tconsole.log(message);\n\t\t\t\twarned.add(message);\n\t\t\t}\n\t\t}\n\t}"}]}