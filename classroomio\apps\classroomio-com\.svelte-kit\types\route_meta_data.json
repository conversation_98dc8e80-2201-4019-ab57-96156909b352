{"/": ["src/routes/+layout.ts", "src/routes/+layout.ts"], "/api/blog": ["src/routes/api/blog/+server.ts"], "/api/contact": ["src/routes/api/contact/+server.ts"], "/blog": ["src/routes/blog/+page.ts", "src/routes/+layout.ts"], "/blog/[slug]": ["src/routes/blog/[slug]/+page.ts", "src/routes/+layout.ts"], "/(marketing)/bootcamps": ["src/routes/+layout.ts"], "/contact": ["src/routes/contact/+layout.ts", "src/routes/+layout.ts", "src/routes/contact/+layout.ts", "src/routes/+layout.ts"], "/(marketing)/customer-education": ["src/routes/+layout.ts"], "/(redirects)/demo": ["src/routes/(redirects)/demo/+page.server.ts", "src/routes/+layout.ts"], "/(redirects)/devanddesign": ["src/routes/(redirects)/devanddesign/+page.server.ts", "src/routes/+layout.ts"], "/(redirects)/discord": ["src/routes/(redirects)/discord/+page.server.ts", "src/routes/+layout.ts"], "/(redirects)/docs": ["src/routes/+layout.ts", "src/routes/+layout.ts"], "/(marketing)/employee-training": ["src/routes/+layout.ts"], "/(redirects)/github": ["src/routes/(redirects)/github/+page.server.ts", "src/routes/+layout.ts"], "/(redirects)/learn": ["src/routes/(redirects)/learn/+page.server.ts", "src/routes/+layout.ts"], "/(redirects)/new": ["src/routes/(redirects)/new/+page.server.ts", "src/routes/+layout.ts"], "/oss-friends": ["src/routes/oss-friends/+page.ts", "src/routes/+layout.ts"], "/(redirects)/ph": ["src/routes/(redirects)/ph/+page.server.ts", "src/routes/+layout.ts"], "/pricing": ["src/routes/pricing/+layout.ts", "src/routes/+layout.ts", "src/routes/pricing/+layout.ts", "src/routes/+layout.ts"], "/(redirects)/privacy": ["src/routes/(redirects)/privacy/+page.server.ts", "src/routes/+layout.ts"], "/roadmap": ["src/routes/+layout.ts"], "/(redirects)/signup": ["src/routes/(redirects)/signup/+page.server.ts", "src/routes/+layout.ts"], "/teach": ["src/routes/teach/+layout.ts", "src/routes/+layout.ts", "src/routes/teach/+layout.ts", "src/routes/+layout.ts"], "/teach/register": ["src/routes/teach/register/+page.server.ts", "src/routes/teach/+layout.ts", "src/routes/+layout.ts"], "/tools": ["src/routes/tools/+layout.ts", "src/routes/+layout.ts", "src/routes/tools/+layout.ts", "src/routes/+layout.ts"], "/tools/name-picker": ["src/routes/tools/+layout.ts", "src/routes/+layout.ts"], "/tools/pomodoro": ["src/routes/tools/+layout.ts", "src/routes/+layout.ts"], "/tools/progress": ["src/routes/tools/+layout.ts", "src/routes/+layout.ts"], "/tools/stopwatch": ["src/routes/tools/+layout.ts", "src/routes/+layout.ts"], "/tools/tic-tac-toe": ["src/routes/tools/+layout.ts", "src/routes/+layout.ts"], "/(redirects)/tos": ["src/routes/(redirects)/tos/+page.server.ts", "src/routes/+layout.ts"], "/(redirects)/waec": ["src/routes/(redirects)/waec/+page.server.ts", "src/routes/+layout.ts"]}