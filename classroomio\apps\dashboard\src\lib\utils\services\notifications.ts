// Notification Services for Educational Platform
// Date: 2025-06-30

import { supabase } from '$lib/utils/functions/supabase';
import type { 
  Notification, 
  NotificationTemplate, 
  ExternalIntegration 
} from '$lib/utils/types/communication';

// Notification Service
export const notificationService = {
  // Send notification
  async sendNotification(notificationData: Omit<Notification, 'id' | 'created_at' | 'is_read' | 'is_sent' | 'delivery_status' | 'retry_count'>): Promise<string> {
    const { data, error } = await supabase
      .rpc('send_notification', {
        p_user_id: notificationData.user_id,
        p_notification_type: notificationData.notification_type,
        p_channel: notificationData.channel,
        p_title: notificationData.title,
        p_content: notificationData.content,
        p_action_url: notificationData.action_url,
        p_template_id: notificationData.template_id,
        p_related_entity_type: notificationData.related_entity_type,
        p_related_entity_id: notificationData.related_entity_id,
        p_scheduled_for: notificationData.scheduled_for
      });

    if (error) throw error;
    return data;
  },

  // Get user notifications
  async getUserNotifications(userId: string, filters?: {
    channel?: string;
    is_read?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<Notification[]> {
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId);

    if (filters?.channel) query = query.eq('channel', filters.channel);
    if (filters?.is_read !== undefined) query = query.eq('is_read', filters.is_read);

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(filters?.limit || 50)
      .range(filters?.offset || 0, (filters?.offset || 0) + (filters?.limit || 50) - 1);

    if (error) throw error;
    return data || [];
  },

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({
        is_read: true,
        read_at: new Date().toISOString()
      })
      .eq('id', notificationId);

    if (error) throw error;
  },

  // Mark all notifications as read
  async markAllAsRead(userId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({
        is_read: true,
        read_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) throw error;
  },

  // Get unread count
  async getUnreadCount(userId: string): Promise<number> {
    const { count, error } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) throw error;
    return count || 0;
  },

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) throw error;
  },

  // Bulk send notifications
  async bulkSendNotifications(notifications: Array<Omit<Notification, 'id' | 'created_at' | 'is_read' | 'is_sent' | 'delivery_status' | 'retry_count'>>): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .insert(notifications.map(notification => ({
        ...notification,
        delivery_status: 'pending'
      })));

    if (error) throw error;
  }
};

// Notification Template Service
export const notificationTemplateService = {
  // Create template
  async createTemplate(templateData: Omit<NotificationTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<NotificationTemplate> {
    const { data, error } = await supabase
      .from('notification_templates')
      .insert(templateData)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get organization templates
  async getOrganizationTemplates(organizationId: string, templateType?: string): Promise<NotificationTemplate[]> {
    let query = supabase
      .from('notification_templates')
      .select('*')
      .eq('organization_id', organizationId);

    if (templateType) {
      query = query.eq('template_type', templateType);
    }

    const { data, error } = await query.order('name', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Update template
  async updateTemplate(templateId: string, updates: Partial<NotificationTemplate>): Promise<NotificationTemplate> {
    const { data, error } = await supabase
      .from('notification_templates')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', templateId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete template
  async deleteTemplate(templateId: string): Promise<void> {
    const { error } = await supabase
      .from('notification_templates')
      .delete()
      .eq('id', templateId);

    if (error) throw error;
  },

  // Render template with variables
  renderTemplate(template: NotificationTemplate, variables: Record<string, any>): { subject?: string; content: string } {
    let renderedContent = template.content;
    let renderedSubject = template.subject;

    // Replace variables in content
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      renderedContent = renderedContent.replace(new RegExp(placeholder, 'g'), String(value));
      if (renderedSubject) {
        renderedSubject = renderedSubject.replace(new RegExp(placeholder, 'g'), String(value));
      }
    });

    return {
      subject: renderedSubject,
      content: renderedContent
    };
  }
};

// External Integration Service
export const externalIntegrationService = {
  // Create integration
  async createIntegration(integrationData: Omit<ExternalIntegration, 'id' | 'created_at' | 'updated_at' | 'sync_status' | 'usage_stats'>): Promise<ExternalIntegration> {
    const { data, error } = await supabase
      .from('external_integrations')
      .insert({
        ...integrationData,
        sync_status: 'connected',
        usage_stats: {
          messages_sent: 0,
          messages_received: 0
        }
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get organization integrations
  async getOrganizationIntegrations(organizationId: string, integrationType?: string): Promise<ExternalIntegration[]> {
    let query = supabase
      .from('external_integrations')
      .select('*')
      .eq('organization_id', organizationId);

    if (integrationType) {
      query = query.eq('integration_type', integrationType);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Update integration
  async updateIntegration(integrationId: string, updates: Partial<ExternalIntegration>): Promise<ExternalIntegration> {
    const { data, error } = await supabase
      .from('external_integrations')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', integrationId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Test integration connection
  async testConnection(integrationId: string): Promise<{ success: boolean; message: string }> {
    // This would typically make an API call to test the integration
    // For now, we'll just update the sync status
    try {
      await this.updateIntegration(integrationId, {
        sync_status: 'connected',
        last_sync: new Date().toISOString(),
        error_message: null
      });

      return { success: true, message: 'Connection successful' };
    } catch (error) {
      await this.updateIntegration(integrationId, {
        sync_status: 'error',
        error_message: error.message
      });

      return { success: false, message: error.message };
    }
  }
};

// WhatsApp Business API Service
export class WhatsAppService {
  private config: {
    accessToken: string;
    phoneNumberId: string;
    businessAccountId: string;
  };

  constructor(config: { accessToken: string; phoneNumberId: string; businessAccountId: string }) {
    this.config = config;
  }

  async sendMessage(to: string, message: { type: 'text' | 'template'; content: any }): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const url = `https://graph.facebook.com/v18.0/${this.config.phoneNumberId}/messages`;
      
      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        ...message.content
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (response.ok) {
        return { success: true, messageId: data.messages[0].id };
      } else {
        return { success: false, error: data.error?.message || 'Failed to send message' };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async sendTextMessage(to: string, text: string): Promise<{ success: boolean; messageId?: string; error?: string }> {
    return this.sendMessage(to, {
      type: 'text',
      content: {
        type: 'text',
        text: { body: text }
      }
    });
  }

  async sendTemplateMessage(to: string, templateName: string, languageCode: string, parameters?: any[]): Promise<{ success: boolean; messageId?: string; error?: string }> {
    return this.sendMessage(to, {
      type: 'template',
      content: {
        type: 'template',
        template: {
          name: templateName,
          language: { code: languageCode },
          components: parameters ? [{
            type: 'body',
            parameters: parameters.map(param => ({ type: 'text', text: param }))
          }] : undefined
        }
      }
    });
  }
}

// Telegram Bot Service
export class TelegramService {
  private botToken: string;
  private baseUrl: string;

  constructor(botToken: string) {
    this.botToken = botToken;
    this.baseUrl = `https://api.telegram.org/bot${botToken}`;
  }

  async sendMessage(chatId: string, text: string, options?: {
    parse_mode?: 'HTML' | 'Markdown';
    reply_markup?: any;
  }): Promise<{ success: boolean; messageId?: number; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          chat_id: chatId,
          text: text,
          ...options
        })
      });

      const data = await response.json();

      if (data.ok) {
        return { success: true, messageId: data.result.message_id };
      } else {
        return { success: false, error: data.description };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async sendDocument(chatId: string, document: string, caption?: string): Promise<{ success: boolean; messageId?: number; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/sendDocument`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          chat_id: chatId,
          document: document,
          caption: caption
        })
      });

      const data = await response.json();

      if (data.ok) {
        return { success: true, messageId: data.result.message_id };
      } else {
        return { success: false, error: data.description };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async setWebhook(url: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/setWebhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: url
        })
      });

      const data = await response.json();

      if (data.ok) {
        return { success: true };
      } else {
        return { success: false, error: data.description };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}
