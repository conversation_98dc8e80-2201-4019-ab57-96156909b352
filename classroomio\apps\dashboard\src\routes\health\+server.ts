// Health Check Endpoint for ClassroomIO
// Provides comprehensive health status for monitoring and load balancers

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

interface HealthCheck {
  status: 'healthy' | 'warning' | 'error';
  message?: string;
  responseTime?: number;
  details?: any;
}

interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks: Record<string, HealthCheck>;
  memory?: NodeJS.MemoryUsage;
}

// Simple in-memory cache for health checks
let lastHealthCheck: HealthResponse | null = null;
let lastCheckTime = 0;
const CACHE_DURATION = 30000; // 30 seconds

async function performHealthChecks(): Promise<HealthResponse> {
  const startTime = Date.now();
  const checks: Record<string, HealthCheck> = {};

  // Check environment variables
  try {
    const requiredEnvVars = ['DATABASE_URL', 'SUPABASE_URL', 'SUPABASE_ANON_KEY'];
    const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    checks.environment = {
      status: missing.length === 0 ? 'healthy' : 'error',
      message: missing.length === 0 ? 'All required environment variables present' : `Missing: ${missing.join(', ')}`,
      details: {
        nodeEnv: process.env.NODE_ENV,
        missing: missing.length > 0 ? missing : undefined
      }
    };
  } catch (error) {
    checks.environment = {
      status: 'error',
      message: `Environment check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }

  // Check database connectivity (basic check)
  try {
    if (process.env.DATABASE_URL) {
      // In a real implementation, this would test actual database connectivity
      // For now, we'll just verify the URL format
      const dbUrl = new URL(process.env.DATABASE_URL);
      checks.database = {
        status: 'healthy',
        message: 'Database URL configured',
        details: {
          host: dbUrl.hostname,
          port: dbUrl.port,
          database: dbUrl.pathname.slice(1)
        }
      };
    } else {
      checks.database = {
        status: 'error',
        message: 'Database URL not configured'
      };
    }
  } catch (error) {
    checks.database = {
      status: 'error',
      message: `Database check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }

  // Check Supabase configuration
  try {
    if (process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY) {
      checks.supabase = {
        status: 'healthy',
        message: 'Supabase configuration present',
        details: {
          url: process.env.SUPABASE_URL,
          hasAnonKey: !!process.env.SUPABASE_ANON_KEY,
          hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
        }
      };
    } else {
      checks.supabase = {
        status: 'error',
        message: 'Supabase configuration incomplete'
      };
    }
  } catch (error) {
    checks.supabase = {
      status: 'error',
      message: `Supabase check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }

  // Check memory usage
  try {
    const memUsage = process.memoryUsage();
    const totalMem = memUsage.heapTotal;
    const usedMem = memUsage.heapUsed;
    const memoryUsagePercent = (usedMem / totalMem) * 100;

    checks.memory = {
      status: memoryUsagePercent < 90 ? 'healthy' : memoryUsagePercent < 95 ? 'warning' : 'error',
      message: `Memory usage: ${Math.round(memoryUsagePercent)}%`,
      details: {
        rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        external: Math.round(memUsage.external / 1024 / 1024) + 'MB',
        usagePercent: Math.round(memoryUsagePercent) + '%'
      }
    };
  } catch (error) {
    checks.memory = {
      status: 'error',
      message: `Memory check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }

  // Check disk space (if available)
  try {
    // This is a simplified check - in production you might want to use a library
    checks.disk = {
      status: 'healthy',
      message: 'Disk space check not implemented',
      details: {
        note: 'Disk space monitoring should be implemented at infrastructure level'
      }
    };
  } catch (error) {
    checks.disk = {
      status: 'warning',
      message: `Disk check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }

  // Determine overall status
  const hasErrors = Object.values(checks).some(check => check.status === 'error');
  const hasWarnings = Object.values(checks).some(check => check.status === 'warning');
  
  const overallStatus = hasErrors ? 'unhealthy' : hasWarnings ? 'degraded' : 'healthy';

  const healthResponse: HealthResponse = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    checks,
    memory: process.memoryUsage()
  };

  // Add response time to each check
  const responseTime = Date.now() - startTime;
  Object.keys(checks).forEach(key => {
    checks[key].responseTime = responseTime;
  });

  return healthResponse;
}

export const GET: RequestHandler = async ({ url }) => {
  try {
    const now = Date.now();
    const detailed = url.searchParams.get('detailed') === 'true';
    
    // Use cached result if available and not expired
    if (lastHealthCheck && (now - lastCheckTime) < CACHE_DURATION && !detailed) {
      return json(lastHealthCheck, {
        status: lastHealthCheck.status === 'healthy' ? 200 : 
                lastHealthCheck.status === 'degraded' ? 200 : 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Content-Type': 'application/json'
        }
      });
    }

    // Perform health checks
    const healthResult = await performHealthChecks();
    
    // Cache the result
    lastHealthCheck = healthResult;
    lastCheckTime = now;

    // Return appropriate HTTP status code
    const httpStatus = healthResult.status === 'healthy' ? 200 : 
                      healthResult.status === 'degraded' ? 200 : 503;

    // Return simplified response for load balancers unless detailed is requested
    const response = detailed ? healthResult : {
      status: healthResult.status,
      timestamp: healthResult.timestamp,
      uptime: healthResult.uptime,
      version: healthResult.version,
      environment: healthResult.environment
    };

    return json(response, {
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    }, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    });
  }
};
