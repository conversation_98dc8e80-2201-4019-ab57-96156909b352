import { config } from '$lib/config';
import { createClient, type SupabaseClient } from '@supabase/supabase-js';

export let supabase: SupabaseClient;

export const getSupabase = () => {
  if (supabase) return supabase;

  try {
    supabase = createClient(config.supabaseConfig.url, config.supabaseConfig.anonKey);
  } catch (error) {
    console.warn('Supabase client creation failed, using fallback configuration:', error);
    // Create a minimal client with fallback configuration
    supabase = createClient('http://localhost:54321', 'demo-key');
  }

  return supabase;
};

export const isSupabaseTokenInLocalStorage = () => {
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key === null) continue; // Skip if null (shouldn't happen)
    if (/sb-[\w-]+-auth-token/.test(key)) {
      return true;
    }
  }

  return false;
};

export const getAccessToken = async () => {
  try {
    const { data } = await getSupabase().auth.getSession();
    return data.session?.access_token || '';
  } catch (error) {
    console.warn('Failed to get access token:', error);
    return '';
  }
};
