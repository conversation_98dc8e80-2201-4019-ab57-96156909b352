@echo off

:: Simple ClassroomIO Development Startup Script
:: Minimal version for testing and debugging

echo ========================================
echo    ClassroomIO Development Startup
echo ========================================
echo.

echo Starting ClassroomIO development environment...
echo Current directory: %CD%
echo.

:: Step 1: Check Node.js
echo [1/6] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from https://nodejs.org
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

for /f %%i in ('node --version') do set NODE_VERSION=%%i
echo SUCCESS: Node.js %NODE_VERSION% found
echo.

:: Step 2: Check directory
echo [2/6] Checking project directory...
if not exist "package.json" (
    echo ERROR: package.json not found!
    echo Please run this script from the ClassroomIO root directory
    echo Current directory: %CD%
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo SUCCESS: Found package.json
echo.

:: Step 3: Check environment
echo [3/6] Checking environment file...
if not exist ".env.local" (
    echo WARNING: .env.local not found
    if exist ".env.example" (
        echo Creating .env.local from .env.example...
        copy ".env.example" ".env.local" >nul 2>&1
        if errorlevel 1 (
            echo ERROR: Failed to create .env.local
            echo.
            echo Press any key to exit...
            pause >nul
            exit /b 1
        )
        echo SUCCESS: Created .env.local
    ) else (
        echo ERROR: .env.example not found
        echo.
        echo Press any key to exit...
        pause >nul
        exit /b 1
    )
) else (
    echo SUCCESS: .env.local exists
)
echo.

:: Step 4: Detect package manager
echo [4/6] Detecting package manager...
set PACKAGE_MANAGER=
set INSTALL_CMD=
set DEV_CMD=

pnpm --version >nul 2>&1
if not errorlevel 1 (
    set PACKAGE_MANAGER=pnpm
    set INSTALL_CMD=pnpm install
    set DEV_CMD=pnpm dev
    echo SUCCESS: Using pnpm
    goto :package_manager_found
)

npm --version >nul 2>&1
if not errorlevel 1 (
    set PACKAGE_MANAGER=npm
    set INSTALL_CMD=npm install
    set DEV_CMD=npm run dev
    echo SUCCESS: Using npm
    goto :package_manager_found
)

echo ERROR: No package manager found!
echo Please install Node.js (includes npm) or pnpm
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:package_manager_found
echo.

:: Step 5: Install dependencies
echo [5/6] Installing dependencies...
echo Running: %INSTALL_CMD%
echo This may take a few minutes...
echo.

%INSTALL_CMD%
if errorlevel 1 (
    echo.
    echo ERROR: Failed to install dependencies!
    echo Try running the command manually: %INSTALL_CMD%
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo.
echo SUCCESS: Dependencies installed
echo.

:: Step 6: Start development server
echo [6/6] Starting development server...
echo Running: %DEV_CMD%
echo.
echo The development server will start on http://localhost:5173
echo Press Ctrl+C in the server window to stop it
echo.

:: Start the server in a new window
start "ClassroomIO Development Server" cmd /k "%DEV_CMD%"

:: Wait a moment and try to open browser
echo Waiting for server to start...
timeout /t 10 /nobreak >nul

echo Opening browser...
start http://localhost:5173

echo.
echo ========================================
echo   ClassroomIO Development Started!
echo ========================================
echo.
echo Application URL: http://localhost:5173
echo Health Check: http://localhost:5173/health
echo API Health: http://localhost:5173/api/health
echo.
echo The development server is running in a separate window.
echo Close that window or press Ctrl+C in it to stop the server.
echo.
echo Press any key to close this setup window...
pause >nul

exit /b 0
