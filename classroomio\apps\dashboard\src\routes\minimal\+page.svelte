<script>
  import { onMount } from 'svelte';
  
  let status = 'Loading...';
  let errors = [];
  
  onMount(async () => {
    try {
      status = 'ClassroomIO Core Application Working!';
      console.log('Minimal ClassroomIO loaded successfully');
      
      // Test basic functionality
      const tests = [
        { name: 'Svelte Components', result: true },
        { name: 'JavaScript Execution', result: true },
        { name: 'CSS Loading', result: true },
        { name: 'Routing', result: true }
      ];
      
      // Try to test Supabase connection
      try {
        const { getSupabase } = await import('$lib/utils/functions/supabase');
        const supabase = getSupabase();
        tests.push({ name: 'Supabase Client', result: !!supabase });
      } catch (error) {
        tests.push({ name: 'Supabase Client', result: false });
        errors.push(`Supabase: ${error.message}`);
      }
      
      console.log('System tests:', tests);
      
    } catch (error) {
      status = 'Error loading application';
      errors.push(error.message);
      console.error('Application error:', error);
    }
  });
</script>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
  <div class="max-w-4xl mx-auto">
    <header class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 mb-2">
        Classroom<span class="text-blue-600">IO</span>
      </h1>
      <p class="text-xl text-gray-600">Minimal Application Test</p>
    </header>
    
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
      <h2 class="text-2xl font-semibold mb-4 text-center">
        {#if status.includes('Working')}
          <span class="text-green-600">✅ {status}</span>
        {:else if status.includes('Error')}
          <span class="text-red-600">❌ {status}</span>
        {:else}
          <span class="text-yellow-600">⏳ {status}</span>
        {/if}
      </h2>
      
      {#if errors.length > 0}
        <div class="bg-red-50 border border-red-200 rounded p-4 mb-4">
          <h3 class="font-semibold text-red-800 mb-2">Errors:</h3>
          <ul class="list-disc list-inside text-red-700">
            {#each errors as error}
              <li>{error}</li>
            {/each}
          </ul>
        </div>
      {/if}
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-semibold mb-2">System Status</h3>
          <ul class="space-y-1 text-sm">
            <li>✅ Node.js 18.17.0</li>
            <li>✅ Vite Development Server</li>
            <li>✅ SvelteKit Framework</li>
            <li>✅ Self-hosted Mode</li>
          </ul>
        </div>
        
        <div class="bg-gray-50 p-4 rounded">
          <h3 class="font-semibold mb-2">Configuration</h3>
          <ul class="space-y-1 text-sm">
            <li>🔧 IS_SELFHOSTED: true</li>
            <li>🔧 SSR: disabled</li>
            <li>🔧 Environment: development</li>
            <li>🔧 Port: 5173</li>
          </ul>
        </div>
      </div>
      
      <div class="text-center space-x-4">
        <a 
          href="/test" 
          class="inline-block bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition-colors"
        >
          Test Page
        </a>
        
        <a 
          href="/simple" 
          class="inline-block bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition-colors"
        >
          Simple Page
        </a>
        
        <a 
          href="/" 
          class="inline-block bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700 transition-colors"
        >
          Full Application
        </a>
      </div>
    </div>
    
    <div class="text-center text-gray-500 text-sm">
      <p>If you can see this page, the core ClassroomIO application is working correctly.</p>
      <p>The white page issue may be related to specific components or configurations.</p>
    </div>
  </div>
</div>
