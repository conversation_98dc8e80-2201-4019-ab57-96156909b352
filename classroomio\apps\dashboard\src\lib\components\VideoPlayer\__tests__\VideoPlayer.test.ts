// Video Player Tests
// Comprehensive test suite for the advanced video player component

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import VideoPlayer from '../VideoPlayer.svelte';
import { 
  setupTest, 
  cleanupTest, 
  TestDataFactory, 
  TestUtils,
  PerformanceTestUtils,
  AccessibilityTestUtils
} from '$lib/utils/testing/test-setup';

describe('VideoPlayer', () => {
  beforeEach(() => {
    setupTest();
    
    // Mock video element
    global.HTMLVideoElement.prototype.play = vi.fn().mockResolvedValue(undefined);
    global.HTMLVideoElement.prototype.pause = vi.fn();
    global.HTMLVideoElement.prototype.load = vi.fn();
    
    // Mock video properties
    Object.defineProperty(global.HTMLVideoElement.prototype, 'currentTime', {
      get: vi.fn(() => 0),
      set: vi.fn(),
      configurable: true
    });
    
    Object.defineProperty(global.HTMLVideoElement.prototype, 'duration', {
      get: vi.fn(() => 100),
      configurable: true
    });
    
    Object.defineProperty(global.HTMLVideoElement.prototype, 'paused', {
      get: vi.fn(() => true),
      configurable: true
    });
  });

  afterEach(() => {
    cleanupTest();
  });

  describe('Component Rendering', () => {
    it('should render video player with basic props', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        poster: 'https://example.com/poster.jpg'
      };

      render(VideoPlayer, { props });

      expect(screen.getByRole('application', { name: /video player/i })).toBeInTheDocument();
      expect(screen.getByText('Test Video')).toBeInTheDocument();
    });

    it('should render with multiple video sources', async () => {
      const props = {
        sources: [
          { src: 'https://example.com/video.mp4', type: 'video/mp4', quality: '1080p' },
          { src: 'https://example.com/video.webm', type: 'video/webm', quality: '720p' }
        ],
        title: 'Multi-source Video'
      };

      render(VideoPlayer, { props });

      expect(screen.getByRole('application')).toBeInTheDocument();
    });

    it('should render with subtitles', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Video with Subtitles',
        subtitles: [
          { src: 'https://example.com/en.vtt', label: 'English', language: 'en', default: true },
          { src: 'https://example.com/es.vtt', label: 'Spanish', language: 'es', default: false }
        ]
      };

      render(VideoPlayer, { props });

      expect(screen.getByRole('application')).toBeInTheDocument();
    });
  });

  describe('Video Controls', () => {
    it('should handle play/pause functionality', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      render(VideoPlayer, { props });

      const playButton = screen.getByRole('button', { name: /play/i });
      await fireEvent.click(playButton);

      expect(global.HTMLVideoElement.prototype.play).toHaveBeenCalled();
    });

    it('should handle volume control', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      render(VideoPlayer, { props });

      const volumeSlider = screen.getByRole('slider', { name: /volume/i });
      await fireEvent.input(volumeSlider, { target: { value: '0.5' } });

      expect(volumeSlider).toHaveValue('0.5');
    });

    it('should handle seeking', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      render(VideoPlayer, { props });

      const progressSlider = screen.getByRole('slider', { name: /progress/i });
      await fireEvent.input(progressSlider, { target: { value: '50' } });

      expect(progressSlider).toHaveValue('50');
    });

    it('should handle playback speed changes', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        showSpeedControl: true
      };

      render(VideoPlayer, { props });

      const speedButton = screen.getByRole('button', { name: /speed/i });
      await fireEvent.click(speedButton);

      // Check if speed menu appears
      await waitFor(() => {
        expect(screen.getByText('1.25x')).toBeInTheDocument();
      });
    });

    it('should handle fullscreen toggle', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      // Mock fullscreen API
      global.document.fullscreenElement = null;
      global.document.requestFullscreen = vi.fn();
      global.document.exitFullscreen = vi.fn();

      render(VideoPlayer, { props });

      const fullscreenButton = screen.getByRole('button', { name: /fullscreen/i });
      await fireEvent.click(fullscreenButton);

      expect(global.document.requestFullscreen).toHaveBeenCalled();
    });
  });

  describe('Analytics Integration', () => {
    it('should track video play events', async () => {
      const mockAnalyticsService = {
        trackVideoAnalytics: vi.fn().mockResolvedValue({}),
        trackEvent: vi.fn().mockResolvedValue('event-id')
      };

      vi.mock('$lib/utils/services/analytics', () => ({
        analyticsService: mockAnalyticsService
      }));

      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        videoId: 'test-video-id',
        enableAnalytics: true
      };

      render(VideoPlayer, { props });

      const playButton = screen.getByRole('button', { name: /play/i });
      await fireEvent.click(playButton);

      await waitFor(() => {
        expect(mockAnalyticsService.trackEvent).toHaveBeenCalledWith(
          expect.objectContaining({
            event_type: 'video_play',
            event_category: 'learning'
          })
        );
      });
    });

    it('should track video completion', async () => {
      const mockAnalyticsService = {
        trackVideoAnalytics: vi.fn().mockResolvedValue({}),
        trackEvent: vi.fn().mockResolvedValue('event-id')
      };

      vi.mock('$lib/utils/services/analytics', () => ({
        analyticsService: mockAnalyticsService
      }));

      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        videoId: 'test-video-id',
        enableAnalytics: true
      };

      const { component } = render(VideoPlayer, { props });

      // Simulate video completion
      const videoElement = screen.getByRole('application').querySelector('video');
      if (videoElement) {
        Object.defineProperty(videoElement, 'currentTime', { value: 100 });
        Object.defineProperty(videoElement, 'duration', { value: 100 });
        
        await fireEvent(videoElement, new Event('timeupdate'));
      }

      await waitFor(() => {
        expect(mockAnalyticsService.trackVideoAnalytics).toHaveBeenCalledWith(
          expect.objectContaining({
            completion_percentage: 100
          })
        );
      });
    });

    it('should track engagement events', async () => {
      const mockAnalyticsService = {
        trackVideoAnalytics: vi.fn().mockResolvedValue({}),
        trackEvent: vi.fn().mockResolvedValue('event-id')
      };

      vi.mock('$lib/utils/services/analytics', () => ({
        analyticsService: mockAnalyticsService
      }));

      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        videoId: 'test-video-id',
        enableAnalytics: true
      };

      render(VideoPlayer, { props });

      // Test seeking
      const progressSlider = screen.getByRole('slider', { name: /progress/i });
      await fireEvent.input(progressSlider, { target: { value: '50' } });

      await waitFor(() => {
        expect(mockAnalyticsService.trackEvent).toHaveBeenCalledWith(
          expect.objectContaining({
            event_type: 'video_seek',
            event_category: 'learning'
          })
        );
      });
    });
  });

  describe('Accessibility Features', () => {
    it('should support keyboard navigation', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      const { container } = render(VideoPlayer, { props });

      const keyboardIssues = AccessibilityTestUtils.checkKeyboardNavigation(container);
      expect(keyboardIssues).toHaveLength(0);
    });

    it('should have proper ARIA labels', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      const { container } = render(VideoPlayer, { props });

      const ariaIssues = AccessibilityTestUtils.checkAriaLabels(container);
      expect(ariaIssues).toHaveLength(0);
    });

    it('should support screen readers', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        description: 'Test video description'
      };

      render(VideoPlayer, { props });

      expect(screen.getByRole('application')).toHaveAttribute('aria-label');
      expect(screen.getByText('Test video description')).toBeInTheDocument();
    });
  });

  describe('Performance Tests', () => {
    it('should render within acceptable time limits', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      const renderTime = PerformanceTestUtils.measureRenderTime(VideoPlayer, props);
      
      // Should render within 50ms
      expect(renderTime).toBeLessThan(50);
    });

    it('should handle multiple video sources efficiently', async () => {
      const props = {
        sources: Array.from({ length: 10 }, (_, i) => ({
          src: `https://example.com/video${i}.mp4`,
          type: 'video/mp4',
          quality: `${720 + i * 180}p`
        })),
        title: 'Multi-source Video'
      };

      const start = performance.now();
      render(VideoPlayer, { props });
      const end = performance.now();
      
      // Should handle multiple sources within 100ms
      expect(end - start).toBeLessThan(100);
    });
  });

  describe('Error Handling', () => {
    it('should handle video load errors', async () => {
      const props = {
        src: 'https://example.com/invalid-video.mp4',
        title: 'Invalid Video'
      };

      render(VideoPlayer, { props });

      const videoElement = screen.getByRole('application').querySelector('video');
      if (videoElement) {
        await fireEvent(videoElement, new Event('error'));
      }

      await waitFor(() => {
        expect(screen.getByText(/error loading video/i)).toBeInTheDocument();
      });
    });

    it('should handle network errors gracefully', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        enableAnalytics: true
      };

      // Mock network error
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      render(VideoPlayer, { props });

      // Video should still render even if analytics fail
      expect(screen.getByRole('application')).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    it('should integrate with learning management system', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video',
        lessonId: 'test-lesson-id',
        batchId: 'test-batch-id'
      };

      const { component } = render(VideoPlayer, { props });

      let progressEvent: any = null;
      component.$on('progress', (event) => {
        progressEvent = event.detail;
      });

      // Simulate video progress
      const videoElement = screen.getByRole('application').querySelector('video');
      if (videoElement) {
        Object.defineProperty(videoElement, 'currentTime', { value: 50 });
        await fireEvent(videoElement, new Event('timeupdate'));
      }

      expect(progressEvent).toBeTruthy();
      expect(progressEvent.currentTime).toBe(50);
    });

    it('should emit completion events', async () => {
      const props = {
        src: 'https://example.com/video.mp4',
        title: 'Test Video'
      };

      const { component } = render(VideoPlayer, { props });

      let completionEvent: any = null;
      component.$on('completed', (event) => {
        completionEvent = event.detail;
      });

      // Simulate video completion
      const videoElement = screen.getByRole('application').querySelector('video');
      if (videoElement) {
        Object.defineProperty(videoElement, 'currentTime', { value: 100 });
        Object.defineProperty(videoElement, 'duration', { value: 100 });
        await fireEvent(videoElement, new Event('ended'));
      }

      expect(completionEvent).toBeTruthy();
    });
  });
});
