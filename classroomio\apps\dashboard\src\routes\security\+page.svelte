<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import SecurityDashboard from '$lib/components/Security/SecurityDashboard.svelte';
  import { PageBody, PageNav } from '$lib/components/Page';
  import { t } from '$lib/utils/functions/translations';
  import { globalStore } from '$lib/utils/store/app';
  import RoleBasedSecurity from '$lib/components/RoleBasedSecurity/index.svelte';
  import { ROLE } from '$lib/utils/constants/roles';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { Settings, Security } from 'carbon-icons-svelte';

  $: organizationId = $globalStore.org?.id || '';
  $: isAdmin = $globalStore.isOrgAdmin;

  onMount(() => {
    // Redirect if user doesn't have permission
    if (!isAdmin) {
      goto('/');
    }
  });

  function goToPolicies() {
    goto('/security/policies');
  }
</script>

<svelte:head>
  <title>{$t('security.dashboard', { default: 'Security Dashboard' })} - ClassroomIO</title>
  <meta name="description" content={$t('security.dashboard_desc', { default: 'Monitor and manage security events and policies' })} />
</svelte:head>

<RoleBasedSecurity allowedRoles={[ROLE.ADMIN]}>
  <PageNav title={$t('security.dashboard', { default: 'Security Dashboard' })}>
    <div class="flex items-center space-x-4">
      <PrimaryButton
        variant={VARIANTS.OUTLINED}
        onClick={goToPolicies}
      >
        <Settings size={20} class="mr-2" />
        {$t('security.manage_policies', { default: 'Manage Policies' })}
      </PrimaryButton>
    </div>
  </PageNav>

  <PageBody>
    <SecurityDashboard {organizationId} />
  </PageBody>
</RoleBasedSecurity>
