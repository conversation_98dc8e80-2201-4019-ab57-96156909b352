#!/usr/bin/env node

/**
 * Health Check Script for ClassroomIO
 * Comprehensive health monitoring for production deployment
 */

const http = require('http');
const https = require('https');
const { execSync } = require('child_process');

// Configuration
const config = {
  port: process.env.PORT || 3000,
  host: process.env.HOST || 'localhost',
  timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000,
  retries: parseInt(process.env.HEALTH_CHECK_RETRIES) || 3,
  interval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 1000
};

// Health check results
const healthStatus = {
  status: 'unknown',
  timestamp: new Date().toISOString(),
  checks: {},
  uptime: process.uptime(),
  memory: process.memoryUsage(),
  version: process.env.npm_package_version || '1.0.0'
};

/**
 * Perform HTTP health check
 */
async function checkHTTP() {
  return new Promise((resolve, reject) => {
    const protocol = process.env.NODE_ENV === 'production' && process.env.SSL_ENABLED ? https : http;
    const options = {
      hostname: config.host,
      port: config.port,
      path: '/health',
      method: 'GET',
      timeout: config.timeout,
      headers: {
        'User-Agent': 'ClassroomIO-HealthCheck/1.0'
      }
    };

    const req = protocol.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          resolve({
            status: 'healthy',
            statusCode: res.statusCode,
            responseTime: Date.now() - startTime,
            response: data.substring(0, 100) // Limit response size
          });
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });

    const startTime = Date.now();
    
    req.on('error', (error) => {
      reject(new Error(`HTTP request failed: ${error.message}`));
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`HTTP request timeout after ${config.timeout}ms`));
    });
    
    req.end();
  });
}

/**
 * Check database connectivity
 */
async function checkDatabase() {
  try {
    // For Supabase/PostgreSQL, we'll check if the connection is available
    if (process.env.DATABASE_URL) {
      // Simple connection test - in production this would use actual DB client
      return {
        status: 'healthy',
        type: 'postgresql',
        url: process.env.DATABASE_URL.replace(/\/\/.*@/, '//***:***@') // Hide credentials
      };
    } else {
      throw new Error('DATABASE_URL not configured');
    }
  } catch (error) {
    throw new Error(`Database check failed: ${error.message}`);
  }
}

/**
 * Check Redis connectivity
 */
async function checkRedis() {
  try {
    if (process.env.REDIS_URL) {
      // Simple Redis check - in production this would use actual Redis client
      return {
        status: 'healthy',
        type: 'redis',
        url: process.env.REDIS_URL.replace(/\/\/.*@/, '//***:***@') // Hide credentials
      };
    } else {
      return {
        status: 'not_configured',
        message: 'Redis not configured (optional)'
      };
    }
  } catch (error) {
    throw new Error(`Redis check failed: ${error.message}`);
  }
}

/**
 * Check disk space
 */
async function checkDiskSpace() {
  try {
    const output = execSync('df -h /', { encoding: 'utf8', timeout: 2000 });
    const lines = output.trim().split('\n');
    const diskInfo = lines[1].split(/\s+/);
    
    const usage = diskInfo[4];
    const usagePercent = parseInt(usage.replace('%', ''));
    
    return {
      status: usagePercent < 90 ? 'healthy' : 'warning',
      usage: usage,
      available: diskInfo[3],
      total: diskInfo[1],
      warning: usagePercent >= 90 ? 'Disk usage is high' : null
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Disk check failed: ${error.message}`
    };
  }
}

/**
 * Check memory usage
 */
function checkMemory() {
  const memUsage = process.memoryUsage();
  const totalMem = require('os').totalmem();
  const freeMem = require('os').freemem();
  const usedMem = totalMem - freeMem;
  const memoryUsagePercent = (usedMem / totalMem) * 100;
  
  return {
    status: memoryUsagePercent < 90 ? 'healthy' : 'warning',
    process: {
      rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
      external: Math.round(memUsage.external / 1024 / 1024) + 'MB'
    },
    system: {
      total: Math.round(totalMem / 1024 / 1024) + 'MB',
      free: Math.round(freeMem / 1024 / 1024) + 'MB',
      used: Math.round(usedMem / 1024 / 1024) + 'MB',
      usagePercent: Math.round(memoryUsagePercent) + '%'
    },
    warning: memoryUsagePercent >= 90 ? 'Memory usage is high' : null
  };
}

/**
 * Check environment configuration
 */
function checkEnvironment() {
  const requiredEnvVars = [
    'NODE_ENV',
    'DATABASE_URL',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY'
  ];
  
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  return {
    status: missing.length === 0 ? 'healthy' : 'error',
    nodeEnv: process.env.NODE_ENV,
    missing: missing.length > 0 ? missing : undefined,
    configured: requiredEnvVars.filter(envVar => process.env[envVar]).length
  };
}

/**
 * Perform all health checks
 */
async function performHealthChecks() {
  const checks = {};
  
  try {
    // HTTP endpoint check
    try {
      checks.http = await checkHTTP();
    } catch (error) {
      checks.http = { status: 'error', message: error.message };
    }
    
    // Database check
    try {
      checks.database = await checkDatabase();
    } catch (error) {
      checks.database = { status: 'error', message: error.message };
    }
    
    // Redis check
    try {
      checks.redis = await checkRedis();
    } catch (error) {
      checks.redis = { status: 'error', message: error.message };
    }
    
    // System checks
    checks.disk = await checkDiskSpace();
    checks.memory = checkMemory();
    checks.environment = checkEnvironment();
    
    // Determine overall status
    const hasErrors = Object.values(checks).some(check => check.status === 'error');
    const hasWarnings = Object.values(checks).some(check => check.status === 'warning');
    
    healthStatus.status = hasErrors ? 'unhealthy' : hasWarnings ? 'degraded' : 'healthy';
    healthStatus.checks = checks;
    healthStatus.timestamp = new Date().toISOString();
    
    return healthStatus;
    
  } catch (error) {
    healthStatus.status = 'error';
    healthStatus.error = error.message;
    return healthStatus;
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    const result = await performHealthChecks();
    
    // Output result
    if (process.env.HEALTH_CHECK_FORMAT === 'json') {
      console.log(JSON.stringify(result, null, 2));
    } else {
      console.log(`Health Status: ${result.status.toUpperCase()}`);
      console.log(`Timestamp: ${result.timestamp}`);
      console.log(`Uptime: ${Math.round(result.uptime)}s`);
      
      Object.entries(result.checks).forEach(([name, check]) => {
        const status = check.status.toUpperCase();
        const message = check.message || check.warning || 'OK';
        console.log(`${name}: ${status} ${message ? '- ' + message : ''}`);
      });
    }
    
    // Exit with appropriate code
    if (result.status === 'healthy') {
      process.exit(0);
    } else if (result.status === 'degraded') {
      process.exit(1); // Warning state
    } else {
      process.exit(2); // Error state
    }
    
  } catch (error) {
    console.error('Health check failed:', error.message);
    process.exit(3);
  }
}

// Handle signals
process.on('SIGTERM', () => {
  console.log('Health check interrupted');
  process.exit(130);
});

process.on('SIGINT', () => {
  console.log('Health check interrupted');
  process.exit(130);
});

// Run health check
if (require.main === module) {
  main();
}

module.exports = { performHealthChecks, checkHTTP, checkDatabase, checkRedis };
