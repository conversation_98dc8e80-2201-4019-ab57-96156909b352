<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { writable } from 'svelte/store';
  import { 
    currentBatch, 
    batchActions 
  } from '$lib/components/Batch/store';
  import { batchService } from '$lib/utils/services/batch';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import { PageBody, PageNav } from '$lib/components/Page';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import DoubtClearingSystem from '$lib/components/Communication/DoubtClearingSystem.svelte';
  import ForumSystem from '$lib/components/Communication/ForumSystem.svelte';
  import MessagingSystem from '$lib/components/Communication/MessagingSystem.svelte';
  import NotificationCenter from '$lib/components/Communication/NotificationCenter.svelte';
  import CommunicationDashboard from '$lib/components/Communication/CommunicationDashboard.svelte';
  import { 
    Chat,
    Forum,
    User,
    Notification,
    Dashboard,
    Settings
  } from 'carbon-icons-svelte';

  let batchId: string;
  let loading = true;
  let error: string | null = null;
  let activeTab = 'dashboard';

  $: organizationId = $globalStore.org?.id;
  $: isInstructor = $globalStore.role === 'teacher' || $globalStore.isOrgAdmin;

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: Dashboard, instructorOnly: true },
    { id: 'doubts', label: 'Doubts', icon: User, instructorOnly: false },
    { id: 'forum', label: 'Forum', icon: Forum, instructorOnly: false },
    { id: 'messaging', label: 'Messaging', icon: Chat, instructorOnly: false },
    { id: 'notifications', label: 'Notifications', icon: Notification, instructorOnly: false }
  ];

  $: visibleTabs = tabs.filter(tab => !tab.instructorOnly || isInstructor);

  onMount(async () => {
    batchId = $page.params.id;
    await loadBatchData();
  });

  async function loadBatchData() {
    try {
      loading = true;
      error = null;

      if (!$currentBatch || $currentBatch.id !== batchId) {
        const batch = await batchService.getBatch(batchId);
        if (!batch) {
          throw new Error('Batch not found');
        }
        batchActions.setBatch(batch);
      }

    } catch (err) {
      console.error('Error loading batch:', err);
      error = err.message || 'Failed to load batch data';
    } finally {
      loading = false;
    }
  }

  function handleDoubtSubmitted(event: CustomEvent) {
    console.log('Doubt submitted:', event.detail);
  }

  function handlePostCreated(event: CustomEvent) {
    console.log('Forum post created:', event.detail);
  }

  function handleMessageSent(event: CustomEvent) {
    console.log('Message sent:', event.detail);
  }

  function handleNotificationRead(event: CustomEvent) {
    console.log('Notification read:', event.detail);
  }
</script>

<svelte:head>
  <title>
    {$t('communication.title', { default: 'Communication' })} - {$currentBatch?.name} - ClassroomIO
  </title>
</svelte:head>

<PageNav title={$t('communication.title', { default: 'Communication' })}>
  <div class="flex items-center space-x-4">
    <!-- Tab Navigation -->
    <div class="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
      {#each visibleTabs as tab}
        <button
          on:click={() => activeTab = tab.id}
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === tab.id 
            ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm' 
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}"
        >
          <svelte:component this={tab.icon} size={16} class="mr-2" />
          {$t(`communication.${tab.id}`, { default: tab.label })}
        </button>
      {/each}
    </div>
  </div>
</PageNav>

<PageBody>
  {#if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('communication.loading', { default: 'Loading Communication' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Settings size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('communication.error', { default: 'Error Loading Communication' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadBatchData}>
        {$t('communication.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else}
    <!-- Tab Content -->
    <div class="min-h-[600px]">
      {#if activeTab === 'dashboard' && isInstructor && organizationId}
        <CommunicationDashboard 
          {organizationId}
          {batchId}
        />

      {:else if activeTab === 'doubts'}
        <DoubtClearingSystem 
          {batchId}
          on:doubtSubmitted={handleDoubtSubmitted}
        />

      {:else if activeTab === 'forum' && organizationId}
        <ForumSystem 
          {organizationId}
          {batchId}
          on:postCreated={handlePostCreated}
        />

      {:else if activeTab === 'messaging'}
        <MessagingSystem 
          {batchId}
          on:messageSent={handleMessageSent}
        />

      {:else if activeTab === 'notifications'}
        <NotificationCenter 
          on:notificationRead={handleNotificationRead}
        />

      {:else}
        <Box className="text-center py-12">
          <Chat size={48} class="text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {$t('communication.select_tab', { default: 'Select a Communication Channel' })}
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            {$t('communication.select_tab_desc', { default: 'Choose a tab to access communication features' })}
          </p>
        </Box>
      {/if}
    </div>

    <!-- Quick Actions (Floating) -->
    <div class="fixed bottom-6 right-6 flex flex-col space-y-3">
      {#if activeTab !== 'doubts'}
        <button
          on:click={() => activeTab = 'doubts'}
          class="w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
          title={$t('communication.quick_doubt', { default: 'Ask a doubt' })}
        >
          <User size={20} />
        </button>
      {/if}

      {#if activeTab !== 'messaging'}
        <button
          on:click={() => activeTab = 'messaging'}
          class="w-12 h-12 bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
          title={$t('communication.quick_message', { default: 'Send a message' })}
        >
          <Chat size={20} />
        </button>
      {/if}

      {#if activeTab !== 'notifications'}
        <button
          on:click={() => activeTab = 'notifications'}
          class="w-12 h-12 bg-orange-600 hover:bg-orange-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
          title={$t('communication.quick_notifications', { default: 'View notifications' })}
        >
          <Notification size={20} />
        </button>
      {/if}
    </div>
  {/if}
</PageBody>

<style>
  /* Custom styles for floating action buttons */
  .fixed {
    z-index: 40;
  }
</style>
