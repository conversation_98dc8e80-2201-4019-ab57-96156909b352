// Batch Management Store
// Date: 2025-06-30

import { writable, derived } from 'svelte/store';
import type { 
  Batch, 
  Subject, 
  Chapter, 
  BatchMember, 
  StudyMaterial, 
  DoubtSystem,
  LiveSession,
  ProgressTracking,
  BatchHierarchy
} from '$lib/utils/types/batch';

// Current batch store
export const currentBatch = writable<Batch | null>(null);

// Batches list store
export const batches = writable<Batch[]>([]);

// Current subject store
export const currentSubject = writable<Subject | null>(null);

// Subjects for current batch
export const subjects = writable<Subject[]>([]);

// Current chapter store
export const currentChapter = writable<Chapter | null>(null);

// Chapters for current subject
export const chapters = writable<Chapter[]>([]);

// Batch members store
export const batchMembers = writable<BatchMember[]>([]);

// Study materials store
export const studyMaterials = writable<StudyMaterial[]>([]);

// Doubts store
export const doubts = writable<DoubtSystem[]>([]);

// Live sessions store
export const liveSessions = writable<LiveSession[]>([]);

// Progress tracking store
export const progressTracking = writable<ProgressTracking[]>([]);

// Batch hierarchy store
export const batchHierarchy = writable<BatchHierarchy[]>([]);

// Loading states
export const batchLoading = writable<boolean>(false);
export const subjectLoading = writable<boolean>(false);
export const chapterLoading = writable<boolean>(false);

// Error states
export const batchError = writable<string | null>(null);
export const subjectError = writable<string | null>(null);
export const chapterError = writable<string | null>(null);

// Derived stores
export const currentBatchStudents = derived(
  [currentBatch, batchMembers],
  ([$currentBatch, $batchMembers]) => {
    if (!$currentBatch) return [];
    return $batchMembers.filter(member => 
      member.batch_id === $currentBatch.id && 
      member.role === 'student' && 
      member.is_active
    );
  }
);

export const currentBatchInstructors = derived(
  [currentBatch, batchMembers],
  ([$currentBatch, $batchMembers]) => {
    if (!$currentBatch) return [];
    return $batchMembers.filter(member => 
      member.batch_id === $currentBatch.id && 
      member.role === 'instructor' && 
      member.is_active
    );
  }
);

export const activeSubjects = derived(
  subjects,
  ($subjects) => $subjects.filter(subject => subject.is_active)
);

export const activeChapters = derived(
  chapters,
  ($chapters) => $chapters.filter(chapter => chapter.is_active)
);

export const openDoubts = derived(
  doubts,
  ($doubts) => $doubts.filter(doubt => doubt.status === 'open')
);

export const upcomingLiveSessions = derived(
  liveSessions,
  ($liveSessions) => $liveSessions.filter(session => 
    session.status === 'scheduled' && 
    new Date(session.scheduled_at) > new Date()
  )
);

// Action creators
export const batchActions = {
  setBatch: (batch: Batch | null) => {
    currentBatch.set(batch);
    if (batch) {
      // Reset related stores when batch changes
      subjects.set([]);
      chapters.set([]);
      currentSubject.set(null);
      currentChapter.set(null);
    }
  },

  setBatches: (batchList: Batch[]) => {
    batches.set(batchList);
  },

  addBatch: (batch: Batch) => {
    batches.update(list => [...list, batch]);
  },

  updateBatch: (batchId: string, updates: Partial<Batch>) => {
    batches.update(list => 
      list.map(batch => 
        batch.id === batchId ? { ...batch, ...updates } : batch
      )
    );
    
    currentBatch.update(batch => 
      batch?.id === batchId ? { ...batch, ...updates } : batch
    );
  },

  removeBatch: (batchId: string) => {
    batches.update(list => list.filter(batch => batch.id !== batchId));
    currentBatch.update(batch => batch?.id === batchId ? null : batch);
  },

  setLoading: (loading: boolean) => {
    batchLoading.set(loading);
  },

  setError: (error: string | null) => {
    batchError.set(error);
  }
};

export const subjectActions = {
  setSubject: (subject: Subject | null) => {
    currentSubject.set(subject);
    if (subject) {
      // Reset chapters when subject changes
      chapters.set([]);
      currentChapter.set(null);
    }
  },

  setSubjects: (subjectList: Subject[]) => {
    subjects.set(subjectList);
  },

  addSubject: (subject: Subject) => {
    subjects.update(list => [...list, subject]);
  },

  updateSubject: (subjectId: string, updates: Partial<Subject>) => {
    subjects.update(list => 
      list.map(subject => 
        subject.id === subjectId ? { ...subject, ...updates } : subject
      )
    );
    
    currentSubject.update(subject => 
      subject?.id === subjectId ? { ...subject, ...updates } : subject
    );
  },

  removeSubject: (subjectId: string) => {
    subjects.update(list => list.filter(subject => subject.id !== subjectId));
    currentSubject.update(subject => subject?.id === subjectId ? null : subject);
  },

  setLoading: (loading: boolean) => {
    subjectLoading.set(loading);
  },

  setError: (error: string | null) => {
    subjectError.set(error);
  }
};

export const chapterActions = {
  setChapter: (chapter: Chapter | null) => {
    currentChapter.set(chapter);
  },

  setChapters: (chapterList: Chapter[]) => {
    chapters.set(chapterList);
  },

  addChapter: (chapter: Chapter) => {
    chapters.update(list => [...list, chapter]);
  },

  updateChapter: (chapterId: string, updates: Partial<Chapter>) => {
    chapters.update(list => 
      list.map(chapter => 
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    );
    
    currentChapter.update(chapter => 
      chapter?.id === chapterId ? { ...chapter, ...updates } : chapter
    );
  },

  removeChapter: (chapterId: string) => {
    chapters.update(list => list.filter(chapter => chapter.id !== chapterId));
    currentChapter.update(chapter => chapter?.id === chapterId ? null : chapter);
  },

  setLoading: (loading: boolean) => {
    chapterLoading.set(loading);
  },

  setError: (error: string | null) => {
    chapterError.set(error);
  }
};

export const memberActions = {
  setMembers: (members: BatchMember[]) => {
    batchMembers.set(members);
  },

  addMember: (member: BatchMember) => {
    batchMembers.update(list => [...list, member]);
  },

  updateMember: (memberId: string, updates: Partial<BatchMember>) => {
    batchMembers.update(list => 
      list.map(member => 
        member.id === memberId ? { ...member, ...updates } : member
      )
    );
  },

  removeMember: (memberId: string) => {
    batchMembers.update(list => list.filter(member => member.id !== memberId));
  }
};

export const materialActions = {
  setMaterials: (materials: StudyMaterial[]) => {
    studyMaterials.set(materials);
  },

  addMaterial: (material: StudyMaterial) => {
    studyMaterials.update(list => [...list, material]);
  },

  updateMaterial: (materialId: string, updates: Partial<StudyMaterial>) => {
    studyMaterials.update(list => 
      list.map(material => 
        material.id === materialId ? { ...material, ...updates } : material
      )
    );
  },

  removeMaterial: (materialId: string) => {
    studyMaterials.update(list => list.filter(material => material.id !== materialId));
  }
};

export const doubtActions = {
  setDoubts: (doubtList: DoubtSystem[]) => {
    doubts.set(doubtList);
  },

  addDoubt: (doubt: DoubtSystem) => {
    doubts.update(list => [...list, doubt]);
  },

  updateDoubt: (doubtId: string, updates: Partial<DoubtSystem>) => {
    doubts.update(list => 
      list.map(doubt => 
        doubt.id === doubtId ? { ...doubt, ...updates } : doubt
      )
    );
  },

  removeDoubt: (doubtId: string) => {
    doubts.update(list => list.filter(doubt => doubt.id !== doubtId));
  }
};

export const liveSessionActions = {
  setSessions: (sessions: LiveSession[]) => {
    liveSessions.set(sessions);
  },

  addSession: (session: LiveSession) => {
    liveSessions.update(list => [...list, session]);
  },

  updateSession: (sessionId: string, updates: Partial<LiveSession>) => {
    liveSessions.update(list => 
      list.map(session => 
        session.id === sessionId ? { ...session, ...updates } : session
      )
    );
  },

  removeSession: (sessionId: string) => {
    liveSessions.update(list => list.filter(session => session.id !== sessionId));
  }
};
