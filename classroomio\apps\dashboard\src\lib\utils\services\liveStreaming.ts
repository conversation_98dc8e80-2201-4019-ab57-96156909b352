// Live Streaming Services for Educational Platform
// Date: 2025-06-30

import { supabase } from '$lib/utils/functions/supabase';
import type { 
  LiveSession, 
  LiveSessionParticipant, 
  BreakoutRoom,
  LivePoll,
  LivePollResponse,
  LiveChatMessage,
  WhiteboardSession,
  MeetingProvider,
  AttendanceReport,
  EngagementMetrics
} from '$lib/utils/types/liveStreaming';

// Live Session Service
export const liveSessionService = {
  // Create a new live session
  async createSession(sessionData: Omit<LiveSession, 'id' | 'created_at' | 'updated_at'>): Promise<LiveSession> {
    const { data, error } = await supabase
      .rpc('create_live_session', {
        p_title: sessionData.title,
        p_description: sessionData.description,
        p_batch_id: sessionData.batch_id,
        p_instructor_id: sessionData.instructor_id,
        p_scheduled_start: sessionData.scheduled_start,
        p_scheduled_end: sessionData.scheduled_end,
        p_session_type: sessionData.session_type,
        p_auto_enroll_batch: true
      });

    if (error) throw error;

    // Get the created session
    const { data: session, error: sessionError } = await supabase
      .from('live_sessions')
      .select('*')
      .eq('id', data)
      .single();

    if (sessionError) throw sessionError;
    return session;
  },

  // Get live sessions for a batch
  async getBatchSessions(batchId: string, filters?: {
    status?: string;
    session_type?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<LiveSession[]> {
    let query = supabase
      .from('live_sessions')
      .select('*')
      .eq('batch_id', batchId);

    if (filters?.status) query = query.eq('status', filters.status);
    if (filters?.session_type) query = query.eq('session_type', filters.session_type);
    if (filters?.start_date) query = query.gte('scheduled_start', filters.start_date);
    if (filters?.end_date) query = query.lte('scheduled_end', filters.end_date);

    const { data, error } = await query.order('scheduled_start', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Get session by ID
  async getSession(sessionId: string): Promise<LiveSession | null> {
    const { data, error } = await supabase
      .from('live_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  },

  // Update session
  async updateSession(sessionId: string, updates: Partial<LiveSession>): Promise<LiveSession> {
    const { data, error } = await supabase
      .from('live_sessions')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', sessionId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Join session
  async joinSession(sessionId: string, userId: string, deviceFingerprint?: string): Promise<any> {
    const { data, error } = await supabase
      .rpc('join_live_session', {
        p_session_id: sessionId,
        p_user_id: userId,
        p_device_fingerprint: deviceFingerprint
      });

    if (error) throw error;
    return data;
  },

  // End session
  async endSession(sessionId: string): Promise<void> {
    const { error } = await supabase
      .from('live_sessions')
      .update({
        status: 'ended',
        actual_end: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', sessionId);

    if (error) throw error;
  },

  // Get upcoming sessions for user
  async getUpcomingSessions(userId: string, role: string): Promise<LiveSession[]> {
    const now = new Date().toISOString();
    
    let query = supabase
      .from('live_sessions')
      .select(`
        *,
        live_session_participants!inner(user_id, role)
      `)
      .eq('live_session_participants.user_id', userId)
      .gte('scheduled_start', now)
      .in('status', ['scheduled', 'live']);

    if (role) {
      query = query.eq('live_session_participants.role', role);
    }

    const { data, error } = await query.order('scheduled_start', { ascending: true });

    if (error) throw error;
    return data || [];
  }
};

// Participant Management Service
export const participantService = {
  // Get session participants
  async getSessionParticipants(sessionId: string): Promise<LiveSessionParticipant[]> {
    const { data, error } = await supabase
      .from('live_session_participants')
      .select(`
        *,
        profile:user_id(id, fullname, email, avatar_url)
      `)
      .eq('session_id', sessionId)
      .order('joined_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Update participant status
  async updateParticipant(participantId: string, updates: Partial<LiveSessionParticipant>): Promise<void> {
    const { error } = await supabase
      .from('live_session_participants')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', participantId);

    if (error) throw error;
  },

  // Approve participant
  async approveParticipant(sessionId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('live_session_participants')
      .update({
        approval_status: 'approved',
        is_approved: true,
        updated_at: new Date().toISOString()
      })
      .eq('session_id', sessionId)
      .eq('user_id', userId);

    if (error) throw error;
  },

  // Track participant activity
  async trackActivity(sessionId: string, userId: string, activityData: any): Promise<void> {
    const { error } = await supabase
      .from('live_session_participants')
      .update({
        participation_data: activityData,
        updated_at: new Date().toISOString()
      })
      .eq('session_id', sessionId)
      .eq('user_id', userId);

    if (error) throw error;
  }
};

// Breakout Room Service
export const breakoutRoomService = {
  // Create breakout rooms
  async createBreakoutRooms(sessionId: string, roomCount: number, duration: number): Promise<BreakoutRoom[]> {
    const rooms: Omit<BreakoutRoom, 'id' | 'created_at' | 'updated_at'>[] = [];
    
    for (let i = 1; i <= roomCount; i++) {
      rooms.push({
        session_id: sessionId,
        room_name: `Breakout Room ${i}`,
        room_number: i,
        max_participants: 10,
        duration_minutes: duration,
        is_active: true,
        room_config: {
          auto_assign: false,
          allow_self_select: true,
          moderator_can_join_all: true,
          recording_enabled: false
        },
        assigned_participants: []
      });
    }

    const { data, error } = await supabase
      .from('breakout_rooms')
      .insert(rooms)
      .select();

    if (error) throw error;
    return data || [];
  },

  // Assign participants to rooms
  async assignParticipants(roomId: string, participantIds: string[]): Promise<void> {
    const { error } = await supabase
      .from('breakout_rooms')
      .update({
        assigned_participants: participantIds,
        updated_at: new Date().toISOString()
      })
      .eq('id', roomId);

    if (error) throw error;
  },

  // Get session breakout rooms
  async getSessionRooms(sessionId: string): Promise<BreakoutRoom[]> {
    const { data, error } = await supabase
      .from('breakout_rooms')
      .select('*')
      .eq('session_id', sessionId)
      .order('room_number', { ascending: true });

    if (error) throw error;
    return data || [];
  }
};

// Live Poll Service
export const livePollService = {
  // Create poll
  async createPoll(pollData: Omit<LivePoll, 'id' | 'created_at' | 'updated_at' | 'results'>): Promise<LivePoll> {
    const { data, error } = await supabase
      .from('live_polls')
      .insert({
        ...pollData,
        results: {
          total_responses: 0,
          response_breakdown: {},
          response_percentage: {}
        }
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Start poll
  async startPoll(pollId: string): Promise<void> {
    const { error } = await supabase
      .from('live_polls')
      .update({
        is_active: true,
        started_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', pollId);

    if (error) throw error;
  },

  // End poll
  async endPoll(pollId: string): Promise<void> {
    const { error } = await supabase
      .from('live_polls')
      .update({
        is_active: false,
        ended_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', pollId);

    if (error) throw error;
  },

  // Submit poll response
  async submitResponse(pollId: string, userId: string, responseData: any): Promise<void> {
    const { error } = await supabase
      .from('live_poll_responses')
      .insert({
        poll_id: pollId,
        user_id: userId,
        response_data: responseData,
        response_time: new Date().toISOString()
      });

    if (error) throw error;

    // Update poll results
    await this.updatePollResults(pollId);
  },

  // Update poll results
  async updatePollResults(pollId: string): Promise<void> {
    const { data: responses, error: responsesError } = await supabase
      .from('live_poll_responses')
      .select('response_data')
      .eq('poll_id', pollId);

    if (responsesError) throw responsesError;

    const totalResponses = responses?.length || 0;
    const responseBreakdown: Record<string, number> = {};
    const responsePercentage: Record<string, number> = {};

    // Calculate response breakdown
    responses?.forEach(response => {
      if (response.response_data.selected_options) {
        response.response_data.selected_options.forEach((option: string) => {
          responseBreakdown[option] = (responseBreakdown[option] || 0) + 1;
        });
      }
    });

    // Calculate percentages
    Object.keys(responseBreakdown).forEach(option => {
      responsePercentage[option] = totalResponses > 0 
        ? Math.round((responseBreakdown[option] / totalResponses) * 100)
        : 0;
    });

    const { error } = await supabase
      .from('live_polls')
      .update({
        results: {
          total_responses: totalResponses,
          response_breakdown: responseBreakdown,
          response_percentage: responsePercentage
        },
        updated_at: new Date().toISOString()
      })
      .eq('id', pollId);

    if (error) throw error;
  },

  // Get session polls
  async getSessionPolls(sessionId: string): Promise<LivePoll[]> {
    const { data, error } = await supabase
      .from('live_polls')
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
};

// Chat Service
export const chatService = {
  // Send message
  async sendMessage(messageData: Omit<LiveChatMessage, 'id' | 'created_at'>): Promise<LiveChatMessage> {
    const { data, error } = await supabase
      .from('live_chat_messages')
      .insert(messageData)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get session messages
  async getSessionMessages(sessionId: string, limit: number = 100): Promise<LiveChatMessage[]> {
    const { data, error } = await supabase
      .from('live_chat_messages')
      .select(`
        *,
        profile:user_id(id, fullname, avatar_url)
      `)
      .eq('session_id', sessionId)
      .eq('moderation_status', 'approved')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  },

  // Moderate message
  async moderateMessage(messageId: string, status: 'approved' | 'rejected', moderatorId: string): Promise<void> {
    const { error } = await supabase
      .from('live_chat_messages')
      .update({
        moderation_status: status,
        moderated_by: moderatorId,
        is_moderated: true
      })
      .eq('id', messageId);

    if (error) throw error;
  }
};

// Analytics Service
export const analyticsService = {
  // Track analytics
  async trackAnalytics(sessionId: string, analyticsType: string, dataPoint: string, value: number, metadata?: any): Promise<void> {
    const { error } = await supabase
      .rpc('track_session_analytics', {
        p_session_id: sessionId,
        p_analytics_type: analyticsType,
        p_data_point: dataPoint,
        p_value: value,
        p_metadata: metadata
      });

    if (error) throw error;
  },

  // Get attendance report
  async getAttendanceReport(sessionId: string): Promise<AttendanceReport> {
    const { data: session, error: sessionError } = await supabase
      .from('live_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (sessionError) throw sessionError;

    const { data: participants, error: participantsError } = await supabase
      .from('live_session_participants')
      .select(`
        *,
        profile:user_id(id, fullname, email)
      `)
      .eq('session_id', sessionId);

    if (participantsError) throw participantsError;

    const totalEnrolled = participants?.length || 0;
    const totalAttended = participants?.filter(p => p.joined_at).length || 0;
    const attendanceRate = totalEnrolled > 0 ? Math.round((totalAttended / totalEnrolled) * 100) : 0;

    return {
      session_id: sessionId,
      session_title: session.title,
      scheduled_start: session.scheduled_start,
      actual_start: session.actual_start,
      total_enrolled: totalEnrolled,
      total_attended: totalAttended,
      attendance_rate: attendanceRate,
      participants: participants?.map(p => ({
        user_id: p.user_id,
        user_name: p.profile?.fullname || 'Unknown',
        role: p.role,
        joined_at: p.joined_at,
        left_at: p.left_at,
        duration_minutes: Math.round(p.total_duration / 60),
        attendance_status: p.joined_at ? 'present' : 'absent'
      })) || []
    };
  }
};
