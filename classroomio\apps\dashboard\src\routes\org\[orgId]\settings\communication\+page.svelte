<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { writable } from 'svelte/store';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import { PageBody, PageNav } from '$lib/components/Page';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import ExternalIntegrations from '$lib/components/Communication/ExternalIntegrations.svelte';
  import { 
    Settings,
    Connect,
    Notification,
    Email,
    Save,
    Warning
  } from 'carbon-icons-svelte';

  let organizationId: string;
  let loading = true;
  let error: string | null = null;
  let saving = false;
  let activeTab = 'integrations';

  // Communication Settings
  let communicationSettings = {
    doubt_clearing: {
      enabled: true,
      auto_assignment: true,
      ai_categorization: true,
      response_time_sla: 60, // minutes
      escalation_enabled: true
    },
    forum: {
      enabled: true,
      moderation_required: false,
      allow_anonymous_posts: false,
      reputation_system: true,
      gamification_enabled: true
    },
    messaging: {
      enabled: true,
      file_sharing_enabled: true,
      voice_messages_enabled: true,
      message_retention_days: 365,
      typing_indicators: true
    },
    notifications: {
      email_enabled: true,
      sms_enabled: false,
      push_enabled: true,
      whatsapp_enabled: false,
      telegram_enabled: false,
      quiet_hours: {
        enabled: true,
        start_time: '22:00',
        end_time: '08:00',
        timezone: 'UTC'
      }
    }
  };

  $: isOrgAdmin = $globalStore.isOrgAdmin;

  const tabs = [
    { id: 'integrations', label: 'External Integrations', icon: Connect },
    { id: 'settings', label: 'Communication Settings', icon: Settings },
    { id: 'notifications', label: 'Notification Settings', icon: Notification },
    { id: 'templates', label: 'Message Templates', icon: Email }
  ];

  onMount(async () => {
    organizationId = $page.params.orgId;
    await loadSettings();
  });

  async function loadSettings() {
    try {
      loading = true;
      error = null;

      // In a real implementation, this would load from the API
      // For now, we'll use the default settings

    } catch (err) {
      console.error('Error loading settings:', err);
      error = err.message || 'Failed to load communication settings';
    } finally {
      loading = false;
    }
  }

  async function saveSettings() {
    try {
      saving = true;
      error = null;

      // In a real implementation, this would save to the API
      console.log('Saving communication settings:', communicationSettings);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (err) {
      console.error('Error saving settings:', err);
      error = err.message || 'Failed to save settings';
    } finally {
      saving = false;
    }
  }

  function handleIntegrationCreated(event: CustomEvent) {
    console.log('Integration created:', event.detail);
  }

  function handleIntegrationUpdated(event: CustomEvent) {
    console.log('Integration updated:', event.detail);
  }

  function handleIntegrationDeleted(event: CustomEvent) {
    console.log('Integration deleted:', event.detail);
  }
</script>

<svelte:head>
  <title>
    {$t('communication.settings_title', { default: 'Communication Settings' })} - ClassroomIO
  </title>
</svelte:head>

<PageNav title={$t('communication.settings_title', { default: 'Communication Settings' })}>
  <div class="flex items-center space-x-4">
    <!-- Tab Navigation -->
    <div class="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
      {#each tabs as tab}
        <button
          on:click={() => activeTab = tab.id}
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === tab.id 
            ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm' 
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}"
        >
          <svelte:component this={tab.icon} size={16} class="mr-2" />
          {$t(`communication.${tab.id}`, { default: tab.label })}
        </button>
      {/each}
    </div>

    {#if activeTab === 'settings' || activeTab === 'notifications'}
      <PrimaryButton
        variant={VARIANTS.CONTAINED}
        onClick={saveSettings}
        disabled={saving}
      >
        {#if saving}
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
        {:else}
          <Save size={20} class="mr-2" />
        {/if}
        {$t('communication.save_settings', { default: 'Save Settings' })}
      </PrimaryButton>
    {/if}
  </div>
</PageNav>

<PageBody>
  {#if !isOrgAdmin}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('communication.access_denied', { default: 'Access Denied' })}
      </h3>
      <p class="text-red-600 dark:text-red-400">
        {$t('communication.admin_required', { default: 'Organization admin access required' })}
      </p>
    </Box>

  {:else if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('communication.loading_settings', { default: 'Loading Settings' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('communication.error_loading', { default: 'Error Loading Settings' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadSettings}>
        {$t('communication.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else}
    <!-- Tab Content -->
    <div class="space-y-6">
      {#if activeTab === 'integrations'}
        <ExternalIntegrations 
          {organizationId}
          on:integrationCreated={handleIntegrationCreated}
          on:integrationUpdated={handleIntegrationUpdated}
          on:integrationDeleted={handleIntegrationDeleted}
        />

      {:else if activeTab === 'settings'}
        <!-- Communication Settings -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Doubt Clearing Settings -->
          <Box>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {$t('communication.doubt_settings', { default: 'Doubt Clearing Settings' })}
              </h3>
              
              <div class="space-y-4">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.doubt_clearing.enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.enable_doubt_clearing', { default: 'Enable doubt clearing system' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.doubt_clearing.auto_assignment}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.auto_assignment', { default: 'Auto-assign doubts to instructors' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.doubt_clearing.ai_categorization}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.ai_categorization', { default: 'AI-powered doubt categorization' })}
                  </span>
                </label>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {$t('communication.response_sla', { default: 'Response Time SLA (minutes)' })}
                  </label>
                  <input
                    type="number"
                    bind:value={communicationSettings.doubt_clearing.response_time_sla}
                    min="5"
                    max="1440"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                  />
                </div>
              </div>
            </div>
          </Box>

          <!-- Forum Settings -->
          <Box>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {$t('communication.forum_settings', { default: 'Forum Settings' })}
              </h3>
              
              <div class="space-y-4">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.forum.enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.enable_forum', { default: 'Enable discussion forums' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.forum.moderation_required}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.moderation_required', { default: 'Require post moderation' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.forum.allow_anonymous_posts}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.anonymous_posts', { default: 'Allow anonymous posts' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.forum.reputation_system}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.reputation_system', { default: 'Enable reputation system' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.forum.gamification_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.gamification', { default: 'Enable gamification features' })}
                  </span>
                </label>
              </div>
            </div>
          </Box>

          <!-- Messaging Settings -->
          <Box>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {$t('communication.messaging_settings', { default: 'Messaging Settings' })}
              </h3>
              
              <div class="space-y-4">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.messaging.enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.enable_messaging', { default: 'Enable real-time messaging' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.messaging.file_sharing_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.file_sharing', { default: 'Enable file sharing' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.messaging.voice_messages_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.voice_messages', { default: 'Enable voice messages' })}
                  </span>
                </label>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {$t('communication.message_retention', { default: 'Message Retention (days)' })}
                  </label>
                  <input
                    type="number"
                    bind:value={communicationSettings.messaging.message_retention_days}
                    min="1"
                    max="3650"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                  />
                </div>
              </div>
            </div>
          </Box>
        </div>

      {:else if activeTab === 'notifications'}
        <!-- Notification Settings -->
        <Box>
          <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">
              {$t('communication.notification_channels', { default: 'Notification Channels' })}
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-4">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.notifications.email_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.email_notifications', { default: 'Email notifications' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.notifications.sms_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.sms_notifications', { default: 'SMS notifications' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.notifications.push_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.push_notifications', { default: 'Push notifications' })}
                  </span>
                </label>
              </div>

              <div class="space-y-4">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.notifications.whatsapp_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.whatsapp_notifications', { default: 'WhatsApp notifications' })}
                  </span>
                </label>

                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.notifications.telegram_enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.telegram_notifications', { default: 'Telegram notifications' })}
                  </span>
                </label>
              </div>
            </div>

            <!-- Quiet Hours -->
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">
                {$t('communication.quiet_hours', { default: 'Quiet Hours' })}
              </h4>
              
              <div class="space-y-4">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    bind:checked={communicationSettings.notifications.quiet_hours.enabled}
                    class="mr-3"
                  />
                  <span class="text-gray-900 dark:text-white">
                    {$t('communication.enable_quiet_hours', { default: 'Enable quiet hours' })}
                  </span>
                </label>

                {#if communicationSettings.notifications.quiet_hours.enabled}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {$t('communication.start_time', { default: 'Start Time' })}
                      </label>
                      <input
                        type="time"
                        bind:value={communicationSettings.notifications.quiet_hours.start_time}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {$t('communication.end_time', { default: 'End Time' })}
                      </label>
                      <input
                        type="time"
                        bind:value={communicationSettings.notifications.quiet_hours.end_time}
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    </div>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        </Box>

      {:else if activeTab === 'templates'}
        <!-- Message Templates -->
        <Box className="text-center py-12">
          <Email size={48} class="text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {$t('communication.templates_coming_soon', { default: 'Message Templates' })}
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            {$t('communication.templates_desc', { default: 'Template management coming soon' })}
          </p>
        </Box>
      {/if}
    </div>
  {/if}
</PageBody>
