<script lang="ts">
  import { onMount, onDestroy, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { DashboardConfig, DashboardWidget, RealTimeMetric } from '$lib/utils/types/analytics';
  import { dashboardService } from '$lib/utils/services/dashboard';
  import { analyticsService } from '$lib/utils/services/analytics';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import WidgetRenderer from './WidgetRenderer.svelte';
  import DashboardEditor from './DashboardEditor.svelte';
  import { 
    Dashboard,
    Edit,
    Add,
    Settings,
    Refresh,
    Save,
    Close,
    Warning
  } from 'carbon-icons-svelte';

  export let organizationId: string;
  export let batchId: string | null = null;
  export let userId: string | null = null;
  export let dashboardType: 'student' | 'instructor' | 'admin' | 'custom' = 'student';
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    dashboardChanged: { dashboard: DashboardConfig };
    widgetClicked: { widget: DashboardWidget };
  }>();

  let dashboards = writable<DashboardConfig[]>([]);
  let selectedDashboard = writable<DashboardConfig | null>(null);
  let realTimeMetrics = writable<RealTimeMetric[]>([]);
  let widgetData = writable<Record<string, any>>({});
  
  let loading = true;
  let error: string | null = null;
  let isEditing = false;
  let showDashboardSelector = false;
  let autoRefresh = true;
  let refreshInterval = 30000; // 30 seconds

  // Real-time update interval
  let updateInterval: NodeJS.Timeout | null = null;

  $: currentUserId = userId || $globalStore.user?.id;
  $: isOrgAdmin = $globalStore.isOrgAdmin;
  $: canEdit = isOrgAdmin || ($selectedDashboard?.created_by === currentUserId);

  onMount(async () => {
    await loadDashboards();
    if (autoRefresh) {
      startAutoRefresh();
    }
  });

  onDestroy(() => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
  });

  async function loadDashboards() {
    try {
      loading = true;
      error = null;

      const dashboardsData = await dashboardService.getDashboards(
        organizationId,
        dashboardType,
        currentUserId
      );
      dashboards.set(dashboardsData);

      // Select default dashboard or first available
      const defaultDashboard = dashboardsData.find(d => d.is_default) || dashboardsData[0];
      if (defaultDashboard) {
        await selectDashboard(defaultDashboard);
      } else {
        // Create default dashboard if none exists
        await createDefaultDashboard();
      }

    } catch (err) {
      console.error('Error loading dashboards:', err);
      error = err.message || 'Failed to load dashboards';
    } finally {
      loading = false;
    }
  }

  async function selectDashboard(dashboard: DashboardConfig) {
    selectedDashboard.set(dashboard);
    await loadDashboardData(dashboard);
    await dashboardService.incrementDashboardUsage(dashboard.id);
    dispatch('dashboardChanged', { dashboard });
  }

  async function loadDashboardData(dashboard: DashboardConfig) {
    try {
      const data: Record<string, any> = {};
      
      // Load data for each widget
      for (const widget of dashboard.config.widgets) {
        try {
          const filters = {
            organization_id: organizationId,
            batch_id: batchId,
            user_id: currentUserId,
            ...dashboard.filters
          };
          
          const widgetResult = await dashboardService.getWidgetData(widget, filters);
          data[widget.id] = widgetResult;
        } catch (err) {
          console.error(`Error loading data for widget ${widget.id}:`, err);
          data[widget.id] = { error: err.message };
        }
      }
      
      widgetData.set(data);

    } catch (err) {
      console.error('Error loading dashboard data:', err);
      error = err.message || 'Failed to load dashboard data';
    }
  }

  async function createDefaultDashboard() {
    try {
      const template = await dashboardService.getDefaultDashboardTemplate(dashboardType);
      
      const dashboardData = {
        ...template,
        organization_id: organizationId,
        created_by: currentUserId,
        is_default: true
      };

      const newDashboard = await dashboardService.createDashboard(dashboardData);
      dashboards.update(current => [newDashboard, ...current]);
      await selectDashboard(newDashboard);

    } catch (err) {
      console.error('Error creating default dashboard:', err);
      error = err.message || 'Failed to create default dashboard';
    }
  }

  async function refreshDashboard() {
    if ($selectedDashboard) {
      await loadDashboardData($selectedDashboard);
    }
  }

  function startAutoRefresh() {
    if (updateInterval) {
      clearInterval(updateInterval);
    }

    updateInterval = setInterval(async () => {
      if ($selectedDashboard && autoRefresh) {
        await refreshDashboard();
      }
    }, refreshInterval);
  }

  function stopAutoRefresh() {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
  }

  function toggleAutoRefresh() {
    autoRefresh = !autoRefresh;
    if (autoRefresh) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  }

  function handleWidgetClick(widget: DashboardWidget) {
    dispatch('widgetClicked', { widget });
  }

  async function saveDashboard(updatedDashboard: DashboardConfig) {
    try {
      const saved = await dashboardService.updateDashboard(
        updatedDashboard.id,
        updatedDashboard
      );
      
      selectedDashboard.set(saved);
      dashboards.update(current => 
        current.map(d => d.id === saved.id ? saved : d)
      );
      
      await loadDashboardData(saved);
      isEditing = false;

    } catch (err) {
      console.error('Error saving dashboard:', err);
      error = err.message || 'Failed to save dashboard';
    }
  }

  function cancelEditing() {
    isEditing = false;
  }
</script>

<div class="analytics-dashboard {className}">
  <!-- Dashboard Header -->
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center space-x-4">
      <div class="flex items-center">
        <Dashboard size={24} class="text-primary-600 mr-3" />
        <div>
          <h2 class="text-xl font-bold text-gray-900 dark:text-white">
            {$selectedDashboard?.name || $t('analytics.dashboard', { default: 'Analytics Dashboard' })}
          </h2>
          {#if $selectedDashboard?.description}
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {$selectedDashboard.description}
            </p>
          {/if}
        </div>
      </div>

      <!-- Dashboard Selector -->
      {#if $dashboards.length > 1}
        <div class="relative">
          <button
            on:click={() => showDashboardSelector = !showDashboardSelector}
            class="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            {$t('analytics.switch_dashboard', { default: 'Switch Dashboard' })}
          </button>
          
          {#if showDashboardSelector}
            <div class="absolute top-full left-0 mt-1 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-10">
              {#each $dashboards as dashboard}
                <button
                  on:click={() => {
                    selectDashboard(dashboard);
                    showDashboardSelector = false;
                  }}
                  class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 {$selectedDashboard?.id === dashboard.id ? 'bg-primary-50 dark:bg-primary-900' : ''}"
                >
                  <div class="font-medium text-gray-900 dark:text-white">
                    {dashboard.name}
                  </div>
                  {#if dashboard.description}
                    <div class="text-xs text-gray-600 dark:text-gray-400">
                      {dashboard.description}
                    </div>
                  {/if}
                </button>
              {/each}
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <!-- Dashboard Controls -->
    <div class="flex items-center space-x-2">
      <!-- Auto Refresh Toggle -->
      <button
        on:click={toggleAutoRefresh}
        class="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 {autoRefresh ? 'bg-green-50 border-green-300 text-green-700' : ''}"
        title={autoRefresh ? $t('analytics.auto_refresh_on', { default: 'Auto-refresh enabled' }) : $t('analytics.auto_refresh_off', { default: 'Auto-refresh disabled' })}
      >
        <Refresh size={16} class={autoRefresh ? 'animate-spin' : ''} />
      </button>

      <!-- Manual Refresh -->
      <PrimaryButton
        variant={VARIANTS.OUTLINED}
        onClick={refreshDashboard}
        size="sm"
      >
        <Refresh size={16} class="mr-1" />
        {$t('analytics.refresh', { default: 'Refresh' })}
      </PrimaryButton>

      <!-- Edit Dashboard -->
      {#if canEdit && !isEditing}
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => isEditing = true}
          size="sm"
        >
          <Edit size={16} class="mr-1" />
          {$t('analytics.edit', { default: 'Edit' })}
        </PrimaryButton>
      {/if}

      <!-- Dashboard Settings -->
      <button
        class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        title={$t('analytics.settings', { default: 'Settings' })}
      >
        <Settings size={20} />
      </button>
    </div>
  </div>

  {#if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.loading_dashboard', { default: 'Loading Dashboard' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.error_loading', { default: 'Error Loading Dashboard' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadDashboards}>
        {$t('analytics.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if isEditing && $selectedDashboard}
    <!-- Dashboard Editor -->
    <DashboardEditor
      dashboard={$selectedDashboard}
      {organizationId}
      {batchId}
      {userId}
      on:save={(e) => saveDashboard(e.detail.dashboard)}
      on:cancel={cancelEditing}
    />

  {:else if $selectedDashboard}
    <!-- Dashboard Grid -->
    <div 
      class="dashboard-grid"
      style="
        display: grid;
        grid-template-columns: repeat({$selectedDashboard.config.layout?.columns || 12}, 1fr);
        gap: {$selectedDashboard.config.layout?.margin?.[0] || 10}px;
        padding: {$selectedDashboard.config.layout?.container_padding?.[0] || 20}px;
      "
    >
      {#each $selectedDashboard.config.widgets as widget (widget.id)}
        <div
          class="widget-container"
          style="
            grid-column: span {widget.size.width};
            grid-row: span {widget.size.height};
            min-height: {($selectedDashboard.config.layout?.row_height || 60) * widget.size.height}px;
          "
        >
          <WidgetRenderer
            {widget}
            data={$widgetData[widget.id]}
            theme={$selectedDashboard.config.theme}
            on:click={() => handleWidgetClick(widget)}
          />
        </div>
      {/each}
    </div>

  {:else}
    <Box className="text-center py-12">
      <Dashboard size={48} class="text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {$t('analytics.no_dashboard', { default: 'No Dashboard Available' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        {$t('analytics.create_dashboard_desc', { default: 'Create your first dashboard to start analyzing data' })}
      </p>
      <PrimaryButton onClick={createDefaultDashboard}>
        <Add size={20} class="mr-2" />
        {$t('analytics.create_dashboard', { default: 'Create Dashboard' })}
      </PrimaryButton>
    </Box>
  {/if}
</div>

<style>
  .dashboard-grid {
    min-height: 400px;
  }

  .widget-container {
    position: relative;
  }

  /* Click outside to close dropdown */
  :global(body) {
    --dashboard-selector-z-index: 50;
  }
</style>
