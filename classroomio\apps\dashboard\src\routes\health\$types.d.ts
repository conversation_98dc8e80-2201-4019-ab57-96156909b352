import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
type RouteParams = {  };
type RouteId = '/health';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];

type OutputDataShape<T> = MaybeWithVoid<Expand<T>>;
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
type PageParentData = EnsureDefined<import('../$types.js').LayoutData>;
type LayoutRouteId = RouteId | "/" | null
type LayoutParams = RouteParams & {  }
type LayoutParentData = EnsureDefined<{}>;

export type PageServerData = null;
export type PageData = Expand<PageParentData>;
export type LayoutData = Expand<LayoutParentData>;
export type RequestEvent = Kit.RequestEvent<RouteParams, RouteId>;
export type RequestHandler = Kit.RequestHandler<RouteParams, RouteId>;
export type ServerLoad = Kit.ServerLoad<RouteParams, PageServerData, LayoutParentData, RouteId>;
export type LayoutServerLoad = Kit.ServerLoad<LayoutParams, LayoutData, LayoutParentData, LayoutRouteId>;
export type Actions = Kit.Actions<RouteParams, RouteId>;
export type PageServerLoad = Kit.ServerLoad<RouteParams, PageServerData, PageParentData, RouteId>;
export type EntryGenerator = Kit.EntryGenerator<RouteParams>;
export type Snapshot<T = any> = Kit.Snapshot<T>;
