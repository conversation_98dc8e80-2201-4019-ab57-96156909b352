import Image from 'next/image';
import CourseLandingPage from './images/course-landing-page.webp';
import CoursePage from './images/course-page.png';
import LandingPage from './images/landing page.png';

# Create a Course Landing Page

ClassroomIO empowers you to design a course landing page, enabling you to showcase your courses to prospective students. On this page, students can explore course details, enrollment statistics, fees (if applicable), and course availability, enabling them to make informed choices. To access this, from your admin dashboard, navigate to **Settings** > **Landing Page.**  

<br />

## Steps

### 1. Go to the Courses page

Once you are logged in, click on **Courses** on the left sidebar, you will be taken to your course list page where you can access the courses you have created.  For this tutorial, we will focus on the **Intro to Javascript** course diaplayed in the image below. Click on the desired course to proceeed to it's respective dashboard.
<br />

<Image src={CourseLandingPage} alt="ClassroomIO Landing Page" quality={100} />

### 2. Open the Landing page editor

Select the "**Landing page**" option from the sidebar, which will lead you to the settings for your landing page. Here, you can customize various aspects of your course, including the course title, fees (if applicable), requirements, description, and more.
<br />

<Image src={CoursePage} alt="ClassroomIO Course Page" quality={100} />

### 3. Edit and Save your changes

After making the desired adjustments, ensure to save your changes by clicking on the "**Save**" button located at the top-left corner of the screen. Then, to preview the updated landing page, click on the arrow button situated to the right of the "**Save**" button.
<br />

<Image src={LandingPage} alt="ClassroomIO Course Page Settings" quality={100} />
