import "./chunk-RPU3HFCT.js";
import {
  HtmlTagHydration,
  SvelteComponentDev,
  add_location,
  append_hydration_dev,
  assign,
  attr_dev,
  claim_element,
  claim_html_tag,
  claim_space,
  destroy_each,
  detach_dev,
  dispatch_dev,
  element,
  empty,
  ensure_array_like_dev,
  get_spread_update,
  globals,
  head_selector,
  init,
  insert_hydration_dev,
  noop,
  safe_not_equal,
  set_attributes,
  space,
  validate_slots
} from "./chunk-E4ZC5ETH.js";
import "./chunk-TCF7Q4S4.js";
import "./chunk-2GTGKKMZ.js";

// ../../node_modules/.pnpm/svelte-meta-tags@3.1.4_svelte@4.2.20/node_modules/svelte-meta-tags/dist/MetaTags.svelte
var { console: console_1 } = globals;
var file = "C:\\Users\\<USER>\\Downloads\\New folder (2)\\classroomio\\node_modules\\.pnpm\\svelte-meta-tags@3.1.4_svelte@4.2.20\\node_modules\\svelte-meta-tags\\dist\\MetaTags.svelte";
function get_each_context(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[16] = list[i];
  return child_ctx;
}
function get_each_context_1(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[16] = list[i];
  return child_ctx;
}
function get_each_context_2(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[21] = list[i];
  return child_ctx;
}
function get_each_context_3(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[24] = list[i];
  return child_ctx;
}
function get_each_context_4(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[27] = list[i];
  return child_ctx;
}
function get_each_context_9(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[16] = list[i];
  return child_ctx;
}
function get_each_context_10(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[41] = list[i];
  return child_ctx;
}
function get_each_context_11(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[44] = list[i];
  return child_ctx;
}
function get_each_context_12(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[47] = list[i];
  return child_ctx;
}
function get_each_context_7(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[16] = list[i];
  return child_ctx;
}
function get_each_context_8(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[32] = list[i];
  return child_ctx;
}
function get_each_context_5(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[16] = list[i];
  return child_ctx;
}
function get_each_context_6(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[32] = list[i];
  return child_ctx;
}
function get_each_context_13(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[50] = list[i];
  return child_ctx;
}
function create_if_block_65(ctx) {
  let title_value;
  document.title = title_value = /*updatedTitle*/
  ctx[12];
  const block = { c: noop, l: noop, m: noop, d: noop };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_65.name,
    type: "if",
    source: "(37:4) {#if updatedTitle}",
    ctx
  });
  return block;
}
function create_key_block(ctx) {
  let if_block_anchor;
  let if_block = (
    /*updatedTitle*/
    ctx[12] && create_if_block_65(ctx)
  );
  const block = {
    c: function create() {
      if (if_block)
        if_block.c();
      if_block_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block)
        if_block.l(nodes);
      if_block_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block)
        if_block.m(target, anchor);
      insert_hydration_dev(target, if_block_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (
        /*updatedTitle*/
        ctx2[12]
      ) {
        if (if_block) {
        } else {
          if_block = create_if_block_65(ctx2);
          if_block.c();
          if_block.m(if_block_anchor.parentNode, if_block_anchor);
        }
      } else if (if_block) {
        if_block.d(1);
        if_block = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(if_block_anchor);
      }
      if (if_block)
        if_block.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_key_block.name,
    type: "key",
    source: "(36:2) {#key updatedTitle}",
    ctx
  });
  return block;
}
function create_if_block_64(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "robots");
      attr_dev(meta, "content", meta_content_value = "" + /*robots*/
      (ctx[0] + /*robotsParams*/
      ctx[11]));
      add_location(meta, file, 42, 4, 1503);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*robots, robotsParams*/
      2049 && meta_content_value !== (meta_content_value = "" + /*robots*/
      (ctx2[0] + /*robotsParams*/
      ctx2[11]))) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_64.name,
    type: "if",
    source: "(42:2) {#if robots !== false}",
    ctx
  });
  return block;
}
function create_if_block_63(ctx) {
  let meta;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "description");
      attr_dev(
        meta,
        "content",
        /*description*/
        ctx[1]
      );
      add_location(meta, file, 46, 4, 1592);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*description*/
      2) {
        attr_dev(
          meta,
          "content",
          /*description*/
          ctx2[1]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_63.name,
    type: "if",
    source: "(46:2) {#if description}",
    ctx
  });
  return block;
}
function create_if_block_62(ctx) {
  let link;
  const block = {
    c: function create() {
      link = element("link");
      this.h();
    },
    l: function claim(nodes) {
      link = claim_element(nodes, "LINK", { rel: true, href: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(link, "rel", "canonical");
      attr_dev(
        link,
        "href",
        /*canonical*/
        ctx[7]
      );
      add_location(link, file, 50, 4, 1673);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, link, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*canonical*/
      128) {
        attr_dev(
          link,
          "href",
          /*canonical*/
          ctx2[7]
        );
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(link);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_62.name,
    type: "if",
    source: "(50:2) {#if canonical}",
    ctx
  });
  return block;
}
function create_if_block_61(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "keywords");
      attr_dev(meta, "content", meta_content_value = /*keywords*/
      ctx[8].join(", "));
      add_location(meta, file, 54, 4, 1753);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*keywords*/
      256 && meta_content_value !== (meta_content_value = /*keywords*/
      ctx2[8].join(", "))) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_61.name,
    type: "if",
    source: "(54:2) {#if keywords?.length}",
    ctx
  });
  return block;
}
function create_if_block_60(ctx) {
  let link;
  let link_media_value;
  let link_href_value;
  const block = {
    c: function create() {
      link = element("link");
      this.h();
    },
    l: function claim(nodes) {
      link = claim_element(nodes, "LINK", { rel: true, media: true, href: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(link, "rel", "alternate");
      attr_dev(link, "media", link_media_value = /*mobileAlternate*/
      ctx[2].media);
      attr_dev(link, "href", link_href_value = /*mobileAlternate*/
      ctx[2].href);
      add_location(link, file, 58, 4, 1845);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, link, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*mobileAlternate*/
      4 && link_media_value !== (link_media_value = /*mobileAlternate*/
      ctx2[2].media)) {
        attr_dev(link, "media", link_media_value);
      }
      if (dirty[0] & /*mobileAlternate*/
      4 && link_href_value !== (link_href_value = /*mobileAlternate*/
      ctx2[2].href)) {
        attr_dev(link, "href", link_href_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(link);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_60.name,
    type: "if",
    source: "(58:2) {#if mobileAlternate}",
    ctx
  });
  return block;
}
function create_if_block_59(ctx) {
  let each_1_anchor;
  let each_value_13 = ensure_array_like_dev(
    /*languageAlternates*/
    ctx[3]
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_13.length; i += 1) {
    each_blocks[i] = create_each_block_13(get_each_context_13(ctx, each_value_13, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*languageAlternates*/
      8) {
        each_value_13 = ensure_array_like_dev(
          /*languageAlternates*/
          ctx2[3]
        );
        let i;
        for (i = 0; i < each_value_13.length; i += 1) {
          const child_ctx = get_each_context_13(ctx2, each_value_13, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_13(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_13.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_59.name,
    type: "if",
    source: "(62:2) {#if languageAlternates && languageAlternates.length > 0}",
    ctx
  });
  return block;
}
function create_each_block_13(ctx) {
  let link;
  let link_hreflang_value;
  let link_href_value;
  const block = {
    c: function create() {
      link = element("link");
      this.h();
    },
    l: function claim(nodes) {
      link = claim_element(nodes, "LINK", { rel: true, hreflang: true, href: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(link, "rel", "alternate");
      attr_dev(link, "hreflang", link_hreflang_value = /*languageAlternate*/
      ctx[50].hrefLang);
      attr_dev(link, "href", link_href_value = /*languageAlternate*/
      ctx[50].href);
      add_location(link, file, 63, 6, 2055);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, link, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*languageAlternates*/
      8 && link_hreflang_value !== (link_hreflang_value = /*languageAlternate*/
      ctx2[50].hrefLang)) {
        attr_dev(link, "hreflang", link_hreflang_value);
      }
      if (dirty[0] & /*languageAlternates*/
      8 && link_href_value !== (link_href_value = /*languageAlternate*/
      ctx2[50].href)) {
        attr_dev(link, "href", link_href_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(link);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_13.name,
    type: "each",
    source: "(63:4) {#each languageAlternates as languageAlternate}",
    ctx
  });
  return block;
}
function create_if_block_51(ctx) {
  let t0;
  let t1;
  let t2;
  let t3;
  let t4;
  let t5;
  let if_block6_anchor;
  let if_block0 = (
    /*twitter*/
    ctx[4].cardType && create_if_block_58(ctx)
  );
  let if_block1 = (
    /*twitter*/
    ctx[4].site && create_if_block_57(ctx)
  );
  let if_block2 = (
    /*twitter*/
    ctx[4].handle && create_if_block_56(ctx)
  );
  let if_block3 = (
    /*twitter*/
    ctx[4].title && create_if_block_55(ctx)
  );
  let if_block4 = (
    /*twitter*/
    ctx[4].description && create_if_block_54(ctx)
  );
  let if_block5 = (
    /*twitter*/
    ctx[4].image && create_if_block_53(ctx)
  );
  let if_block6 = (
    /*twitter*/
    ctx[4].imageAlt && create_if_block_52(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      t0 = space();
      if (if_block1)
        if_block1.c();
      t1 = space();
      if (if_block2)
        if_block2.c();
      t2 = space();
      if (if_block3)
        if_block3.c();
      t3 = space();
      if (if_block4)
        if_block4.c();
      t4 = space();
      if (if_block5)
        if_block5.c();
      t5 = space();
      if (if_block6)
        if_block6.c();
      if_block6_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block0)
        if_block0.l(nodes);
      t0 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t1 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t2 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      t3 = claim_space(nodes);
      if (if_block4)
        if_block4.l(nodes);
      t4 = claim_space(nodes);
      if (if_block5)
        if_block5.l(nodes);
      t5 = claim_space(nodes);
      if (if_block6)
        if_block6.l(nodes);
      if_block6_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, t3, anchor);
      if (if_block4)
        if_block4.m(target, anchor);
      insert_hydration_dev(target, t4, anchor);
      if (if_block5)
        if_block5.m(target, anchor);
      insert_hydration_dev(target, t5, anchor);
      if (if_block6)
        if_block6.m(target, anchor);
      insert_hydration_dev(target, if_block6_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (
        /*twitter*/
        ctx2[4].cardType
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_58(ctx2);
          if_block0.c();
          if_block0.m(t0.parentNode, t0);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*twitter*/
        ctx2[4].site
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_57(ctx2);
          if_block1.c();
          if_block1.m(t1.parentNode, t1);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*twitter*/
        ctx2[4].handle
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_56(ctx2);
          if_block2.c();
          if_block2.m(t2.parentNode, t2);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*twitter*/
        ctx2[4].title
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_55(ctx2);
          if_block3.c();
          if_block3.m(t3.parentNode, t3);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
      if (
        /*twitter*/
        ctx2[4].description
      ) {
        if (if_block4) {
          if_block4.p(ctx2, dirty);
        } else {
          if_block4 = create_if_block_54(ctx2);
          if_block4.c();
          if_block4.m(t4.parentNode, t4);
        }
      } else if (if_block4) {
        if_block4.d(1);
        if_block4 = null;
      }
      if (
        /*twitter*/
        ctx2[4].image
      ) {
        if (if_block5) {
          if_block5.p(ctx2, dirty);
        } else {
          if_block5 = create_if_block_53(ctx2);
          if_block5.c();
          if_block5.m(t5.parentNode, t5);
        }
      } else if (if_block5) {
        if_block5.d(1);
        if_block5 = null;
      }
      if (
        /*twitter*/
        ctx2[4].imageAlt
      ) {
        if (if_block6) {
          if_block6.p(ctx2, dirty);
        } else {
          if_block6 = create_if_block_52(ctx2);
          if_block6.c();
          if_block6.m(if_block6_anchor.parentNode, if_block6_anchor);
        }
      } else if (if_block6) {
        if_block6.d(1);
        if_block6 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(t3);
        detach_dev(t4);
        detach_dev(t5);
        detach_dev(if_block6_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
      if (if_block4)
        if_block4.d(detaching);
      if (if_block5)
        if_block5.d(detaching);
      if (if_block6)
        if_block6.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_51.name,
    type: "if",
    source: "(68:2) {#if twitter}",
    ctx
  });
  return block;
}
function create_if_block_58(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "twitter:card");
      attr_dev(meta, "content", meta_content_value = /*twitter*/
      ctx[4].cardType);
      add_location(meta, file, 69, 6, 2218);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*twitter*/
      16 && meta_content_value !== (meta_content_value = /*twitter*/
      ctx2[4].cardType)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_58.name,
    type: "if",
    source: "(69:4) {#if twitter.cardType}",
    ctx
  });
  return block;
}
function create_if_block_57(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "twitter:site");
      attr_dev(meta, "content", meta_content_value = /*twitter*/
      ctx[4].site);
      add_location(meta, file, 72, 6, 2313);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*twitter*/
      16 && meta_content_value !== (meta_content_value = /*twitter*/
      ctx2[4].site)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_57.name,
    type: "if",
    source: "(72:4) {#if twitter.site}",
    ctx
  });
  return block;
}
function create_if_block_56(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "twitter:creator");
      attr_dev(meta, "content", meta_content_value = /*twitter*/
      ctx[4].handle);
      add_location(meta, file, 75, 6, 2406);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*twitter*/
      16 && meta_content_value !== (meta_content_value = /*twitter*/
      ctx2[4].handle)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_56.name,
    type: "if",
    source: "(75:4) {#if twitter.handle}",
    ctx
  });
  return block;
}
function create_if_block_55(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "twitter:title");
      attr_dev(meta, "content", meta_content_value = /*twitter*/
      ctx[4].title);
      add_location(meta, file, 78, 6, 2503);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*twitter*/
      16 && meta_content_value !== (meta_content_value = /*twitter*/
      ctx2[4].title)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_55.name,
    type: "if",
    source: "(78:4) {#if twitter.title}",
    ctx
  });
  return block;
}
function create_if_block_54(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "twitter:description");
      attr_dev(meta, "content", meta_content_value = /*twitter*/
      ctx[4].description);
      add_location(meta, file, 81, 6, 2603);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*twitter*/
      16 && meta_content_value !== (meta_content_value = /*twitter*/
      ctx2[4].description)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_54.name,
    type: "if",
    source: "(81:4) {#if twitter.description}",
    ctx
  });
  return block;
}
function create_if_block_53(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "twitter:image");
      attr_dev(meta, "content", meta_content_value = /*twitter*/
      ctx[4].image);
      add_location(meta, file, 84, 6, 2709);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*twitter*/
      16 && meta_content_value !== (meta_content_value = /*twitter*/
      ctx2[4].image)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_53.name,
    type: "if",
    source: "(84:4) {#if twitter.image}",
    ctx
  });
  return block;
}
function create_if_block_52(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { name: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "name", "twitter:image:alt");
      attr_dev(meta, "content", meta_content_value = /*twitter*/
      ctx[4].imageAlt);
      add_location(meta, file, 87, 6, 2806);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*twitter*/
      16 && meta_content_value !== (meta_content_value = /*twitter*/
      ctx2[4].imageAlt)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_52.name,
    type: "if",
    source: "(87:4) {#if twitter.imageAlt}",
    ctx
  });
  return block;
}
function create_if_block_50(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "fb:app_id");
      attr_dev(meta, "content", meta_content_value = /*facebook*/
      ctx[5].appId);
      add_location(meta, file, 92, 4, 2907);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*facebook*/
      32 && meta_content_value !== (meta_content_value = /*facebook*/
      ctx2[5].appId)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_50.name,
    type: "if",
    source: "(92:2) {#if facebook}",
    ctx
  });
  return block;
}
function create_if_block_2(ctx) {
  let t0;
  let t1;
  let t2;
  let t3;
  let t4;
  let t5;
  let t6;
  let t7;
  let if_block8_anchor;
  let if_block0 = (
    /*openGraph*/
    (ctx[6].url || /*canonical*/
    ctx[7]) && create_if_block_49(ctx)
  );
  let if_block1 = (
    /*openGraph*/
    ctx[6].type && create_if_block_21(ctx)
  );
  let if_block2 = (
    /*openGraph*/
    (ctx[6].title || /*updatedTitle*/
    ctx[12]) && create_if_block_20(ctx)
  );
  let if_block3 = (
    /*openGraph*/
    (ctx[6].description || /*description*/
    ctx[1]) && create_if_block_19(ctx)
  );
  let if_block4 = (
    /*openGraph*/
    ctx[6].images && /*openGraph*/
    ctx[6].images.length && create_if_block_13(ctx)
  );
  let if_block5 = (
    /*openGraph*/
    ctx[6].videos && /*openGraph*/
    ctx[6].videos.length && create_if_block_8(ctx)
  );
  let if_block6 = (
    /*openGraph*/
    ctx[6].audio && /*openGraph*/
    ctx[6].audio.length && create_if_block_5(ctx)
  );
  let if_block7 = (
    /*openGraph*/
    ctx[6].locale && create_if_block_4(ctx)
  );
  let if_block8 = (
    /*openGraph*/
    ctx[6].siteName && create_if_block_3(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      t0 = space();
      if (if_block1)
        if_block1.c();
      t1 = space();
      if (if_block2)
        if_block2.c();
      t2 = space();
      if (if_block3)
        if_block3.c();
      t3 = space();
      if (if_block4)
        if_block4.c();
      t4 = space();
      if (if_block5)
        if_block5.c();
      t5 = space();
      if (if_block6)
        if_block6.c();
      t6 = space();
      if (if_block7)
        if_block7.c();
      t7 = space();
      if (if_block8)
        if_block8.c();
      if_block8_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block0)
        if_block0.l(nodes);
      t0 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t1 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t2 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      t3 = claim_space(nodes);
      if (if_block4)
        if_block4.l(nodes);
      t4 = claim_space(nodes);
      if (if_block5)
        if_block5.l(nodes);
      t5 = claim_space(nodes);
      if (if_block6)
        if_block6.l(nodes);
      t6 = claim_space(nodes);
      if (if_block7)
        if_block7.l(nodes);
      t7 = claim_space(nodes);
      if (if_block8)
        if_block8.l(nodes);
      if_block8_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, t3, anchor);
      if (if_block4)
        if_block4.m(target, anchor);
      insert_hydration_dev(target, t4, anchor);
      if (if_block5)
        if_block5.m(target, anchor);
      insert_hydration_dev(target, t5, anchor);
      if (if_block6)
        if_block6.m(target, anchor);
      insert_hydration_dev(target, t6, anchor);
      if (if_block7)
        if_block7.m(target, anchor);
      insert_hydration_dev(target, t7, anchor);
      if (if_block8)
        if_block8.m(target, anchor);
      insert_hydration_dev(target, if_block8_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (
        /*openGraph*/
        ctx2[6].url || /*canonical*/
        ctx2[7]
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_49(ctx2);
          if_block0.c();
          if_block0.m(t0.parentNode, t0);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].type
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_21(ctx2);
          if_block1.c();
          if_block1.m(t1.parentNode, t1);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].title || /*updatedTitle*/
        ctx2[12]
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_20(ctx2);
          if_block2.c();
          if_block2.m(t2.parentNode, t2);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].description || /*description*/
        ctx2[1]
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_19(ctx2);
          if_block3.c();
          if_block3.m(t3.parentNode, t3);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].images && /*openGraph*/
        ctx2[6].images.length
      ) {
        if (if_block4) {
          if_block4.p(ctx2, dirty);
        } else {
          if_block4 = create_if_block_13(ctx2);
          if_block4.c();
          if_block4.m(t4.parentNode, t4);
        }
      } else if (if_block4) {
        if_block4.d(1);
        if_block4 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].videos && /*openGraph*/
        ctx2[6].videos.length
      ) {
        if (if_block5) {
          if_block5.p(ctx2, dirty);
        } else {
          if_block5 = create_if_block_8(ctx2);
          if_block5.c();
          if_block5.m(t5.parentNode, t5);
        }
      } else if (if_block5) {
        if_block5.d(1);
        if_block5 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].audio && /*openGraph*/
        ctx2[6].audio.length
      ) {
        if (if_block6) {
          if_block6.p(ctx2, dirty);
        } else {
          if_block6 = create_if_block_5(ctx2);
          if_block6.c();
          if_block6.m(t6.parentNode, t6);
        }
      } else if (if_block6) {
        if_block6.d(1);
        if_block6 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].locale
      ) {
        if (if_block7) {
          if_block7.p(ctx2, dirty);
        } else {
          if_block7 = create_if_block_4(ctx2);
          if_block7.c();
          if_block7.m(t7.parentNode, t7);
        }
      } else if (if_block7) {
        if_block7.d(1);
        if_block7 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].siteName
      ) {
        if (if_block8) {
          if_block8.p(ctx2, dirty);
        } else {
          if_block8 = create_if_block_3(ctx2);
          if_block8.c();
          if_block8.m(if_block8_anchor.parentNode, if_block8_anchor);
        }
      } else if (if_block8) {
        if_block8.d(1);
        if_block8 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(t3);
        detach_dev(t4);
        detach_dev(t5);
        detach_dev(t6);
        detach_dev(t7);
        detach_dev(if_block8_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
      if (if_block4)
        if_block4.d(detaching);
      if (if_block5)
        if_block5.d(detaching);
      if (if_block6)
        if_block6.d(detaching);
      if (if_block7)
        if_block7.d(detaching);
      if (if_block8)
        if_block8.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_2.name,
    type: "if",
    source: "(96:2) {#if openGraph}",
    ctx
  });
  return block;
}
function create_if_block_49(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:url");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].url || /*canonical*/
      ctx[7]);
      add_location(meta, file, 97, 6, 3032);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph, canonical*/
      192 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].url || /*canonical*/
      ctx2[7])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_49.name,
    type: "if",
    source: "(97:4) {#if openGraph.url || canonical}",
    ctx
  });
  return block;
}
function create_if_block_21(ctx) {
  let meta;
  let meta_content_value;
  let t;
  let show_if;
  let show_if_1;
  let show_if_2;
  let show_if_3;
  let if_block_anchor;
  function select_block_type(ctx2, dirty) {
    if (dirty[0] & /*openGraph*/
    64)
      show_if = null;
    if (dirty[0] & /*openGraph*/
    64)
      show_if_1 = null;
    if (dirty[0] & /*openGraph*/
    64)
      show_if_2 = null;
    if (dirty[0] & /*openGraph*/
    64)
      show_if_3 = null;
    if (show_if == null)
      show_if = !!/*openGraph*/
      (ctx2[6].type.toLowerCase() === "profile" && /*openGraph*/
      ctx2[6].profile);
    if (show_if)
      return create_if_block_22;
    if (show_if_1 == null)
      show_if_1 = !!/*openGraph*/
      (ctx2[6].type.toLowerCase() === "book" && /*openGraph*/
      ctx2[6].book);
    if (show_if_1)
      return create_if_block_27;
    if (show_if_2 == null)
      show_if_2 = !!/*openGraph*/
      (ctx2[6].type.toLowerCase() === "article" && /*openGraph*/
      ctx2[6].article);
    if (show_if_2)
      return create_if_block_32;
    if (show_if_3 == null)
      show_if_3 = !!/*openGraph*/
      (ctx2[6].type.toLowerCase() === "video.movie" || /*openGraph*/
      ctx2[6].type.toLowerCase() === "video.episode" || /*openGraph*/
      ctx2[6].type.toLowerCase() === "video.tv_show" || /*openGraph*/
      ctx2[6].type.toLowerCase() === "video.other" && /*openGraph*/
      ctx2[6].video);
    if (show_if_3)
      return create_if_block_39;
  }
  let current_block_type = select_block_type(ctx, [-1, -1]);
  let if_block = current_block_type && current_block_type(ctx);
  const block = {
    c: function create() {
      meta = element("meta");
      t = space();
      if (if_block)
        if_block.c();
      if_block_anchor = empty();
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      t = claim_space(nodes);
      if (if_block)
        if_block.l(nodes);
      if_block_anchor = empty();
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:type");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].type.toLowerCase());
      add_location(meta, file, 101, 6, 3138);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
      insert_hydration_dev(target, t, anchor);
      if (if_block)
        if_block.m(target, anchor);
      insert_hydration_dev(target, if_block_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].type.toLowerCase())) {
        attr_dev(meta, "content", meta_content_value);
      }
      if (current_block_type === (current_block_type = select_block_type(ctx2, dirty)) && if_block) {
        if_block.p(ctx2, dirty);
      } else {
        if (if_block)
          if_block.d(1);
        if_block = current_block_type && current_block_type(ctx2);
        if (if_block) {
          if_block.c();
          if_block.m(if_block_anchor.parentNode, if_block_anchor);
        }
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
        detach_dev(t);
        detach_dev(if_block_anchor);
      }
      if (if_block) {
        if_block.d(detaching);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_21.name,
    type: "if",
    source: "(101:4) {#if openGraph.type}",
    ctx
  });
  return block;
}
function create_if_block_39(ctx) {
  var _a, _b, _c, _d, _e, _f, _g;
  let t0;
  let t1;
  let t2;
  let t3;
  let t4;
  let t5;
  let if_block6_anchor;
  let if_block0 = (
    /*openGraph*/
    ((_a = ctx[6].video) == null ? void 0 : _a.actors) && /*openGraph*/
    ctx[6].video.actors.length && create_if_block_46(ctx)
  );
  let if_block1 = (
    /*openGraph*/
    ((_b = ctx[6].video) == null ? void 0 : _b.directors) && /*openGraph*/
    ctx[6].video.directors.length && create_if_block_45(ctx)
  );
  let if_block2 = (
    /*openGraph*/
    ((_c = ctx[6].video) == null ? void 0 : _c.writers) && /*openGraph*/
    ctx[6].video.writers.length && create_if_block_44(ctx)
  );
  let if_block3 = (
    /*openGraph*/
    ((_d = ctx[6].video) == null ? void 0 : _d.duration) && create_if_block_43(ctx)
  );
  let if_block4 = (
    /*openGraph*/
    ((_e = ctx[6].video) == null ? void 0 : _e.releaseDate) && create_if_block_42(ctx)
  );
  let if_block5 = (
    /*openGraph*/
    ((_f = ctx[6].video) == null ? void 0 : _f.tags) && /*openGraph*/
    ctx[6].video.tags.length && create_if_block_41(ctx)
  );
  let if_block6 = (
    /*openGraph*/
    ((_g = ctx[6].video) == null ? void 0 : _g.series) && create_if_block_40(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      t0 = space();
      if (if_block1)
        if_block1.c();
      t1 = space();
      if (if_block2)
        if_block2.c();
      t2 = space();
      if (if_block3)
        if_block3.c();
      t3 = space();
      if (if_block4)
        if_block4.c();
      t4 = space();
      if (if_block5)
        if_block5.c();
      t5 = space();
      if (if_block6)
        if_block6.c();
      if_block6_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block0)
        if_block0.l(nodes);
      t0 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t1 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t2 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      t3 = claim_space(nodes);
      if (if_block4)
        if_block4.l(nodes);
      t4 = claim_space(nodes);
      if (if_block5)
        if_block5.l(nodes);
      t5 = claim_space(nodes);
      if (if_block6)
        if_block6.l(nodes);
      if_block6_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, t3, anchor);
      if (if_block4)
        if_block4.m(target, anchor);
      insert_hydration_dev(target, t4, anchor);
      if (if_block5)
        if_block5.m(target, anchor);
      insert_hydration_dev(target, t5, anchor);
      if (if_block6)
        if_block6.m(target, anchor);
      insert_hydration_dev(target, if_block6_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      var _a2, _b2, _c2, _d2, _e2, _f2, _g2;
      if (
        /*openGraph*/
        ((_a2 = ctx2[6].video) == null ? void 0 : _a2.actors) && /*openGraph*/
        ctx2[6].video.actors.length
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_46(ctx2);
          if_block0.c();
          if_block0.m(t0.parentNode, t0);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*openGraph*/
        ((_b2 = ctx2[6].video) == null ? void 0 : _b2.directors) && /*openGraph*/
        ctx2[6].video.directors.length
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_45(ctx2);
          if_block1.c();
          if_block1.m(t1.parentNode, t1);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*openGraph*/
        ((_c2 = ctx2[6].video) == null ? void 0 : _c2.writers) && /*openGraph*/
        ctx2[6].video.writers.length
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_44(ctx2);
          if_block2.c();
          if_block2.m(t2.parentNode, t2);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*openGraph*/
        (_d2 = ctx2[6].video) == null ? void 0 : _d2.duration
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_43(ctx2);
          if_block3.c();
          if_block3.m(t3.parentNode, t3);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
      if (
        /*openGraph*/
        (_e2 = ctx2[6].video) == null ? void 0 : _e2.releaseDate
      ) {
        if (if_block4) {
          if_block4.p(ctx2, dirty);
        } else {
          if_block4 = create_if_block_42(ctx2);
          if_block4.c();
          if_block4.m(t4.parentNode, t4);
        }
      } else if (if_block4) {
        if_block4.d(1);
        if_block4 = null;
      }
      if (
        /*openGraph*/
        ((_f2 = ctx2[6].video) == null ? void 0 : _f2.tags) && /*openGraph*/
        ctx2[6].video.tags.length
      ) {
        if (if_block5) {
          if_block5.p(ctx2, dirty);
        } else {
          if_block5 = create_if_block_41(ctx2);
          if_block5.c();
          if_block5.m(t5.parentNode, t5);
        }
      } else if (if_block5) {
        if_block5.d(1);
        if_block5 = null;
      }
      if (
        /*openGraph*/
        (_g2 = ctx2[6].video) == null ? void 0 : _g2.series
      ) {
        if (if_block6) {
          if_block6.p(ctx2, dirty);
        } else {
          if_block6 = create_if_block_40(ctx2);
          if_block6.c();
          if_block6.m(if_block6_anchor.parentNode, if_block6_anchor);
        }
      } else if (if_block6) {
        if_block6.d(1);
        if_block6 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(t3);
        detach_dev(t4);
        detach_dev(t5);
        detach_dev(if_block6_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
      if (if_block4)
        if_block4.d(detaching);
      if (if_block5)
        if_block5.d(detaching);
      if (if_block6)
        if_block6.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_39.name,
    type: "if",
    source: "(167:238) ",
    ctx
  });
  return block;
}
function create_if_block_32(ctx) {
  let t0;
  let t1;
  let t2;
  let t3;
  let t4;
  let if_block5_anchor;
  let if_block0 = (
    /*openGraph*/
    ctx[6].article.publishedTime && create_if_block_38(ctx)
  );
  let if_block1 = (
    /*openGraph*/
    ctx[6].article.modifiedTime && create_if_block_37(ctx)
  );
  let if_block2 = (
    /*openGraph*/
    ctx[6].article.expirationTime && create_if_block_36(ctx)
  );
  let if_block3 = (
    /*openGraph*/
    ctx[6].article.authors && /*openGraph*/
    ctx[6].article.authors.length && create_if_block_35(ctx)
  );
  let if_block4 = (
    /*openGraph*/
    ctx[6].article.section && create_if_block_34(ctx)
  );
  let if_block5 = (
    /*openGraph*/
    ctx[6].article.tags && /*openGraph*/
    ctx[6].article.tags.length && create_if_block_33(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      t0 = space();
      if (if_block1)
        if_block1.c();
      t1 = space();
      if (if_block2)
        if_block2.c();
      t2 = space();
      if (if_block3)
        if_block3.c();
      t3 = space();
      if (if_block4)
        if_block4.c();
      t4 = space();
      if (if_block5)
        if_block5.c();
      if_block5_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block0)
        if_block0.l(nodes);
      t0 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t1 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t2 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      t3 = claim_space(nodes);
      if (if_block4)
        if_block4.l(nodes);
      t4 = claim_space(nodes);
      if (if_block5)
        if_block5.l(nodes);
      if_block5_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, t3, anchor);
      if (if_block4)
        if_block4.m(target, anchor);
      insert_hydration_dev(target, t4, anchor);
      if (if_block5)
        if_block5.m(target, anchor);
      insert_hydration_dev(target, if_block5_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (
        /*openGraph*/
        ctx2[6].article.publishedTime
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_38(ctx2);
          if_block0.c();
          if_block0.m(t0.parentNode, t0);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].article.modifiedTime
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_37(ctx2);
          if_block1.c();
          if_block1.m(t1.parentNode, t1);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].article.expirationTime
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_36(ctx2);
          if_block2.c();
          if_block2.m(t2.parentNode, t2);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].article.authors && /*openGraph*/
        ctx2[6].article.authors.length
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_35(ctx2);
          if_block3.c();
          if_block3.m(t3.parentNode, t3);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].article.section
      ) {
        if (if_block4) {
          if_block4.p(ctx2, dirty);
        } else {
          if_block4 = create_if_block_34(ctx2);
          if_block4.c();
          if_block4.m(t4.parentNode, t4);
        }
      } else if (if_block4) {
        if_block4.d(1);
        if_block4 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].article.tags && /*openGraph*/
        ctx2[6].article.tags.length
      ) {
        if (if_block5) {
          if_block5.p(ctx2, dirty);
        } else {
          if_block5 = create_if_block_33(ctx2);
          if_block5.c();
          if_block5.m(if_block5_anchor.parentNode, if_block5_anchor);
        }
      } else if (if_block5) {
        if_block5.d(1);
        if_block5 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(t3);
        detach_dev(t4);
        detach_dev(if_block5_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
      if (if_block4)
        if_block4.d(detaching);
      if (if_block5)
        if_block5.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_32.name,
    type: "if",
    source: "(139:80) ",
    ctx
  });
  return block;
}
function create_if_block_27(ctx) {
  let t0;
  let t1;
  let t2;
  let if_block3_anchor;
  let if_block0 = (
    /*openGraph*/
    ctx[6].book.authors && /*openGraph*/
    ctx[6].book.authors.length && create_if_block_31(ctx)
  );
  let if_block1 = (
    /*openGraph*/
    ctx[6].book.isbn && create_if_block_30(ctx)
  );
  let if_block2 = (
    /*openGraph*/
    ctx[6].book.releaseDate && create_if_block_29(ctx)
  );
  let if_block3 = (
    /*openGraph*/
    ctx[6].book.tags && /*openGraph*/
    ctx[6].book.tags.length && create_if_block_28(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      t0 = space();
      if (if_block1)
        if_block1.c();
      t1 = space();
      if (if_block2)
        if_block2.c();
      t2 = space();
      if (if_block3)
        if_block3.c();
      if_block3_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block0)
        if_block0.l(nodes);
      t0 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t1 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t2 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      if_block3_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, if_block3_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (
        /*openGraph*/
        ctx2[6].book.authors && /*openGraph*/
        ctx2[6].book.authors.length
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_31(ctx2);
          if_block0.c();
          if_block0.m(t0.parentNode, t0);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].book.isbn
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_30(ctx2);
          if_block1.c();
          if_block1.m(t1.parentNode, t1);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].book.releaseDate
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_29(ctx2);
          if_block2.c();
          if_block2.m(t2.parentNode, t2);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].book.tags && /*openGraph*/
        ctx2[6].book.tags.length
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_28(ctx2);
          if_block3.c();
          if_block3.m(if_block3_anchor.parentNode, if_block3_anchor);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(if_block3_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_27.name,
    type: "if",
    source: "(119:74) ",
    ctx
  });
  return block;
}
function create_if_block_22(ctx) {
  let t0;
  let t1;
  let t2;
  let if_block3_anchor;
  let if_block0 = (
    /*openGraph*/
    ctx[6].profile.firstName && create_if_block_26(ctx)
  );
  let if_block1 = (
    /*openGraph*/
    ctx[6].profile.lastName && create_if_block_25(ctx)
  );
  let if_block2 = (
    /*openGraph*/
    ctx[6].profile.username && create_if_block_24(ctx)
  );
  let if_block3 = (
    /*openGraph*/
    ctx[6].profile.gender && create_if_block_23(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      t0 = space();
      if (if_block1)
        if_block1.c();
      t1 = space();
      if (if_block2)
        if_block2.c();
      t2 = space();
      if (if_block3)
        if_block3.c();
      if_block3_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block0)
        if_block0.l(nodes);
      t0 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t1 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t2 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      if_block3_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, if_block3_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (
        /*openGraph*/
        ctx2[6].profile.firstName
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_26(ctx2);
          if_block0.c();
          if_block0.m(t0.parentNode, t0);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].profile.lastName
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_25(ctx2);
          if_block1.c();
          if_block1.m(t1.parentNode, t1);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].profile.username
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_24(ctx2);
          if_block2.c();
          if_block2.m(t2.parentNode, t2);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*openGraph*/
        ctx2[6].profile.gender
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_23(ctx2);
          if_block3.c();
          if_block3.m(if_block3_anchor.parentNode, if_block3_anchor);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(if_block3_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_22.name,
    type: "if",
    source: "(103:6) {#if openGraph.type.toLowerCase() === 'profile' && openGraph.profile}",
    ctx
  });
  return block;
}
function create_if_block_46(ctx) {
  let each_1_anchor;
  let each_value_12 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].video.actors
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_12.length; i += 1) {
    each_blocks[i] = create_each_block_12(get_each_context_12(ctx, each_value_12, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_12 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].video.actors
        );
        let i;
        for (i = 0; i < each_value_12.length; i += 1) {
          const child_ctx = get_each_context_12(ctx2, each_value_12, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_12(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_12.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_46.name,
    type: "if",
    source: "(168:8) {#if openGraph.video?.actors && openGraph.video.actors.length}",
    ctx
  });
  return block;
}
function create_if_block_48(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:actor");
      attr_dev(meta, "content", meta_content_value = /*actor*/
      ctx[47].profile);
      add_location(meta, file, 170, 14, 6112);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*actor*/
      ctx2[47].profile)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_48.name,
    type: "if",
    source: "(170:12) {#if actor.profile}",
    ctx
  });
  return block;
}
function create_if_block_47(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:actor:role");
      attr_dev(meta, "content", meta_content_value = /*actor*/
      ctx[47].role);
      add_location(meta, file, 173, 14, 6229);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*actor*/
      ctx2[47].role)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_47.name,
    type: "if",
    source: "(173:12) {#if actor.role}",
    ctx
  });
  return block;
}
function create_each_block_12(ctx) {
  let t;
  let if_block1_anchor;
  let if_block0 = (
    /*actor*/
    ctx[47].profile && create_if_block_48(ctx)
  );
  let if_block1 = (
    /*actor*/
    ctx[47].role && create_if_block_47(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      t = space();
      if (if_block1)
        if_block1.c();
      if_block1_anchor = empty();
    },
    l: function claim(nodes) {
      if (if_block0)
        if_block0.l(nodes);
      t = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      if_block1_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, if_block1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (
        /*actor*/
        ctx2[47].profile
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_48(ctx2);
          if_block0.c();
          if_block0.m(t.parentNode, t);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*actor*/
        ctx2[47].role
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_47(ctx2);
          if_block1.c();
          if_block1.m(if_block1_anchor.parentNode, if_block1_anchor);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t);
        detach_dev(if_block1_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_12.name,
    type: "each",
    source: "(169:10) {#each openGraph.video.actors as actor}",
    ctx
  });
  return block;
}
function create_if_block_45(ctx) {
  let each_1_anchor;
  let each_value_11 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].video.directors
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_11.length; i += 1) {
    each_blocks[i] = create_each_block_11(get_each_context_11(ctx, each_value_11, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_11 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].video.directors
        );
        let i;
        for (i = 0; i < each_value_11.length; i += 1) {
          const child_ctx = get_each_context_11(ctx2, each_value_11, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_11(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_11.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_45.name,
    type: "if",
    source: "(179:8) {#if openGraph.video?.directors && openGraph.video.directors.length}",
    ctx
  });
  return block;
}
function create_each_block_11(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:director");
      attr_dev(meta, "content", meta_content_value = /*director*/
      ctx[44]);
      add_location(meta, file, 180, 12, 6483);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*director*/
      ctx2[44])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_11.name,
    type: "each",
    source: "(180:10) {#each openGraph.video.directors as director}",
    ctx
  });
  return block;
}
function create_if_block_44(ctx) {
  let each_1_anchor;
  let each_value_10 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].video.writers
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_10.length; i += 1) {
    each_blocks[i] = create_each_block_10(get_each_context_10(ctx, each_value_10, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_10 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].video.writers
        );
        let i;
        for (i = 0; i < each_value_10.length; i += 1) {
          const child_ctx = get_each_context_10(ctx2, each_value_10, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_10(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_10.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_44.name,
    type: "if",
    source: "(185:8) {#if openGraph.video?.writers && openGraph.video.writers.length}",
    ctx
  });
  return block;
}
function create_each_block_10(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:writer");
      attr_dev(meta, "content", meta_content_value = /*writer*/
      ctx[41]);
      add_location(meta, file, 186, 12, 6707);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*writer*/
      ctx2[41])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_10.name,
    type: "each",
    source: "(186:10) {#each openGraph.video.writers as writer}",
    ctx
  });
  return block;
}
function create_if_block_43(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:duration");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].video.duration.toString());
      add_location(meta, file, 191, 10, 6840);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].video.duration.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_43.name,
    type: "if",
    source: "(191:8) {#if openGraph.video?.duration}",
    ctx
  });
  return block;
}
function create_if_block_42(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:release_date");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].video.releaseDate);
      add_location(meta, file, 195, 10, 6989);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].video.releaseDate)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_42.name,
    type: "if",
    source: "(195:8) {#if openGraph.video?.releaseDate}",
    ctx
  });
  return block;
}
function create_if_block_41(ctx) {
  let each_1_anchor;
  let each_value_9 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].video.tags
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_9.length; i += 1) {
    each_blocks[i] = create_each_block_9(get_each_context_9(ctx, each_value_9, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_9 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].video.tags
        );
        let i;
        for (i = 0; i < each_value_9.length; i += 1) {
          const child_ctx = get_each_context_9(ctx2, each_value_9, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_9(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_9.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_41.name,
    type: "if",
    source: "(199:8) {#if openGraph.video?.tags && openGraph.video.tags.length}",
    ctx
  });
  return block;
}
function create_each_block_9(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:tag");
      attr_dev(meta, "content", meta_content_value = /*tag*/
      ctx[16]);
      add_location(meta, file, 200, 12, 7206);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*tag*/
      ctx2[16])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_9.name,
    type: "each",
    source: "(200:10) {#each openGraph.video.tags as tag}",
    ctx
  });
  return block;
}
function create_if_block_40(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "video:series");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].video.series);
      add_location(meta, file, 205, 10, 7331);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].video.series)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_40.name,
    type: "if",
    source: "(205:8) {#if openGraph.video?.series}",
    ctx
  });
  return block;
}
function create_if_block_38(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "article:published_time");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].article.publishedTime);
      add_location(meta, file, 140, 10, 4722);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].article.publishedTime)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_38.name,
    type: "if",
    source: "(140:8) {#if openGraph.article.publishedTime}",
    ctx
  });
  return block;
}
function create_if_block_37(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "article:modified_time");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].article.modifiedTime);
      add_location(meta, file, 144, 10, 4877);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].article.modifiedTime)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_37.name,
    type: "if",
    source: "(144:8) {#if openGraph.article.modifiedTime}",
    ctx
  });
  return block;
}
function create_if_block_36(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "article:expiration_time");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].article.expirationTime);
      add_location(meta, file, 148, 10, 5032);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].article.expirationTime)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_36.name,
    type: "if",
    source: "(148:8) {#if openGraph.article.expirationTime}",
    ctx
  });
  return block;
}
function create_if_block_35(ctx) {
  let each_1_anchor;
  let each_value_8 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].article.authors
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_8.length; i += 1) {
    each_blocks[i] = create_each_block_8(get_each_context_8(ctx, each_value_8, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_8 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].article.authors
        );
        let i;
        for (i = 0; i < each_value_8.length; i += 1) {
          const child_ctx = get_each_context_8(ctx2, each_value_8, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_8(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_8.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_35.name,
    type: "if",
    source: "(152:8) {#if openGraph.article.authors && openGraph.article.authors.length}",
    ctx
  });
  return block;
}
function create_each_block_8(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "article:author");
      attr_dev(meta, "content", meta_content_value = /*author*/
      ctx[32]);
      add_location(meta, file, 153, 12, 5276);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*author*/
      ctx2[32])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_8.name,
    type: "each",
    source: "(153:10) {#each openGraph.article.authors as author}",
    ctx
  });
  return block;
}
function create_if_block_34(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "article:section");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].article.section);
      add_location(meta, file, 158, 10, 5411);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].article.section)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_34.name,
    type: "if",
    source: "(158:8) {#if openGraph.article.section}",
    ctx
  });
  return block;
}
function create_if_block_33(ctx) {
  let each_1_anchor;
  let each_value_7 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].article.tags
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_7.length; i += 1) {
    each_blocks[i] = create_each_block_7(get_each_context_7(ctx, each_value_7, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_7 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].article.tags
        );
        let i;
        for (i = 0; i < each_value_7.length; i += 1) {
          const child_ctx = get_each_context_7(ctx2, each_value_7, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_7(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_7.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_33.name,
    type: "if",
    source: "(162:8) {#if openGraph.article.tags && openGraph.article.tags.length}",
    ctx
  });
  return block;
}
function create_each_block_7(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "article:tag");
      attr_dev(meta, "content", meta_content_value = /*tag*/
      ctx[16]);
      add_location(meta, file, 163, 12, 5628);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*tag*/
      ctx2[16])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_7.name,
    type: "each",
    source: "(163:10) {#each openGraph.article.tags as tag}",
    ctx
  });
  return block;
}
function create_if_block_31(ctx) {
  let each_1_anchor;
  let each_value_6 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].book.authors
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_6.length; i += 1) {
    each_blocks[i] = create_each_block_6(get_each_context_6(ctx, each_value_6, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_6 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].book.authors
        );
        let i;
        for (i = 0; i < each_value_6.length; i += 1) {
          const child_ctx = get_each_context_6(ctx2, each_value_6, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_6(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_6.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_31.name,
    type: "if",
    source: "(120:8) {#if openGraph.book.authors && openGraph.book.authors.length}",
    ctx
  });
  return block;
}
function create_each_block_6(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "book:author");
      attr_dev(meta, "content", meta_content_value = /*author*/
      ctx[32]);
      add_location(meta, file, 121, 12, 4047);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*author*/
      ctx2[32])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_6.name,
    type: "each",
    source: "(121:10) {#each openGraph.book.authors as author}",
    ctx
  });
  return block;
}
function create_if_block_30(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "book:isbn");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].book.isbn);
      add_location(meta, file, 126, 10, 4173);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].book.isbn)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_30.name,
    type: "if",
    source: "(126:8) {#if openGraph.book.isbn}",
    ctx
  });
  return block;
}
function create_if_block_29(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "book:release_date");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].book.releaseDate);
      add_location(meta, file, 130, 10, 4299);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].book.releaseDate)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_29.name,
    type: "if",
    source: "(130:8) {#if openGraph.book.releaseDate}",
    ctx
  });
  return block;
}
function create_if_block_28(ctx) {
  let each_1_anchor;
  let each_value_5 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].book.tags
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_5.length; i += 1) {
    each_blocks[i] = create_each_block_5(get_each_context_5(ctx, each_value_5, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_5 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].book.tags
        );
        let i;
        for (i = 0; i < each_value_5.length; i += 1) {
          const child_ctx = get_each_context_5(ctx2, each_value_5, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_5(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_5.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_28.name,
    type: "if",
    source: "(134:8) {#if openGraph.book.tags && openGraph.book.tags.length}",
    ctx
  });
  return block;
}
function create_each_block_5(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "book:tag");
      attr_dev(meta, "content", meta_content_value = /*tag*/
      ctx[16]);
      add_location(meta, file, 135, 12, 4510);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*tag*/
      ctx2[16])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_5.name,
    type: "each",
    source: "(135:10) {#each openGraph.book.tags as tag}",
    ctx
  });
  return block;
}
function create_if_block_26(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "profile:first_name");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].profile.firstName);
      add_location(meta, file, 104, 10, 3333);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].profile.firstName)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_26.name,
    type: "if",
    source: "(104:8) {#if openGraph.profile.firstName}",
    ctx
  });
  return block;
}
function create_if_block_25(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "profile:last_name");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].profile.lastName);
      add_location(meta, file, 108, 10, 3476);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].profile.lastName)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_25.name,
    type: "if",
    source: "(108:8) {#if openGraph.profile.lastName}",
    ctx
  });
  return block;
}
function create_if_block_24(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "profile:username");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].profile.username);
      add_location(meta, file, 112, 10, 3617);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].profile.username)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_24.name,
    type: "if",
    source: "(112:8) {#if openGraph.profile.username}",
    ctx
  });
  return block;
}
function create_if_block_23(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "profile:gender");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].profile.gender);
      add_location(meta, file, 116, 10, 3755);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].profile.gender)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_23.name,
    type: "if",
    source: "(116:8) {#if openGraph.profile.gender}",
    ctx
  });
  return block;
}
function create_if_block_20(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:title");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].title || /*updatedTitle*/
      ctx[12]);
      add_location(meta, file, 211, 6, 7482);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph, updatedTitle*/
      4160 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].title || /*updatedTitle*/
      ctx2[12])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_20.name,
    type: "if",
    source: "(211:4) {#if openGraph.title || updatedTitle}",
    ctx
  });
  return block;
}
function create_if_block_19(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:description");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].description || /*description*/
      ctx[1]);
      add_location(meta, file, 215, 6, 7617);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph, description*/
      66 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].description || /*description*/
      ctx2[1])) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_19.name,
    type: "if",
    source: "(215:4) {#if openGraph.description || description}",
    ctx
  });
  return block;
}
function create_if_block_13(ctx) {
  let each_1_anchor;
  let each_value_4 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].images
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_4.length; i += 1) {
    each_blocks[i] = create_each_block_4(get_each_context_4(ctx, each_value_4, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_4 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].images
        );
        let i;
        for (i = 0; i < each_value_4.length; i += 1) {
          const child_ctx = get_each_context_4(ctx2, each_value_4, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_4(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_4.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_13.name,
    type: "if",
    source: "(219:4) {#if openGraph.images && openGraph.images.length}",
    ctx
  });
  return block;
}
function create_if_block_18(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:image:alt");
      attr_dev(meta, "content", meta_content_value = /*image*/
      ctx[27].alt);
      add_location(meta, file, 222, 10, 7895);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*image*/
      ctx2[27].alt)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_18.name,
    type: "if",
    source: "(222:8) {#if image.alt}",
    ctx
  });
  return block;
}
function create_if_block_17(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:image:width");
      attr_dev(meta, "content", meta_content_value = /*image*/
      ctx[27].width.toString());
      add_location(meta, file, 225, 10, 7998);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*image*/
      ctx2[27].width.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_17.name,
    type: "if",
    source: "(225:8) {#if image.width}",
    ctx
  });
  return block;
}
function create_if_block_16(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:image:height");
      attr_dev(meta, "content", meta_content_value = /*image*/
      ctx[27].height.toString());
      add_location(meta, file, 228, 10, 8117);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*image*/
      ctx2[27].height.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_16.name,
    type: "if",
    source: "(228:8) {#if image.height}",
    ctx
  });
  return block;
}
function create_if_block_15(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:image:secure_url");
      attr_dev(meta, "content", meta_content_value = /*image*/
      ctx[27].secureUrl.toString());
      add_location(meta, file, 231, 10, 8241);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*image*/
      ctx2[27].secureUrl.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_15.name,
    type: "if",
    source: "(231:8) {#if image.secureUrl}",
    ctx
  });
  return block;
}
function create_if_block_14(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:image:type");
      attr_dev(meta, "content", meta_content_value = /*image*/
      ctx[27].type.toString());
      add_location(meta, file, 234, 10, 8367);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*image*/
      ctx2[27].type.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_14.name,
    type: "if",
    source: "(234:8) {#if image.type}",
    ctx
  });
  return block;
}
function create_each_block_4(ctx) {
  let meta;
  let meta_content_value;
  let t0;
  let t1;
  let t2;
  let t3;
  let t4;
  let if_block4_anchor;
  let if_block0 = (
    /*image*/
    ctx[27].alt && create_if_block_18(ctx)
  );
  let if_block1 = (
    /*image*/
    ctx[27].width && create_if_block_17(ctx)
  );
  let if_block2 = (
    /*image*/
    ctx[27].height && create_if_block_16(ctx)
  );
  let if_block3 = (
    /*image*/
    ctx[27].secureUrl && create_if_block_15(ctx)
  );
  let if_block4 = (
    /*image*/
    ctx[27].type && create_if_block_14(ctx)
  );
  const block = {
    c: function create() {
      meta = element("meta");
      t0 = space();
      if (if_block0)
        if_block0.c();
      t1 = space();
      if (if_block1)
        if_block1.c();
      t2 = space();
      if (if_block2)
        if_block2.c();
      t3 = space();
      if (if_block3)
        if_block3.c();
      t4 = space();
      if (if_block4)
        if_block4.c();
      if_block4_anchor = empty();
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      t0 = claim_space(nodes);
      if (if_block0)
        if_block0.l(nodes);
      t1 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t2 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t3 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      t4 = claim_space(nodes);
      if (if_block4)
        if_block4.l(nodes);
      if_block4_anchor = empty();
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:image");
      attr_dev(meta, "content", meta_content_value = /*image*/
      ctx[27].url);
      add_location(meta, file, 220, 8, 7812);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t3, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, t4, anchor);
      if (if_block4)
        if_block4.m(target, anchor);
      insert_hydration_dev(target, if_block4_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*image*/
      ctx2[27].url)) {
        attr_dev(meta, "content", meta_content_value);
      }
      if (
        /*image*/
        ctx2[27].alt
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_18(ctx2);
          if_block0.c();
          if_block0.m(t1.parentNode, t1);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*image*/
        ctx2[27].width
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_17(ctx2);
          if_block1.c();
          if_block1.m(t2.parentNode, t2);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*image*/
        ctx2[27].height
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_16(ctx2);
          if_block2.c();
          if_block2.m(t3.parentNode, t3);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*image*/
        ctx2[27].secureUrl
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_15(ctx2);
          if_block3.c();
          if_block3.m(t4.parentNode, t4);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
      if (
        /*image*/
        ctx2[27].type
      ) {
        if (if_block4) {
          if_block4.p(ctx2, dirty);
        } else {
          if_block4 = create_if_block_14(ctx2);
          if_block4.c();
          if_block4.m(if_block4_anchor.parentNode, if_block4_anchor);
        }
      } else if (if_block4) {
        if_block4.d(1);
        if_block4 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(t3);
        detach_dev(t4);
        detach_dev(if_block4_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
      if (if_block4)
        if_block4.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_4.name,
    type: "each",
    source: "(220:6) {#each openGraph.images as image}",
    ctx
  });
  return block;
}
function create_if_block_8(ctx) {
  let each_1_anchor;
  let each_value_3 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].videos
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_3.length; i += 1) {
    each_blocks[i] = create_each_block_3(get_each_context_3(ctx, each_value_3, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_3 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].videos
        );
        let i;
        for (i = 0; i < each_value_3.length; i += 1) {
          const child_ctx = get_each_context_3(ctx2, each_value_3, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_3(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_3.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_8.name,
    type: "if",
    source: "(240:4) {#if openGraph.videos && openGraph.videos.length}",
    ctx
  });
  return block;
}
function create_if_block_12(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:video:width");
      attr_dev(meta, "content", meta_content_value = /*video*/
      ctx[24].width.toString());
      add_location(meta, file, 243, 10, 8659);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*video*/
      ctx2[24].width.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_12.name,
    type: "if",
    source: "(243:8) {#if video.width}",
    ctx
  });
  return block;
}
function create_if_block_11(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:video:height");
      attr_dev(meta, "content", meta_content_value = /*video*/
      ctx[24].height.toString());
      add_location(meta, file, 246, 10, 8778);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*video*/
      ctx2[24].height.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_11.name,
    type: "if",
    source: "(246:8) {#if video.height}",
    ctx
  });
  return block;
}
function create_if_block_10(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:video:secure_url");
      attr_dev(meta, "content", meta_content_value = /*video*/
      ctx[24].secureUrl.toString());
      add_location(meta, file, 249, 10, 8902);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*video*/
      ctx2[24].secureUrl.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_10.name,
    type: "if",
    source: "(249:8) {#if video.secureUrl}",
    ctx
  });
  return block;
}
function create_if_block_9(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:video:type");
      attr_dev(meta, "content", meta_content_value = /*video*/
      ctx[24].type.toString());
      add_location(meta, file, 252, 10, 9028);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*video*/
      ctx2[24].type.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_9.name,
    type: "if",
    source: "(252:8) {#if video.type}",
    ctx
  });
  return block;
}
function create_each_block_3(ctx) {
  let meta;
  let meta_content_value;
  let t0;
  let t1;
  let t2;
  let t3;
  let if_block3_anchor;
  let if_block0 = (
    /*video*/
    ctx[24].width && create_if_block_12(ctx)
  );
  let if_block1 = (
    /*video*/
    ctx[24].height && create_if_block_11(ctx)
  );
  let if_block2 = (
    /*video*/
    ctx[24].secureUrl && create_if_block_10(ctx)
  );
  let if_block3 = (
    /*video*/
    ctx[24].type && create_if_block_9(ctx)
  );
  const block = {
    c: function create() {
      meta = element("meta");
      t0 = space();
      if (if_block0)
        if_block0.c();
      t1 = space();
      if (if_block1)
        if_block1.c();
      t2 = space();
      if (if_block2)
        if_block2.c();
      t3 = space();
      if (if_block3)
        if_block3.c();
      if_block3_anchor = empty();
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      t0 = claim_space(nodes);
      if (if_block0)
        if_block0.l(nodes);
      t1 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      t2 = claim_space(nodes);
      if (if_block2)
        if_block2.l(nodes);
      t3 = claim_space(nodes);
      if (if_block3)
        if_block3.l(nodes);
      if_block3_anchor = empty();
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:video");
      attr_dev(meta, "content", meta_content_value = /*video*/
      ctx[24].url);
      add_location(meta, file, 241, 8, 8574);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, t2, anchor);
      if (if_block2)
        if_block2.m(target, anchor);
      insert_hydration_dev(target, t3, anchor);
      if (if_block3)
        if_block3.m(target, anchor);
      insert_hydration_dev(target, if_block3_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*video*/
      ctx2[24].url)) {
        attr_dev(meta, "content", meta_content_value);
      }
      if (
        /*video*/
        ctx2[24].width
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_12(ctx2);
          if_block0.c();
          if_block0.m(t1.parentNode, t1);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*video*/
        ctx2[24].height
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_11(ctx2);
          if_block1.c();
          if_block1.m(t2.parentNode, t2);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*video*/
        ctx2[24].secureUrl
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_10(ctx2);
          if_block2.c();
          if_block2.m(t3.parentNode, t3);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*video*/
        ctx2[24].type
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_9(ctx2);
          if_block3.c();
          if_block3.m(if_block3_anchor.parentNode, if_block3_anchor);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(t2);
        detach_dev(t3);
        detach_dev(if_block3_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
      if (if_block2)
        if_block2.d(detaching);
      if (if_block3)
        if_block3.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_3.name,
    type: "each",
    source: "(241:6) {#each openGraph.videos as video}",
    ctx
  });
  return block;
}
function create_if_block_5(ctx) {
  let each_1_anchor;
  let each_value_2 = ensure_array_like_dev(
    /*openGraph*/
    ctx[6].audio
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_2.length; i += 1) {
    each_blocks[i] = create_each_block_2(get_each_context_2(ctx, each_value_2, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64) {
        each_value_2 = ensure_array_like_dev(
          /*openGraph*/
          ctx2[6].audio
        );
        let i;
        for (i = 0; i < each_value_2.length; i += 1) {
          const child_ctx = get_each_context_2(ctx2, each_value_2, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_2(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_2.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_5.name,
    type: "if",
    source: "(258:4) {#if openGraph.audio && openGraph.audio.length}",
    ctx
  });
  return block;
}
function create_if_block_7(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:audio:secure_url");
      attr_dev(meta, "content", meta_content_value = /*audio*/
      ctx[21].secureUrl.toString());
      add_location(meta, file, 261, 10, 9321);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*audio*/
      ctx2[21].secureUrl.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_7.name,
    type: "if",
    source: "(261:8) {#if audio.secureUrl}",
    ctx
  });
  return block;
}
function create_if_block_6(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:audio:type");
      attr_dev(meta, "content", meta_content_value = /*audio*/
      ctx[21].type.toString());
      add_location(meta, file, 264, 10, 9447);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*audio*/
      ctx2[21].type.toString())) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_6.name,
    type: "if",
    source: "(264:8) {#if audio.type}",
    ctx
  });
  return block;
}
function create_each_block_2(ctx) {
  let meta;
  let meta_content_value;
  let t0;
  let t1;
  let if_block1_anchor;
  let if_block0 = (
    /*audio*/
    ctx[21].secureUrl && create_if_block_7(ctx)
  );
  let if_block1 = (
    /*audio*/
    ctx[21].type && create_if_block_6(ctx)
  );
  const block = {
    c: function create() {
      meta = element("meta");
      t0 = space();
      if (if_block0)
        if_block0.c();
      t1 = space();
      if (if_block1)
        if_block1.c();
      if_block1_anchor = empty();
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      t0 = claim_space(nodes);
      if (if_block0)
        if_block0.l(nodes);
      t1 = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      if_block1_anchor = empty();
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:audio");
      attr_dev(meta, "content", meta_content_value = /*audio*/
      ctx[21].url);
      add_location(meta, file, 259, 8, 9232);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
      insert_hydration_dev(target, t0, anchor);
      if (if_block0)
        if_block0.m(target, anchor);
      insert_hydration_dev(target, t1, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, if_block1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*audio*/
      ctx2[21].url)) {
        attr_dev(meta, "content", meta_content_value);
      }
      if (
        /*audio*/
        ctx2[21].secureUrl
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_7(ctx2);
          if_block0.c();
          if_block0.m(t1.parentNode, t1);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*audio*/
        ctx2[21].type
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_6(ctx2);
          if_block1.c();
          if_block1.m(if_block1_anchor.parentNode, if_block1_anchor);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
        detach_dev(t0);
        detach_dev(t1);
        detach_dev(if_block1_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      if (if_block1)
        if_block1.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_2.name,
    type: "each",
    source: "(259:6) {#each openGraph.audio as audio}",
    ctx
  });
  return block;
}
function create_if_block_4(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:locale");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].locale);
      add_location(meta, file, 270, 6, 9585);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].locale)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_4.name,
    type: "if",
    source: "(270:4) {#if openGraph.locale}",
    ctx
  });
  return block;
}
function create_if_block_3(ctx) {
  let meta;
  let meta_content_value;
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", { property: true, content: true });
      this.h();
    },
    h: function hydrate() {
      attr_dev(meta, "property", "og:site_name");
      attr_dev(meta, "content", meta_content_value = /*openGraph*/
      ctx[6].siteName);
      add_location(meta, file, 274, 6, 9688);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*openGraph*/
      64 && meta_content_value !== (meta_content_value = /*openGraph*/
      ctx2[6].siteName)) {
        attr_dev(meta, "content", meta_content_value);
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_3.name,
    type: "if",
    source: "(274:4) {#if openGraph.siteName}",
    ctx
  });
  return block;
}
function create_if_block_1(ctx) {
  let each_1_anchor;
  let each_value_1 = ensure_array_like_dev(
    /*additionalMetaTags*/
    ctx[9]
  );
  let each_blocks = [];
  for (let i = 0; i < each_value_1.length; i += 1) {
    each_blocks[i] = create_each_block_1(get_each_context_1(ctx, each_value_1, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*additionalMetaTags*/
      512) {
        each_value_1 = ensure_array_like_dev(
          /*additionalMetaTags*/
          ctx2[9]
        );
        let i;
        for (i = 0; i < each_value_1.length; i += 1) {
          const child_ctx = get_each_context_1(ctx2, each_value_1, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block_1(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value_1.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_1.name,
    type: "if",
    source: "(279:2) {#if additionalMetaTags && Array.isArray(additionalMetaTags)}",
    ctx
  });
  return block;
}
function create_each_block_1(ctx) {
  let meta;
  let meta_levels = [
    /*tag*/
    ctx[16]
  ];
  let meta_data = {};
  for (let i = 0; i < meta_levels.length; i += 1) {
    meta_data = assign(meta_data, meta_levels[i]);
  }
  const block = {
    c: function create() {
      meta = element("meta");
      this.h();
    },
    l: function claim(nodes) {
      meta = claim_element(nodes, "META", {});
      this.h();
    },
    h: function hydrate() {
      set_attributes(meta, meta_data);
      add_location(meta, file, 280, 6, 9877);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, meta, anchor);
    },
    p: function update(ctx2, dirty) {
      set_attributes(meta, meta_data = get_spread_update(meta_levels, [dirty[0] & /*additionalMetaTags*/
      512 && /*tag*/
      ctx2[16]]));
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(meta);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block_1.name,
    type: "each",
    source: "(280:4) {#each additionalMetaTags as tag}",
    ctx
  });
  return block;
}
function create_if_block(ctx) {
  let each_1_anchor;
  let each_value = ensure_array_like_dev(
    /*additionalLinkTags*/
    ctx[10]
  );
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));
  }
  const block = {
    c: function create() {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      each_1_anchor = empty();
    },
    l: function claim(nodes) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].l(nodes);
      }
      each_1_anchor = empty();
    },
    m: function mount(target, anchor) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(target, anchor);
        }
      }
      insert_hydration_dev(target, each_1_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty[0] & /*additionalLinkTags*/
      1024) {
        each_value = ensure_array_like_dev(
          /*additionalLinkTags*/
          ctx2[10]
        );
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(each_1_anchor);
      }
      destroy_each(each_blocks, detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block.name,
    type: "if",
    source: "(285:2) {#if additionalLinkTags?.length}",
    ctx
  });
  return block;
}
function create_each_block(ctx) {
  let link;
  let link_levels = [
    /*tag*/
    ctx[16]
  ];
  let link_data = {};
  for (let i = 0; i < link_levels.length; i += 1) {
    link_data = assign(link_data, link_levels[i]);
  }
  const block = {
    c: function create() {
      link = element("link");
      this.h();
    },
    l: function claim(nodes) {
      link = claim_element(nodes, "LINK", {});
      this.h();
    },
    h: function hydrate() {
      set_attributes(link, link_data);
      add_location(link, file, 286, 6, 9995);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, link, anchor);
    },
    p: function update(ctx2, dirty) {
      set_attributes(link, link_data = get_spread_update(link_levels, [dirty[0] & /*additionalLinkTags*/
      1024 && /*tag*/
      ctx2[16]]));
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(link);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_each_block.name,
    type: "each",
    source: "(286:4) {#each additionalLinkTags as tag}",
    ctx
  });
  return block;
}
function create_fragment(ctx) {
  var _a, _b;
  let previous_key = (
    /*updatedTitle*/
    ctx[12]
  );
  let key_block_anchor;
  let if_block0_anchor;
  let if_block1_anchor;
  let if_block2_anchor;
  let if_block3_anchor;
  let if_block4_anchor;
  let if_block5_anchor;
  let if_block6_anchor;
  let if_block7_anchor;
  let if_block8_anchor;
  let show_if = (
    /*additionalMetaTags*/
    ctx[9] && Array.isArray(
      /*additionalMetaTags*/
      ctx[9]
    )
  );
  let if_block9_anchor;
  let if_block10_anchor;
  let key_block = create_key_block(ctx);
  let if_block0 = (
    /*robots*/
    ctx[0] !== false && create_if_block_64(ctx)
  );
  let if_block1 = (
    /*description*/
    ctx[1] && create_if_block_63(ctx)
  );
  let if_block2 = (
    /*canonical*/
    ctx[7] && create_if_block_62(ctx)
  );
  let if_block3 = (
    /*keywords*/
    ((_a = ctx[8]) == null ? void 0 : _a.length) && create_if_block_61(ctx)
  );
  let if_block4 = (
    /*mobileAlternate*/
    ctx[2] && create_if_block_60(ctx)
  );
  let if_block5 = (
    /*languageAlternates*/
    ctx[3] && /*languageAlternates*/
    ctx[3].length > 0 && create_if_block_59(ctx)
  );
  let if_block6 = (
    /*twitter*/
    ctx[4] && create_if_block_51(ctx)
  );
  let if_block7 = (
    /*facebook*/
    ctx[5] && create_if_block_50(ctx)
  );
  let if_block8 = (
    /*openGraph*/
    ctx[6] && create_if_block_2(ctx)
  );
  let if_block9 = show_if && create_if_block_1(ctx);
  let if_block10 = (
    /*additionalLinkTags*/
    ((_b = ctx[10]) == null ? void 0 : _b.length) && create_if_block(ctx)
  );
  const block = {
    c: function create() {
      key_block.c();
      key_block_anchor = empty();
      if (if_block0)
        if_block0.c();
      if_block0_anchor = empty();
      if (if_block1)
        if_block1.c();
      if_block1_anchor = empty();
      if (if_block2)
        if_block2.c();
      if_block2_anchor = empty();
      if (if_block3)
        if_block3.c();
      if_block3_anchor = empty();
      if (if_block4)
        if_block4.c();
      if_block4_anchor = empty();
      if (if_block5)
        if_block5.c();
      if_block5_anchor = empty();
      if (if_block6)
        if_block6.c();
      if_block6_anchor = empty();
      if (if_block7)
        if_block7.c();
      if_block7_anchor = empty();
      if (if_block8)
        if_block8.c();
      if_block8_anchor = empty();
      if (if_block9)
        if_block9.c();
      if_block9_anchor = empty();
      if (if_block10)
        if_block10.c();
      if_block10_anchor = empty();
    },
    l: function claim(nodes) {
      const head_nodes = head_selector("svelte-18pzfb8", document.head);
      key_block.l(head_nodes);
      key_block_anchor = empty();
      if (if_block0)
        if_block0.l(head_nodes);
      if_block0_anchor = empty();
      if (if_block1)
        if_block1.l(head_nodes);
      if_block1_anchor = empty();
      if (if_block2)
        if_block2.l(head_nodes);
      if_block2_anchor = empty();
      if (if_block3)
        if_block3.l(head_nodes);
      if_block3_anchor = empty();
      if (if_block4)
        if_block4.l(head_nodes);
      if_block4_anchor = empty();
      if (if_block5)
        if_block5.l(head_nodes);
      if_block5_anchor = empty();
      if (if_block6)
        if_block6.l(head_nodes);
      if_block6_anchor = empty();
      if (if_block7)
        if_block7.l(head_nodes);
      if_block7_anchor = empty();
      if (if_block8)
        if_block8.l(head_nodes);
      if_block8_anchor = empty();
      if (if_block9)
        if_block9.l(head_nodes);
      if_block9_anchor = empty();
      if (if_block10)
        if_block10.l(head_nodes);
      if_block10_anchor = empty();
      head_nodes.forEach(detach_dev);
    },
    m: function mount(target, anchor) {
      key_block.m(document.head, null);
      append_hydration_dev(document.head, key_block_anchor);
      if (if_block0)
        if_block0.m(document.head, null);
      append_hydration_dev(document.head, if_block0_anchor);
      if (if_block1)
        if_block1.m(document.head, null);
      append_hydration_dev(document.head, if_block1_anchor);
      if (if_block2)
        if_block2.m(document.head, null);
      append_hydration_dev(document.head, if_block2_anchor);
      if (if_block3)
        if_block3.m(document.head, null);
      append_hydration_dev(document.head, if_block3_anchor);
      if (if_block4)
        if_block4.m(document.head, null);
      append_hydration_dev(document.head, if_block4_anchor);
      if (if_block5)
        if_block5.m(document.head, null);
      append_hydration_dev(document.head, if_block5_anchor);
      if (if_block6)
        if_block6.m(document.head, null);
      append_hydration_dev(document.head, if_block6_anchor);
      if (if_block7)
        if_block7.m(document.head, null);
      append_hydration_dev(document.head, if_block7_anchor);
      if (if_block8)
        if_block8.m(document.head, null);
      append_hydration_dev(document.head, if_block8_anchor);
      if (if_block9)
        if_block9.m(document.head, null);
      append_hydration_dev(document.head, if_block9_anchor);
      if (if_block10)
        if_block10.m(document.head, null);
      append_hydration_dev(document.head, if_block10_anchor);
    },
    p: function update(ctx2, dirty) {
      var _a2, _b2;
      if (dirty[0] & /*updatedTitle*/
      4096 && safe_not_equal(previous_key, previous_key = /*updatedTitle*/
      ctx2[12])) {
        key_block.d(1);
        key_block = create_key_block(ctx2);
        key_block.c();
        key_block.m(key_block_anchor.parentNode, key_block_anchor);
      } else {
        key_block.p(ctx2, dirty);
      }
      if (
        /*robots*/
        ctx2[0] !== false
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_64(ctx2);
          if_block0.c();
          if_block0.m(if_block0_anchor.parentNode, if_block0_anchor);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*description*/
        ctx2[1]
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block_63(ctx2);
          if_block1.c();
          if_block1.m(if_block1_anchor.parentNode, if_block1_anchor);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
      if (
        /*canonical*/
        ctx2[7]
      ) {
        if (if_block2) {
          if_block2.p(ctx2, dirty);
        } else {
          if_block2 = create_if_block_62(ctx2);
          if_block2.c();
          if_block2.m(if_block2_anchor.parentNode, if_block2_anchor);
        }
      } else if (if_block2) {
        if_block2.d(1);
        if_block2 = null;
      }
      if (
        /*keywords*/
        (_a2 = ctx2[8]) == null ? void 0 : _a2.length
      ) {
        if (if_block3) {
          if_block3.p(ctx2, dirty);
        } else {
          if_block3 = create_if_block_61(ctx2);
          if_block3.c();
          if_block3.m(if_block3_anchor.parentNode, if_block3_anchor);
        }
      } else if (if_block3) {
        if_block3.d(1);
        if_block3 = null;
      }
      if (
        /*mobileAlternate*/
        ctx2[2]
      ) {
        if (if_block4) {
          if_block4.p(ctx2, dirty);
        } else {
          if_block4 = create_if_block_60(ctx2);
          if_block4.c();
          if_block4.m(if_block4_anchor.parentNode, if_block4_anchor);
        }
      } else if (if_block4) {
        if_block4.d(1);
        if_block4 = null;
      }
      if (
        /*languageAlternates*/
        ctx2[3] && /*languageAlternates*/
        ctx2[3].length > 0
      ) {
        if (if_block5) {
          if_block5.p(ctx2, dirty);
        } else {
          if_block5 = create_if_block_59(ctx2);
          if_block5.c();
          if_block5.m(if_block5_anchor.parentNode, if_block5_anchor);
        }
      } else if (if_block5) {
        if_block5.d(1);
        if_block5 = null;
      }
      if (
        /*twitter*/
        ctx2[4]
      ) {
        if (if_block6) {
          if_block6.p(ctx2, dirty);
        } else {
          if_block6 = create_if_block_51(ctx2);
          if_block6.c();
          if_block6.m(if_block6_anchor.parentNode, if_block6_anchor);
        }
      } else if (if_block6) {
        if_block6.d(1);
        if_block6 = null;
      }
      if (
        /*facebook*/
        ctx2[5]
      ) {
        if (if_block7) {
          if_block7.p(ctx2, dirty);
        } else {
          if_block7 = create_if_block_50(ctx2);
          if_block7.c();
          if_block7.m(if_block7_anchor.parentNode, if_block7_anchor);
        }
      } else if (if_block7) {
        if_block7.d(1);
        if_block7 = null;
      }
      if (
        /*openGraph*/
        ctx2[6]
      ) {
        if (if_block8) {
          if_block8.p(ctx2, dirty);
        } else {
          if_block8 = create_if_block_2(ctx2);
          if_block8.c();
          if_block8.m(if_block8_anchor.parentNode, if_block8_anchor);
        }
      } else if (if_block8) {
        if_block8.d(1);
        if_block8 = null;
      }
      if (dirty[0] & /*additionalMetaTags*/
      512)
        show_if = /*additionalMetaTags*/
        ctx2[9] && Array.isArray(
          /*additionalMetaTags*/
          ctx2[9]
        );
      if (show_if) {
        if (if_block9) {
          if_block9.p(ctx2, dirty);
        } else {
          if_block9 = create_if_block_1(ctx2);
          if_block9.c();
          if_block9.m(if_block9_anchor.parentNode, if_block9_anchor);
        }
      } else if (if_block9) {
        if_block9.d(1);
        if_block9 = null;
      }
      if (
        /*additionalLinkTags*/
        (_b2 = ctx2[10]) == null ? void 0 : _b2.length
      ) {
        if (if_block10) {
          if_block10.p(ctx2, dirty);
        } else {
          if_block10 = create_if_block(ctx2);
          if_block10.c();
          if_block10.m(if_block10_anchor.parentNode, if_block10_anchor);
        }
      } else if (if_block10) {
        if_block10.d(1);
        if_block10 = null;
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      detach_dev(key_block_anchor);
      key_block.d(detaching);
      if (if_block0)
        if_block0.d(detaching);
      detach_dev(if_block0_anchor);
      if (if_block1)
        if_block1.d(detaching);
      detach_dev(if_block1_anchor);
      if (if_block2)
        if_block2.d(detaching);
      detach_dev(if_block2_anchor);
      if (if_block3)
        if_block3.d(detaching);
      detach_dev(if_block3_anchor);
      if (if_block4)
        if_block4.d(detaching);
      detach_dev(if_block4_anchor);
      if (if_block5)
        if_block5.d(detaching);
      detach_dev(if_block5_anchor);
      if (if_block6)
        if_block6.d(detaching);
      detach_dev(if_block6_anchor);
      if (if_block7)
        if_block7.d(detaching);
      detach_dev(if_block7_anchor);
      if (if_block8)
        if_block8.d(detaching);
      detach_dev(if_block8_anchor);
      if (if_block9)
        if_block9.d(detaching);
      detach_dev(if_block9_anchor);
      if (if_block10)
        if_block10.d(detaching);
      detach_dev(if_block10_anchor);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance($$self, $$props, $$invalidate) {
  let updatedTitle;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("MetaTags", slots, []);
  let { title = "" } = $$props;
  let { titleTemplate = "" } = $$props;
  let { robots = "index,follow" } = $$props;
  let { additionalRobotsProps = void 0 } = $$props;
  let { description = void 0 } = $$props;
  let { mobileAlternate = void 0 } = $$props;
  let { languageAlternates = void 0 } = $$props;
  let { twitter = void 0 } = $$props;
  let { facebook = void 0 } = $$props;
  let { openGraph = void 0 } = $$props;
  let { canonical = void 0 } = $$props;
  let { keywords = void 0 } = $$props;
  let { additionalMetaTags = void 0 } = $$props;
  let { additionalLinkTags = void 0 } = $$props;
  let robotsParams = "";
  if (additionalRobotsProps) {
    const { nosnippet, maxSnippet, maxImagePreview, maxVideoPreview, noarchive, noimageindex, notranslate, unavailableAfter } = additionalRobotsProps;
    robotsParams = `${nosnippet ? ",nosnippet" : ""}${maxSnippet ? `,max-snippet:${maxSnippet}` : ""}${maxImagePreview ? `,max-image-preview:${maxImagePreview}` : ""}${noarchive ? ",noarchive" : ""}${unavailableAfter ? `,unavailable_after:${unavailableAfter}` : ""}${noimageindex ? ",noimageindex" : ""}${maxVideoPreview ? `,max-video-preview:${maxVideoPreview}` : ""}${notranslate ? ",notranslate" : ""}`;
  }
  const writable_props = [
    "title",
    "titleTemplate",
    "robots",
    "additionalRobotsProps",
    "description",
    "mobileAlternate",
    "languageAlternates",
    "twitter",
    "facebook",
    "openGraph",
    "canonical",
    "keywords",
    "additionalMetaTags",
    "additionalLinkTags"
  ];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console_1.warn(`<MetaTags> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("title" in $$props2)
      $$invalidate(13, title = $$props2.title);
    if ("titleTemplate" in $$props2)
      $$invalidate(14, titleTemplate = $$props2.titleTemplate);
    if ("robots" in $$props2)
      $$invalidate(0, robots = $$props2.robots);
    if ("additionalRobotsProps" in $$props2)
      $$invalidate(15, additionalRobotsProps = $$props2.additionalRobotsProps);
    if ("description" in $$props2)
      $$invalidate(1, description = $$props2.description);
    if ("mobileAlternate" in $$props2)
      $$invalidate(2, mobileAlternate = $$props2.mobileAlternate);
    if ("languageAlternates" in $$props2)
      $$invalidate(3, languageAlternates = $$props2.languageAlternates);
    if ("twitter" in $$props2)
      $$invalidate(4, twitter = $$props2.twitter);
    if ("facebook" in $$props2)
      $$invalidate(5, facebook = $$props2.facebook);
    if ("openGraph" in $$props2)
      $$invalidate(6, openGraph = $$props2.openGraph);
    if ("canonical" in $$props2)
      $$invalidate(7, canonical = $$props2.canonical);
    if ("keywords" in $$props2)
      $$invalidate(8, keywords = $$props2.keywords);
    if ("additionalMetaTags" in $$props2)
      $$invalidate(9, additionalMetaTags = $$props2.additionalMetaTags);
    if ("additionalLinkTags" in $$props2)
      $$invalidate(10, additionalLinkTags = $$props2.additionalLinkTags);
  };
  $$self.$capture_state = () => ({
    title,
    titleTemplate,
    robots,
    additionalRobotsProps,
    description,
    mobileAlternate,
    languageAlternates,
    twitter,
    facebook,
    openGraph,
    canonical,
    keywords,
    additionalMetaTags,
    additionalLinkTags,
    robotsParams,
    updatedTitle
  });
  $$self.$inject_state = ($$props2) => {
    if ("title" in $$props2)
      $$invalidate(13, title = $$props2.title);
    if ("titleTemplate" in $$props2)
      $$invalidate(14, titleTemplate = $$props2.titleTemplate);
    if ("robots" in $$props2)
      $$invalidate(0, robots = $$props2.robots);
    if ("additionalRobotsProps" in $$props2)
      $$invalidate(15, additionalRobotsProps = $$props2.additionalRobotsProps);
    if ("description" in $$props2)
      $$invalidate(1, description = $$props2.description);
    if ("mobileAlternate" in $$props2)
      $$invalidate(2, mobileAlternate = $$props2.mobileAlternate);
    if ("languageAlternates" in $$props2)
      $$invalidate(3, languageAlternates = $$props2.languageAlternates);
    if ("twitter" in $$props2)
      $$invalidate(4, twitter = $$props2.twitter);
    if ("facebook" in $$props2)
      $$invalidate(5, facebook = $$props2.facebook);
    if ("openGraph" in $$props2)
      $$invalidate(6, openGraph = $$props2.openGraph);
    if ("canonical" in $$props2)
      $$invalidate(7, canonical = $$props2.canonical);
    if ("keywords" in $$props2)
      $$invalidate(8, keywords = $$props2.keywords);
    if ("additionalMetaTags" in $$props2)
      $$invalidate(9, additionalMetaTags = $$props2.additionalMetaTags);
    if ("additionalLinkTags" in $$props2)
      $$invalidate(10, additionalLinkTags = $$props2.additionalLinkTags);
    if ("robotsParams" in $$props2)
      $$invalidate(11, robotsParams = $$props2.robotsParams);
    if ("updatedTitle" in $$props2)
      $$invalidate(12, updatedTitle = $$props2.updatedTitle);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty[0] & /*titleTemplate, title*/
    24576) {
      $:
        $$invalidate(12, updatedTitle = titleTemplate ? title ? titleTemplate.replace(/%s/g, title) : title : title);
    }
    if ($$self.$$.dirty[0] & /*robots, additionalRobotsProps*/
    32769) {
      $:
        if (!robots && additionalRobotsProps) {
          console.warn("additionalRobotsProps cannot be used when robots is set to false");
        }
    }
  };
  return [
    robots,
    description,
    mobileAlternate,
    languageAlternates,
    twitter,
    facebook,
    openGraph,
    canonical,
    keywords,
    additionalMetaTags,
    additionalLinkTags,
    robotsParams,
    updatedTitle,
    title,
    titleTemplate,
    additionalRobotsProps
  ];
}
var MetaTags = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(
      this,
      options,
      instance,
      create_fragment,
      safe_not_equal,
      {
        title: 13,
        titleTemplate: 14,
        robots: 0,
        additionalRobotsProps: 15,
        description: 1,
        mobileAlternate: 2,
        languageAlternates: 3,
        twitter: 4,
        facebook: 5,
        openGraph: 6,
        canonical: 7,
        keywords: 8,
        additionalMetaTags: 9,
        additionalLinkTags: 10
      },
      null,
      [-1, -1]
    );
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "MetaTags",
      options,
      id: create_fragment.name
    });
  }
  get title() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set title(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get titleTemplate() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set titleTemplate(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get robots() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set robots(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get additionalRobotsProps() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set additionalRobotsProps(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get description() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set description(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get mobileAlternate() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set mobileAlternate(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get languageAlternates() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set languageAlternates(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get twitter() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set twitter(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get facebook() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set facebook(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get openGraph() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set openGraph(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get canonical() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set canonical(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get keywords() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set keywords(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get additionalMetaTags() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set additionalMetaTags(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get additionalLinkTags() {
    throw new Error("<MetaTags>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set additionalLinkTags(value) {
    throw new Error("<MetaTags>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var MetaTags_default = MetaTags;

// ../../node_modules/.pnpm/svelte-meta-tags@3.1.4_svelte@4.2.20/node_modules/svelte-meta-tags/dist/JsonLd.svelte
function create_if_block_110(ctx) {
  let html_tag;
  let html_anchor;
  const block = {
    c: function create() {
      html_tag = new HtmlTagHydration(false);
      html_anchor = empty();
      this.h();
    },
    l: function claim(nodes) {
      html_tag = claim_html_tag(nodes, false);
      html_anchor = empty();
      this.h();
    },
    h: function hydrate() {
      html_tag.a = html_anchor;
    },
    m: function mount(target, anchor) {
      html_tag.m(
        /*json*/
        ctx[1],
        target,
        anchor
      );
      insert_hydration_dev(target, html_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*json*/
      2)
        html_tag.p(
          /*json*/
          ctx2[1]
        );
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(html_anchor);
        html_tag.d();
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block_110.name,
    type: "if",
    source: "(15:2) {#if isValid && output === 'head'}",
    ctx
  });
  return block;
}
function create_if_block2(ctx) {
  let html_tag;
  let html_anchor;
  const block = {
    c: function create() {
      html_tag = new HtmlTagHydration(false);
      html_anchor = empty();
      this.h();
    },
    l: function claim(nodes) {
      html_tag = claim_html_tag(nodes, false);
      html_anchor = empty();
      this.h();
    },
    h: function hydrate() {
      html_tag.a = html_anchor;
    },
    m: function mount(target, anchor) {
      html_tag.m(
        /*json*/
        ctx[1],
        target,
        anchor
      );
      insert_hydration_dev(target, html_anchor, anchor);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*json*/
      2)
        html_tag.p(
          /*json*/
          ctx2[1]
        );
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(html_anchor);
        html_tag.d();
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block2.name,
    type: "if",
    source: "(20:0) {#if isValid && output === 'body'}",
    ctx
  });
  return block;
}
function create_fragment2(ctx) {
  let if_block0_anchor;
  let t;
  let if_block1_anchor;
  let if_block0 = (
    /*isValid*/
    ctx[2] && /*output*/
    ctx[0] === "head" && create_if_block_110(ctx)
  );
  let if_block1 = (
    /*isValid*/
    ctx[2] && /*output*/
    ctx[0] === "body" && create_if_block2(ctx)
  );
  const block = {
    c: function create() {
      if (if_block0)
        if_block0.c();
      if_block0_anchor = empty();
      t = space();
      if (if_block1)
        if_block1.c();
      if_block1_anchor = empty();
    },
    l: function claim(nodes) {
      const head_nodes = head_selector("svelte-1rghca7", document.head);
      if (if_block0)
        if_block0.l(head_nodes);
      if_block0_anchor = empty();
      head_nodes.forEach(detach_dev);
      t = claim_space(nodes);
      if (if_block1)
        if_block1.l(nodes);
      if_block1_anchor = empty();
    },
    m: function mount(target, anchor) {
      if (if_block0)
        if_block0.m(document.head, null);
      append_hydration_dev(document.head, if_block0_anchor);
      insert_hydration_dev(target, t, anchor);
      if (if_block1)
        if_block1.m(target, anchor);
      insert_hydration_dev(target, if_block1_anchor, anchor);
    },
    p: function update(ctx2, [dirty]) {
      if (
        /*isValid*/
        ctx2[2] && /*output*/
        ctx2[0] === "head"
      ) {
        if (if_block0) {
          if_block0.p(ctx2, dirty);
        } else {
          if_block0 = create_if_block_110(ctx2);
          if_block0.c();
          if_block0.m(if_block0_anchor.parentNode, if_block0_anchor);
        }
      } else if (if_block0) {
        if_block0.d(1);
        if_block0 = null;
      }
      if (
        /*isValid*/
        ctx2[2] && /*output*/
        ctx2[0] === "body"
      ) {
        if (if_block1) {
          if_block1.p(ctx2, dirty);
        } else {
          if_block1 = create_if_block2(ctx2);
          if_block1.c();
          if_block1.m(if_block1_anchor.parentNode, if_block1_anchor);
        }
      } else if (if_block1) {
        if_block1.d(1);
        if_block1 = null;
      }
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(t);
        detach_dev(if_block1_anchor);
      }
      if (if_block0)
        if_block0.d(detaching);
      detach_dev(if_block0_anchor);
      if (if_block1)
        if_block1.d(detaching);
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment2.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance2($$self, $$props, $$invalidate) {
  let isValid;
  let json;
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("JsonLd", slots, []);
  let { output = "head" } = $$props;
  let { schema = void 0 } = $$props;
  const createSchema = (schema2) => {
    const addContext = (context) => ({
      "@context": "https://schema.org",
      ...context
    });
    return Array.isArray(schema2) ? schema2.map((context) => addContext(context)) : addContext(schema2);
  };
  const writable_props = ["output", "schema"];
  Object.keys($$props).forEach((key) => {
    if (!~writable_props.indexOf(key) && key.slice(0, 2) !== "$$" && key !== "slot")
      console.warn(`<JsonLd> was created with unknown prop '${key}'`);
  });
  $$self.$$set = ($$props2) => {
    if ("output" in $$props2)
      $$invalidate(0, output = $$props2.output);
    if ("schema" in $$props2)
      $$invalidate(3, schema = $$props2.schema);
  };
  $$self.$capture_state = () => ({
    output,
    schema,
    createSchema,
    json,
    isValid
  });
  $$self.$inject_state = ($$props2) => {
    if ("output" in $$props2)
      $$invalidate(0, output = $$props2.output);
    if ("schema" in $$props2)
      $$invalidate(3, schema = $$props2.schema);
    if ("json" in $$props2)
      $$invalidate(1, json = $$props2.json);
    if ("isValid" in $$props2)
      $$invalidate(2, isValid = $$props2.isValid);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*schema*/
    8) {
      $:
        $$invalidate(2, isValid = schema && typeof schema === "object");
    }
    if ($$self.$$.dirty & /*schema*/
    8) {
      $:
        $$invalidate(1, json = `${'<script type="application/ld+json">'}${JSON.stringify(createSchema(schema))}${"<\/script>"}`);
    }
  };
  return [output, json, isValid, schema];
}
var JsonLd = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance2, create_fragment2, safe_not_equal, { output: 0, schema: 3 });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "JsonLd",
      options,
      id: create_fragment2.name
    });
  }
  get output() {
    throw new Error("<JsonLd>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set output(value) {
    throw new Error("<JsonLd>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get schema() {
    throw new Error("<JsonLd>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set schema(value) {
    throw new Error("<JsonLd>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var JsonLd_default = JsonLd;
export {
  JsonLd_default as JsonLd,
  MetaTags_default as MetaTags
};
//# sourceMappingURL=svelte-meta-tags.js.map
