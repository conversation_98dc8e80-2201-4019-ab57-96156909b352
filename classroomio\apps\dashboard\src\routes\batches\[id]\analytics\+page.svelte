<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { writable } from 'svelte/store';
  import { 
    currentBatch, 
    batchActions 
  } from '$lib/components/Batch/store';
  import { batchService } from '$lib/utils/services/batch';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import { PageBody, PageNav } from '$lib/components/Page';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import AnalyticsDashboard from '$lib/components/Analytics/AnalyticsDashboard.svelte';
  import StudentAnalyticsDashboard from '$lib/components/Analytics/StudentAnalyticsDashboard.svelte';
  import InstructorAnalyticsDashboard from '$lib/components/Analytics/InstructorAnalyticsDashboard.svelte';
  import AdminAnalyticsDashboard from '$lib/components/Analytics/AdminAnalyticsDashboard.svelte';
  import ReportsManager from '$lib/components/Analytics/ReportsManager.svelte';
  import { 
    Dashboard,
    User,
    ChartLine,
    Report,
    Settings,
    Warning
  } from 'carbon-icons-svelte';

  let batchId: string;
  let loading = true;
  let error: string | null = null;
  let activeTab = 'dashboard';

  $: organizationId = $globalStore.org?.id;
  $: currentUserId = $globalStore.user?.id;
  $: userRole = $globalStore.role;
  $: isInstructor = userRole === 'teacher' || $globalStore.isOrgAdmin;
  $: isAdmin = $globalStore.isOrgAdmin;

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: Dashboard, roles: ['student', 'teacher', 'admin'] },
    { id: 'student', label: 'Student Analytics', icon: User, roles: ['teacher', 'admin'] },
    { id: 'instructor', label: 'Instructor Analytics', icon: ChartLine, roles: ['teacher', 'admin'] },
    { id: 'admin', label: 'Admin Analytics', icon: Settings, roles: ['admin'] },
    { id: 'reports', label: 'Reports', icon: Report, roles: ['teacher', 'admin'] }
  ];

  $: visibleTabs = tabs.filter(tab => {
    if (isAdmin) return tab.roles.includes('admin');
    if (isInstructor) return tab.roles.includes('teacher');
    return tab.roles.includes('student');
  });

  onMount(async () => {
    batchId = $page.params.id;
    await loadBatchData();
    
    // Set default tab based on user role
    if (isAdmin) {
      activeTab = 'admin';
    } else if (isInstructor) {
      activeTab = 'instructor';
    } else {
      activeTab = 'dashboard';
    }
  });

  async function loadBatchData() {
    try {
      loading = true;
      error = null;

      if (!$currentBatch || $currentBatch.id !== batchId) {
        const batch = await batchService.getBatch(batchId);
        if (!batch) {
          throw new Error('Batch not found');
        }
        batchActions.setBatch(batch);
      }

    } catch (err) {
      console.error('Error loading batch:', err);
      error = err.message || 'Failed to load batch data';
    } finally {
      loading = false;
    }
  }

  function handleDashboardChanged(event: CustomEvent) {
    console.log('Dashboard changed:', event.detail);
  }

  function handleWidgetClicked(event: CustomEvent) {
    console.log('Widget clicked:', event.detail);
  }

  function handleReportGenerated(event: CustomEvent) {
    console.log('Report generated:', event.detail);
  }
</script>

<svelte:head>
  <title>
    {$t('analytics.title', { default: 'Analytics' })} - {$currentBatch?.name} - ClassroomIO
  </title>
</svelte:head>

<PageNav title={$t('analytics.title', { default: 'Analytics' })}>
  <div class="flex items-center space-x-4">
    <!-- Tab Navigation -->
    <div class="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
      {#each visibleTabs as tab}
        <button
          on:click={() => activeTab = tab.id}
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === tab.id 
            ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm' 
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}"
        >
          <svelte:component this={tab.icon} size={16} class="mr-2" />
          {$t(`analytics.${tab.id}`, { default: tab.label })}
        </button>
      {/each}
    </div>

    <!-- Batch Info -->
    {#if $currentBatch}
      <div class="text-sm text-gray-600 dark:text-gray-400">
        <span class="font-medium">{$currentBatch.name}</span>
        {#if $currentBatch.description}
          <span class="mx-2">•</span>
          <span>{$currentBatch.description}</span>
        {/if}
      </div>
    {/if}
  </div>
</PageNav>

<PageBody>
  {#if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.loading', { default: 'Loading Analytics' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.error_loading', { default: 'Error Loading Analytics' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadBatchData}>
        {$t('analytics.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if !organizationId}
    <Box className="text-center py-12">
      <Warning size={48} class="text-yellow-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.no_organization', { default: 'No Organization' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {$t('analytics.no_organization_desc', { default: 'Analytics require an organization context' })}
      </p>
    </Box>

  {:else}
    <!-- Tab Content -->
    <div class="min-h-[600px]">
      {#if activeTab === 'dashboard'}
        <!-- General Analytics Dashboard -->
        <AnalyticsDashboard
          {organizationId}
          {batchId}
          userId={currentUserId}
          dashboardType={isAdmin ? 'admin' : isInstructor ? 'instructor' : 'student'}
          on:dashboardChanged={handleDashboardChanged}
          on:widgetClicked={handleWidgetClicked}
        />

      {:else if activeTab === 'student'}
        <!-- Student-Specific Analytics -->
        <StudentAnalyticsDashboard
          {organizationId}
          {batchId}
          on:studentSelected={(e) => console.log('Student selected:', e.detail)}
        />

      {:else if activeTab === 'instructor'}
        <!-- Instructor-Specific Analytics -->
        <InstructorAnalyticsDashboard
          {organizationId}
          {batchId}
          instructorId={currentUserId}
          on:batchAnalytics={(e) => console.log('Batch analytics:', e.detail)}
        />

      {:else if activeTab === 'admin' && isAdmin}
        <!-- Admin-Specific Analytics -->
        <AdminAnalyticsDashboard
          {organizationId}
          {batchId}
          on:organizationAnalytics={(e) => console.log('Organization analytics:', e.detail)}
        />

      {:else if activeTab === 'reports'}
        <!-- Reports Manager -->
        <ReportsManager
          {organizationId}
          {batchId}
          userId={currentUserId}
          on:reportGenerated={handleReportGenerated}
        />

      {:else}
        <!-- Fallback for unknown tabs -->
        <Box className="text-center py-12">
          <Dashboard size={48} class="text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {$t('analytics.tab_not_found', { default: 'Tab Not Found' })}
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            {$t('analytics.tab_not_found_desc', { default: 'The requested analytics tab could not be found' })}
          </p>
        </Box>
      {/if}
    </div>

    <!-- Analytics Summary Footer -->
    {#if $currentBatch}
      <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
          <div>
            <div class="text-2xl font-bold text-primary-600">
              {$currentBatch.total_students || 0}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('analytics.total_students', { default: 'Total Students' })}
            </div>
          </div>

          <div>
            <div class="text-2xl font-bold text-green-600">
              {$currentBatch.active_students || 0}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('analytics.active_students', { default: 'Active Students' })}
            </div>
          </div>

          <div>
            <div class="text-2xl font-bold text-blue-600">
              {$currentBatch.completion_rate || 0}%
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('analytics.completion_rate', { default: 'Completion Rate' })}
            </div>
          </div>

          <div>
            <div class="text-2xl font-bold text-purple-600">
              {$currentBatch.engagement_score || 0}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('analytics.engagement_score', { default: 'Engagement Score' })}
            </div>
          </div>
        </div>
      </div>
    {/if}
  {/if}
</PageBody>

<style>
  /* Custom styles for analytics page */
  :global(.analytics-dashboard) {
    min-height: 600px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    :global(.analytics-dashboard .dashboard-grid) {
      grid-template-columns: 1fr !important;
    }
  }
</style>
