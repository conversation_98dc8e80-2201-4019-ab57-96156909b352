-- Migration: Batch-Centric Educational Platform Structure
-- Date: 2025-06-30
-- Description: Implements batch → subjects → chapters → sections hierarchy for educational platform

-- Create batch table (replaces/extends group concept for educational batches)
CREATE TABLE IF NOT EXISTS "public"."batch" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "name" character varying NOT NULL,
    "description" text,
    "start_date" timestamp with time zone,
    "end_date" timestamp with time zone,
    "organization_id" uuid NOT NULL,
    "is_active" boolean DEFAULT true,
    "batch_code" character varying UNIQUE,
    "max_students" integer DEFAULT 50,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."batch" ENABLE ROW LEVEL SECURITY;

-- Create subject table (educational subjects within a batch)
CREATE TABLE IF NOT EXISTS "public"."subject" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "name" character varying NOT NULL,
    "description" text,
    "batch_id" uuid NOT NULL,
    "order" bigint DEFAULT 0,
    "is_active" boolean DEFAULT true,
    "subject_code" character varying,
    "instructor_id" uuid,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."subject" ENABLE ROW LEVEL SECURITY;

-- Create chapter table (chapters within subjects)
CREATE TABLE IF NOT EXISTS "public"."chapter" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "title" character varying NOT NULL,
    "description" text,
    "subject_id" uuid NOT NULL,
    "order" bigint DEFAULT 0,
    "is_active" boolean DEFAULT true,
    "estimated_duration" integer, -- in minutes
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."chapter" ENABLE ROW LEVEL SECURITY;

-- Extend lesson_section table to link with chapters
ALTER TABLE "public"."lesson_section" ADD COLUMN IF NOT EXISTS "chapter_id" uuid;

-- Create batch_member table (students and instructors in batches)
CREATE TABLE IF NOT EXISTS "public"."batch_member" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "batch_id" uuid NOT NULL,
    "profile_id" uuid NOT NULL,
    "role" character varying NOT NULL DEFAULT 'student', -- 'student', 'instructor', 'admin'
    "joined_at" timestamp with time zone DEFAULT now(),
    "is_active" boolean DEFAULT true,
    "student_id" character varying, -- custom student ID
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."batch_member" ENABLE ROW LEVEL SECURITY;

-- Create study_material table for DPPs, notes, assignments
CREATE TABLE IF NOT EXISTS "public"."study_material" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "title" character varying NOT NULL,
    "description" text,
    "type" character varying NOT NULL, -- 'note', 'dpp', 'assignment', 'reference'
    "content" text,
    "file_url" text,
    "lesson_id" uuid,
    "chapter_id" uuid,
    "subject_id" uuid,
    "batch_id" uuid,
    "created_by" uuid,
    "is_downloadable" boolean DEFAULT false,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."study_material" ENABLE ROW LEVEL SECURITY;

-- Create video_content table for enhanced video management
CREATE TABLE IF NOT EXISTS "public"."video_content" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "title" character varying NOT NULL,
    "description" text,
    "video_url" text NOT NULL,
    "thumbnail_url" text,
    "duration" integer, -- in seconds
    "lesson_id" uuid,
    "quality_options" jsonb DEFAULT '[]'::jsonb, -- different quality URLs
    "is_downloadable" boolean DEFAULT false,
    "watermark_settings" jsonb DEFAULT '{}'::jsonb,
    "security_settings" jsonb DEFAULT '{}'::jsonb,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."video_content" ENABLE ROW LEVEL SECURITY;

-- Create doubt_system table for student queries
CREATE TABLE IF NOT EXISTS "public"."doubt_system" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "title" character varying NOT NULL,
    "description" text NOT NULL,
    "student_id" uuid NOT NULL,
    "batch_id" uuid,
    "subject_id" uuid,
    "lesson_id" uuid,
    "status" character varying DEFAULT 'open', -- 'open', 'answered', 'closed'
    "priority" character varying DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
    "images" jsonb DEFAULT '[]'::jsonb, -- array of image URLs
    "answered_by" uuid,
    "answered_at" timestamp with time zone,
    "answer" text,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."doubt_system" ENABLE ROW LEVEL SECURITY;

-- Create device_session table for device locking
CREATE TABLE IF NOT EXISTS "public"."device_session" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "profile_id" uuid NOT NULL,
    "device_fingerprint" text NOT NULL,
    "device_info" jsonb DEFAULT '{}'::jsonb,
    "is_active" boolean DEFAULT true,
    "last_activity" timestamp with time zone DEFAULT now(),
    "ip_address" inet,
    "user_agent" text,
    "session_token" text UNIQUE
);

ALTER TABLE "public"."device_session" ENABLE ROW LEVEL SECURITY;

-- Create live_session table for live classes
CREATE TABLE IF NOT EXISTS "public"."live_session" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "title" character varying NOT NULL,
    "description" text,
    "batch_id" uuid,
    "subject_id" uuid,
    "lesson_id" uuid,
    "instructor_id" uuid NOT NULL,
    "scheduled_at" timestamp with time zone NOT NULL,
    "duration" integer, -- in minutes
    "meeting_url" text,
    "meeting_id" text,
    "meeting_password" text,
    "status" character varying DEFAULT 'scheduled', -- 'scheduled', 'live', 'ended', 'cancelled'
    "recording_url" text,
    "attendance" jsonb DEFAULT '[]'::jsonb,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."live_session" ENABLE ROW LEVEL SECURITY;

-- Create progress_tracking table for detailed student progress
CREATE TABLE IF NOT EXISTS "public"."progress_tracking" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "student_id" uuid NOT NULL,
    "batch_id" uuid,
    "subject_id" uuid,
    "chapter_id" uuid,
    "lesson_id" uuid,
    "video_id" uuid,
    "progress_type" character varying NOT NULL, -- 'video_watch', 'lesson_complete', 'assignment_submit', 'test_attempt'
    "progress_value" numeric DEFAULT 0, -- percentage or score
    "time_spent" integer DEFAULT 0, -- in seconds
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."progress_tracking" ENABLE ROW LEVEL SECURITY;

-- Add Primary Keys
ALTER TABLE "public"."batch" ADD CONSTRAINT "batch_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."subject" ADD CONSTRAINT "subject_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."chapter" ADD CONSTRAINT "chapter_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."batch_member" ADD CONSTRAINT "batch_member_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."study_material" ADD CONSTRAINT "study_material_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."video_content" ADD CONSTRAINT "video_content_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."doubt_system" ADD CONSTRAINT "doubt_system_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."device_session" ADD CONSTRAINT "device_session_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."live_session" ADD CONSTRAINT "live_session_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."progress_tracking" ADD CONSTRAINT "progress_tracking_pkey" PRIMARY KEY ("id");

-- Add Foreign Key Constraints
ALTER TABLE "public"."batch" ADD CONSTRAINT "batch_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."subject" ADD CONSTRAINT "subject_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."subject" ADD CONSTRAINT "subject_instructor_id_fkey"
    FOREIGN KEY ("instructor_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."chapter" ADD CONSTRAINT "chapter_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."lesson_section" ADD CONSTRAINT "lesson_section_chapter_id_fkey"
    FOREIGN KEY ("chapter_id") REFERENCES "chapter"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."batch_member" ADD CONSTRAINT "batch_member_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."batch_member" ADD CONSTRAINT "batch_member_profile_id_fkey"
    FOREIGN KEY ("profile_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."study_material" ADD CONSTRAINT "study_material_lesson_id_fkey"
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."study_material" ADD CONSTRAINT "study_material_chapter_id_fkey"
    FOREIGN KEY ("chapter_id") REFERENCES "chapter"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."study_material" ADD CONSTRAINT "study_material_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."study_material" ADD CONSTRAINT "study_material_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."study_material" ADD CONSTRAINT "study_material_created_by_fkey"
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."video_content" ADD CONSTRAINT "video_content_lesson_id_fkey"
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_system" ADD CONSTRAINT "doubt_system_student_id_fkey"
    FOREIGN KEY ("student_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_system" ADD CONSTRAINT "doubt_system_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_system" ADD CONSTRAINT "doubt_system_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_system" ADD CONSTRAINT "doubt_system_lesson_id_fkey"
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."doubt_system" ADD CONSTRAINT "doubt_system_answered_by_fkey"
    FOREIGN KEY ("answered_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."device_session" ADD CONSTRAINT "device_session_profile_id_fkey"
    FOREIGN KEY ("profile_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_session" ADD CONSTRAINT "live_session_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_session" ADD CONSTRAINT "live_session_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_session" ADD CONSTRAINT "live_session_lesson_id_fkey"
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."live_session" ADD CONSTRAINT "live_session_instructor_id_fkey"
    FOREIGN KEY ("instructor_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."progress_tracking" ADD CONSTRAINT "progress_tracking_student_id_fkey"
    FOREIGN KEY ("student_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."progress_tracking" ADD CONSTRAINT "progress_tracking_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."progress_tracking" ADD CONSTRAINT "progress_tracking_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."progress_tracking" ADD CONSTRAINT "progress_tracking_chapter_id_fkey"
    FOREIGN KEY ("chapter_id") REFERENCES "chapter"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."progress_tracking" ADD CONSTRAINT "progress_tracking_lesson_id_fkey"
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."progress_tracking" ADD CONSTRAINT "progress_tracking_video_id_fkey"
    FOREIGN KEY ("video_id") REFERENCES "video_content"("id") ON UPDATE CASCADE ON DELETE CASCADE;

-- Add Indexes for Performance
CREATE INDEX IF NOT EXISTS "idx_batch_organization_id" ON "public"."batch"("organization_id");
CREATE INDEX IF NOT EXISTS "idx_batch_active" ON "public"."batch"("is_active");
CREATE INDEX IF NOT EXISTS "idx_batch_dates" ON "public"."batch"("start_date", "end_date");

CREATE INDEX IF NOT EXISTS "idx_subject_batch_id" ON "public"."subject"("batch_id");
CREATE INDEX IF NOT EXISTS "idx_subject_order" ON "public"."subject"("batch_id", "order");
CREATE INDEX IF NOT EXISTS "idx_subject_instructor" ON "public"."subject"("instructor_id");

CREATE INDEX IF NOT EXISTS "idx_chapter_subject_id" ON "public"."chapter"("subject_id");
CREATE INDEX IF NOT EXISTS "idx_chapter_order" ON "public"."chapter"("subject_id", "order");

CREATE INDEX IF NOT EXISTS "idx_lesson_section_chapter" ON "public"."lesson_section"("chapter_id");

CREATE INDEX IF NOT EXISTS "idx_batch_member_batch" ON "public"."batch_member"("batch_id");
CREATE INDEX IF NOT EXISTS "idx_batch_member_profile" ON "public"."batch_member"("profile_id");
CREATE INDEX IF NOT EXISTS "idx_batch_member_role" ON "public"."batch_member"("batch_id", "role");

CREATE INDEX IF NOT EXISTS "idx_study_material_lesson" ON "public"."study_material"("lesson_id");
CREATE INDEX IF NOT EXISTS "idx_study_material_chapter" ON "public"."study_material"("chapter_id");
CREATE INDEX IF NOT EXISTS "idx_study_material_type" ON "public"."study_material"("type");

CREATE INDEX IF NOT EXISTS "idx_video_content_lesson" ON "public"."video_content"("lesson_id");

CREATE INDEX IF NOT EXISTS "idx_doubt_system_student" ON "public"."doubt_system"("student_id");
CREATE INDEX IF NOT EXISTS "idx_doubt_system_status" ON "public"."doubt_system"("status");
CREATE INDEX IF NOT EXISTS "idx_doubt_system_batch" ON "public"."doubt_system"("batch_id");

CREATE INDEX IF NOT EXISTS "idx_device_session_profile" ON "public"."device_session"("profile_id");
CREATE INDEX IF NOT EXISTS "idx_device_session_active" ON "public"."device_session"("profile_id", "is_active");

CREATE INDEX IF NOT EXISTS "idx_live_session_batch" ON "public"."live_session"("batch_id");
CREATE INDEX IF NOT EXISTS "idx_live_session_scheduled" ON "public"."live_session"("scheduled_at");
CREATE INDEX IF NOT EXISTS "idx_live_session_status" ON "public"."live_session"("status");

CREATE INDEX IF NOT EXISTS "idx_progress_tracking_student" ON "public"."progress_tracking"("student_id");
CREATE INDEX IF NOT EXISTS "idx_progress_tracking_lesson" ON "public"."progress_tracking"("lesson_id");
CREATE INDEX IF NOT EXISTS "idx_progress_tracking_type" ON "public"."progress_tracking"("progress_type");

-- Add Unique Constraints
ALTER TABLE "public"."batch_member" ADD CONSTRAINT "unique_batch_member"
    UNIQUE ("batch_id", "profile_id");

ALTER TABLE "public"."device_session" ADD CONSTRAINT "unique_active_device_per_user"
    EXCLUDE (profile_id WITH =) WHERE (is_active = true);

-- Create Functions for Batch Management
CREATE OR REPLACE FUNCTION get_batch_hierarchy(batch_id_arg uuid)
RETURNS TABLE(
    batch_id uuid,
    batch_name character varying,
    subject_id uuid,
    subject_name character varying,
    subject_order bigint,
    chapter_id uuid,
    chapter_title character varying,
    chapter_order bigint,
    section_id uuid,
    section_title character varying,
    section_order bigint,
    lesson_id uuid,
    lesson_title character varying,
    lesson_order bigint
) LANGUAGE plpgsql AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.id as batch_id,
        b.name as batch_name,
        s.id as subject_id,
        s.name as subject_name,
        s.order as subject_order,
        c.id as chapter_id,
        c.title as chapter_title,
        c.order as chapter_order,
        ls.id as section_id,
        ls.title as section_title,
        ls.order as section_order,
        l.id as lesson_id,
        l.title as lesson_title,
        l.order as lesson_order
    FROM batch b
    LEFT JOIN subject s ON s.batch_id = b.id
    LEFT JOIN chapter c ON c.subject_id = s.id
    LEFT JOIN lesson_section ls ON ls.chapter_id = c.id
    LEFT JOIN lesson l ON l.section_id = ls.id
    WHERE b.id = batch_id_arg
    ORDER BY s.order, c.order, ls.order, l.order;
END;
$$;
