// BigBlueButton Integration Service
// Date: 2025-06-30

import crypto from 'crypto';
import type { MeetingConfig, LiveSession } from '$lib/utils/types/liveStreaming';

export interface BBBConfig {
  serverUrl: string;
  secret: string;
}

export interface BBBMeetingInfo {
  meetingID: string;
  meetingName: string;
  createTime: string;
  voiceBridge: string;
  dialNumber: string;
  attendeePW: string;
  moderatorPW: string;
  running: boolean;
  duration: number;
  hasUserJoined: boolean;
  recording: boolean;
  hasBeenForciblyEnded: boolean;
  startTime: string;
  endTime: string;
  participantCount: number;
  listenerCount: number;
  voiceParticipantCount: number;
  videoCount: number;
  maxUsers: number;
  moderatorCount: number;
}

export interface BBBJoinInfo {
  meetingID: string;
  fullName: string;
  password: string;
  createTime: string;
  userID: string;
  webVoiceConf: string;
  configToken: string;
  avatarURL?: string;
  redirect: boolean;
  clientURL: string;
}

export class BigBlueButtonService {
  private config: BBBConfig;

  constructor(config: BBBConfig) {
    this.config = config;
  }

  // Generate API checksum
  private generateChecksum(apiCall: string, queryString: string): string {
    const data = apiCall + queryString + this.config.secret;
    return crypto.createHash('sha1').update(data).digest('hex');
  }

  // Build API URL
  private buildApiUrl(apiCall: string, params: Record<string, any>): string {
    const queryParams = new URLSearchParams();
    
    // Add parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        queryParams.append(key, params[key].toString());
      }
    });

    const queryString = queryParams.toString();
    const checksum = this.generateChecksum(apiCall, queryString);
    
    return `${this.config.serverUrl}/api/${apiCall}?${queryString}&checksum=${checksum}`;
  }

  // Create a meeting
  async createMeeting(session: LiveSession, meetingConfig: MeetingConfig): Promise<string> {
    const meetingID = `session_${session.id}`;
    const params = {
      name: session.title,
      meetingID: meetingID,
      attendeePW: this.generatePassword(),
      moderatorPW: this.generatePassword(),
      welcome: `Welcome to ${session.title}`,
      dialNumber: '',
      voiceBridge: Math.floor(Math.random() * 99999).toString(),
      maxParticipants: session.max_participants,
      logoutURL: `${window.location.origin}/live-sessions/${session.id}/ended`,
      record: meetingConfig.recording_enabled ? 'true' : 'false',
      duration: Math.ceil((new Date(session.scheduled_end).getTime() - new Date(session.scheduled_start).getTime()) / 60000), // in minutes
      isBreakout: 'false',
      parentMeetingID: '',
      sequence: '1',
      freeJoin: 'false',
      meta_description: session.description || '',
      meta_batch_id: session.batch_id,
      meta_instructor_id: session.instructor_id,
      meta_session_type: session.session_type,
      
      // Security settings
      moderatorOnlyMessage: meetingConfig.waiting_room_enabled ? 'Waiting for moderator approval' : '',
      autoStartRecording: meetingConfig.recording_enabled ? 'true' : 'false',
      allowStartStopRecording: 'true',
      webcamsOnlyForModerator: 'false',
      muteOnStart: 'true',
      allowModsToUnmuteUsers: 'true',
      lockSettingsDisableCam: 'false',
      lockSettingsDisableMic: 'false',
      lockSettingsDisablePrivateChat: !meetingConfig.chat_enabled,
      lockSettingsDisablePublicChat: !meetingConfig.chat_enabled,
      lockSettingsDisableNote: 'false',
      lockSettingsLockedLayout: 'false',
      lockSettingsLockOnJoin: 'false',
      lockSettingsLockOnJoinConfigurable: 'false',
      
      // Layout settings
      meetingLayout: meetingConfig.layout_settings.default_layout,
      
      // Breakout rooms
      breakoutRoomsEnabled: meetingConfig.breakout_rooms_enabled ? 'true' : 'false',
      breakoutRoomsPrivateChatEnabled: 'true',
      breakoutRoomsRecord: 'false',
      
      // Polls
      allowRequestsWithoutSession: 'false',
      
      // Presentation
      disabledFeatures: this.buildDisabledFeatures(meetingConfig),
      
      // Custom parameters for ClassroomIO
      'meta_classroomio_session_id': session.id,
      'meta_classroomio_batch_id': session.batch_id,
      'meta_classroomio_security': JSON.stringify(session.security_settings)
    };

    const url = this.buildApiUrl('create', params);

    try {
      const response = await fetch(url, { method: 'GET' });
      const xmlText = await response.text();
      
      // Parse XML response
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
      
      const returnCode = xmlDoc.getElementsByTagName('returncode')[0]?.textContent;
      
      if (returnCode === 'SUCCESS') {
        const meetingIDElement = xmlDoc.getElementsByTagName('meetingID')[0];
        const attendeePWElement = xmlDoc.getElementsByTagName('attendeePW')[0];
        const moderatorPWElement = xmlDoc.getElementsByTagName('moderatorPW')[0];
        
        // Store meeting credentials in session config
        const updatedConfig = {
          ...meetingConfig,
          meeting_id: meetingIDElement?.textContent || meetingID,
          attendee_password: attendeePWElement?.textContent || params.attendeePW,
          moderator_password: moderatorPWElement?.textContent || params.moderatorPW,
          meeting_url: this.config.serverUrl
        };

        return JSON.stringify(updatedConfig);
      } else {
        const messageElement = xmlDoc.getElementsByTagName('message')[0];
        throw new Error(`Failed to create meeting: ${messageElement?.textContent || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error creating BBB meeting:', error);
      throw error;
    }
  }

  // Generate join URL
  generateJoinUrl(meetingID: string, fullName: string, password: string, userID: string, role: 'moderator' | 'attendee', avatarURL?: string): string {
    const params = {
      fullName: fullName,
      meetingID: meetingID,
      password: password,
      userID: userID,
      joinViaHtml5: 'true',
      guest: 'false',
      avatarURL: avatarURL || '',
      
      // Custom parameters
      'userdata-bbb_custom_style_url': '', // Custom CSS if needed
      'userdata-bbb_show_participants_on_login': 'true',
      'userdata-bbb_multi_user_pen_only': role === 'moderator' ? 'false' : 'true',
      'userdata-bbb_presenter_tools': role === 'moderator' ? 'true' : 'false',
      
      // ClassroomIO specific
      'userdata-classroomio_user_id': userID,
      'userdata-classroomio_role': role
    };

    return this.buildApiUrl('join', params);
  }

  // Get meeting info
  async getMeetingInfo(meetingID: string, password: string): Promise<BBBMeetingInfo | null> {
    const params = {
      meetingID: meetingID,
      password: password
    };

    const url = this.buildApiUrl('getMeetingInfo', params);

    try {
      const response = await fetch(url, { method: 'GET' });
      const xmlText = await response.text();
      
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
      
      const returnCode = xmlDoc.getElementsByTagName('returncode')[0]?.textContent;
      
      if (returnCode === 'SUCCESS') {
        return {
          meetingID: this.getXmlValue(xmlDoc, 'meetingID'),
          meetingName: this.getXmlValue(xmlDoc, 'meetingName'),
          createTime: this.getXmlValue(xmlDoc, 'createTime'),
          voiceBridge: this.getXmlValue(xmlDoc, 'voiceBridge'),
          dialNumber: this.getXmlValue(xmlDoc, 'dialNumber'),
          attendeePW: this.getXmlValue(xmlDoc, 'attendeePW'),
          moderatorPW: this.getXmlValue(xmlDoc, 'moderatorPW'),
          running: this.getXmlValue(xmlDoc, 'running') === 'true',
          duration: parseInt(this.getXmlValue(xmlDoc, 'duration') || '0'),
          hasUserJoined: this.getXmlValue(xmlDoc, 'hasUserJoined') === 'true',
          recording: this.getXmlValue(xmlDoc, 'recording') === 'true',
          hasBeenForciblyEnded: this.getXmlValue(xmlDoc, 'hasBeenForciblyEnded') === 'true',
          startTime: this.getXmlValue(xmlDoc, 'startTime'),
          endTime: this.getXmlValue(xmlDoc, 'endTime'),
          participantCount: parseInt(this.getXmlValue(xmlDoc, 'participantCount') || '0'),
          listenerCount: parseInt(this.getXmlValue(xmlDoc, 'listenerCount') || '0'),
          voiceParticipantCount: parseInt(this.getXmlValue(xmlDoc, 'voiceParticipantCount') || '0'),
          videoCount: parseInt(this.getXmlValue(xmlDoc, 'videoCount') || '0'),
          maxUsers: parseInt(this.getXmlValue(xmlDoc, 'maxUsers') || '0'),
          moderatorCount: parseInt(this.getXmlValue(xmlDoc, 'moderatorCount') || '0')
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error getting BBB meeting info:', error);
      return null;
    }
  }

  // End meeting
  async endMeeting(meetingID: string, password: string): Promise<boolean> {
    const params = {
      meetingID: meetingID,
      password: password
    };

    const url = this.buildApiUrl('end', params);

    try {
      const response = await fetch(url, { method: 'GET' });
      const xmlText = await response.text();
      
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
      
      const returnCode = xmlDoc.getElementsByTagName('returncode')[0]?.textContent;
      return returnCode === 'SUCCESS';
    } catch (error) {
      console.error('Error ending BBB meeting:', error);
      return false;
    }
  }

  // Get recordings
  async getRecordings(meetingID?: string): Promise<any[]> {
    const params: Record<string, any> = {};
    if (meetingID) {
      params.meetingID = meetingID;
    }

    const url = this.buildApiUrl('getRecordings', params);

    try {
      const response = await fetch(url, { method: 'GET' });
      const xmlText = await response.text();
      
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
      
      const returnCode = xmlDoc.getElementsByTagName('returncode')[0]?.textContent;
      
      if (returnCode === 'SUCCESS') {
        const recordings: any[] = [];
        const recordingElements = xmlDoc.getElementsByTagName('recording');
        
        for (let i = 0; i < recordingElements.length; i++) {
          const recording = recordingElements[i];
          recordings.push({
            recordID: this.getXmlValue(recording, 'recordID'),
            meetingID: this.getXmlValue(recording, 'meetingID'),
            name: this.getXmlValue(recording, 'name'),
            published: this.getXmlValue(recording, 'published') === 'true',
            state: this.getXmlValue(recording, 'state'),
            startTime: this.getXmlValue(recording, 'startTime'),
            endTime: this.getXmlValue(recording, 'endTime'),
            participants: parseInt(this.getXmlValue(recording, 'participants') || '0'),
            playback: this.parsePlaybackFormats(recording)
          });
        }
        
        return recordings;
      }
      
      return [];
    } catch (error) {
      console.error('Error getting BBB recordings:', error);
      return [];
    }
  }

  // Helper methods
  private generatePassword(): string {
    return crypto.randomBytes(8).toString('hex');
  }

  private buildDisabledFeatures(config: MeetingConfig): string {
    const disabled: string[] = [];
    
    if (!config.chat_enabled) {
      disabled.push('chat');
    }
    
    if (!config.screen_sharing_enabled) {
      disabled.push('screenshare');
    }
    
    if (!config.polls_enabled) {
      disabled.push('polls');
    }
    
    if (!config.whiteboard_enabled) {
      disabled.push('presentation');
    }
    
    return disabled.join(',');
  }

  private getXmlValue(xmlDoc: Document | Element, tagName: string): string {
    const element = xmlDoc.getElementsByTagName(tagName)[0];
    return element?.textContent || '';
  }

  private parsePlaybackFormats(recording: Element): any[] {
    const playbackElements = recording.getElementsByTagName('playback');
    const formats: any[] = [];
    
    for (let i = 0; i < playbackElements.length; i++) {
      const playback = playbackElements[i];
      const formatElements = playback.getElementsByTagName('format');
      
      for (let j = 0; j < formatElements.length; j++) {
        const format = formatElements[j];
        formats.push({
          type: this.getXmlValue(format, 'type'),
          url: this.getXmlValue(format, 'url'),
          length: parseInt(this.getXmlValue(format, 'length') || '0')
        });
      }
    }
    
    return formats;
  }

  // Webhook handling for real-time events
  handleWebhook(webhookData: any): void {
    // Process BBB webhook events
    const eventType = webhookData.event;
    
    switch (eventType) {
      case 'meeting-created':
        this.handleMeetingCreated(webhookData);
        break;
      case 'meeting-ended':
        this.handleMeetingEnded(webhookData);
        break;
      case 'user-joined':
        this.handleUserJoined(webhookData);
        break;
      case 'user-left':
        this.handleUserLeft(webhookData);
        break;
      case 'recording-ready':
        this.handleRecordingReady(webhookData);
        break;
      default:
        console.log('Unknown BBB webhook event:', eventType);
    }
  }

  private handleMeetingCreated(data: any): void {
    console.log('Meeting created:', data);
    // Update session status in database
  }

  private handleMeetingEnded(data: any): void {
    console.log('Meeting ended:', data);
    // Update session status and analytics
  }

  private handleUserJoined(data: any): void {
    console.log('User joined:', data);
    // Update participant status and analytics
  }

  private handleUserLeft(data: any): void {
    console.log('User left:', data);
    // Update participant status and duration
  }

  private handleRecordingReady(data: any): void {
    console.log('Recording ready:', data);
    // Update session with recording URL
  }
}
