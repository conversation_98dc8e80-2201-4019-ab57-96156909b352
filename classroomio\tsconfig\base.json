{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "types": ["node", "vite/client"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.svelte"], "exclude": ["node_modules", "dist", "build", ".svelte-kit"]}