// Global Test Setup for ClassroomIO
// Runs once before all tests to set up the testing environment

import { beforeAll, afterAll } from 'vitest';

// Global test configuration
const globalConfig = {
  testTimeout: 10000,
  setupTimeout: 30000,
  teardownTimeout: 10000
};

// Mock environment variables for testing
const mockEnvVars = {
  NODE_ENV: 'test',
  VITE_SUPABASE_URL: 'http://localhost:54321',
  VITE_SUPABASE_ANON_KEY: 'test-anon-key',
  VITE_APP_ENV: 'test',
  DATABASE_URL: 'postgresql://postgres:postgres@localhost:54321/classroomio_test',
  REDIS_URL: 'redis://localhost:6379/1'
};

// Global setup function
export async function setup() {
  console.log('🚀 Starting global test setup...');
  
  // Set up environment variables
  Object.entries(mockEnvVars).forEach(([key, value]) => {
    if (!process.env[key]) {
      process.env[key] = value;
    }
  });
  
  // Set up global mocks
  setupGlobalMocks();
  
  // Set up test database if needed
  await setupTestDatabase();
  
  // Set up test Redis if needed
  await setupTestRedis();
  
  console.log('✅ Global test setup completed');
}

// Global teardown function
export async function teardown() {
  console.log('🧹 Starting global test teardown...');
  
  // Clean up test database
  await cleanupTestDatabase();
  
  // Clean up test Redis
  await cleanupTestRedis();
  
  // Clean up temporary files
  await cleanupTempFiles();
  
  console.log('✅ Global test teardown completed');
}

// Set up global mocks
function setupGlobalMocks() {
  // Mock fetch globally
  global.fetch = vi.fn();
  
  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn()
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  });
  
  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn()
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock
  });
  
  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));
  
  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));
  
  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
  
  // Mock URL.createObjectURL
  global.URL.createObjectURL = vi.fn(() => 'mock-object-url');
  global.URL.revokeObjectURL = vi.fn();
  
  // Mock WebSocket
  global.WebSocket = vi.fn().mockImplementation(() => ({
    send: vi.fn(),
    close: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    readyState: WebSocket.OPEN
  }));
  
  // Mock navigator.mediaDevices
  Object.defineProperty(navigator, 'mediaDevices', {
    writable: true,
    value: {
      getUserMedia: vi.fn().mockResolvedValue({
        getTracks: () => [{ stop: vi.fn() }]
      }),
      getDisplayMedia: vi.fn().mockResolvedValue({
        getTracks: () => [{ stop: vi.fn() }]
      }),
      enumerateDevices: vi.fn().mockResolvedValue([])
    }
  });
  
  // Mock Notification API
  global.Notification = vi.fn().mockImplementation(() => ({
    close: vi.fn()
  }));
  Object.defineProperty(Notification, 'permission', {
    value: 'granted',
    writable: true
  });
  Object.defineProperty(Notification, 'requestPermission', {
    value: vi.fn().mockResolvedValue('granted'),
    writable: true
  });
}

// Set up test database
async function setupTestDatabase() {
  try {
    // In a real implementation, this would:
    // 1. Create test database if it doesn't exist
    // 2. Run migrations
    // 3. Seed with test data
    console.log('📊 Setting up test database...');
    
    // Mock database setup for now
    await new Promise(resolve => setTimeout(resolve, 100));
    
    console.log('✅ Test database ready');
  } catch (error) {
    console.error('❌ Failed to setup test database:', error);
    throw error;
  }
}

// Set up test Redis
async function setupTestRedis() {
  try {
    console.log('🔴 Setting up test Redis...');
    
    // Mock Redis setup for now
    await new Promise(resolve => setTimeout(resolve, 50));
    
    console.log('✅ Test Redis ready');
  } catch (error) {
    console.error('❌ Failed to setup test Redis:', error);
    // Don't throw error for Redis as it's optional in tests
  }
}

// Clean up test database
async function cleanupTestDatabase() {
  try {
    console.log('🧹 Cleaning up test database...');
    
    // In a real implementation, this would:
    // 1. Drop test tables
    // 2. Close connections
    await new Promise(resolve => setTimeout(resolve, 100));
    
    console.log('✅ Test database cleaned up');
  } catch (error) {
    console.error('❌ Failed to cleanup test database:', error);
  }
}

// Clean up test Redis
async function cleanupTestRedis() {
  try {
    console.log('🧹 Cleaning up test Redis...');
    
    // Mock Redis cleanup
    await new Promise(resolve => setTimeout(resolve, 50));
    
    console.log('✅ Test Redis cleaned up');
  } catch (error) {
    console.error('❌ Failed to cleanup test Redis:', error);
  }
}

// Clean up temporary files
async function cleanupTempFiles() {
  try {
    console.log('🧹 Cleaning up temporary files...');
    
    // Clean up any temporary files created during tests
    // This would include uploaded files, generated reports, etc.
    
    console.log('✅ Temporary files cleaned up');
  } catch (error) {
    console.error('❌ Failed to cleanup temporary files:', error);
  }
}

// Export for use in vitest config
export default setup;
