# ClassroomIO Development Dockerfile
# Optimized for development with hot reload and debugging

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    bash \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Install pnpm
RUN npm install -g pnpm@8.0.0

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY turbo.json ./
COPY tsconfig.json ./

# Copy workspace packages
COPY packages/ ./packages/
COPY apps/dashboard/package.json ./apps/dashboard/
COPY apps/worker/package.json ./apps/worker/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p logs uploads

# Set permissions
RUN chown -R node:node /app
USER node

# Expose ports
EXPOSE 5173 24678

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5173/health || exit 1

# Development command
CMD ["pnpm", "dev"]
