import Image from 'next/image';
import { Steps } from 'nextra/components';
import classroomioBasePage from './images/classroomio.webp';
import signUpPage from './images/login-page.webp';
import unEditedProfilePage from './images/unedited-profile-page.webp';
import editedProfilePage from './images/edited-profile-page.webp';

# Setup Profile

<Steps>
### Go to Classroomio

Go to [app.classroomio.com](https://app.classroomio.com). Click on the "Login"" button on the top right to take you to the Login page.

<Image src={classroomioBasePage} alt="Classroomio Page" quality="100" />

### Sign In

Sign in with your email & password.

<Image src={signUpPage} alt="Classroomio Page" quality="100" />

### Go to the Settings page

Click on the "Settings" button on the lower left corner on your dashboard page and you'll be routed to the profile page.

<Image src={unEditedProfilePage} alt="Unedited Profile Page" quality="100" />

### Make Changes

Upload a profile picture and edit any input field you want to then click "Update Profile".

<Image src={editedProfilePage} alt="Edited Profile Page" quality="100" />

</Steps>
