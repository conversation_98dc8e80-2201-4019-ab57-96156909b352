import { Callout } from 'nextra/components';

# Student Dashboard - Developers guide

In this guide I will be showing you how to set up a local student dashboard that a student will see. This dashboard will allow students to view their grades, attendance, and other information. This dashboard will also allow students to submit assignments and view their grades for those assignments.

## Follow the steps in this video

### Requirements

<Callout type="info" emoji="ℹ️">
  Make sure to use 2 different browser windows: The reason is that one will be for the admin and
  another for the students. For the student we will need to set the cookies value to hold the
  organization siteName since we are using the subdomain to access the student dashboard like on
  production.
</Callout>

### Demo

<div className="nx-w-full nx-flex nx-items-center nx-justify-center">
  <iframe
    width="560"
    height="315"
    src="https://www.youtube.com/embed/QGTnK39xexs?si=ogFbyl2KK0XaFob4"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    className="nx-my-4 nx-rounded-md"
    allowfullscreen
  ></iframe>
</div>
