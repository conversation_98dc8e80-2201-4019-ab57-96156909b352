import { Callout } from 'nextra/components';

# Login quickly with Demo Accounts

This guide will help you get started quickly once you setup our local environment and you want to start using the [dashboard](https://app.classroomio.com).

<Callout>
  The assumption of this guide is that you've gone through our README and you've setup `supabase`
  and you have `dashboard` running locally.
</Callout>

### Admin Login Credentials.

Once you have the `dashboard` service running locally and you have setup supabase, we added seed data that can allow you access an admin account to a demo organization and a student account to a student dashboard. This allows you see what ClassroomIO can do quickly without trying to figure it out.

**Login:** <EMAIL>

**Password:** 123456

<div className="nx-w-full nx-flex nx-items-center nx-justify-center">
  <iframe
    width="560"
    height="315"
    src="https://www.youtube.com/embed/RfE4tQ_BEds?si=uXpYKokMg4SWMkQX"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    className="nx-my-4 nx-rounded-md"
    allowfullscreen
  ></iframe>
</div>

### Student Login Credentials

**Login:** <EMAIL>

**Password:** 123456

<div className="nx-w-full nx-flex nx-items-center nx-justify-center">
  <iframe
    width="560"
    height="315"
    src="https://www.youtube.com/embed/FG7B9rKjVi4?si=TeY088zf8OsELocC"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    className="nx-my-4 nx-rounded-md"
    allowfullscreen
  ></iframe>
</div>
