import {
  derived,
  writable
} from "./chunk-J5MXP32H.js";
import {
  get_store_value
} from "./chunk-E4ZC5ETH.js";
import "./chunk-TCF7Q4S4.js";
import "./chunk-2GTGKKMZ.js";

// ../../node_modules/.pnpm/@sveltekit-i18n+base@1.3.7_svelte@4.2.20/node_modules/@sveltekit-i18n/base/dist/index.js
var H = Object.defineProperty;
var q = Object.defineProperties;
var B = Object.getOwnPropertyDescriptors;
var x = Object.getOwnPropertySymbols;
var K = Object.prototype.hasOwnProperty;
var A = Object.prototype.propertyIsEnumerable;
var N = (s, t, e) => t in s ? H(s, t, { enumerable: true, configurable: true, writable: true, value: e }) : s[t] = e;
var l = (s, t) => {
  for (var e in t || (t = {}))
    K.call(t, e) && N(s, e, t[e]);
  if (x)
    for (var e of x(t))
      A.call(t, e) && N(s, e, t[e]);
  return s;
};
var f = (s, t) => q(s, B(t));
var L = (s, t) => {
  var e = {};
  for (var a in s)
    K.call(s, a) && t.indexOf(a) < 0 && (e[a] = s[a]);
  if (s != null && x)
    for (var a of x(s))
      t.indexOf(a) < 0 && A.call(s, a) && (e[a] = s[a]);
  return e;
};
var C = ["error", "warn", "debug"];
var $ = ({ logger: s = console, level: t = C[1], prefix: e = "[i18n]: " }) => C.reduce((a, r, i) => f(l({}, a), { [r]: (o) => C.indexOf(t) >= i && s[r](`${e}${o}`) }), {});
var c = $({});
var V = (s) => {
  c = s;
};
var z = (n) => {
  var d = n, { parser: s, key: t, params: e, translations: a, locale: r, fallbackLocale: i } = d, o = L(d, ["parser", "key", "params", "translations", "locale", "fallbackLocale"]);
  if (!t)
    return c.warn(`No translation key provided ('${r}' locale). Skipping translation...`), "";
  if (!r)
    return c.warn(`No locale provided for '${t}' key. Skipping translation...`), "";
  let u = (a[r] || {})[t];
  if (i && u === void 0 && (c.debug(`No translation provided for '${t}' key in locale '${r}'. Trying fallback '${i}'`), u = (a[i] || {})[t]), u === void 0) {
    if (c.debug(`No translation provided for '${t}' key in fallback '${i}'.`), o.hasOwnProperty("fallbackValue"))
      return o.fallbackValue;
    c.warn(`No translation nor fallback found for '${t}' .`);
  }
  return s.parse(u, e, r, t);
};
var h = (...s) => s.length ? s.filter((t) => !!t).map((t) => {
  let e = `${t}`.toLowerCase();
  try {
    let [a] = Intl.Collator.supportedLocalesOf(t);
    if (!a)
      throw new Error();
    e = a;
  } catch (a) {
    c.warn(`'${t}' locale is non-standard.`);
  }
  return e;
}) : [];
var w = (s, t, e) => {
  if (t && Array.isArray(s))
    return s.map((a) => w(a, t));
  if (s && typeof s == "object") {
    let a = Object.keys(s).reduce((r, i) => {
      let o = s[i], n = e ? `${e}.${i}` : `${i}`;
      return o && typeof o == "object" && !(t && Array.isArray(o)) ? l(l({}, r), w(o, t, n)) : f(l({}, r), { [n]: w(o, t) });
    }, {});
    return Object.keys(a).length ? a : null;
  }
  return s;
};
var G = (s) => s.reduce((t, { key: e, data: a, locale: r }) => {
  if (!a)
    return t;
  let [i] = h(r), o = f(l({}, t[i] || {}), { [e]: a });
  return f(l({}, t), { [i]: o });
}, {});
var E = async (s) => {
  try {
    let t = await Promise.all(s.map((r) => {
      var i = r, { loader: e } = i, a = L(i, ["loader"]);
      return new Promise(async (o) => {
        let n;
        try {
          n = await e();
        } catch (d) {
          c.error(`Failed to load translation. Verify your '${a.locale}' > '${a.key}' Loader.`), c.error(d);
        }
        o(f(l({ loader: e }, a), { data: n }));
      });
    }));
    return G(t);
  } catch (t) {
    c.error(t);
  }
  return {};
};
var W = (s) => (t) => {
  try {
    if (typeof t == "string")
      return t === s;
    if (typeof t == "object")
      return t.test(s);
  } catch (e) {
    c.error("Invalid route config!");
  }
  return false;
};
var F = (s, t) => {
  let e = true;
  try {
    e = Object.keys(s).filter((a) => s[a] !== void 0).every((a) => s[a] === t[a]);
  } catch (a) {
  }
  return e;
};
var D = 1e3 * 60 * 60 * 24;
var O = class {
  constructor(t) {
    this.cachedAt = 0;
    this.loadedKeys = {};
    this.currentRoute = writable();
    this.config = writable();
    this.isLoading = writable(false);
    this.promises = /* @__PURE__ */ new Set();
    this.loading = { subscribe: this.isLoading.subscribe, toPromise: (t2, e) => {
      let { fallbackLocale: a } = get_store_value(this.config), r = Array.from(this.promises).filter((i) => {
        let o = F({ locale: h(t2)[0], route: e }, i);
        return a && (o = o || F({ locale: h(a)[0], route: e }, i)), o;
      }).map(({ promise: i }) => i);
      return Promise.all(r);
    }, get: () => get_store_value(this.isLoading) };
    this.privateRawTranslations = writable({});
    this.rawTranslations = { subscribe: this.privateRawTranslations.subscribe, get: () => get_store_value(this.rawTranslations) };
    this.privateTranslations = writable({});
    this.translations = { subscribe: this.privateTranslations.subscribe, get: () => get_store_value(this.translations) };
    this.locales = f(l({}, derived([this.config, this.privateTranslations], ([t2, e]) => {
      if (!t2)
        return [];
      let { loaders: a = [] } = t2, r = a.map(({ locale: o }) => o), i = Object.keys(e).map((o) => o);
      return Array.from(/* @__PURE__ */ new Set([...h(...r), ...h(...i)]));
    }, [])), { get: () => get_store_value(this.locales) });
    this.internalLocale = writable();
    this.loaderTrigger = derived([this.internalLocale, this.currentRoute], ([t2, e], a) => {
      var r, i;
      t2 !== void 0 && e !== void 0 && !(t2 === ((r = get_store_value(this.loaderTrigger)) == null ? void 0 : r[0]) && e === ((i = get_store_value(this.loaderTrigger)) == null ? void 0 : i[1])) && (c.debug("Triggering translation load..."), a([t2, e]));
    }, []);
    this.localeHelper = writable();
    this.locale = { subscribe: this.localeHelper.subscribe, forceSet: this.localeHelper.set, set: this.internalLocale.set, update: this.internalLocale.update, get: () => get_store_value(this.locale) };
    this.initialized = derived([this.locale, this.currentRoute, this.privateTranslations], ([t2, e, a], r) => {
      get_store_value(this.initialized) || r(t2 !== void 0 && e !== void 0 && !!Object.keys(a).length);
    });
    this.translation = derived([this.privateTranslations, this.locale, this.isLoading], ([t2, e, a], r) => {
      let i = t2[e];
      i && Object.keys(i).length && !a && r(i);
    }, {});
    this.t = f(l({}, derived([this.config, this.translation], (r) => {
      var [i] = r, o = i, { parser: t2, fallbackLocale: e } = o, a = L(o, ["parser", "fallbackLocale"]);
      return (n, ...d) => z(l({ parser: t2, key: n, params: d, translations: this.translations.get(), locale: this.locale.get(), fallbackLocale: e }, a.hasOwnProperty("fallbackValue") ? { fallbackValue: a.fallbackValue } : {}));
    })), { get: (t2, ...e) => get_store_value(this.t)(t2, ...e) });
    this.l = f(l({}, derived([this.config, this.translations], (i) => {
      var [o, ...n] = i, d = o, { parser: t2, fallbackLocale: e } = d, a = L(d, ["parser", "fallbackLocale"]), [r] = n;
      return (u, b, ...k) => z(l({ parser: t2, key: b, params: k, translations: r, locale: u, fallbackLocale: e }, a.hasOwnProperty("fallbackValue") ? { fallbackValue: a.fallbackValue } : {}));
    })), { get: (t2, e, ...a) => get_store_value(this.l)(t2, e, ...a) });
    this.getLocale = (t2) => {
      let { fallbackLocale: e } = get_store_value(this.config) || {}, a = t2 || e;
      if (!a)
        return;
      let r = this.locales.get();
      return r.find((o) => h(a).includes(o)) || r.find((o) => h(e).includes(o));
    };
    this.setLocale = (t2) => {
      if (t2 && t2 !== get_store_value(this.internalLocale))
        return c.debug(`Setting '${t2}' locale.`), this.internalLocale.set(t2), this.loading.toPromise(t2, get_store_value(this.currentRoute));
    };
    this.setRoute = (t2) => {
      if (t2 !== get_store_value(this.currentRoute)) {
        c.debug(`Setting '${t2}' route.`), this.currentRoute.set(t2);
        let e = get_store_value(this.internalLocale);
        return this.loading.toPromise(e, t2);
      }
    };
    this.loadConfig = async (t2) => {
      await this.configLoader(t2);
    };
    this.getTranslationProps = async (t2 = this.locale.get(), e = get_store_value(this.currentRoute)) => {
      let a = get_store_value(this.config);
      if (!a || !t2)
        return [];
      let r = this.translations.get(), { loaders: i, fallbackLocale: o = "", cache: n = D } = a || {}, d = Number.isNaN(+n) ? D : +n;
      this.cachedAt ? Date.now() > d + this.cachedAt && (c.debug("Refreshing cache."), this.loadedKeys = {}, this.cachedAt = 0) : (c.debug("Setting cache timestamp."), this.cachedAt = Date.now());
      let [u, b] = h(t2, o), k = r[u], I = r[b], R = (i || []).map((j) => {
        var T = j, { locale: p } = T, y = L(T, ["locale"]);
        return f(l({}, y), { locale: h(p)[0] });
      }).filter(({ routes: p }) => !p || (p || []).some(W(e))).filter(({ key: p, locale: y }) => y === u && (!k || !(this.loadedKeys[u] || []).includes(p)) || o && y === b && (!I || !(this.loadedKeys[b] || []).includes(p)));
      if (R.length) {
        this.isLoading.set(true), c.debug("Fetching translations...");
        let p = await E(R);
        this.isLoading.set(false);
        let y = Object.keys(p).reduce((T, P) => f(l({}, T), { [P]: Object.keys(p[P]) }), {}), j = R.filter(({ key: T, locale: P }) => (y[P] || []).some((S) => `${S}`.startsWith(T))).reduce((T, { key: P, locale: S }) => f(l({}, T), { [S]: [...T[S] || [], P] }), {});
        return [p, j];
      }
      return [];
    };
    this.addTranslations = (t2, e) => {
      if (!t2)
        return;
      let a = get_store_value(this.config), { preprocess: r } = a || {};
      c.debug("Adding translations...");
      let i = Object.keys(t2 || {});
      this.privateRawTranslations.update((o) => i.reduce((n, d) => f(l({}, n), { [d]: l(l({}, n[d] || {}), t2[d]) }), o)), this.privateTranslations.update((o) => i.reduce((n, d) => {
        let u = true, b = t2[d];
        return typeof r == "function" && (b = r(b)), (typeof r == "function" || r === "none") && (u = false), f(l({}, n), { [d]: l(l({}, n[d] || {}), u ? w(b, r === "preserveArrays") : b) });
      }, o)), i.forEach((o) => {
        let n = Object.keys(t2[o]).map((d) => `${d}`.split(".")[0]);
        e && (n = e[o]), this.loadedKeys[o] = Array.from(/* @__PURE__ */ new Set([...this.loadedKeys[o] || [], ...n || []]));
      });
    };
    this.loader = async ([t2, e]) => {
      let a = this.getLocale(t2) || void 0;
      c.debug(`Adding loader promise for '${a}' locale and '${e}' route.`);
      let r = (async () => {
        let i = await this.getTranslationProps(a, e);
        i.length && this.addTranslations(...i);
      })();
      this.promises.add({ locale: a, route: e, promise: r }), r.then(() => {
        a && this.locale.get() !== a && this.locale.forceSet(a);
      });
    };
    this.loadTranslations = (t2, e = get_store_value(this.currentRoute) || "") => {
      let a = this.getLocale(t2);
      if (a)
        return this.setRoute(e), this.setLocale(a), this.loading.toPromise(a, e);
    };
    this.loaderTrigger.subscribe(this.loader), this.isLoading.subscribe(async (e) => {
      e && this.promises.size && (await this.loading.toPromise(), this.promises.clear(), c.debug("Loader promises have been purged."));
    }), t && this.loadConfig(t);
  }
  async configLoader(t) {
    if (!t)
      return c.error("No config provided!");
    let n = t, { initLocale: e, fallbackLocale: a, translations: r, log: i } = n, o = L(n, ["initLocale", "fallbackLocale", "translations", "log"]);
    i && V($(i)), [e] = h(e), [a] = h(a), c.debug("Setting config."), this.config.set(l({ initLocale: e, fallbackLocale: a, translations: r }, o)), r && this.addTranslations(r), e && await this.loadTranslations(e);
  }
};
export {
  O as default
};
//# sourceMappingURL=@sveltekit-i18n_base.js.map
