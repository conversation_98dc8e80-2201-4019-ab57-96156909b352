@echo off

:: ClassroomIO Docker Development Startup
:: Simple Docker-based development environment

echo ========================================
echo   ClassroomIO Docker Development
echo ========================================
echo.

echo Starting ClassroomIO with Docker...
echo This will download and start all required services.
echo.

:: Check if Docker is installed
echo [1/5] Checking Docker installation...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed or not running!
    echo.
    echo Please install Docker Desktop from:
    echo https://www.docker.com/products/docker-desktop
    echo.
    echo After installation:
    echo 1. Start Docker Desktop
    echo 2. Wait for it to fully start
    echo 3. Run this script again
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo SUCCESS: Docker is installed
docker --version
echo.

:: Check if Docker is running
echo [2/5] Checking if Dock<PERSON> is running...
docker info >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not running!
    echo.
    echo Please start Docker Desktop and wait for it to fully start.
    echo Look for the Docker icon in your system tray.
    echo When it shows "Docker Desktop is running", try again.
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo SUCCESS: Docker is running
echo.

:: Check if we're in the right directory
echo [3/5] Checking project directory...
if not exist "docker-compose.dev.yml" (
    echo ERROR: docker-compose.dev.yml not found!
    echo Please run this script from the ClassroomIO root directory.
    echo Current directory: %CD%
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo SUCCESS: Found docker-compose.dev.yml
echo.

:: Stop any existing containers
echo [4/5] Stopping any existing containers...
docker-compose -f docker-compose.dev.yml down >nul 2>&1
echo SUCCESS: Cleaned up existing containers
echo.

:: Start the development environment
echo [5/5] Starting ClassroomIO development environment...
echo.
echo This may take several minutes on first run (downloading images)...
echo Please be patient while Docker downloads and builds the containers.
echo.

docker-compose -f docker-compose.dev.yml up --build -d

if errorlevel 1 (
    echo.
    echo ERROR: Failed to start Docker containers!
    echo.
    echo Common solutions:
    echo 1. Make sure Docker Desktop is running
    echo 2. Check if ports 5173, 54321, 54322 are available
    echo 3. Try running: docker-compose -f docker-compose.dev.yml logs
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo.
echo SUCCESS: Containers started successfully!
echo.

:: Wait for services to be ready
echo Waiting for services to start...
echo This may take 1-2 minutes...
echo.

:: Wait for the application to be ready
set /a counter=0
:wait_loop
if %counter% geq 60 (
    echo WARNING: Services are taking longer than expected to start
    echo You can check the status manually with:
    echo   docker-compose -f docker-compose.dev.yml logs dashboard
    goto :show_status
)

timeout /t 5 /nobreak >nul
set /a counter+=5

:: Check if the main application is responding
curl -f http://localhost:5173/health >nul 2>&1
if not errorlevel 1 (
    echo SUCCESS: Application is ready!
    goto :show_status
)

echo Waiting... (%counter%/60 seconds)
goto :wait_loop

:show_status
echo.
echo ========================================
echo   ClassroomIO Development Ready!
echo ========================================
echo.
echo Services:
echo   Main Application:  http://localhost:5173
echo   Health Check:      http://localhost:5173/health
echo   API Health:        http://localhost:5173/api/health
echo   Database:          localhost:54322 (postgres/postgres)
echo   Redis:             localhost:6379
echo   Supabase Studio:   http://localhost:54323
echo.
echo Useful Commands:
echo   View logs:         docker-compose -f docker-compose.dev.yml logs -f
echo   Stop services:     docker-compose -f docker-compose.dev.yml down
echo   Restart:           docker-compose -f docker-compose.dev.yml restart
echo   Rebuild:           docker-compose -f docker-compose.dev.yml up --build
echo.

:: Try to open the browser
echo Opening browser...
start http://localhost:5173

echo.
echo The development environment is now running in Docker containers.
echo Press any key to view live logs (Ctrl+C to stop viewing logs)...
pause >nul

:: Show logs
echo.
echo Showing live logs (Press Ctrl+C to exit log view):
echo ========================================
docker-compose -f docker-compose.dev.yml logs -f dashboard

exit /b 0
