<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { DashboardConfig, DashboardWidget } from '$lib/utils/types/analytics';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Save, 
    Close, 
    Add, 
    Edit, 
    TrashCan,
    DragHorizontal,
    Settings,
    ChartLine,
    DataTable,
    View,
    TextAlignLeft
  } from 'carbon-icons-svelte';

  export let dashboard: DashboardConfig;
  export let organizationId: string;
  export let batchId: string | null = null;
  export let userId: string | null = null;

  const dispatch = createEventDispatcher<{
    save: { dashboard: DashboardConfig };
    cancel: void;
  }>();

  let editedDashboard: DashboardConfig = JSON.parse(JSON.stringify(dashboard));
  let selectedWidget: DashboardWidget | null = null;
  let showWidgetEditor = false;
  let showThemeEditor = false;
  let draggedWidget: DashboardWidget | null = null;

  const widgetTypes = [
    { type: 'metric', label: 'Metric', icon: View, description: 'Display key performance indicators' },
    { type: 'chart', label: 'Chart', icon: ChartLine, description: 'Visualize data with charts' },
    { type: 'table', label: 'Table', icon: DataTable, description: 'Display data in tabular format' },
    { type: 'text', label: 'Text', icon: TextAlignLeft, description: 'Add text content and descriptions' }
  ];

  const chartTypes = [
    { value: 'line', label: 'Line Chart' },
    { value: 'bar', label: 'Bar Chart' },
    { value: 'pie', label: 'Pie Chart' },
    { value: 'area', label: 'Area Chart' },
    { value: 'scatter', label: 'Scatter Plot' }
  ];

  function addWidget(type: string) {
    const newWidget: DashboardWidget = {
      id: `widget_${Date.now()}`,
      type: type as any,
      title: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      position: { x: 0, y: 0 },
      size: { width: 4, height: 3 },
      config: getDefaultWidgetConfig(type),
      data_source: {
        type: 'sql',
        query: 'SELECT 1 as value',
        parameters: {}
      },
      filters: []
    };

    editedDashboard.config.widgets = [...editedDashboard.config.widgets, newWidget];
    selectedWidget = newWidget;
    showWidgetEditor = true;
  }

  function getDefaultWidgetConfig(type: string) {
    switch (type) {
      case 'chart':
        return {
          chart_type: 'line',
          show_legend: true,
          show_grid: true,
          animation: true
        };
      case 'metric':
        return {
          show_trend: true,
          show_icon: true,
          format: 'number'
        };
      case 'table':
        return {
          show_search: true,
          show_pagination: true,
          page_size: 10
        };
      case 'text':
        return {
          text_align: 'left',
          font_size: 'base',
          font_weight: 'normal',
          enable_markdown: true
        };
      default:
        return {};
    }
  }

  function editWidget(widget: DashboardWidget) {
    selectedWidget = widget;
    showWidgetEditor = true;
  }

  function deleteWidget(widget: DashboardWidget) {
    if (confirm($t('analytics.confirm_delete_widget', { default: 'Are you sure you want to delete this widget?' }))) {
      editedDashboard.config.widgets = editedDashboard.config.widgets.filter(w => w.id !== widget.id);
    }
  }

  function saveWidget() {
    if (selectedWidget) {
      const index = editedDashboard.config.widgets.findIndex(w => w.id === selectedWidget!.id);
      if (index >= 0) {
        editedDashboard.config.widgets[index] = { ...selectedWidget };
      }
    }
    showWidgetEditor = false;
    selectedWidget = null;
  }

  function cancelWidgetEdit() {
    showWidgetEditor = false;
    selectedWidget = null;
  }

  function saveDashboard() {
    dispatch('save', { dashboard: editedDashboard });
  }

  function cancelEdit() {
    dispatch('cancel');
  }

  function handleDragStart(widget: DashboardWidget) {
    draggedWidget = widget;
  }

  function handleDragOver(event: DragEvent) {
    event.preventDefault();
  }

  function handleDrop(event: DragEvent, targetWidget: DashboardWidget) {
    event.preventDefault();
    if (draggedWidget && draggedWidget.id !== targetWidget.id) {
      // Swap positions
      const draggedIndex = editedDashboard.config.widgets.findIndex(w => w.id === draggedWidget!.id);
      const targetIndex = editedDashboard.config.widgets.findIndex(w => w.id === targetWidget.id);
      
      if (draggedIndex >= 0 && targetIndex >= 0) {
        const temp = editedDashboard.config.widgets[draggedIndex].position;
        editedDashboard.config.widgets[draggedIndex].position = editedDashboard.config.widgets[targetIndex].position;
        editedDashboard.config.widgets[targetIndex].position = temp;
      }
    }
    draggedWidget = null;
  }
</script>

<div class="dashboard-editor">
  <!-- Editor Header -->
  <div class="flex items-center justify-between mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
    <div>
      <h2 class="text-xl font-bold text-gray-900 dark:text-white">
        {$t('analytics.edit_dashboard', { default: 'Edit Dashboard' })}
      </h2>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        {editedDashboard.name}
      </p>
    </div>

    <div class="flex items-center space-x-2">
      <PrimaryButton
        variant={VARIANTS.OUTLINED}
        onClick={() => showThemeEditor = true}
        size="sm"
      >
        <Settings size={16} class="mr-1" />
        {$t('analytics.theme', { default: 'Theme' })}
      </PrimaryButton>

      <PrimaryButton
        variant={VARIANTS.OUTLINED}
        onClick={cancelEdit}
        size="sm"
      >
        <Close size={16} class="mr-1" />
        {$t('analytics.cancel', { default: 'Cancel' })}
      </PrimaryButton>

      <PrimaryButton
        variant={VARIANTS.CONTAINED}
        onClick={saveDashboard}
        size="sm"
      >
        <Save size={16} class="mr-1" />
        {$t('analytics.save', { default: 'Save' })}
      </PrimaryButton>
    </div>
  </div>

  <!-- Widget Palette -->
  <div class="mb-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
      {$t('analytics.add_widgets', { default: 'Add Widgets' })}
    </h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      {#each widgetTypes as widgetType}
        <button
          on:click={() => addWidget(widgetType.type)}
          class="p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900 transition-colors"
        >
          <div class="text-center">
            <svelte:component this={widgetType.icon} size={24} class="text-gray-600 dark:text-gray-400 mx-auto mb-2" />
            <div class="font-medium text-gray-900 dark:text-white text-sm">
              {widgetType.label}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">
              {widgetType.description}
            </div>
          </div>
        </button>
      {/each}
    </div>
  </div>

  <!-- Dashboard Preview -->
  <div class="mb-6">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
      {$t('analytics.dashboard_preview', { default: 'Dashboard Preview' })}
    </h3>
    
    <div 
      class="dashboard-grid border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 min-h-[400px]"
      style="
        display: grid;
        grid-template-columns: repeat({editedDashboard.config.layout?.columns || 12}, 1fr);
        gap: {editedDashboard.config.layout?.margin?.[0] || 10}px;
      "
    >
      {#each editedDashboard.config.widgets as widget (widget.id)}
        <div
          class="widget-preview border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800 cursor-move"
          style="
            grid-column: span {widget.size.width};
            grid-row: span {widget.size.height};
            min-height: {(editedDashboard.config.layout?.row_height || 60) * widget.size.height}px;
          "
          draggable="true"
          on:dragstart={() => handleDragStart(widget)}
          on:dragover={handleDragOver}
          on:drop={(e) => handleDrop(e, widget)}
          role="button"
          tabindex="0"
        >
          <!-- Widget Header -->
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <svelte:component 
                this={widgetTypes.find(t => t.type === widget.type)?.icon || View} 
                size={16} 
                class="text-gray-600 dark:text-gray-400" 
              />
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {widget.title}
              </span>
            </div>

            <div class="flex items-center space-x-1">
              <button
                on:click={() => editWidget(widget)}
                class="p-1 text-gray-400 hover:text-blue-600"
                title={$t('analytics.edit_widget', { default: 'Edit widget' })}
              >
                <Edit size={14} />
              </button>
              
              <button
                on:click={() => deleteWidget(widget)}
                class="p-1 text-gray-400 hover:text-red-600"
                title={$t('analytics.delete_widget', { default: 'Delete widget' })}
              >
                <TrashCan size={14} />
              </button>

              <DragHorizontal size={14} class="text-gray-400" />
            </div>
          </div>

          <!-- Widget Preview Content -->
          <div class="text-xs text-gray-600 dark:text-gray-400">
            <div class="mb-1">
              <strong>{$t('analytics.type', { default: 'Type' })}:</strong> {widget.type}
            </div>
            <div class="mb-1">
              <strong>{$t('analytics.size', { default: 'Size' })}:</strong> {widget.size.width}x{widget.size.height}
            </div>
            {#if widget.config.chart_type}
              <div>
                <strong>{$t('analytics.chart_type', { default: 'Chart Type' })}:</strong> {widget.config.chart_type}
              </div>
            {/if}
          </div>
        </div>
      {/each}

      {#if editedDashboard.config.widgets.length === 0}
        <div class="col-span-full flex items-center justify-center py-12">
          <div class="text-center">
            <Add size={32} class="text-gray-400 mx-auto mb-2" />
            <p class="text-gray-600 dark:text-gray-400">
              {$t('analytics.no_widgets', { default: 'No widgets added yet. Click on a widget type above to get started.' })}
            </p>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- Widget Editor Modal -->
{#if showWidgetEditor && selectedWidget}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('analytics.edit_widget', { default: 'Edit Widget' })}
        </h2>
        <button
          on:click={cancelWidgetEdit}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-4">
        <!-- Widget Title -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('analytics.widget_title', { default: 'Widget Title' })}
          </label>
          <input
            type="text"
            bind:value={selectedWidget.title}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <!-- Widget Size -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('analytics.width', { default: 'Width' })}
            </label>
            <input
              type="number"
              bind:value={selectedWidget.size.width}
              min="1"
              max="12"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('analytics.height', { default: 'Height' })}
            </label>
            <input
              type="number"
              bind:value={selectedWidget.size.height}
              min="1"
              max="10"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
        </div>

        <!-- Chart Type (for chart widgets) -->
        {#if selectedWidget.type === 'chart'}
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('analytics.chart_type', { default: 'Chart Type' })}
            </label>
            <select
              bind:value={selectedWidget.config.chart_type}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              {#each chartTypes as chartType}
                <option value={chartType.value}>{chartType.label}</option>
              {/each}
            </select>
          </div>
        {/if}

        <!-- Data Source -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('analytics.data_source', { default: 'Data Source' })}
          </label>
          <select
            bind:value={selectedWidget.data_source.type}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="sql">SQL Query</option>
            <option value="function">Database Function</option>
            <option value="api">API Endpoint</option>
          </select>
        </div>

        <!-- SQL Query (if SQL data source) -->
        {#if selectedWidget.data_source.type === 'sql'}
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('analytics.sql_query', { default: 'SQL Query' })}
            </label>
            <textarea
              bind:value={selectedWidget.data_source.query}
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
              placeholder="SELECT * FROM table_name WHERE condition = $parameter"
            ></textarea>
          </div>
        {/if}

        <!-- Function Name (if function data source) -->
        {#if selectedWidget.data_source.type === 'function'}
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('analytics.function_name', { default: 'Function Name' })}
            </label>
            <input
              type="text"
              bind:value={selectedWidget.data_source.function_name}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="get_analytics_data"
            />
          </div>
        {/if}

        <!-- API Endpoint (if API data source) -->
        {#if selectedWidget.data_source.type === 'api'}
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {$t('analytics.api_endpoint', { default: 'API Endpoint' })}
            </label>
            <input
              type="url"
              bind:value={selectedWidget.data_source.endpoint}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="https://api.example.com/data"
            />
          </div>
        {/if}
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={cancelWidgetEdit}
        >
          {$t('analytics.cancel', { default: 'Cancel' })}
        </PrimaryButton>
        
        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={saveWidget}
        >
          <Save size={20} class="mr-2" />
          {$t('analytics.save_widget', { default: 'Save Widget' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}

<style>
  .dashboard-grid {
    min-height: 400px;
  }

  .widget-preview {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .widget-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .widget-preview:focus {
    outline: 2px solid var(--primary-color, #3B82F6);
    outline-offset: 2px;
  }
</style>
