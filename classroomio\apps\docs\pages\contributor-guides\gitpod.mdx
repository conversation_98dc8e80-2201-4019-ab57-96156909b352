import Image from 'next/image';
import { Callout } from 'nextra/components';
import GitpodEnv from './images/gitpod_env.png';

# Gitpod Setup

## One Click Setup

- This will open a fully configured workspace in your browser with all the necessary dependencies already installed.

- Click the button below to open this project in Gitpod.

[![Open in Gitpod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/rotimi-best/classroomio)

<Callout type="info">
  Gitpod does not utilize `localhost` for accessing services. Instead, it uses its own URL after
  starting all the necessary services. The correct URLs to access the project are automatically
  inserted into the `.env` file upon initialization.
</Callout>

### Viewing URLs in Terminals

To view the URLs, follow these steps:

1. Open your terminal.
2. Click on the `port` to see the URLs.

<Image src={GitpodEnv} alt="gitpod" quality="100" />
