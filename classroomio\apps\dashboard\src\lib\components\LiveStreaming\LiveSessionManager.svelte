<script lang="ts">
  import { onMount, onDestroy, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { LiveSession, LiveSessionParticipant, MeetingConfig } from '$lib/utils/types/liveStreaming';
  import type { SecurityPolicy } from '$lib/utils/types/security';
  import { liveSessionService, participantService } from '$lib/utils/services/liveStreaming';
  import { BigBlueButtonService } from '$lib/utils/services/bigBlueButton';
  import { DeviceFingerprintGenerator } from '$lib/utils/security/deviceFingerprint';
  import { securityEventService } from '$lib/utils/services/security';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Video, 
    VideoOff, 
    Microphone, 
    MicrophoneOff,
    Screen,
    Chat,
    Group,
    Settings,
    Calendar,
    Time,
    UserMultiple,
    CheckmarkFilled,
    Warning,
    Launch
  } from 'carbon-icons-svelte';

  export let batchId: string;
  export let sessionId: string | null = null;
  export let securityPolicy: SecurityPolicy | null = null;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    sessionJoined: { sessionId: string; meetingUrl: string };
    sessionEnded: { sessionId: string };
    participantUpdate: { participant: LiveSessionParticipant };
  }>();

  let session = writable<LiveSession | null>(null);
  let participants = writable<LiveSessionParticipant[]>([]);
  let loading = true;
  let error: string | null = null;
  let joinError: string | null = null;
  let isJoining = false;
  let deviceFingerprint: string = '';
  let bbbService: BigBlueButtonService | null = null;
  let meetingInfo: any = null;
  let refreshInterval: NodeJS.Timeout | null = null;

  // User state
  $: userId = $globalStore.user?.id;
  $: userRole = $globalStore.role;
  $: isInstructor = userRole === 'teacher' || $globalStore.isOrgAdmin;
  $: currentParticipant = $participants.find(p => p.user_id === userId);
  $: canJoin = $session && ['scheduled', 'live'].includes($session.status);
  $: isSessionLive = $session?.status === 'live';
  $: isWaitingForApproval = currentParticipant?.approval_status === 'pending';

  onMount(async () => {
    await initializeSession();
    startRefreshInterval();
  });

  onDestroy(() => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }
  });

  async function initializeSession() {
    try {
      loading = true;
      error = null;

      if (!sessionId) {
        throw new Error('Session ID is required');
      }

      // Generate device fingerprint
      const fingerprintGenerator = DeviceFingerprintGenerator.getInstance();
      const fingerprint = await fingerprintGenerator.generateFingerprint();
      deviceFingerprint = fingerprint.composite_hash;

      // Load session data
      const sessionData = await liveSessionService.getSession(sessionId);
      if (!sessionData) {
        throw new Error('Session not found');
      }
      session.set(sessionData);

      // Load participants
      const participantsData = await participantService.getSessionParticipants(sessionId);
      participants.set(participantsData);

      // Initialize BBB service if configured
      if (sessionData.meeting_config.provider === 'bigbluebutton') {
        // This would typically come from organization settings
        bbbService = new BigBlueButtonService({
          serverUrl: 'https://your-bbb-server.com/bigbluebutton',
          secret: 'your-bbb-secret'
        });

        // Get meeting info if session is live
        if (sessionData.status === 'live' && sessionData.meeting_config.meeting_id) {
          meetingInfo = await bbbService.getMeetingInfo(
            sessionData.meeting_config.meeting_id,
            sessionData.meeting_config.moderator_password || ''
          );
        }
      }

    } catch (err) {
      console.error('Error initializing session:', err);
      error = err.message || 'Failed to load session';
    } finally {
      loading = false;
    }
  }

  function startRefreshInterval() {
    refreshInterval = setInterval(async () => {
      if (sessionId) {
        try {
          // Refresh session data
          const sessionData = await liveSessionService.getSession(sessionId);
          if (sessionData) {
            session.set(sessionData);
          }

          // Refresh participants
          const participantsData = await participantService.getSessionParticipants(sessionId);
          participants.set(participantsData);

          // Refresh meeting info if live
          if (bbbService && sessionData?.status === 'live' && sessionData.meeting_config.meeting_id) {
            meetingInfo = await bbbService.getMeetingInfo(
              sessionData.meeting_config.meeting_id,
              sessionData.meeting_config.moderator_password || ''
            );
          }
        } catch (err) {
          console.error('Error refreshing session data:', err);
        }
      }
    }, 10000); // Refresh every 10 seconds
  }

  async function joinSession() {
    if (!$session || !userId || isJoining) return;

    try {
      isJoining = true;
      joinError = null;

      // Log security event
      await securityEventService.logEvent({
        event_type: 'live_session_join_attempt',
        severity: 'low',
        user_id: userId,
        device_fingerprint: deviceFingerprint,
        event_data: {
          session_id: sessionId,
          session_title: $session.title,
          user_role: userRole
        },
        location: {},
        is_blocked: false,
        metadata: {}
      });

      // Join session through service
      const joinResult = await liveSessionService.joinSession(sessionId!, userId, deviceFingerprint);
      
      if (!joinResult.success) {
        throw new Error(joinResult.error || 'Failed to join session');
      }

      // Create or get BBB meeting
      if ($session.meeting_config.provider === 'bigbluebutton' && bbbService) {
        let meetingConfig = $session.meeting_config;

        // Create meeting if instructor and not created yet
        if (isInstructor && !meetingConfig.meeting_id) {
          const configString = await bbbService.createMeeting($session, meetingConfig);
          meetingConfig = JSON.parse(configString);
          
          // Update session with meeting config
          await liveSessionService.updateSession(sessionId!, {
            meeting_config: meetingConfig,
            status: 'live'
          });
        }

        // Generate join URL
        if (meetingConfig.meeting_id) {
          const password = isInstructor 
            ? meetingConfig.moderator_password 
            : meetingConfig.attendee_password;
          
          const joinUrl = bbbService.generateJoinUrl(
            meetingConfig.meeting_id,
            $globalStore.user?.fullname || 'User',
            password || '',
            userId,
            isInstructor ? 'moderator' : 'attendee',
            $globalStore.user?.avatar_url
          );

          // Open meeting in new window/tab
          window.open(joinUrl, '_blank', 'width=1200,height=800');

          dispatch('sessionJoined', { 
            sessionId: sessionId!, 
            meetingUrl: joinUrl 
          });
        }
      }

      // Refresh data
      await initializeSession();

    } catch (err) {
      console.error('Error joining session:', err);
      joinError = err.message || 'Failed to join session';
    } finally {
      isJoining = false;
    }
  }

  async function endSession() {
    if (!$session || !isInstructor) return;

    try {
      // End BBB meeting if exists
      if (bbbService && $session.meeting_config.meeting_id) {
        await bbbService.endMeeting(
          $session.meeting_config.meeting_id,
          $session.meeting_config.moderator_password || ''
        );
      }

      // End session in database
      await liveSessionService.endSession(sessionId!);

      dispatch('sessionEnded', { sessionId: sessionId! });

      // Refresh data
      await initializeSession();

    } catch (err) {
      console.error('Error ending session:', err);
      error = err.message || 'Failed to end session';
    }
  }

  async function approveParticipant(participantUserId: string) {
    if (!sessionId || !isInstructor) return;

    try {
      await participantService.approveParticipant(sessionId, participantUserId);
      
      // Refresh participants
      const participantsData = await participantService.getSessionParticipants(sessionId);
      participants.set(participantsData);

    } catch (err) {
      console.error('Error approving participant:', err);
    }
  }

  function formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'live': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'scheduled': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'ended': return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
      case 'cancelled': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  }

  function formatTime(timestamp: string): string {
    return new Date(timestamp).toLocaleString();
  }
</script>

<div class="live-session-manager {className}">
  {#if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('live_session.loading', { default: 'Loading Live Session' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('live_session.error', { default: 'Error Loading Session' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={initializeSession}>
        {$t('live_session.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if $session}
    <!-- Session Header -->
    <Box className="mb-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-3 mb-2">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
              {$session.title}
            </h1>
            <span class="px-3 py-1 text-sm rounded-full {getStatusColor($session.status)}">
              {$session.status.toUpperCase()}
            </span>
          </div>
          
          {#if $session.description}
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              {$session.description}
            </p>
          {/if}

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div class="flex items-center text-gray-600 dark:text-gray-400">
              <Calendar size={16} class="mr-2" />
              <span>
                {$t('live_session.scheduled', { default: 'Scheduled' })}: 
                {formatTime($session.scheduled_start)}
              </span>
            </div>
            
            <div class="flex items-center text-gray-600 dark:text-gray-400">
              <Time size={16} class="mr-2" />
              <span>
                {$t('live_session.duration', { default: 'Duration' })}: 
                {formatDuration((new Date($session.scheduled_end).getTime() - new Date($session.scheduled_start).getTime()) / 1000)}
              </span>
            </div>
            
            <div class="flex items-center text-gray-600 dark:text-gray-400">
              <UserMultiple size={16} class="mr-2" />
              <span>
                {$t('live_session.participants', { default: 'Participants' })}: 
                {$participants.filter(p => p.joined_at).length} / {$session.max_participants}
              </span>
            </div>
          </div>
        </div>

        <!-- Join/Control Buttons -->
        <div class="flex items-center space-x-3">
          {#if isWaitingForApproval}
            <div class="flex items-center text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300 px-4 py-2 rounded-lg">
              <Time size={20} class="mr-2" />
              {$t('live_session.waiting_approval', { default: 'Waiting for approval' })}
            </div>
          {:else if canJoin && !currentParticipant?.joined_at}
            <PrimaryButton
              variant={VARIANTS.CONTAINED}
              onClick={joinSession}
              disabled={isJoining}
              size="lg"
            >
              {#if isJoining}
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              {:else}
                <Launch size={20} class="mr-2" />
              {/if}
              {$t('live_session.join', { default: 'Join Session' })}
            </PrimaryButton>
          {:else if currentParticipant?.joined_at}
            <div class="flex items-center text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300 px-4 py-2 rounded-lg">
              <CheckmarkFilled size={20} class="mr-2" />
              {$t('live_session.joined', { default: 'Joined' })}
            </div>
          {/if}

          {#if isInstructor && isSessionLive}
            <PrimaryButton
              variant={VARIANTS.OUTLINED}
              onClick={endSession}
            >
              {$t('live_session.end_session', { default: 'End Session' })}
            </PrimaryButton>
          {/if}
        </div>
      </div>

      {#if joinError}
        <div class="mt-4 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg">
          <div class="flex items-center">
            <Warning size={20} class="text-red-500 mr-2" />
            <span class="text-red-700 dark:text-red-300">{joinError}</span>
          </div>
        </div>
      {/if}
    </Box>

    <!-- Meeting Info -->
    {#if meetingInfo && isSessionLive}
      <Box className="mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {$t('live_session.meeting_info', { default: 'Meeting Information' })}
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600 mb-1">
              {meetingInfo.participantCount}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('live_session.total_participants', { default: 'Total Participants' })}
            </div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600 mb-1">
              {meetingInfo.videoCount}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('live_session.video_streams', { default: 'Video Streams' })}
            </div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600 mb-1">
              {meetingInfo.voiceParticipantCount}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('live_session.voice_participants', { default: 'Voice Participants' })}
            </div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600 mb-1">
              {formatDuration(meetingInfo.duration)}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('live_session.session_duration', { default: 'Session Duration' })}
            </div>
          </div>
        </div>
      </Box>
    {/if}

    <!-- Participants List -->
    <Box>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          {$t('live_session.participants_list', { default: 'Participants' })} ({$participants.length})
        </h3>
      </div>

      <div class="space-y-3">
        {#each $participants as participant (participant.id)}
          <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                <span class="text-primary-600 dark:text-primary-300 font-semibold">
                  {participant.profile?.fullname?.charAt(0) || 'U'}
                </span>
              </div>
              
              <div>
                <p class="font-medium text-gray-900 dark:text-white">
                  {participant.profile?.fullname || 'Unknown User'}
                </p>
                <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <span class="capitalize">{participant.role}</span>
                  {#if participant.joined_at}
                    <span>•</span>
                    <span>{$t('live_session.joined_at', { default: 'Joined' })}: {formatTime(participant.joined_at)}</span>
                  {/if}
                  {#if participant.total_duration > 0}
                    <span>•</span>
                    <span>{formatDuration(participant.total_duration)}</span>
                  {/if}
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <!-- Approval Status -->
              {#if participant.approval_status === 'pending' && isInstructor}
                <PrimaryButton
                  variant={VARIANTS.OUTLINED}
                  onClick={() => approveParticipant(participant.user_id)}
                  size="sm"
                >
                  {$t('live_session.approve', { default: 'Approve' })}
                </PrimaryButton>
              {:else if participant.approval_status === 'approved' || participant.approval_status === 'auto_approved'}
                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300">
                  {$t('live_session.approved', { default: 'Approved' })}
                </span>
              {:else if participant.approval_status === 'pending'}
                <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300">
                  {$t('live_session.pending', { default: 'Pending' })}
                </span>
              {/if}

              <!-- Online Status -->
              {#if participant.joined_at && !participant.left_at}
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              {:else}
                <div class="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
              {/if}
            </div>
          </div>
        {:else}
          <p class="text-gray-600 dark:text-gray-400 text-center py-8">
            {$t('live_session.no_participants', { default: 'No participants yet' })}
          </p>
        {/each}
      </div>
    </Box>
  {/if}
</div>

<style>
  .live-session-manager {
    @apply w-full;
  }
</style>
