
// this file is generated — do not edit it


/// <reference types="@sveltejs/kit" />

/**
 * Environment variables [loaded by Vite](https://vitejs.dev/guide/env-and-mode.html#env-files) from `.env` files and `process.env`. Like [`$env/dynamic/private`](https://kit.svelte.dev/docs/modules#$env-dynamic-private), this module cannot be imported into client-side code. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://kit.svelte.dev/docs/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://kit.svelte.dev/docs/configuration#env) (if configured).
 * 
 * _Unlike_ [`$env/dynamic/private`](https://kit.svelte.dev/docs/modules#$env-dynamic-private), the values exported from this module are statically injected into your bundle at build time, enabling optimisations like dead code elimination.
 * 
 * ```ts
 * import { API_KEY } from '$env/static/private';
 * ```
 * 
 * Note that all environment variables referenced in your code should be declared (for example in an `.env` file), even if they don't have a value until the app is deployed:
 * 
 * ```
 * MY_FEATURE_FLAG=""
 * ```
 * 
 * You can override `.env` values from the command line like so:
 * 
 * ```bash
 * MY_FEATURE_FLAG="enabled" npm run dev
 * ```
 */
declare module '$env/static/private' {
	export const PRIVATE_SUPABASE_SERVICE_ROLE: string;
	export const IS_SELFHOSTED: string;
	export const DEPLOYMENT_PROVIDER: string;
	export const UNSPLASH_API_KEY: string;
	export const OPENAI_API_KEY: string;
	export const PRIVATE_APP_HOST: string;
	export const PRIVATE_APP_SUBDOMAINS: string;
	export const LEMON_SQUEEZY_API_KEY: string;
	export const LEMON_SQUEEZY_STORE_ID: string;
	export const LEMON_SQUEEZY_WEBHOOK_SECRET: string;
	export const VITE_SENTRY_AUTH_TOKEN: string;
	export const VITE_SENTRY_ORG_NAME: string;
	export const VITE_SENTRY_PROJECT_NAME: string;
	export const USE_HTTPS_ON_LOCALHOST: string;
	export const TEAM_ID_VERCEL: string;
	export const PROJECT_ID_VERCEL: string;
	export const AUTH_BEARER_TOKEN: string;
	export const ALLUSERSPROFILE: string;
	export const APPDATA: string;
	export const ChocolateyInstall: string;
	export const ChocolateyLastPathUpdate: string;
	export const CHROME_CRASHPAD_PIPE_NAME: string;
	export const COLORTERM: string;
	export const CommonProgramFiles: string;
	export const CommonProgramW6432: string;
	export const COMPUTERNAME: string;
	export const ComSpec: string;
	export const DriverData: string;
	export const EFC_1072_1262719628: string;
	export const EFC_1072_1592913036: string;
	export const EFC_1072_2283032206: string;
	export const EFC_1072_2775293581: string;
	export const EFC_1072_3789132940: string;
	export const GIT_ASKPASS: string;
	export const GIT_PAGER: string;
	export const HOME: string;
	export const HOMEDRIVE: string;
	export const HOMEPATH: string;
	export const INIT_CWD: string;
	export const LANG: string;
	export const LOCALAPPDATA: string;
	export const LOGONSERVER: string;
	export const NODE: string;
	export const NODE_ENV: string;
	export const NODE_PATH: string;
	export const npm_command: string;
	export const npm_config_auto_install_peers: string;
	export const npm_config_engine_strict: string;
	export const npm_config_node_gyp: string;
	export const npm_config_npm_config_registry: string;
	export const npm_config_package_manager_strict: string;
	export const npm_config_registry: string;
	export const npm_config_user_agent: string;
	export const npm_execpath: string;
	export const npm_lifecycle_event: string;
	export const npm_lifecycle_script: string;
	export const npm_node_execpath: string;
	export const npm_package_browser_crypto: string;
	export const npm_package_dependencies_ai: string;
	export const npm_package_dependencies_axios: string;
	export const npm_package_dependencies_body_parser: string;
	export const npm_package_dependencies_bufferutil: string;
	export const npm_package_dependencies_canvas_confetti: string;
	export const npm_package_dependencies_clsx: string;
	export const npm_package_dependencies_color2k: string;
	export const npm_package_dependencies_cookie_parser: string;
	export const npm_package_dependencies_copy_to_clipboard: string;
	export const npm_package_dependencies_d3: string;
	export const npm_package_dependencies_d3_cloud: string;
	export const npm_package_dependencies_d3_sankey: string;
	export const npm_package_dependencies_dashjs: string;
	export const npm_package_dependencies_dayjs: string;
	export const npm_package_dependencies_encoding: string;
	export const npm_package_dependencies_hls_js: string;
	export const npm_package_dependencies_hotkeys_js: string;
	export const npm_package_dependencies_html_to_image: string;
	export const npm_package_dependencies_is_valid_domain: string;
	export const npm_package_dependencies_jspdf: string;
	export const npm_package_dependencies_jspdf_autotable: string;
	export const npm_package_dependencies_js_yaml: string;
	export const npm_package_dependencies_lodash: string;
	export const npm_package_dependencies_openai_edge: string;
	export const npm_package_dependencies_papaparse: string;
	export const npm_package_dependencies_pluralize: string;
	export const npm_package_dependencies_plyr: string;
	export const npm_package_dependencies_posthog_js: string;
	export const npm_package_dependencies_shared: string;
	export const npm_package_dependencies_sirv: string;
	export const npm_package_dependencies_stripe: string;
	export const npm_package_dependencies_sveltekit_i18n: string;
	export const npm_package_dependencies_svelte_awesome_color_picker: string;
	export const npm_package_dependencies_svelte_email: string;
	export const npm_package_dependencies_svelte_meta_tags: string;
	export const npm_package_dependencies_tailwind_merge: string;
	export const npm_package_dependencies_tldts: string;
	export const npm_package_dependencies_unsplash_js: string;
	export const npm_package_dependencies_utf_8_validate: string;
	export const npm_package_dependencies_vidstack: string;
	export const npm_package_dependencies_wait_on: string;
	export const npm_package_dependencies_zod: string;
	export const npm_package_dependencies__carbon_charts_svelte: string;
	export const npm_package_dependencies__lemonsqueezy_lemonsqueezy_js: string;
	export const npm_package_dependencies__polar_sh_sveltekit: string;
	export const npm_package_dependencies__supabase_supabase_js: string;
	export const npm_package_dependencies__sveltejs_adapter_auto: string;
	export const npm_package_dependencies__sveltejs_adapter_node: string;
	export const npm_package_dependencies__sveltejs_adapter_vercel: string;
	export const npm_package_dependencies__sveltekit_i18n_base: string;
	export const npm_package_dependencies__sveltekit_i18n_parser_icu: string;
	export const npm_package_dependencies__tailwindcss_forms: string;
	export const npm_package_dependencies__types_pluralize: string;
	export const npm_package_description: string;
	export const npm_package_devDependencies_all_object_keys: string;
	export const npm_package_devDependencies_autoprefixer: string;
	export const npm_package_devDependencies_babel_jest: string;
	export const npm_package_devDependencies_carbon_components_svelte: string;
	export const npm_package_devDependencies_carbon_icons_svelte: string;
	export const npm_package_devDependencies_diff: string;
	export const npm_package_devDependencies_dotenv: string;
	export const npm_package_devDependencies_eslint: string;
	export const npm_package_devDependencies_eslint_config_prettier: string;
	export const npm_package_devDependencies_eslint_plugin_svelte: string;
	export const npm_package_devDependencies_happy_dom: string;
	export const npm_package_devDependencies_jessy: string;
	export const npm_package_devDependencies_jest: string;
	export const npm_package_devDependencies_jsdom: string;
	export const npm_package_devDependencies_postcss: string;
	export const npm_package_devDependencies_postcss_load_config: string;
	export const npm_package_devDependencies_prettier: string;
	export const npm_package_devDependencies_prettier_plugin_svelte: string;
	export const npm_package_devDependencies_qrcode: string;
	export const npm_package_devDependencies_sass: string;
	export const npm_package_devDependencies_svelte: string;
	export const npm_package_devDependencies_svelte_calendar: string;
	export const npm_package_devDependencies_svelte_check: string;
	export const npm_package_devDependencies_svelte_dnd_action: string;
	export const npm_package_devDependencies_svelte_jester: string;
	export const npm_package_devDependencies_svelte_loading_spinners: string;
	export const npm_package_devDependencies_svelte_preprocess: string;
	export const npm_package_devDependencies_tailwindcss: string;
	export const npm_package_devDependencies_tsconfig: string;
	export const npm_package_devDependencies_tslib: string;
	export const npm_package_devDependencies_ts_jest: string;
	export const npm_package_devDependencies_ts_node: string;
	export const npm_package_devDependencies_typescript: string;
	export const npm_package_devDependencies_vite: string;
	export const npm_package_devDependencies_vitest: string;
	export const npm_package_devDependencies__babel_core: string;
	export const npm_package_devDependencies__babel_plugin_syntax_dynamic_import: string;
	export const npm_package_devDependencies__babel_plugin_transform_runtime: string;
	export const npm_package_devDependencies__babel_preset_env: string;
	export const npm_package_devDependencies__babel_preset_typescript: string;
	export const npm_package_devDependencies__babel_runtime: string;
	export const npm_package_devDependencies__playwright_test: string;
	export const npm_package_devDependencies__sveltejs_kit: string;
	export const npm_package_devDependencies__tailwindcss_typography: string;
	export const npm_package_devDependencies__testing_library_jest_dom: string;
	export const npm_package_devDependencies__testing_library_svelte: string;
	export const npm_package_devDependencies__testing_library_user_event: string;
	export const npm_package_devDependencies__typescript_eslint_eslint_plugin: string;
	export const npm_package_devDependencies__typescript_eslint_parser: string;
	export const npm_package_devDependencies__types_jest: string;
	export const npm_package_devDependencies__types_lodash: string;
	export const npm_package_devDependencies__vitest_coverage_v8: string;
	export const npm_package_devDependencies__vitest_ui: string;
	export const npm_package_engines_node: string;
	export const npm_package_main: string;
	export const npm_package_name: string;
	export const npm_package_scripts_build: string;
	export const npm_package_scripts_ci: string;
	export const npm_package_scripts_clean: string;
	export const npm_package_scripts_db_migrate: string;
	export const npm_package_scripts_db_migrate_test: string;
	export const npm_package_scripts_db_seed: string;
	export const npm_package_scripts_db_seed_test: string;
	export const npm_package_scripts_deploy_production: string;
	export const npm_package_scripts_deploy_staging: string;
	export const npm_package_scripts_dev: string;
	export const npm_package_scripts_docker_build: string;
	export const npm_package_scripts_docker_run: string;
	export const npm_package_scripts_export: string;
	export const npm_package_scripts_format: string;
	export const npm_package_scripts_format_check: string;
	export const npm_package_scripts_lint: string;
	export const npm_package_scripts_postexport: string;
	export const npm_package_scripts_prepare: string;
	export const npm_package_scripts_preview: string;
	export const npm_package_scripts_script_translate: string;
	export const npm_package_scripts_start: string;
	export const npm_package_scripts_test: string;
	export const npm_package_scripts_test_components: string;
	export const npm_package_scripts_test_coverage: string;
	export const npm_package_scripts_test_e2e: string;
	export const npm_package_scripts_test_e2e_headed: string;
	export const npm_package_scripts_test_health: string;
	export const npm_package_scripts_test_integration: string;
	export const npm_package_scripts_test_load: string;
	export const npm_package_scripts_test_smoke: string;
	export const npm_package_scripts_test_ui: string;
	export const npm_package_scripts_test_unit: string;
	export const npm_package_scripts_test_watch: string;
	export const npm_package_scripts_type_check: string;
	export const npm_package_type: string;
	export const npm_package_version: string;
	export const NUMBER_OF_PROCESSORS: string;
	export const NVM_HOME: string;
	export const NVM_SYMLINK: string;
	export const OneDrive: string;
	export const OneDriveConsumer: string;
	export const ORIGINAL_XDG_CURRENT_DESKTOP: string;
	export const OS: string;
	export const Path: string;
	export const PATHEXT: string;
	export const PNPM_HOME: string;
	export const PNPM_SCRIPT_SRC_DIR: string;
	export const PROCESSOR_ARCHITECTURE: string;
	export const PROCESSOR_IDENTIFIER: string;
	export const PROCESSOR_LEVEL: string;
	export const PROCESSOR_REVISION: string;
	export const ProgramData: string;
	export const ProgramFiles: string;
	export const ProgramW6432: string;
	export const PROMPT: string;
	export const PSModulePath: string;
	export const PUBLIC: string;
	export const SESSIONNAME: string;
	export const SystemDrive: string;
	export const SystemRoot: string;
	export const TEMP: string;
	export const TERM_PROGRAM: string;
	export const TERM_PROGRAM_VERSION: string;
	export const TMP: string;
	export const USERDOMAIN: string;
	export const USERDOMAIN_ROAMINGPROFILE: string;
	export const USERNAME: string;
	export const USERPROFILE: string;
	export const VSCODE_GIT_ASKPASS_EXTRA_ARGS: string;
	export const VSCODE_GIT_ASKPASS_MAIN: string;
	export const VSCODE_GIT_ASKPASS_NODE: string;
	export const VSCODE_GIT_IPC_HANDLE: string;
	export const VSCODE_INJECTION: string;
	export const windir: string;
	export const ZES_ENABLE_SYSMAN: string;
	export const __PSLockDownPolicy: string;
}

/**
 * Similar to [`$env/static/private`](https://kit.svelte.dev/docs/modules#$env-static-private), except that it only includes environment variables that begin with [`config.kit.env.publicPrefix`](https://kit.svelte.dev/docs/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Values are replaced statically at build time.
 * 
 * ```ts
 * import { PUBLIC_BASE_URL } from '$env/static/public';
 * ```
 */
declare module '$env/static/public' {
	export const PUBLIC_SUPABASE_URL: string;
	export const PUBLIC_SUPABASE_ANON_KEY: string;
	export const PUBLIC_SERVER_URL: string;
	export const PUBLIC_IP_REGISTRY_KEY: string;
}

/**
 * This module provides access to runtime environment variables, as defined by the platform you're running on. For example if you're using [`adapter-node`](https://github.com/sveltejs/kit/tree/master/packages/adapter-node) (or running [`vite preview`](https://kit.svelte.dev/docs/cli)), this is equivalent to `process.env`. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://kit.svelte.dev/docs/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://kit.svelte.dev/docs/configuration#env) (if configured).
 * 
 * This module cannot be imported into client-side code.
 * 
 * ```ts
 * import { env } from '$env/dynamic/private';
 * console.log(env.DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 * 
 * > In `dev`, `$env/dynamic` always includes environment variables from `.env`. In `prod`, this behavior will depend on your adapter.
 */
declare module '$env/dynamic/private' {
	export const env: {
		PRIVATE_SUPABASE_SERVICE_ROLE: string;
		IS_SELFHOSTED: string;
		DEPLOYMENT_PROVIDER: string;
		UNSPLASH_API_KEY: string;
		OPENAI_API_KEY: string;
		PRIVATE_APP_HOST: string;
		PRIVATE_APP_SUBDOMAINS: string;
		LEMON_SQUEEZY_API_KEY: string;
		LEMON_SQUEEZY_STORE_ID: string;
		LEMON_SQUEEZY_WEBHOOK_SECRET: string;
		VITE_SENTRY_AUTH_TOKEN: string;
		VITE_SENTRY_ORG_NAME: string;
		VITE_SENTRY_PROJECT_NAME: string;
		USE_HTTPS_ON_LOCALHOST: string;
		TEAM_ID_VERCEL: string;
		PROJECT_ID_VERCEL: string;
		AUTH_BEARER_TOKEN: string;
		ALLUSERSPROFILE: string;
		APPDATA: string;
		ChocolateyInstall: string;
		ChocolateyLastPathUpdate: string;
		CHROME_CRASHPAD_PIPE_NAME: string;
		COLORTERM: string;
		CommonProgramFiles: string;
		CommonProgramW6432: string;
		COMPUTERNAME: string;
		ComSpec: string;
		DriverData: string;
		EFC_1072_1262719628: string;
		EFC_1072_1592913036: string;
		EFC_1072_2283032206: string;
		EFC_1072_2775293581: string;
		EFC_1072_3789132940: string;
		GIT_ASKPASS: string;
		GIT_PAGER: string;
		HOME: string;
		HOMEDRIVE: string;
		HOMEPATH: string;
		INIT_CWD: string;
		LANG: string;
		LOCALAPPDATA: string;
		LOGONSERVER: string;
		NODE: string;
		NODE_ENV: string;
		NODE_PATH: string;
		npm_command: string;
		npm_config_auto_install_peers: string;
		npm_config_engine_strict: string;
		npm_config_node_gyp: string;
		npm_config_npm_config_registry: string;
		npm_config_package_manager_strict: string;
		npm_config_registry: string;
		npm_config_user_agent: string;
		npm_execpath: string;
		npm_lifecycle_event: string;
		npm_lifecycle_script: string;
		npm_node_execpath: string;
		npm_package_browser_crypto: string;
		npm_package_dependencies_ai: string;
		npm_package_dependencies_axios: string;
		npm_package_dependencies_body_parser: string;
		npm_package_dependencies_bufferutil: string;
		npm_package_dependencies_canvas_confetti: string;
		npm_package_dependencies_clsx: string;
		npm_package_dependencies_color2k: string;
		npm_package_dependencies_cookie_parser: string;
		npm_package_dependencies_copy_to_clipboard: string;
		npm_package_dependencies_d3: string;
		npm_package_dependencies_d3_cloud: string;
		npm_package_dependencies_d3_sankey: string;
		npm_package_dependencies_dashjs: string;
		npm_package_dependencies_dayjs: string;
		npm_package_dependencies_encoding: string;
		npm_package_dependencies_hls_js: string;
		npm_package_dependencies_hotkeys_js: string;
		npm_package_dependencies_html_to_image: string;
		npm_package_dependencies_is_valid_domain: string;
		npm_package_dependencies_jspdf: string;
		npm_package_dependencies_jspdf_autotable: string;
		npm_package_dependencies_js_yaml: string;
		npm_package_dependencies_lodash: string;
		npm_package_dependencies_openai_edge: string;
		npm_package_dependencies_papaparse: string;
		npm_package_dependencies_pluralize: string;
		npm_package_dependencies_plyr: string;
		npm_package_dependencies_posthog_js: string;
		npm_package_dependencies_shared: string;
		npm_package_dependencies_sirv: string;
		npm_package_dependencies_stripe: string;
		npm_package_dependencies_sveltekit_i18n: string;
		npm_package_dependencies_svelte_awesome_color_picker: string;
		npm_package_dependencies_svelte_email: string;
		npm_package_dependencies_svelte_meta_tags: string;
		npm_package_dependencies_tailwind_merge: string;
		npm_package_dependencies_tldts: string;
		npm_package_dependencies_unsplash_js: string;
		npm_package_dependencies_utf_8_validate: string;
		npm_package_dependencies_vidstack: string;
		npm_package_dependencies_wait_on: string;
		npm_package_dependencies_zod: string;
		npm_package_dependencies__carbon_charts_svelte: string;
		npm_package_dependencies__lemonsqueezy_lemonsqueezy_js: string;
		npm_package_dependencies__polar_sh_sveltekit: string;
		npm_package_dependencies__supabase_supabase_js: string;
		npm_package_dependencies__sveltejs_adapter_auto: string;
		npm_package_dependencies__sveltejs_adapter_node: string;
		npm_package_dependencies__sveltejs_adapter_vercel: string;
		npm_package_dependencies__sveltekit_i18n_base: string;
		npm_package_dependencies__sveltekit_i18n_parser_icu: string;
		npm_package_dependencies__tailwindcss_forms: string;
		npm_package_dependencies__types_pluralize: string;
		npm_package_description: string;
		npm_package_devDependencies_all_object_keys: string;
		npm_package_devDependencies_autoprefixer: string;
		npm_package_devDependencies_babel_jest: string;
		npm_package_devDependencies_carbon_components_svelte: string;
		npm_package_devDependencies_carbon_icons_svelte: string;
		npm_package_devDependencies_diff: string;
		npm_package_devDependencies_dotenv: string;
		npm_package_devDependencies_eslint: string;
		npm_package_devDependencies_eslint_config_prettier: string;
		npm_package_devDependencies_eslint_plugin_svelte: string;
		npm_package_devDependencies_happy_dom: string;
		npm_package_devDependencies_jessy: string;
		npm_package_devDependencies_jest: string;
		npm_package_devDependencies_jsdom: string;
		npm_package_devDependencies_postcss: string;
		npm_package_devDependencies_postcss_load_config: string;
		npm_package_devDependencies_prettier: string;
		npm_package_devDependencies_prettier_plugin_svelte: string;
		npm_package_devDependencies_qrcode: string;
		npm_package_devDependencies_sass: string;
		npm_package_devDependencies_svelte: string;
		npm_package_devDependencies_svelte_calendar: string;
		npm_package_devDependencies_svelte_check: string;
		npm_package_devDependencies_svelte_dnd_action: string;
		npm_package_devDependencies_svelte_jester: string;
		npm_package_devDependencies_svelte_loading_spinners: string;
		npm_package_devDependencies_svelte_preprocess: string;
		npm_package_devDependencies_tailwindcss: string;
		npm_package_devDependencies_tsconfig: string;
		npm_package_devDependencies_tslib: string;
		npm_package_devDependencies_ts_jest: string;
		npm_package_devDependencies_ts_node: string;
		npm_package_devDependencies_typescript: string;
		npm_package_devDependencies_vite: string;
		npm_package_devDependencies_vitest: string;
		npm_package_devDependencies__babel_core: string;
		npm_package_devDependencies__babel_plugin_syntax_dynamic_import: string;
		npm_package_devDependencies__babel_plugin_transform_runtime: string;
		npm_package_devDependencies__babel_preset_env: string;
		npm_package_devDependencies__babel_preset_typescript: string;
		npm_package_devDependencies__babel_runtime: string;
		npm_package_devDependencies__playwright_test: string;
		npm_package_devDependencies__sveltejs_kit: string;
		npm_package_devDependencies__tailwindcss_typography: string;
		npm_package_devDependencies__testing_library_jest_dom: string;
		npm_package_devDependencies__testing_library_svelte: string;
		npm_package_devDependencies__testing_library_user_event: string;
		npm_package_devDependencies__typescript_eslint_eslint_plugin: string;
		npm_package_devDependencies__typescript_eslint_parser: string;
		npm_package_devDependencies__types_jest: string;
		npm_package_devDependencies__types_lodash: string;
		npm_package_devDependencies__vitest_coverage_v8: string;
		npm_package_devDependencies__vitest_ui: string;
		npm_package_engines_node: string;
		npm_package_main: string;
		npm_package_name: string;
		npm_package_scripts_build: string;
		npm_package_scripts_ci: string;
		npm_package_scripts_clean: string;
		npm_package_scripts_db_migrate: string;
		npm_package_scripts_db_migrate_test: string;
		npm_package_scripts_db_seed: string;
		npm_package_scripts_db_seed_test: string;
		npm_package_scripts_deploy_production: string;
		npm_package_scripts_deploy_staging: string;
		npm_package_scripts_dev: string;
		npm_package_scripts_docker_build: string;
		npm_package_scripts_docker_run: string;
		npm_package_scripts_export: string;
		npm_package_scripts_format: string;
		npm_package_scripts_format_check: string;
		npm_package_scripts_lint: string;
		npm_package_scripts_postexport: string;
		npm_package_scripts_prepare: string;
		npm_package_scripts_preview: string;
		npm_package_scripts_script_translate: string;
		npm_package_scripts_start: string;
		npm_package_scripts_test: string;
		npm_package_scripts_test_components: string;
		npm_package_scripts_test_coverage: string;
		npm_package_scripts_test_e2e: string;
		npm_package_scripts_test_e2e_headed: string;
		npm_package_scripts_test_health: string;
		npm_package_scripts_test_integration: string;
		npm_package_scripts_test_load: string;
		npm_package_scripts_test_smoke: string;
		npm_package_scripts_test_ui: string;
		npm_package_scripts_test_unit: string;
		npm_package_scripts_test_watch: string;
		npm_package_scripts_type_check: string;
		npm_package_type: string;
		npm_package_version: string;
		NUMBER_OF_PROCESSORS: string;
		NVM_HOME: string;
		NVM_SYMLINK: string;
		OneDrive: string;
		OneDriveConsumer: string;
		ORIGINAL_XDG_CURRENT_DESKTOP: string;
		OS: string;
		Path: string;
		PATHEXT: string;
		PNPM_HOME: string;
		PNPM_SCRIPT_SRC_DIR: string;
		PROCESSOR_ARCHITECTURE: string;
		PROCESSOR_IDENTIFIER: string;
		PROCESSOR_LEVEL: string;
		PROCESSOR_REVISION: string;
		ProgramData: string;
		ProgramFiles: string;
		ProgramW6432: string;
		PROMPT: string;
		PSModulePath: string;
		PUBLIC: string;
		SESSIONNAME: string;
		SystemDrive: string;
		SystemRoot: string;
		TEMP: string;
		TERM_PROGRAM: string;
		TERM_PROGRAM_VERSION: string;
		TMP: string;
		USERDOMAIN: string;
		USERDOMAIN_ROAMINGPROFILE: string;
		USERNAME: string;
		USERPROFILE: string;
		VSCODE_GIT_ASKPASS_EXTRA_ARGS: string;
		VSCODE_GIT_ASKPASS_MAIN: string;
		VSCODE_GIT_ASKPASS_NODE: string;
		VSCODE_GIT_IPC_HANDLE: string;
		VSCODE_INJECTION: string;
		windir: string;
		ZES_ENABLE_SYSMAN: string;
		__PSLockDownPolicy: string;
		[key: `PUBLIC_${string}`]: undefined;
		[key: `${string}`]: string | undefined;
	}
}

/**
 * Similar to [`$env/dynamic/private`](https://kit.svelte.dev/docs/modules#$env-dynamic-private), but only includes variables that begin with [`config.kit.env.publicPrefix`](https://kit.svelte.dev/docs/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Note that public dynamic environment variables must all be sent from the server to the client, causing larger network requests — when possible, use `$env/static/public` instead.
 * 
 * ```ts
 * import { env } from '$env/dynamic/public';
 * console.log(env.PUBLIC_DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 */
declare module '$env/dynamic/public' {
	export const env: {
		PUBLIC_SUPABASE_URL: string;
		PUBLIC_SUPABASE_ANON_KEY: string;
		PUBLIC_SERVER_URL: string;
		PUBLIC_IP_REGISTRY_KEY: string;
		[key: `PUBLIC_${string}`]: string | undefined;
	}
}
