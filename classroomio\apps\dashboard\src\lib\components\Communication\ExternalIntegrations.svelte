<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { ExternalIntegration } from '$lib/utils/types/communication';
  import { externalIntegrationService } from '$lib/utils/services/notifications';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Add,
    Settings,
    CheckmarkFilled,
    Warning,
    Close,
    Edit,
    TrashCan,
    Connect,
    Disconnect,
    Test,
    Save
  } from 'carbon-icons-svelte';

  export let organizationId: string;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    integrationCreated: { integration: ExternalIntegration };
    integrationUpdated: { integration: ExternalIntegration };
    integrationDeleted: { integrationId: string };
  }>();

  let integrations = writable<ExternalIntegration[]>([]);
  let loading = true;
  let error: string | null = null;
  let showCreateModal = false;
  let showEditModal = false;
  let editingIntegration: ExternalIntegration | null = null;
  let testingIntegration: string | null = null;

  // Form data
  let integrationType: 'whatsapp' | 'telegram' | 'slack' | 'discord' = 'whatsapp';
  let integrationName = '';
  let config = {
    api_key: '',
    webhook_url: '',
    bot_token: '',
    phone_number: '',
    business_account_id: ''
  };

  $: isOrgAdmin = $globalStore.isOrgAdmin;

  onMount(async () => {
    await loadIntegrations();
  });

  async function loadIntegrations() {
    try {
      loading = true;
      error = null;

      const integrationsData = await externalIntegrationService.getOrganizationIntegrations(organizationId);
      integrations.set(integrationsData);

    } catch (err) {
      console.error('Error loading integrations:', err);
      error = err.message || 'Failed to load integrations';
    } finally {
      loading = false;
    }
  }

  async function createIntegration() {
    if (!integrationName.trim()) return;

    try {
      const integrationData = {
        organization_id: organizationId,
        integration_type: integrationType,
        integration_name: integrationName.trim(),
        config: config,
        is_active: true,
        rate_limits: {
          messages_per_minute: getDefaultRateLimit(integrationType, 'minute'),
          messages_per_hour: getDefaultRateLimit(integrationType, 'hour'),
          messages_per_day: getDefaultRateLimit(integrationType, 'day')
        }
      };

      const integration = await externalIntegrationService.createIntegration(integrationData);
      
      // Reload integrations
      await loadIntegrations();
      
      // Reset form
      resetForm();
      showCreateModal = false;

      dispatch('integrationCreated', { integration });

    } catch (err) {
      console.error('Error creating integration:', err);
      error = err.message || 'Failed to create integration';
    }
  }

  async function updateIntegration() {
    if (!editingIntegration) return;

    try {
      const updates = {
        integration_name: integrationName.trim(),
        config: config
      };

      const integration = await externalIntegrationService.updateIntegration(editingIntegration.id, updates);
      
      // Reload integrations
      await loadIntegrations();
      
      // Reset form
      resetForm();
      showEditModal = false;
      editingIntegration = null;

      dispatch('integrationUpdated', { integration });

    } catch (err) {
      console.error('Error updating integration:', err);
      error = err.message || 'Failed to update integration';
    }
  }

  async function deleteIntegration(integrationId: string) {
    if (!confirm($t('integrations.confirm_delete', { default: 'Are you sure you want to delete this integration?' }))) {
      return;
    }

    try {
      // In a real implementation, this would call the delete API
      // For now, we'll just remove from local state
      integrations.update(current => current.filter(i => i.id !== integrationId));
      
      dispatch('integrationDeleted', { integrationId });

    } catch (err) {
      console.error('Error deleting integration:', err);
      error = err.message || 'Failed to delete integration';
    }
  }

  async function testIntegration(integrationId: string) {
    try {
      testingIntegration = integrationId;
      
      const result = await externalIntegrationService.testConnection(integrationId);
      
      if (result.success) {
        // Show success message
        alert($t('integrations.test_success', { default: 'Connection test successful!' }));
      } else {
        // Show error message
        alert($t('integrations.test_failed', { default: 'Connection test failed: ' }) + result.message);
      }

    } catch (err) {
      console.error('Error testing integration:', err);
      alert($t('integrations.test_error', { default: 'Error testing connection: ' }) + err.message);
    } finally {
      testingIntegration = null;
    }
  }

  async function toggleIntegration(integrationId: string, isActive: boolean) {
    try {
      await externalIntegrationService.updateIntegration(integrationId, { is_active: isActive });
      await loadIntegrations();
    } catch (err) {
      console.error('Error toggling integration:', err);
      error = err.message || 'Failed to toggle integration';
    }
  }

  function editIntegration(integration: ExternalIntegration) {
    editingIntegration = integration;
    integrationType = integration.integration_type;
    integrationName = integration.integration_name;
    config = { ...integration.config };
    showEditModal = true;
  }

  function resetForm() {
    integrationType = 'whatsapp';
    integrationName = '';
    config = {
      api_key: '',
      webhook_url: '',
      bot_token: '',
      phone_number: '',
      business_account_id: ''
    };
  }

  function getDefaultRateLimit(type: string, period: 'minute' | 'hour' | 'day'): number {
    const limits = {
      whatsapp: { minute: 10, hour: 100, day: 1000 },
      telegram: { minute: 30, hour: 300, day: 3000 },
      slack: { minute: 50, hour: 500, day: 5000 },
      discord: { minute: 50, hour: 500, day: 5000 }
    };
    
    return limits[type]?.[period] || 10;
  }

  function getIntegrationIcon(type: string) {
    switch (type) {
      case 'whatsapp': return '💬';
      case 'telegram': return '✈️';
      case 'slack': return '💼';
      case 'discord': return '🎮';
      default: return '🔗';
    }
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'connected': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'disconnected': return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
      case 'error': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  }

  function getConfigFields(type: string): Array<{ key: string; label: string; type: 'text' | 'password'; required: boolean }> {
    switch (type) {
      case 'whatsapp':
        return [
          { key: 'api_key', label: 'API Key', type: 'password', required: true },
          { key: 'phone_number', label: 'Phone Number', type: 'text', required: true },
          { key: 'business_account_id', label: 'Business Account ID', type: 'text', required: true },
          { key: 'webhook_url', label: 'Webhook URL', type: 'text', required: false }
        ];
      case 'telegram':
        return [
          { key: 'bot_token', label: 'Bot Token', type: 'password', required: true },
          { key: 'webhook_url', label: 'Webhook URL', type: 'text', required: false }
        ];
      case 'slack':
        return [
          { key: 'api_key', label: 'Bot Token', type: 'password', required: true },
          { key: 'webhook_url', label: 'Webhook URL', type: 'text', required: false }
        ];
      case 'discord':
        return [
          { key: 'bot_token', label: 'Bot Token', type: 'password', required: true },
          { key: 'webhook_url', label: 'Webhook URL', type: 'text', required: false }
        ];
      default:
        return [];
    }
  }
</script>

<div class="external-integrations {className}">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center">
      <Connect size={24} class="text-primary-600 mr-3" />
      <div>
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('integrations.title', { default: 'External Integrations' })}
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          {$t('integrations.description', { default: 'Connect with external messaging platforms' })}
        </p>
      </div>
    </div>

    {#if isOrgAdmin}
      <PrimaryButton
        variant={VARIANTS.CONTAINED}
        onClick={() => showCreateModal = true}
      >
        <Add size={20} class="mr-2" />
        {$t('integrations.add_integration', { default: 'Add Integration' })}
      </PrimaryButton>
    {/if}
  </div>

  {#if loading}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each Array(3) as _}
        <Box className="animate-pulse">
          <div class="p-6">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div class="flex-1">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
            <div class="space-y-2">
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
            </div>
          </div>
        </Box>
      {/each}
    </div>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('integrations.error', { default: 'Error Loading Integrations' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadIntegrations}>
        {$t('integrations.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if $integrations.length === 0}
    <Box className="text-center py-12">
      <Connect size={48} class="text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {$t('integrations.no_integrations', { default: 'No Integrations' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        {$t('integrations.no_integrations_desc', { default: 'Connect with external platforms to enhance communication' })}
      </p>
      {#if isOrgAdmin}
        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={() => showCreateModal = true}
        >
          <Add size={20} class="mr-2" />
          {$t('integrations.add_first', { default: 'Add First Integration' })}
        </PrimaryButton>
      {/if}
    </Box>

  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each $integrations as integration (integration.id)}
        <Box>
          <div class="p-6">
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <span class="text-2xl">{getIntegrationIcon(integration.integration_type)}</span>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {integration.integration_name}
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400 capitalize">
                    {integration.integration_type}
                  </p>
                </div>
              </div>
              
              <span class="px-2 py-1 text-xs rounded-full {getStatusColor(integration.sync_status)}">
                {integration.sync_status.toUpperCase()}
              </span>
            </div>

            <div class="space-y-3 mb-4">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">
                  {$t('integrations.messages_sent', { default: 'Messages Sent' })}
                </span>
                <span class="font-medium text-gray-900 dark:text-white">
                  {integration.usage_stats.messages_sent || 0}
                </span>
              </div>
              
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400">
                  {$t('integrations.last_sync', { default: 'Last Sync' })}
                </span>
                <span class="font-medium text-gray-900 dark:text-white">
                  {integration.last_sync ? new Date(integration.last_sync).toLocaleDateString() : 'Never'}
                </span>
              </div>

              {#if integration.error_message}
                <div class="text-sm text-red-600 dark:text-red-400">
                  {integration.error_message}
                </div>
              {/if}
            </div>

            <div class="flex items-center space-x-2">
              <button
                on:click={() => toggleIntegration(integration.id, !integration.is_active)}
                class="flex items-center text-sm {integration.is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}"
              >
                {#if integration.is_active}
                  <Disconnect size={16} class="mr-1" />
                  {$t('integrations.disable', { default: 'Disable' })}
                {:else}
                  <Connect size={16} class="mr-1" />
                  {$t('integrations.enable', { default: 'Enable' })}
                {/if}
              </button>

              <button
                on:click={() => testIntegration(integration.id)}
                disabled={testingIntegration === integration.id}
                class="flex items-center text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50"
              >
                {#if testingIntegration === integration.id}
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-1"></div>
                {:else}
                  <Test size={16} class="mr-1" />
                {/if}
                {$t('integrations.test', { default: 'Test' })}
              </button>

              {#if isOrgAdmin}
                <button
                  on:click={() => editIntegration(integration)}
                  class="flex items-center text-sm text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  <Edit size={16} class="mr-1" />
                  {$t('integrations.edit', { default: 'Edit' })}
                </button>

                <button
                  on:click={() => deleteIntegration(integration.id)}
                  class="flex items-center text-sm text-red-600 hover:text-red-700"
                >
                  <TrashCan size={16} class="mr-1" />
                  {$t('integrations.delete', { default: 'Delete' })}
                </button>
              {/if}
            </div>
          </div>
        </Box>
      {/each}
    </div>
  {/if}
</div>

<!-- Create Integration Modal -->
{#if showCreateModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('integrations.create_integration', { default: 'Create Integration' })}
        </h2>
        <button
          on:click={() => showCreateModal = false}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('integrations.integration_type', { default: 'Integration Type' })} *
          </label>
          <select
            bind:value={integrationType}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="whatsapp">{$t('integrations.whatsapp', { default: 'WhatsApp Business' })}</option>
            <option value="telegram">{$t('integrations.telegram', { default: 'Telegram Bot' })}</option>
            <option value="slack">{$t('integrations.slack', { default: 'Slack' })}</option>
            <option value="discord">{$t('integrations.discord', { default: 'Discord' })}</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('integrations.integration_name', { default: 'Integration Name' })} *
          </label>
          <input
            type="text"
            bind:value={integrationName}
            placeholder={$t('integrations.name_placeholder', { default: 'Enter integration name' })}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          />
        </div>

        <!-- Dynamic Config Fields -->
        {#each getConfigFields(integrationType) as field}
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {field.label} {field.required ? '*' : ''}
            </label>
            <input
              type={field.type}
              bind:value={config[field.key]}
              placeholder={$t(`integrations.${field.key}_placeholder`, { default: `Enter ${field.label.toLowerCase()}` })}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              required={field.required}
            />
          </div>
        {/each}
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => showCreateModal = false}
        >
          {$t('integrations.cancel', { default: 'Cancel' })}
        </PrimaryButton>

        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={createIntegration}
          disabled={!integrationName.trim()}
        >
          <Add size={20} class="mr-2" />
          {$t('integrations.create', { default: 'Create Integration' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}

<!-- Edit Integration Modal -->
{#if showEditModal && editingIntegration}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('integrations.edit_integration', { default: 'Edit Integration' })}
        </h2>
        <button
          on:click={() => showEditModal = false}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('integrations.integration_name', { default: 'Integration Name' })} *
          </label>
          <input
            type="text"
            bind:value={integrationName}
            placeholder={$t('integrations.name_placeholder', { default: 'Enter integration name' })}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          />
        </div>

        <!-- Dynamic Config Fields -->
        {#each getConfigFields(integrationType) as field}
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {field.label} {field.required ? '*' : ''}
            </label>
            <input
              type={field.type}
              bind:value={config[field.key]}
              placeholder={$t(`integrations.${field.key}_placeholder`, { default: `Enter ${field.label.toLowerCase()}` })}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              required={field.required}
            />
          </div>
        {/each}
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => showEditModal = false}
        >
          {$t('integrations.cancel', { default: 'Cancel' })}
        </PrimaryButton>

        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={updateIntegration}
          disabled={!integrationName.trim()}
        >
          <Save size={20} class="mr-2" />
          {$t('integrations.save_changes', { default: 'Save Changes' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}
