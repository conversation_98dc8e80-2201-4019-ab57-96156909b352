<script>
  import { onMount } from 'svelte';
  
  let message = 'Loading...';
  
  onMount(() => {
    message = 'ClassroomIO Test Page - Application is working!';
    console.log('Test page loaded successfully');
  });
</script>

<svelte:head>
  <title>ClassroomIO Test</title>
</svelte:head>

<div class="p-8">
  <h1 class="text-3xl font-bold text-green-600 mb-4">✅ {message}</h1>
  <p class="text-lg mb-4">If you can see this page, the basic application is working correctly.</p>
  
  <div class="bg-gray-100 p-4 rounded">
    <h2 class="text-xl font-semibold mb-2">System Status:</h2>
    <ul class="list-disc list-inside">
      <li>✅ Svelte components loading</li>
      <li>✅ Routing working</li>
      <li>✅ JavaScript execution</li>
      <li>✅ CSS styling</li>
    </ul>
  </div>
  
  <div class="mt-4">
    <a href="/" class="text-blue-600 hover:underline">← Back to Home</a>
  </div>
</div>
