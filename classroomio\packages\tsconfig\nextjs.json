{"$schema": "https://json.schemastore.org/tsconfig", "display": "Next.js", "extends": "./base.json", "compilerOptions": {"plugins": [{"name": "next"}], "allowJs": true, "declaration": false, "declarationMap": false, "incremental": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "noEmit": true, "resolveJsonModule": true, "strict": false, "target": "es5", "paths": {"@/*": ["./*"]}}, "include": ["src", "next-env.d.ts"], "exclude": ["node_modules"]}