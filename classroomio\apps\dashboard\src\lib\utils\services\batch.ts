// Batch Management API Services
// Date: 2025-06-30

import { supabase } from '$lib/utils/functions/supabase';
import type { 
  Batch, 
  Subject, 
  Chapter, 
  BatchMember, 
  StudyMaterial, 
  DoubtSystem,
  LiveSession,
  ProgressTracking,
  BatchHierarchy
} from '$lib/utils/types/batch';

// Batch CRUD Operations
export const batchService = {
  // Get all batches for an organization
  async getBatches(organizationId: string): Promise<Batch[]> {
    const { data, error } = await supabase
      .from('batch')
      .select(`
        *,
        subjects:subject(count),
        members:batch_member(count)
      `)
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Get single batch with full details
  async getBatch(batchId: string): Promise<Batch | null> {
    const { data, error } = await supabase
      .from('batch')
      .select(`
        *,
        subjects:subject(*),
        members:batch_member(*, profile(*))
      `)
      .eq('id', batchId)
      .single();

    if (error) throw error;
    return data;
  },

  // Create new batch
  async createBatch(batch: Omit<Batch, 'id' | 'created_at' | 'updated_at'>): Promise<Batch> {
    const { data, error } = await supabase
      .from('batch')
      .insert(batch)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update batch
  async updateBatch(batchId: string, updates: Partial<Batch>): Promise<Batch> {
    const { data, error } = await supabase
      .from('batch')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', batchId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete batch (soft delete)
  async deleteBatch(batchId: string): Promise<void> {
    const { error } = await supabase
      .from('batch')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', batchId);

    if (error) throw error;
  },

  // Get batch hierarchy
  async getBatchHierarchy(batchId: string): Promise<BatchHierarchy[]> {
    const { data, error } = await supabase
      .rpc('get_batch_hierarchy', { batch_id_arg: batchId });

    if (error) throw error;
    return data || [];
  }
};

// Subject CRUD Operations
export const subjectService = {
  // Get subjects for a batch
  async getSubjects(batchId: string): Promise<Subject[]> {
    const { data, error } = await supabase
      .from('subject')
      .select(`
        *,
        instructor:profile(*),
        chapters:chapter(count),
        batch(*)
      `)
      .eq('batch_id', batchId)
      .eq('is_active', true)
      .order('order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Create new subject
  async createSubject(subject: Omit<Subject, 'id' | 'created_at' | 'updated_at'>): Promise<Subject> {
    const { data, error } = await supabase
      .from('subject')
      .insert(subject)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update subject
  async updateSubject(subjectId: string, updates: Partial<Subject>): Promise<Subject> {
    const { data, error } = await supabase
      .from('subject')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', subjectId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete subject
  async deleteSubject(subjectId: string): Promise<void> {
    const { error } = await supabase
      .from('subject')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', subjectId);

    if (error) throw error;
  }
};

// Chapter CRUD Operations
export const chapterService = {
  // Get chapters for a subject
  async getChapters(subjectId: string): Promise<Chapter[]> {
    const { data, error } = await supabase
      .from('chapter')
      .select(`
        *,
        subject(*),
        sections:lesson_section(*, lessons:lesson(count))
      `)
      .eq('subject_id', subjectId)
      .eq('is_active', true)
      .order('order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Create new chapter
  async createChapter(chapter: Omit<Chapter, 'id' | 'created_at' | 'updated_at'>): Promise<Chapter> {
    const { data, error } = await supabase
      .from('chapter')
      .insert(chapter)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update chapter
  async updateChapter(chapterId: string, updates: Partial<Chapter>): Promise<Chapter> {
    const { data, error } = await supabase
      .from('chapter')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', chapterId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete chapter
  async deleteChapter(chapterId: string): Promise<void> {
    const { error } = await supabase
      .from('chapter')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', chapterId);

    if (error) throw error;
  }
};

// Batch Member Operations
export const memberService = {
  // Get batch members
  async getBatchMembers(batchId: string): Promise<BatchMember[]> {
    const { data, error } = await supabase
      .from('batch_member')
      .select(`
        *,
        profile(*),
        batch(*)
      `)
      .eq('batch_id', batchId)
      .eq('is_active', true)
      .order('joined_at', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Add member to batch
  async addBatchMember(member: Omit<BatchMember, 'id' | 'created_at' | 'updated_at'>): Promise<BatchMember> {
    const { data, error } = await supabase
      .from('batch_member')
      .insert(member)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update batch member
  async updateBatchMember(memberId: string, updates: Partial<BatchMember>): Promise<BatchMember> {
    const { data, error } = await supabase
      .from('batch_member')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', memberId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Remove member from batch
  async removeBatchMember(memberId: string): Promise<void> {
    const { error } = await supabase
      .from('batch_member')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', memberId);

    if (error) throw error;
  }
};

// Study Material Operations
export const materialService = {
  // Get study materials
  async getStudyMaterials(filters: {
    batchId?: string;
    subjectId?: string;
    chapterId?: string;
    lessonId?: string;
    type?: string;
  }): Promise<StudyMaterial[]> {
    let query = supabase
      .from('study_material')
      .select(`
        *,
        creator:profile(*),
        lesson(*),
        chapter(*),
        subject(*),
        batch(*)
      `);

    if (filters.batchId) query = query.eq('batch_id', filters.batchId);
    if (filters.subjectId) query = query.eq('subject_id', filters.subjectId);
    if (filters.chapterId) query = query.eq('chapter_id', filters.chapterId);
    if (filters.lessonId) query = query.eq('lesson_id', filters.lessonId);
    if (filters.type) query = query.eq('type', filters.type);

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Create study material
  async createStudyMaterial(material: Omit<StudyMaterial, 'id' | 'created_at' | 'updated_at'>): Promise<StudyMaterial> {
    const { data, error } = await supabase
      .from('study_material')
      .insert(material)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update study material
  async updateStudyMaterial(materialId: string, updates: Partial<StudyMaterial>): Promise<StudyMaterial> {
    const { data, error } = await supabase
      .from('study_material')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', materialId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete study material
  async deleteStudyMaterial(materialId: string): Promise<void> {
    const { error } = await supabase
      .from('study_material')
      .delete()
      .eq('id', materialId);

    if (error) throw error;
  }
};
