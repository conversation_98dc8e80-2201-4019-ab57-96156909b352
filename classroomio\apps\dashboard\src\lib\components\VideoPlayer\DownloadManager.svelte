<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { DownloadItem, VideoContent } from '$lib/utils/types/batch';
  import { downloadService } from '$lib/utils/services/video';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { Download, Trash, Pause, Play, CheckmarkFilled } from 'carbon-icons-svelte';

  export let videoContent: VideoContent | null = null;
  export let showAddButton: boolean = true;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    downloadStarted: { downloadId: string };
    downloadCompleted: { downloadId: string; localPath: string };
    downloadFailed: { downloadId: string; error: string };
  }>();

  let downloadQueue = writable<DownloadItem[]>([]);
  let loading = false;
  let error: string | null = null;
  let selectedQuality = 'auto';

  $: studentId = $globalStore.user?.id;
  $: availableQualities = videoContent?.quality_options || [];

  onMount(async () => {
    if (studentId) {
      await loadDownloadQueue();
      // Clean expired downloads on load
      await downloadService.cleanExpiredDownloads();
    }
  });

  async function loadDownloadQueue() {
    if (!studentId) return;

    try {
      loading = true;
      error = null;
      const queue = await downloadService.getDownloadQueue(studentId);
      downloadQueue.set(queue);
    } catch (err) {
      console.error('Error loading download queue:', err);
      error = err.message || 'Failed to load download queue';
    } finally {
      loading = false;
    }
  }

  async function addToDownloadQueue() {
    if (!videoContent || !studentId) return;

    try {
      loading = true;
      error = null;

      const downloadItem = await downloadService.addToDownloadQueue(
        videoContent.id,
        studentId,
        selectedQuality
      );

      // Add to local queue
      downloadQueue.update(queue => [downloadItem, ...queue]);

      // Start download process
      await startDownload(downloadItem.id);

      dispatch('downloadStarted', { downloadId: downloadItem.id });
    } catch (err) {
      console.error('Error adding to download queue:', err);
      error = err.message || 'Failed to add to download queue';
    } finally {
      loading = false;
    }
  }

  async function startDownload(downloadId: string) {
    try {
      // Update status to downloading
      await downloadService.updateDownloadProgress(downloadId, 0, 'downloading');
      
      // Update local state
      downloadQueue.update(queue => 
        queue.map(item => 
          item.id === downloadId 
            ? { ...item, status: 'downloading', progress: 0 }
            : item
        )
      );

      // Simulate download progress (in real implementation, this would be handled by a service worker or native app)
      simulateDownloadProgress(downloadId);

    } catch (err) {
      console.error('Error starting download:', err);
      await downloadService.updateDownloadProgress(downloadId, 0, 'failed');
      
      downloadQueue.update(queue => 
        queue.map(item => 
          item.id === downloadId 
            ? { ...item, status: 'failed' }
            : item
        )
      );

      dispatch('downloadFailed', { downloadId, error: err.message });
    }
  }

  function simulateDownloadProgress(downloadId: string) {
    let progress = 0;
    const interval = setInterval(async () => {
      progress += Math.random() * 10;
      
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        
        try {
          // Mark as completed
          const localPath = `/downloads/video_${downloadId}.mp4`;
          await downloadService.completeDownload(downloadId, localPath);
          
          downloadQueue.update(queue => 
            queue.map(item => 
              item.id === downloadId 
                ? { ...item, status: 'completed', progress: 100, local_path: localPath }
                : item
            )
          );

          dispatch('downloadCompleted', { downloadId, localPath });
        } catch (err) {
          console.error('Error completing download:', err);
          dispatch('downloadFailed', { downloadId, error: err.message });
        }
      } else {
        try {
          await downloadService.updateDownloadProgress(downloadId, progress);
          
          downloadQueue.update(queue => 
            queue.map(item => 
              item.id === downloadId 
                ? { ...item, progress }
                : item
            )
          );
        } catch (err) {
          console.error('Error updating download progress:', err);
          clearInterval(interval);
        }
      }
    }, 1000);
  }

  async function pauseDownload(downloadId: string) {
    try {
      await downloadService.updateDownloadProgress(downloadId, 0, 'pending');
      
      downloadQueue.update(queue => 
        queue.map(item => 
          item.id === downloadId 
            ? { ...item, status: 'pending' }
            : item
        )
      );
    } catch (err) {
      console.error('Error pausing download:', err);
    }
  }

  async function resumeDownload(downloadId: string) {
    await startDownload(downloadId);
  }

  async function removeFromQueue(downloadId: string) {
    try {
      await downloadService.removeFromQueue(downloadId);
      
      downloadQueue.update(queue => 
        queue.filter(item => item.id !== downloadId)
      );
    } catch (err) {
      console.error('Error removing from queue:', err);
      error = err.message || 'Failed to remove from queue';
    }
  }

  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatTimeRemaining(progress: number, startTime: number): string {
    if (progress <= 0) return 'Calculating...';
    
    const elapsed = Date.now() - startTime;
    const rate = progress / elapsed;
    const remaining = (100 - progress) / rate;
    
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'completed':
        return CheckmarkFilled;
      case 'downloading':
        return Pause;
      case 'pending':
        return Play;
      case 'failed':
      case 'expired':
        return Trash;
      default:
        return Download;
    }
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'text-green-500';
      case 'downloading':
        return 'text-blue-500';
      case 'pending':
        return 'text-yellow-500';
      case 'failed':
      case 'expired':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  }

  $: currentVideoInQueue = videoContent ? 
    $downloadQueue.find(item => item.video_id === videoContent.id) : null;
</script>

<div class="download-manager {className}">
  {#if videoContent && showAddButton && !currentVideoInQueue}
    <Box className="mb-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-white">
            {$t('download.offline_access', { default: 'Offline Access' })}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {$t('download.description', { default: 'Download this video to watch offline' })}
          </p>
        </div>
        
        <div class="flex items-center space-x-3">
          {#if availableQualities.length > 1}
            <select 
              bind:value={selectedQuality}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
            >
              {#each availableQualities as quality}
                <option value={quality.quality}>
                  {quality.quality === 'auto' ? 'Auto' : quality.quality}
                  {#if quality.size}
                    ({formatFileSize(quality.size)})
                  {/if}
                </option>
              {/each}
            </select>
          {/if}
          
          <PrimaryButton
            variant={VARIANTS.CONTAINED}
            onClick={addToDownloadQueue}
            disabled={loading || !videoContent.is_downloadable}
          >
            <Download size={20} class="mr-2" />
            {$t('download.add_to_queue', { default: 'Download' })}
          </PrimaryButton>
        </div>
      </div>
      
      {#if !videoContent.is_downloadable}
        <p class="text-sm text-yellow-600 dark:text-yellow-400 mt-2">
          {$t('download.not_available', { default: 'This video is not available for download' })}
        </p>
      {/if}
    </Box>
  {/if}

  {#if $downloadQueue.length > 0}
    <Box>
      <div class="flex items-center justify-between mb-4">
        <h3 class="font-semibold text-gray-900 dark:text-white">
          {$t('download.queue', { default: 'Download Queue' })}
        </h3>
        <button 
          on:click={loadDownloadQueue}
          class="text-sm text-primary-600 hover:text-primary-700"
        >
          {$t('download.refresh', { default: 'Refresh' })}
        </button>
      </div>

      {#if error}
        <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-3 mb-4">
          <p class="text-sm text-red-700 dark:text-red-300">{error}</p>
        </div>
      {/if}

      <div class="space-y-3">
        {#each $downloadQueue as item (item.id)}
          {@const StatusIcon = getStatusIcon(item.status)}
          <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center space-x-3 flex-1">
              <div class="flex-shrink-0">
                <StatusIcon size={20} class={getStatusColor(item.status)} />
              </div>
              
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {item.video_content?.title || 'Unknown Video'}
                </p>
                <div class="flex items-center space-x-4 mt-1">
                  <span class="text-xs text-gray-500 capitalize">
                    {$t(`download.status.${item.status}`, { default: item.status })}
                  </span>
                  <span class="text-xs text-gray-500">
                    {item.quality === 'auto' ? 'Auto' : item.quality}
                  </span>
                  {#if item.file_size}
                    <span class="text-xs text-gray-500">
                      {formatFileSize(item.file_size)}
                    </span>
                  {/if}
                </div>
                
                {#if item.status === 'downloading'}
                  <div class="mt-2">
                    <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                      <span>{Math.round(item.progress)}%</span>
                      <span>{formatTimeRemaining(item.progress, new Date(item.created_at).getTime())}</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        class="bg-primary-500 h-2 rounded-full transition-all duration-300"
                        style="width: {item.progress}%"
                      ></div>
                    </div>
                  </div>
                {/if}
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              {#if item.status === 'downloading'}
                <button
                  on:click={() => pauseDownload(item.id)}
                  class="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  title={$t('download.pause', { default: 'Pause' })}
                >
                  <Pause size={16} />
                </button>
              {:else if item.status === 'pending'}
                <button
                  on:click={() => resumeDownload(item.id)}
                  class="p-2 text-primary-500 hover:text-primary-700"
                  title={$t('download.resume', { default: 'Resume' })}
                >
                  <Play size={16} />
                </button>
              {/if}
              
              <button
                on:click={() => removeFromQueue(item.id)}
                class="p-2 text-red-500 hover:text-red-700"
                title={$t('download.remove', { default: 'Remove' })}
              >
                <Trash size={16} />
              </button>
            </div>
          </div>
        {/each}
      </div>
    </Box>
  {/if}
</div>

<style>
  .download-manager {
    @apply w-full;
  }
</style>
