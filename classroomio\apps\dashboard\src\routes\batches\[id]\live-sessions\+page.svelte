<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { writable } from 'svelte/store';
  import type { LiveSession } from '$lib/utils/types/liveStreaming';
  import { liveSessionService } from '$lib/utils/services/liveStreaming';
  import { 
    currentBatch, 
    batchActions 
  } from '$lib/components/Batch/store';
  import { batchService } from '$lib/utils/services/batch';
  import CreateLiveSession from '$lib/components/LiveStreaming/CreateLiveSession.svelte';
  import { PageBody, PageNav } from '$lib/components/Page';
  import { t } from '$lib/utils/functions/translations';
  import { globalStore } from '$lib/utils/store/app';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Add, 
    Video, 
    Calendar, 
    Time, 
    UserMultiple,
    Play,
    Stop,
    Edit,
    View,
    Filter,
    Refresh
  } from 'carbon-icons-svelte';

  let batchId: string;
  let sessions = writable<LiveSession[]>([]);
  let loading = true;
  let error: string | null = null;
  let showCreateModal = false;
  let selectedFilter = 'all';
  let selectedType = 'all';

  $: isInstructor = $globalStore.role === 'teacher' || $globalStore.isOrgAdmin;
  $: canCreateSessions = isInstructor;

  onMount(async () => {
    batchId = $page.params.id;
    await loadBatchData();
    await loadSessions();
  });

  async function loadBatchData() {
    try {
      if (!$currentBatch || $currentBatch.id !== batchId) {
        const batch = await batchService.getBatch(batchId);
        if (!batch) {
          throw new Error('Batch not found');
        }
        batchActions.setBatch(batch);
      }
    } catch (err) {
      console.error('Error loading batch:', err);
      error = err.message || 'Failed to load batch data';
    }
  }

  async function loadSessions() {
    try {
      loading = true;
      error = null;

      const filters: any = {};
      if (selectedFilter !== 'all') {
        filters.status = selectedFilter;
      }
      if (selectedType !== 'all') {
        filters.session_type = selectedType;
      }

      const sessionsData = await liveSessionService.getBatchSessions(batchId, filters);
      sessions.set(sessionsData);

    } catch (err) {
      console.error('Error loading sessions:', err);
      error = err.message || 'Failed to load sessions';
    } finally {
      loading = false;
    }
  }

  function handleSessionCreated(event: CustomEvent) {
    const { session } = event.detail;
    sessions.update(current => [session, ...current]);
    showCreateModal = false;
  }

  function joinSession(sessionId: string) {
    goto(`/batches/${batchId}/live-sessions/${sessionId}`);
  }

  function editSession(sessionId: string) {
    goto(`/batches/${batchId}/live-sessions/${sessionId}/edit`);
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'live': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'scheduled': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'ended': return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
      case 'cancelled': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  }

  function getTypeIcon(type: string) {
    switch (type) {
      case 'exam': return '📝';
      case 'meeting': return '🤝';
      case 'workshop': return '🔧';
      default: return '📚';
    }
  }

  function formatDateTime(timestamp: string): string {
    return new Date(timestamp).toLocaleString();
  }

  function formatDuration(start: string, end: string): string {
    const duration = (new Date(end).getTime() - new Date(start).getTime()) / (1000 * 60);
    const hours = Math.floor(duration / 60);
    const minutes = Math.floor(duration % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  function isSessionLive(session: LiveSession): boolean {
    const now = new Date();
    const start = new Date(session.scheduled_start);
    const end = new Date(session.scheduled_end);
    return session.status === 'live' || (now >= start && now <= end && session.status === 'scheduled');
  }

  function isSessionUpcoming(session: LiveSession): boolean {
    const now = new Date();
    const start = new Date(session.scheduled_start);
    return start > now && session.status === 'scheduled';
  }
</script>

<svelte:head>
  <title>
    {$t('live_sessions.title', { default: 'Live Sessions' })} - {$currentBatch?.name} - ClassroomIO
  </title>
</svelte:head>

<PageNav title={$t('live_sessions.title', { default: 'Live Sessions' })}>
  <div class="flex items-center space-x-4">
    <!-- Filters -->
    <select 
      bind:value={selectedFilter}
      on:change={loadSessions}
      class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
    >
      <option value="all">{$t('live_sessions.all_status', { default: 'All Status' })}</option>
      <option value="scheduled">{$t('live_sessions.scheduled', { default: 'Scheduled' })}</option>
      <option value="live">{$t('live_sessions.live', { default: 'Live' })}</option>
      <option value="ended">{$t('live_sessions.ended', { default: 'Ended' })}</option>
    </select>

    <select 
      bind:value={selectedType}
      on:change={loadSessions}
      class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
    >
      <option value="all">{$t('live_sessions.all_types', { default: 'All Types' })}</option>
      <option value="class">{$t('live_sessions.class', { default: 'Class' })}</option>
      <option value="exam">{$t('live_sessions.exam', { default: 'Exam' })}</option>
      <option value="meeting">{$t('live_sessions.meeting', { default: 'Meeting' })}</option>
      <option value="workshop">{$t('live_sessions.workshop', { default: 'Workshop' })}</option>
    </select>

    <PrimaryButton
      variant={VARIANTS.OUTLINED}
      onClick={loadSessions}
    >
      <Refresh size={20} class="mr-2" />
      {$t('live_sessions.refresh', { default: 'Refresh' })}
    </PrimaryButton>

    {#if canCreateSessions}
      <PrimaryButton
        variant={VARIANTS.CONTAINED}
        onClick={() => showCreateModal = true}
      >
        <Add size={20} class="mr-2" />
        {$t('live_sessions.create_session', { default: 'Create Session' })}
      </PrimaryButton>
    {/if}
  </div>
</PageNav>

<PageBody>
  {#if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('live_sessions.loading', { default: 'Loading Live Sessions' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Video size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('live_sessions.error', { default: 'Error Loading Sessions' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadSessions}>
        {$t('live_sessions.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if $sessions.length === 0}
    <Box className="text-center py-12">
      <Video size={48} class="text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {$t('live_sessions.no_sessions', { default: 'No Live Sessions' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        {$t('live_sessions.no_sessions_desc', { default: 'No live sessions found for this batch' })}
      </p>
      {#if canCreateSessions}
        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={() => showCreateModal = true}
        >
          <Add size={20} class="mr-2" />
          {$t('live_sessions.create_first', { default: 'Create First Session' })}
        </PrimaryButton>
      {/if}
    </Box>

  {:else}
    <!-- Sessions Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      {#each $sessions as session (session.id)}
        <Box className="relative">
          <!-- Live Indicator -->
          {#if isSessionLive(session)}
            <div class="absolute top-4 right-4 flex items-center">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse mr-2"></div>
              <span class="text-sm font-medium text-red-600 dark:text-red-400">LIVE</span>
            </div>
          {/if}

          <div class="p-6">
            <!-- Header -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center">
                <span class="text-2xl mr-3">{getTypeIcon(session.session_type)}</span>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
                    {session.title}
                  </h3>
                  <span class="px-2 py-1 text-xs rounded-full {getStatusColor(session.status)}">
                    {session.status.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            <!-- Description -->
            {#if session.description}
              <p class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                {session.description}
              </p>
            {/if}

            <!-- Session Info -->
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Calendar size={16} class="mr-2" />
                <span>{formatDateTime(session.scheduled_start)}</span>
              </div>
              
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Time size={16} class="mr-2" />
                <span>{formatDuration(session.scheduled_start, session.scheduled_end)}</span>
              </div>
              
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <UserMultiple size={16} class="mr-2" />
                <span>{$t('live_sessions.max_participants', { default: 'Max' })}: {session.max_participants}</span>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center space-x-2">
              {#if isSessionLive(session)}
                <PrimaryButton
                  variant={VARIANTS.CONTAINED}
                  onClick={() => joinSession(session.id)}
                  size="sm"
                  className="flex-1"
                >
                  <Play size={16} class="mr-2" />
                  {$t('live_sessions.join', { default: 'Join' })}
                </PrimaryButton>
              {:else if isSessionUpcoming(session)}
                <PrimaryButton
                  variant={VARIANTS.OUTLINED}
                  onClick={() => joinSession(session.id)}
                  size="sm"
                  className="flex-1"
                >
                  <View size={16} class="mr-2" />
                  {$t('live_sessions.view', { default: 'View' })}
                </PrimaryButton>
              {:else}
                <PrimaryButton
                  variant={VARIANTS.OUTLINED}
                  onClick={() => joinSession(session.id)}
                  size="sm"
                  className="flex-1"
                >
                  <View size={16} class="mr-2" />
                  {$t('live_sessions.details', { default: 'Details' })}
                </PrimaryButton>
              {/if}

              {#if canCreateSessions}
                <PrimaryButton
                  variant={VARIANTS.OUTLINED}
                  onClick={() => editSession(session.id)}
                  size="sm"
                >
                  <Edit size={16} />
                </PrimaryButton>
              {/if}
            </div>

            <!-- Recording Indicator -->
            {#if session.is_recorded && session.recording_url}
              <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center text-sm text-green-600 dark:text-green-400">
                  <Video size={16} class="mr-2" />
                  <span>{$t('live_sessions.recording_available', { default: 'Recording Available' })}</span>
                </div>
              </div>
            {/if}
          </div>
        </Box>
      {/each}
    </div>
  {/if}
</PageBody>

<!-- Create Session Modal -->
<CreateLiveSession
  {batchId}
  bind:isOpen={showCreateModal}
  on:sessionCreated={handleSessionCreated}
  on:close={() => showCreateModal = false}
/>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
