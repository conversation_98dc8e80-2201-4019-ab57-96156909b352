-- Migration: Analytics and Reporting System
-- Date: 2025-06-30
-- Description: Comprehensive analytics and reporting system with Metabase/Superset integration

-- Create analytics_events table for comprehensive event tracking
CREATE TABLE IF NOT EXISTS "public"."analytics_events" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "user_id" uuid,
    "session_id" character varying,
    "organization_id" uuid NOT NULL,
    "batch_id" uuid,
    "event_type" character varying NOT NULL, -- 'video_play', 'video_pause', 'assignment_submit', etc.
    "event_category" character varying NOT NULL, -- 'learning', 'communication', 'assessment', 'system'
    "event_action" character varying NOT NULL,
    "event_label" character varying,
    "event_value" numeric,
    "properties" jsonb DEFAULT '{}'::jsonb,
    "context" jsonb DEFAULT '{}'::jsonb, -- browser, device, location info
    "ip_address" inet,
    "user_agent" text,
    "referrer" text,
    "page_url" text,
    "processed" boolean DEFAULT false,
    "processed_at" timestamp with time zone
);

ALTER TABLE "public"."analytics_events" ENABLE ROW LEVEL SECURITY;

-- Create learning_analytics table for aggregated learning metrics
CREATE TABLE IF NOT EXISTS "public"."learning_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "user_id" uuid NOT NULL,
    "organization_id" uuid NOT NULL,
    "batch_id" uuid NOT NULL,
    "subject_id" uuid,
    "chapter_id" uuid,
    "lesson_id" uuid,
    "date" date NOT NULL,
    "total_study_time_minutes" integer DEFAULT 0,
    "video_watch_time_minutes" integer DEFAULT 0,
    "videos_completed" integer DEFAULT 0,
    "assignments_completed" integer DEFAULT 0,
    "quizzes_completed" integer DEFAULT 0,
    "forum_posts" integer DEFAULT 0,
    "doubts_submitted" integer DEFAULT 0,
    "live_sessions_attended" integer DEFAULT 0,
    "engagement_score" numeric DEFAULT 0, -- 0-100 calculated score
    "progress_percentage" numeric DEFAULT 0,
    "last_activity_at" timestamp with time zone,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."learning_analytics" ENABLE ROW LEVEL SECURITY;

-- Create video_analytics table for detailed video engagement
CREATE TABLE IF NOT EXISTS "public"."video_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "user_id" uuid NOT NULL,
    "video_id" uuid NOT NULL,
    "session_id" character varying NOT NULL,
    "total_watch_time_seconds" integer DEFAULT 0,
    "completion_percentage" numeric DEFAULT 0,
    "play_count" integer DEFAULT 0,
    "pause_count" integer DEFAULT 0,
    "seek_count" integer DEFAULT 0,
    "replay_count" integer DEFAULT 0,
    "speed_changes" integer DEFAULT 0,
    "quality_changes" integer DEFAULT 0,
    "fullscreen_toggles" integer DEFAULT 0,
    "watch_segments" jsonb DEFAULT '[]'::jsonb, -- array of {start, end, duration}
    "engagement_events" jsonb DEFAULT '[]'::jsonb, -- detailed interaction events
    "device_info" jsonb DEFAULT '{}'::jsonb,
    "network_info" jsonb DEFAULT '{}'::jsonb,
    "completed_at" timestamp with time zone,
    "last_position_seconds" integer DEFAULT 0
);

ALTER TABLE "public"."video_analytics" ENABLE ROW LEVEL SECURITY;

-- Create assessment_analytics table for quiz and assignment performance
CREATE TABLE IF NOT EXISTS "public"."assessment_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "user_id" uuid NOT NULL,
    "assessment_id" uuid NOT NULL,
    "assessment_type" character varying NOT NULL, -- 'quiz', 'assignment', 'exam'
    "batch_id" uuid NOT NULL,
    "subject_id" uuid,
    "chapter_id" uuid,
    "attempt_number" integer DEFAULT 1,
    "start_time" timestamp with time zone NOT NULL,
    "end_time" timestamp with time zone,
    "duration_minutes" integer,
    "score" numeric,
    "max_score" numeric,
    "percentage" numeric,
    "questions_attempted" integer DEFAULT 0,
    "questions_correct" integer DEFAULT 0,
    "questions_total" integer DEFAULT 0,
    "time_per_question" jsonb DEFAULT '[]'::jsonb, -- array of time spent per question
    "answer_changes" integer DEFAULT 0,
    "hints_used" integer DEFAULT 0,
    "status" character varying DEFAULT 'in_progress', -- 'in_progress', 'completed', 'abandoned'
    "submission_data" jsonb DEFAULT '{}'::jsonb,
    "grading_data" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."assessment_analytics" ENABLE ROW LEVEL SECURITY;

-- Create communication_analytics table for forum and messaging metrics
CREATE TABLE IF NOT EXISTS "public"."communication_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "user_id" uuid NOT NULL,
    "organization_id" uuid NOT NULL,
    "batch_id" uuid,
    "date" date NOT NULL,
    "forum_posts_created" integer DEFAULT 0,
    "forum_replies_created" integer DEFAULT 0,
    "forum_posts_viewed" integer DEFAULT 0,
    "forum_upvotes_given" integer DEFAULT 0,
    "forum_upvotes_received" integer DEFAULT 0,
    "doubts_submitted" integer DEFAULT 0,
    "doubts_resolved" integer DEFAULT 0,
    "doubt_responses_given" integer DEFAULT 0,
    "messages_sent" integer DEFAULT 0,
    "messages_received" integer DEFAULT 0,
    "channels_active" integer DEFAULT 0,
    "voice_messages_sent" integer DEFAULT 0,
    "files_shared" integer DEFAULT 0,
    "reputation_points_earned" integer DEFAULT 0,
    "badges_earned" integer DEFAULT 0
);

ALTER TABLE "public"."communication_analytics" ENABLE ROW LEVEL SECURITY;

-- Create live_session_analytics table for live streaming metrics
CREATE TABLE IF NOT EXISTS "public"."live_session_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "session_id" uuid NOT NULL,
    "user_id" uuid NOT NULL,
    "join_time" timestamp with time zone NOT NULL,
    "leave_time" timestamp with time zone,
    "duration_minutes" integer,
    "attendance_percentage" numeric DEFAULT 0,
    "camera_on_duration" integer DEFAULT 0,
    "microphone_on_duration" integer DEFAULT 0,
    "chat_messages_sent" integer DEFAULT 0,
    "polls_participated" integer DEFAULT 0,
    "screen_shares" integer DEFAULT 0,
    "reactions_sent" integer DEFAULT 0,
    "breakout_rooms_joined" integer DEFAULT 0,
    "connection_quality" character varying, -- 'excellent', 'good', 'fair', 'poor'
    "technical_issues" jsonb DEFAULT '[]'::jsonb,
    "engagement_score" numeric DEFAULT 0,
    "device_info" jsonb DEFAULT '{}'::jsonb,
    "network_stats" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."live_session_analytics" ENABLE ROW LEVEL SECURITY;

-- Create system_analytics table for platform performance metrics
CREATE TABLE IF NOT EXISTS "public"."system_analytics" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "metric_name" character varying NOT NULL,
    "metric_category" character varying NOT NULL, -- 'performance', 'usage', 'security', 'error'
    "metric_value" numeric NOT NULL,
    "metric_unit" character varying, -- 'ms', 'count', 'percentage', 'bytes'
    "organization_id" uuid,
    "batch_id" uuid,
    "user_id" uuid,
    "session_id" character varying,
    "tags" jsonb DEFAULT '{}'::jsonb,
    "metadata" jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE "public"."system_analytics" ENABLE ROW LEVEL SECURITY;

-- Create dashboard_configs table for custom dashboard configurations
CREATE TABLE IF NOT EXISTS "public"."dashboard_configs" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "name" character varying NOT NULL,
    "description" text,
    "dashboard_type" character varying NOT NULL, -- 'student', 'instructor', 'admin', 'custom'
    "organization_id" uuid NOT NULL,
    "created_by" uuid NOT NULL,
    "is_public" boolean DEFAULT false,
    "is_default" boolean DEFAULT false,
    "config" jsonb NOT NULL DEFAULT '{}'::jsonb, -- dashboard layout and widget configs
    "filters" jsonb DEFAULT '{}'::jsonb, -- default filters
    "permissions" jsonb DEFAULT '{}'::jsonb, -- access permissions
    "usage_count" integer DEFAULT 0,
    "last_used_at" timestamp with time zone
);

ALTER TABLE "public"."dashboard_configs" ENABLE ROW LEVEL SECURITY;

-- Create report_templates table for custom report configurations
CREATE TABLE IF NOT EXISTS "public"."report_templates" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "name" character varying NOT NULL,
    "description" text,
    "report_type" character varying NOT NULL, -- 'student_progress', 'batch_performance', 'engagement', 'custom'
    "organization_id" uuid NOT NULL,
    "created_by" uuid NOT NULL,
    "is_public" boolean DEFAULT false,
    "query_config" jsonb NOT NULL DEFAULT '{}'::jsonb, -- SQL query or data source config
    "visualization_config" jsonb DEFAULT '{}'::jsonb, -- chart and table configs
    "filters" jsonb DEFAULT '{}'::jsonb, -- available filters
    "schedule_config" jsonb DEFAULT '{}'::jsonb, -- automated report scheduling
    "export_formats" jsonb DEFAULT '["pdf", "excel", "csv"]'::jsonb,
    "permissions" jsonb DEFAULT '{}'::jsonb,
    "usage_count" integer DEFAULT 0,
    "last_generated_at" timestamp with time zone
);

ALTER TABLE "public"."report_templates" ENABLE ROW LEVEL SECURITY;

-- Create scheduled_reports table for automated report generation
CREATE TABLE IF NOT EXISTS "public"."scheduled_reports" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "template_id" uuid NOT NULL,
    "name" character varying NOT NULL,
    "schedule_expression" character varying NOT NULL, -- cron expression
    "timezone" character varying DEFAULT 'UTC',
    "recipients" jsonb NOT NULL DEFAULT '[]'::jsonb, -- email addresses
    "filters" jsonb DEFAULT '{}'::jsonb, -- report-specific filters
    "format" character varying DEFAULT 'pdf', -- 'pdf', 'excel', 'csv'
    "is_active" boolean DEFAULT true,
    "last_run_at" timestamp with time zone,
    "next_run_at" timestamp with time zone,
    "run_count" integer DEFAULT 0,
    "error_count" integer DEFAULT 0,
    "last_error" text,
    "created_by" uuid NOT NULL
);

ALTER TABLE "public"."scheduled_reports" ENABLE ROW LEVEL SECURITY;

-- Create analytics_cache table for pre-computed metrics
CREATE TABLE IF NOT EXISTS "public"."analytics_cache" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone DEFAULT now(),
    "cache_key" character varying NOT NULL,
    "cache_type" character varying NOT NULL, -- 'dashboard', 'report', 'metric'
    "organization_id" uuid,
    "batch_id" uuid,
    "user_id" uuid,
    "date_range_start" date,
    "date_range_end" date,
    "data" jsonb NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "hit_count" integer DEFAULT 0,
    "last_accessed_at" timestamp with time zone DEFAULT now()
);

ALTER TABLE "public"."analytics_cache" ENABLE ROW LEVEL SECURITY;

-- Add Primary Keys
ALTER TABLE "public"."analytics_events" ADD CONSTRAINT "analytics_events_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."video_analytics" ADD CONSTRAINT "video_analytics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."assessment_analytics" ADD CONSTRAINT "assessment_analytics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."communication_analytics" ADD CONSTRAINT "communication_analytics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."live_session_analytics" ADD CONSTRAINT "live_session_analytics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."system_analytics" ADD CONSTRAINT "system_analytics_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."dashboard_configs" ADD CONSTRAINT "dashboard_configs_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."report_templates" ADD CONSTRAINT "report_templates_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."scheduled_reports" ADD CONSTRAINT "scheduled_reports_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."analytics_cache" ADD CONSTRAINT "analytics_cache_pkey" PRIMARY KEY ("id");

-- Add Foreign Key Constraints
ALTER TABLE "public"."analytics_events" ADD CONSTRAINT "analytics_events_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."analytics_events" ADD CONSTRAINT "analytics_events_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."analytics_events" ADD CONSTRAINT "analytics_events_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_chapter_id_fkey"
    FOREIGN KEY ("chapter_id") REFERENCES "chapter"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_lesson_id_fkey"
    FOREIGN KEY ("lesson_id") REFERENCES "lesson"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."video_analytics" ADD CONSTRAINT "video_analytics_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."assessment_analytics" ADD CONSTRAINT "assessment_analytics_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."assessment_analytics" ADD CONSTRAINT "assessment_analytics_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."assessment_analytics" ADD CONSTRAINT "assessment_analytics_subject_id_fkey"
    FOREIGN KEY ("subject_id") REFERENCES "subject"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."assessment_analytics" ADD CONSTRAINT "assessment_analytics_chapter_id_fkey"
    FOREIGN KEY ("chapter_id") REFERENCES "chapter"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."communication_analytics" ADD CONSTRAINT "communication_analytics_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."communication_analytics" ADD CONSTRAINT "communication_analytics_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."communication_analytics" ADD CONSTRAINT "communication_analytics_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."live_session_analytics" ADD CONSTRAINT "live_session_analytics_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."system_analytics" ADD CONSTRAINT "system_analytics_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."system_analytics" ADD CONSTRAINT "system_analytics_batch_id_fkey"
    FOREIGN KEY ("batch_id") REFERENCES "batch"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."system_analytics" ADD CONSTRAINT "system_analytics_user_id_fkey"
    FOREIGN KEY ("user_id") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE SET NULL;

ALTER TABLE "public"."dashboard_configs" ADD CONSTRAINT "dashboard_configs_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."dashboard_configs" ADD CONSTRAINT "dashboard_configs_created_by_fkey"
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."report_templates" ADD CONSTRAINT "report_templates_organization_id_fkey"
    FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."report_templates" ADD CONSTRAINT "report_templates_created_by_fkey"
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."scheduled_reports" ADD CONSTRAINT "scheduled_reports_template_id_fkey"
    FOREIGN KEY ("template_id") REFERENCES "report_templates"("id") ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE "public"."scheduled_reports" ADD CONSTRAINT "scheduled_reports_created_by_fkey"
    FOREIGN KEY ("created_by") REFERENCES "profile"("id") ON UPDATE CASCADE ON DELETE CASCADE;

-- Add Unique Constraints
ALTER TABLE "public"."learning_analytics" ADD CONSTRAINT "learning_analytics_user_date_unique"
    UNIQUE ("user_id", "batch_id", "subject_id", "date");

ALTER TABLE "public"."communication_analytics" ADD CONSTRAINT "communication_analytics_user_date_unique"
    UNIQUE ("user_id", "organization_id", "batch_id", "date");

ALTER TABLE "public"."analytics_cache" ADD CONSTRAINT "analytics_cache_key_unique"
    UNIQUE ("cache_key", "organization_id", "batch_id", "user_id");

-- Create Performance Indexes
CREATE INDEX IF NOT EXISTS "idx_analytics_events_user_created" ON "public"."analytics_events" ("user_id", "created_at");
CREATE INDEX IF NOT EXISTS "idx_analytics_events_org_type" ON "public"."analytics_events" ("organization_id", "event_type");
CREATE INDEX IF NOT EXISTS "idx_analytics_events_batch_category" ON "public"."analytics_events" ("batch_id", "event_category");
CREATE INDEX IF NOT EXISTS "idx_analytics_events_created_at" ON "public"."analytics_events" ("created_at");
CREATE INDEX IF NOT EXISTS "idx_analytics_events_session" ON "public"."analytics_events" ("session_id");

CREATE INDEX IF NOT EXISTS "idx_learning_analytics_user_date" ON "public"."learning_analytics" ("user_id", "date");
CREATE INDEX IF NOT EXISTS "idx_learning_analytics_batch_date" ON "public"."learning_analytics" ("batch_id", "date");
CREATE INDEX IF NOT EXISTS "idx_learning_analytics_subject_date" ON "public"."learning_analytics" ("subject_id", "date");
CREATE INDEX IF NOT EXISTS "idx_learning_analytics_engagement" ON "public"."learning_analytics" ("engagement_score");

CREATE INDEX IF NOT EXISTS "idx_video_analytics_user_video" ON "public"."video_analytics" ("user_id", "video_id");
CREATE INDEX IF NOT EXISTS "idx_video_analytics_completion" ON "public"."video_analytics" ("completion_percentage");
CREATE INDEX IF NOT EXISTS "idx_video_analytics_created" ON "public"."video_analytics" ("created_at");

CREATE INDEX IF NOT EXISTS "idx_assessment_analytics_user_type" ON "public"."assessment_analytics" ("user_id", "assessment_type");
CREATE INDEX IF NOT EXISTS "idx_assessment_analytics_batch_subject" ON "public"."assessment_analytics" ("batch_id", "subject_id");
CREATE INDEX IF NOT EXISTS "idx_assessment_analytics_score" ON "public"."assessment_analytics" ("percentage");
CREATE INDEX IF NOT EXISTS "idx_assessment_analytics_created" ON "public"."assessment_analytics" ("created_at");

CREATE INDEX IF NOT EXISTS "idx_communication_analytics_user_date" ON "public"."communication_analytics" ("user_id", "date");
CREATE INDEX IF NOT EXISTS "idx_communication_analytics_batch_date" ON "public"."communication_analytics" ("batch_id", "date");
CREATE INDEX IF NOT EXISTS "idx_communication_analytics_reputation" ON "public"."communication_analytics" ("reputation_points_earned");

CREATE INDEX IF NOT EXISTS "idx_live_session_analytics_session_user" ON "public"."live_session_analytics" ("session_id", "user_id");
CREATE INDEX IF NOT EXISTS "idx_live_session_analytics_join_time" ON "public"."live_session_analytics" ("join_time");
CREATE INDEX IF NOT EXISTS "idx_live_session_analytics_engagement" ON "public"."live_session_analytics" ("engagement_score");

CREATE INDEX IF NOT EXISTS "idx_system_analytics_metric_created" ON "public"."system_analytics" ("metric_name", "created_at");
CREATE INDEX IF NOT EXISTS "idx_system_analytics_category_created" ON "public"."system_analytics" ("metric_category", "created_at");
CREATE INDEX IF NOT EXISTS "idx_system_analytics_org_created" ON "public"."system_analytics" ("organization_id", "created_at");

CREATE INDEX IF NOT EXISTS "idx_dashboard_configs_org_type" ON "public"."dashboard_configs" ("organization_id", "dashboard_type");
CREATE INDEX IF NOT EXISTS "idx_dashboard_configs_public" ON "public"."dashboard_configs" ("is_public", "is_default");

CREATE INDEX IF NOT EXISTS "idx_report_templates_org_type" ON "public"."report_templates" ("organization_id", "report_type");
CREATE INDEX IF NOT EXISTS "idx_report_templates_public" ON "public"."report_templates" ("is_public");

CREATE INDEX IF NOT EXISTS "idx_scheduled_reports_next_run" ON "public"."scheduled_reports" ("next_run_at", "is_active");
CREATE INDEX IF NOT EXISTS "idx_scheduled_reports_template" ON "public"."scheduled_reports" ("template_id");

CREATE INDEX IF NOT EXISTS "idx_analytics_cache_key" ON "public"."analytics_cache" ("cache_key");
CREATE INDEX IF NOT EXISTS "idx_analytics_cache_expires" ON "public"."analytics_cache" ("expires_at");
CREATE INDEX IF NOT EXISTS "idx_analytics_cache_type_org" ON "public"."analytics_cache" ("cache_type", "organization_id");

-- Create Analytics Functions

-- Function to track analytics events
CREATE OR REPLACE FUNCTION track_analytics_event(
    p_user_id uuid,
    p_session_id varchar,
    p_organization_id uuid,
    p_batch_id uuid,
    p_event_type varchar,
    p_event_category varchar,
    p_event_action varchar,
    p_event_label varchar DEFAULT NULL,
    p_event_value numeric DEFAULT NULL,
    p_properties jsonb DEFAULT '{}'::jsonb,
    p_context jsonb DEFAULT '{}'::jsonb
) RETURNS uuid AS $$
DECLARE
    event_id uuid;
BEGIN
    INSERT INTO analytics_events (
        user_id, session_id, organization_id, batch_id,
        event_type, event_category, event_action, event_label,
        event_value, properties, context
    ) VALUES (
        p_user_id, p_session_id, p_organization_id, p_batch_id,
        p_event_type, p_event_category, p_event_action, p_event_label,
        p_event_value, p_properties, p_context
    ) RETURNING id INTO event_id;

    RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update learning analytics
CREATE OR REPLACE FUNCTION update_learning_analytics(
    p_user_id uuid,
    p_organization_id uuid,
    p_batch_id uuid,
    p_subject_id uuid DEFAULT NULL,
    p_chapter_id uuid DEFAULT NULL,
    p_lesson_id uuid DEFAULT NULL,
    p_study_time_minutes integer DEFAULT 0,
    p_video_watch_time_minutes integer DEFAULT 0,
    p_videos_completed integer DEFAULT 0,
    p_assignments_completed integer DEFAULT 0,
    p_quizzes_completed integer DEFAULT 0,
    p_forum_posts integer DEFAULT 0,
    p_doubts_submitted integer DEFAULT 0,
    p_live_sessions_attended integer DEFAULT 0
) RETURNS void AS $$
DECLARE
    current_date date := CURRENT_DATE;
    engagement_score numeric;
BEGIN
    -- Calculate engagement score (0-100)
    engagement_score := LEAST(100,
        (p_study_time_minutes * 0.3) +
        (p_videos_completed * 5) +
        (p_assignments_completed * 10) +
        (p_quizzes_completed * 8) +
        (p_forum_posts * 3) +
        (p_doubts_submitted * 2) +
        (p_live_sessions_attended * 15)
    );

    INSERT INTO learning_analytics (
        user_id, organization_id, batch_id, subject_id, chapter_id, lesson_id, date,
        total_study_time_minutes, video_watch_time_minutes, videos_completed,
        assignments_completed, quizzes_completed, forum_posts, doubts_submitted,
        live_sessions_attended, engagement_score, last_activity_at
    ) VALUES (
        p_user_id, p_organization_id, p_batch_id, p_subject_id, p_chapter_id, p_lesson_id, current_date,
        p_study_time_minutes, p_video_watch_time_minutes, p_videos_completed,
        p_assignments_completed, p_quizzes_completed, p_forum_posts, p_doubts_submitted,
        p_live_sessions_attended, engagement_score, NOW()
    )
    ON CONFLICT (user_id, batch_id, subject_id, date)
    DO UPDATE SET
        total_study_time_minutes = learning_analytics.total_study_time_minutes + p_study_time_minutes,
        video_watch_time_minutes = learning_analytics.video_watch_time_minutes + p_video_watch_time_minutes,
        videos_completed = learning_analytics.videos_completed + p_videos_completed,
        assignments_completed = learning_analytics.assignments_completed + p_assignments_completed,
        quizzes_completed = learning_analytics.quizzes_completed + p_quizzes_completed,
        forum_posts = learning_analytics.forum_posts + p_forum_posts,
        doubts_submitted = learning_analytics.doubts_submitted + p_doubts_submitted,
        live_sessions_attended = learning_analytics.live_sessions_attended + p_live_sessions_attended,
        engagement_score = LEAST(100,
            ((learning_analytics.total_study_time_minutes + p_study_time_minutes) * 0.3) +
            ((learning_analytics.videos_completed + p_videos_completed) * 5) +
            ((learning_analytics.assignments_completed + p_assignments_completed) * 10) +
            ((learning_analytics.quizzes_completed + p_quizzes_completed) * 8) +
            ((learning_analytics.forum_posts + p_forum_posts) * 3) +
            ((learning_analytics.doubts_submitted + p_doubts_submitted) * 2) +
            ((learning_analytics.live_sessions_attended + p_live_sessions_attended) * 15)
        ),
        last_activity_at = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get student engagement metrics
CREATE OR REPLACE FUNCTION get_student_engagement_metrics(
    p_user_id uuid,
    p_batch_id uuid,
    p_date_from date DEFAULT NULL,
    p_date_to date DEFAULT NULL
) RETURNS jsonb AS $$
DECLARE
    result jsonb;
    date_from date := COALESCE(p_date_from, CURRENT_DATE - INTERVAL '30 days');
    date_to date := COALESCE(p_date_to, CURRENT_DATE);
BEGIN
    SELECT jsonb_build_object(
        'total_study_time_minutes', COALESCE(SUM(total_study_time_minutes), 0),
        'video_watch_time_minutes', COALESCE(SUM(video_watch_time_minutes), 0),
        'videos_completed', COALESCE(SUM(videos_completed), 0),
        'assignments_completed', COALESCE(SUM(assignments_completed), 0),
        'quizzes_completed', COALESCE(SUM(quizzes_completed), 0),
        'forum_posts', COALESCE(SUM(forum_posts), 0),
        'doubts_submitted', COALESCE(SUM(doubts_submitted), 0),
        'live_sessions_attended', COALESCE(SUM(live_sessions_attended), 0),
        'average_engagement_score', COALESCE(AVG(engagement_score), 0),
        'days_active', COUNT(*),
        'streak_days', (
            SELECT COUNT(*) FROM (
                SELECT date,
                       ROW_NUMBER() OVER (ORDER BY date) -
                       ROW_NUMBER() OVER (PARTITION BY date - ROW_NUMBER() OVER (ORDER BY date) * INTERVAL '1 day' ORDER BY date) as grp
                FROM learning_analytics
                WHERE user_id = p_user_id AND batch_id = p_batch_id
                  AND date BETWEEN date_from AND date_to
                  AND engagement_score > 0
                ORDER BY date DESC
                LIMIT 1
            ) t
        )
    ) INTO result
    FROM learning_analytics
    WHERE user_id = p_user_id
      AND batch_id = p_batch_id
      AND date BETWEEN date_from AND date_to;

    RETURN COALESCE(result, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get batch performance analytics
CREATE OR REPLACE FUNCTION get_batch_performance_analytics(
    p_batch_id uuid,
    p_date_from date DEFAULT NULL,
    p_date_to date DEFAULT NULL
) RETURNS jsonb AS $$
DECLARE
    result jsonb;
    date_from date := COALESCE(p_date_from, CURRENT_DATE - INTERVAL '30 days');
    date_to date := COALESCE(p_date_to, CURRENT_DATE);
BEGIN
    SELECT jsonb_build_object(
        'total_students', COUNT(DISTINCT user_id),
        'active_students', COUNT(DISTINCT CASE WHEN engagement_score > 0 THEN user_id END),
        'average_engagement_score', COALESCE(AVG(engagement_score), 0),
        'total_study_time_hours', COALESCE(SUM(total_study_time_minutes), 0) / 60.0,
        'total_videos_completed', COALESCE(SUM(videos_completed), 0),
        'total_assignments_completed', COALESCE(SUM(assignments_completed), 0),
        'total_quizzes_completed', COALESCE(SUM(quizzes_completed), 0),
        'total_forum_posts', COALESCE(SUM(forum_posts), 0),
        'total_doubts_submitted', COALESCE(SUM(doubts_submitted), 0),
        'attendance_rate', COALESCE(AVG(live_sessions_attended), 0),
        'top_performers', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'user_id', user_id,
                    'engagement_score', AVG(engagement_score)
                ) ORDER BY AVG(engagement_score) DESC
            )
            FROM learning_analytics
            WHERE batch_id = p_batch_id
              AND date BETWEEN date_from AND date_to
            GROUP BY user_id
            LIMIT 10
        ),
        'subject_performance', (
            SELECT jsonb_object_agg(
                COALESCE(s.name, 'Unknown'),
                jsonb_build_object(
                    'average_engagement', AVG(la.engagement_score),
                    'total_study_time', SUM(la.total_study_time_minutes),
                    'videos_completed', SUM(la.videos_completed),
                    'assignments_completed', SUM(la.assignments_completed)
                )
            )
            FROM learning_analytics la
            LEFT JOIN subject s ON la.subject_id = s.id
            WHERE la.batch_id = p_batch_id
              AND la.date BETWEEN date_from AND date_to
            GROUP BY s.id, s.name
        )
    ) INTO result
    FROM learning_analytics
    WHERE batch_id = p_batch_id
      AND date BETWEEN date_from AND date_to;

    RETURN COALESCE(result, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cache analytics data
CREATE OR REPLACE FUNCTION cache_analytics_data(
    p_cache_key varchar,
    p_cache_type varchar,
    p_organization_id uuid DEFAULT NULL,
    p_batch_id uuid DEFAULT NULL,
    p_user_id uuid DEFAULT NULL,
    p_data jsonb,
    p_expires_in_hours integer DEFAULT 24
) RETURNS void AS $$
BEGIN
    INSERT INTO analytics_cache (
        cache_key, cache_type, organization_id, batch_id, user_id,
        data, expires_at
    ) VALUES (
        p_cache_key, p_cache_type, p_organization_id, p_batch_id, p_user_id,
        p_data, NOW() + (p_expires_in_hours || ' hours')::interval
    )
    ON CONFLICT (cache_key, organization_id, batch_id, user_id)
    DO UPDATE SET
        data = p_data,
        expires_at = NOW() + (p_expires_in_hours || ' hours')::interval,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get cached analytics data
CREATE OR REPLACE FUNCTION get_cached_analytics_data(
    p_cache_key varchar,
    p_organization_id uuid DEFAULT NULL,
    p_batch_id uuid DEFAULT NULL,
    p_user_id uuid DEFAULT NULL
) RETURNS jsonb AS $$
DECLARE
    result jsonb;
BEGIN
    SELECT data INTO result
    FROM analytics_cache
    WHERE cache_key = p_cache_key
      AND (p_organization_id IS NULL OR organization_id = p_organization_id)
      AND (p_batch_id IS NULL OR batch_id = p_batch_id)
      AND (p_user_id IS NULL OR user_id = p_user_id)
      AND expires_at > NOW();

    -- Update hit count and last accessed
    UPDATE analytics_cache
    SET hit_count = hit_count + 1,
        last_accessed_at = NOW()
    WHERE cache_key = p_cache_key
      AND (p_organization_id IS NULL OR organization_id = p_organization_id)
      AND (p_batch_id IS NULL OR batch_id = p_batch_id)
      AND (p_user_id IS NULL OR user_id = p_user_id)
      AND expires_at > NOW();

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Row Level Security Policies

-- Analytics Events Policies
CREATE POLICY "Users can view their own analytics events" ON "public"."analytics_events"
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Organization admins can view all analytics events" ON "public"."analytics_events"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = analytics_events.organization_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

CREATE POLICY "Teachers can view batch analytics events" ON "public"."analytics_events"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM batch_member bm
            WHERE bm.batch_id = analytics_events.batch_id
              AND bm.profile_id = auth.uid()
              AND bm.role = 'teacher'
        )
    );

CREATE POLICY "System can insert analytics events" ON "public"."analytics_events"
    FOR INSERT WITH CHECK (true);

-- Learning Analytics Policies
CREATE POLICY "Users can view their own learning analytics" ON "public"."learning_analytics"
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Organization admins can view all learning analytics" ON "public"."learning_analytics"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = learning_analytics.organization_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

CREATE POLICY "Teachers can view batch learning analytics" ON "public"."learning_analytics"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM batch_member bm
            WHERE bm.batch_id = learning_analytics.batch_id
              AND bm.profile_id = auth.uid()
              AND bm.role = 'teacher'
        )
    );

CREATE POLICY "System can manage learning analytics" ON "public"."learning_analytics"
    FOR ALL WITH CHECK (true);

-- Video Analytics Policies
CREATE POLICY "Users can view their own video analytics" ON "public"."video_analytics"
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Organization admins can view video analytics" ON "public"."video_analytics"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM lesson l
            JOIN chapter c ON l.chapter_id = c.id
            JOIN subject s ON c.subject_id = s.id
            JOIN batch b ON s.batch_id = b.id
            JOIN organization_member om ON b.organization_id = om.organization_id
            WHERE om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

CREATE POLICY "System can manage video analytics" ON "public"."video_analytics"
    FOR ALL WITH CHECK (true);

-- Assessment Analytics Policies
CREATE POLICY "Users can view their own assessment analytics" ON "public"."assessment_analytics"
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Organization admins can view assessment analytics" ON "public"."assessment_analytics"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM batch_member bm
            JOIN organization_member om ON bm.batch_id = assessment_analytics.batch_id
            WHERE om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

CREATE POLICY "Teachers can view batch assessment analytics" ON "public"."assessment_analytics"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM batch_member bm
            WHERE bm.batch_id = assessment_analytics.batch_id
              AND bm.profile_id = auth.uid()
              AND bm.role = 'teacher'
        )
    );

CREATE POLICY "System can manage assessment analytics" ON "public"."assessment_analytics"
    FOR ALL WITH CHECK (true);

-- Communication Analytics Policies
CREATE POLICY "Users can view their own communication analytics" ON "public"."communication_analytics"
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Organization admins can view communication analytics" ON "public"."communication_analytics"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = communication_analytics.organization_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

CREATE POLICY "System can manage communication analytics" ON "public"."communication_analytics"
    FOR ALL WITH CHECK (true);

-- Live Session Analytics Policies
CREATE POLICY "Users can view their own live session analytics" ON "public"."live_session_analytics"
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Organization admins can view live session analytics" ON "public"."live_session_analytics"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM live_session ls
            JOIN batch b ON ls.batch_id = b.id
            JOIN organization_member om ON b.organization_id = om.organization_id
            WHERE ls.id = live_session_analytics.session_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

CREATE POLICY "System can manage live session analytics" ON "public"."live_session_analytics"
    FOR ALL WITH CHECK (true);

-- System Analytics Policies
CREATE POLICY "Organization admins can view system analytics" ON "public"."system_analytics"
    FOR SELECT USING (
        organization_id IS NULL OR
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = system_analytics.organization_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

CREATE POLICY "System can manage system analytics" ON "public"."system_analytics"
    FOR ALL WITH CHECK (true);

-- Dashboard Configs Policies
CREATE POLICY "Users can view organization dashboard configs" ON "public"."dashboard_configs"
    FOR SELECT USING (
        is_public = true OR
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = dashboard_configs.organization_id
              AND om.profile_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage their own dashboard configs" ON "public"."dashboard_configs"
    FOR ALL USING (created_by = auth.uid());

CREATE POLICY "Organization admins can manage dashboard configs" ON "public"."dashboard_configs"
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = dashboard_configs.organization_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

-- Report Templates Policies
CREATE POLICY "Users can view organization report templates" ON "public"."report_templates"
    FOR SELECT USING (
        is_public = true OR
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = report_templates.organization_id
              AND om.profile_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage their own report templates" ON "public"."report_templates"
    FOR ALL USING (created_by = auth.uid());

CREATE POLICY "Organization admins can manage report templates" ON "public"."report_templates"
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = report_templates.organization_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

-- Scheduled Reports Policies
CREATE POLICY "Users can view their own scheduled reports" ON "public"."scheduled_reports"
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can manage their own scheduled reports" ON "public"."scheduled_reports"
    FOR ALL USING (created_by = auth.uid());

CREATE POLICY "Organization admins can view scheduled reports" ON "public"."scheduled_reports"
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM report_templates rt
            JOIN organization_member om ON rt.organization_id = om.organization_id
            WHERE rt.id = scheduled_reports.template_id
              AND om.profile_id = auth.uid()
              AND om.role = 'admin'
        )
    );

-- Analytics Cache Policies
CREATE POLICY "Users can view relevant cached analytics" ON "public"."analytics_cache"
    FOR SELECT USING (
        user_id = auth.uid() OR
        (organization_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM organization_member om
            WHERE om.organization_id = analytics_cache.organization_id
              AND om.profile_id = auth.uid()
        )) OR
        (batch_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM batch_member bm
            WHERE bm.batch_id = analytics_cache.batch_id
              AND bm.profile_id = auth.uid()
        ))
    );

CREATE POLICY "System can manage analytics cache" ON "public"."analytics_cache"
    FOR ALL WITH CHECK (true);

-- Create triggers for automatic analytics updates
CREATE OR REPLACE FUNCTION update_analytics_cache_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_learning_analytics_timestamp
    BEFORE UPDATE ON learning_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_cache_timestamp();

CREATE TRIGGER update_video_analytics_timestamp
    BEFORE UPDATE ON video_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_cache_timestamp();

CREATE TRIGGER update_assessment_analytics_timestamp
    BEFORE UPDATE ON assessment_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_cache_timestamp();

CREATE TRIGGER update_communication_analytics_timestamp
    BEFORE UPDATE ON communication_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_cache_timestamp();

CREATE TRIGGER update_live_session_analytics_timestamp
    BEFORE UPDATE ON live_session_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_cache_timestamp();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
