<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import BatchList from '$lib/components/Batch/BatchList.svelte';
  import { PageBody, PageNav } from '$lib/components/Page';
  import { t } from '$lib/utils/functions/translations';
  import { globalStore } from '$lib/utils/store/app';
  import RoleBasedSecurity from '$lib/components/RoleBasedSecurity/index.svelte';
  import { ROLE } from '$lib/utils/constants/roles';

  // Check if user has permission to access batches
  $: canAccessBatches = $globalStore.isOrgAdmin || $globalStore.isOrgTeacher;

  onMount(() => {
    // Redirect if user doesn't have permission
    if (!canAccessBatches) {
      goto('/');
    }
  });

  function handleBatchSelect(batch: any) {
    goto(`/batches/${batch.id}`);
  }
</script>

<svelte:head>
  <title>{$t('batch.page_title', { default: 'Batches' })} - ClassroomIO</title>
  <meta name="description" content={$t('batch.page_description', { default: 'Manage educational batches and student groups' })} />
</svelte:head>

<RoleBasedSecurity allowedRoles={[ROLE.ADMIN, ROLE.TUTOR]}>
  <PageNav title={$t('batch.nav_title', { default: 'Batch Management' })}>
    <div class="flex items-center space-x-4">
      <!-- Add any navigation items here -->
    </div>
  </PageNav>

  <PageBody>
    <BatchList onBatchSelect={handleBatchSelect} />
  </PageBody>
</RoleBasedSecurity>
