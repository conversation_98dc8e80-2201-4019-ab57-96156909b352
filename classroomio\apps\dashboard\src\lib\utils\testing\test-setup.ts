// Test Setup and Configuration
// Comprehensive testing infrastructure for ClassroomIO

import { vi } from 'vitest';
import { render } from '@testing-library/svelte';
import { writable } from 'svelte/store';
import type { User, Organization, Batch } from '$lib/utils/types';

// Mock Supabase client
export const mockSupabase = {
  auth: {
    getUser: vi.fn(),
    signIn: vi.fn(),
    signOut: vi.fn(),
    onAuthStateChange: vi.fn(),
    getSession: vi.fn()
  },
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    single: vi.fn(),
    then: vi.fn()
  })),
  rpc: vi.fn(),
  storage: {
    from: vi.fn(() => ({
      upload: vi.fn(),
      download: vi.fn(),
      remove: vi.fn(),
      list: vi.fn(),
      getPublicUrl: vi.fn()
    }))
  }
};

// Mock stores
export const mockGlobalStore = writable({
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    fullname: 'Test User',
    avatar_url: 'https://example.com/avatar.jpg'
  } as User,
  org: {
    id: 'test-org-id',
    name: 'Test Organization',
    description: 'Test organization for testing',
    logo: 'https://example.com/logo.jpg'
  } as Organization,
  role: 'student' as 'student' | 'teacher' | 'admin',
  isOrgAdmin: false,
  isLoggedIn: true
});

export const mockCurrentBatch = writable({
  id: 'test-batch-id',
  name: 'Test Batch',
  description: 'Test batch for testing',
  organization_id: 'test-org-id',
  start_date: '2024-01-01',
  end_date: '2024-12-31',
  status: 'active',
  total_students: 25,
  active_students: 20,
  completion_rate: 75,
  engagement_score: 85
} as Batch);

// Test data factories
export class TestDataFactory {
  static createUser(overrides: Partial<User> = {}): User {
    return {
      id: 'user-' + Math.random().toString(36).substr(2, 9),
      email: '<EMAIL>',
      fullname: 'Test User',
      avatar_url: 'https://example.com/avatar.jpg',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }

  static createOrganization(overrides: Partial<Organization> = {}): Organization {
    return {
      id: 'org-' + Math.random().toString(36).substr(2, 9),
      name: 'Test Organization',
      description: 'Test organization',
      logo: 'https://example.com/logo.jpg',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }

  static createBatch(overrides: Partial<Batch> = {}): Batch {
    return {
      id: 'batch-' + Math.random().toString(36).substr(2, 9),
      name: 'Test Batch',
      description: 'Test batch',
      organization_id: 'test-org-id',
      start_date: '2024-01-01',
      end_date: '2024-12-31',
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }

  static createAnalyticsEvent(overrides: any = {}) {
    return {
      id: 'event-' + Math.random().toString(36).substr(2, 9),
      created_at: new Date().toISOString(),
      user_id: 'test-user-id',
      session_id: 'test-session-id',
      organization_id: 'test-org-id',
      batch_id: 'test-batch-id',
      event_type: 'video_play',
      event_category: 'learning',
      event_action: 'play',
      event_label: 'test-video',
      event_value: 1,
      properties: {},
      context: {},
      ...overrides
    };
  }

  static createVideoAnalytics(overrides: any = {}) {
    return {
      id: 'video-analytics-' + Math.random().toString(36).substr(2, 9),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      user_id: 'test-user-id',
      video_id: 'test-video-id',
      session_id: 'test-session-id',
      total_watch_time_seconds: 300,
      completion_percentage: 75,
      play_count: 1,
      pause_count: 2,
      seek_count: 1,
      replay_count: 0,
      speed_changes: 0,
      quality_changes: 0,
      fullscreen_toggles: 1,
      watch_segments: [{ start: 0, end: 300, duration: 300 }],
      engagement_events: [],
      device_info: { device_type: 'desktop', os: 'Windows', browser: 'Chrome' },
      network_info: { connection_type: 'wifi', effective_type: '4g' },
      last_position_seconds: 300,
      ...overrides
    };
  }
}

// Test utilities
export class TestUtils {
  static async waitFor(condition: () => boolean, timeout = 5000): Promise<void> {
    const start = Date.now();
    while (!condition() && Date.now() - start < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    if (!condition()) {
      throw new Error('Condition not met within timeout');
    }
  }

  static mockFetch(responses: Record<string, any>) {
    global.fetch = vi.fn((url: string) => {
      const response = responses[url] || responses['*'];
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve(response),
        text: () => Promise.resolve(JSON.stringify(response))
      } as Response);
    });
  }

  static mockLocalStorage() {
    const storage: Record<string, string> = {};
    global.localStorage = {
      getItem: vi.fn((key: string) => storage[key] || null),
      setItem: vi.fn((key: string, value: string) => {
        storage[key] = value;
      }),
      removeItem: vi.fn((key: string) => {
        delete storage[key];
      }),
      clear: vi.fn(() => {
        Object.keys(storage).forEach(key => delete storage[key]);
      }),
      length: 0,
      key: vi.fn()
    };
  }

  static mockSessionStorage() {
    const storage: Record<string, string> = {};
    global.sessionStorage = {
      getItem: vi.fn((key: string) => storage[key] || null),
      setItem: vi.fn((key: string, value: string) => {
        storage[key] = value;
      }),
      removeItem: vi.fn((key: string) => {
        delete storage[key];
      }),
      clear: vi.fn(() => {
        Object.keys(storage).forEach(key => delete storage[key]);
      }),
      length: 0,
      key: vi.fn()
    };
  }

  static mockWebSocket() {
    global.WebSocket = vi.fn().mockImplementation(() => ({
      send: vi.fn(),
      close: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      readyState: WebSocket.OPEN
    }));
  }

  static mockIntersectionObserver() {
    global.IntersectionObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn()
    }));
  }

  static mockResizeObserver() {
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn()
    }));
  }

  static mockMediaDevices() {
    global.navigator.mediaDevices = {
      getUserMedia: vi.fn().mockResolvedValue({
        getTracks: () => [{ stop: vi.fn() }]
      }),
      getDisplayMedia: vi.fn().mockResolvedValue({
        getTracks: () => [{ stop: vi.fn() }]
      }),
      enumerateDevices: vi.fn().mockResolvedValue([])
    } as any;
  }
}

// Performance testing utilities
export class PerformanceTestUtils {
  static measureRenderTime<T>(component: any, props: T): number {
    const start = performance.now();
    render(component, { props });
    const end = performance.now();
    return end - start;
  }

  static async measureAsyncOperation(operation: () => Promise<any>): Promise<number> {
    const start = performance.now();
    await operation();
    const end = performance.now();
    return end - start;
  }

  static createLargeDataset(size: number): any[] {
    return Array.from({ length: size }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      value: Math.random() * 100,
      created_at: new Date().toISOString()
    }));
  }
}

// Accessibility testing utilities
export class AccessibilityTestUtils {
  static checkAriaLabels(container: HTMLElement): string[] {
    const issues: string[] = [];
    const interactiveElements = container.querySelectorAll('button, input, select, textarea, a[href]');
    
    interactiveElements.forEach((element, index) => {
      const hasAriaLabel = element.hasAttribute('aria-label');
      const hasAriaLabelledBy = element.hasAttribute('aria-labelledby');
      const hasTitle = element.hasAttribute('title');
      const hasTextContent = element.textContent?.trim();
      
      if (!hasAriaLabel && !hasAriaLabelledBy && !hasTitle && !hasTextContent) {
        issues.push(`Interactive element at index ${index} lacks accessible name`);
      }
    });
    
    return issues;
  }

  static checkColorContrast(element: HTMLElement): boolean {
    const styles = window.getComputedStyle(element);
    const backgroundColor = styles.backgroundColor;
    const color = styles.color;
    
    // Simplified contrast check - in real implementation, use proper contrast calculation
    return backgroundColor !== color;
  }

  static checkKeyboardNavigation(container: HTMLElement): string[] {
    const issues: string[] = [];
    const focusableElements = container.querySelectorAll(
      'button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
    );
    
    focusableElements.forEach((element, index) => {
      if (!element.hasAttribute('tabindex') && element.tagName !== 'BUTTON' && element.tagName !== 'INPUT') {
        issues.push(`Element at index ${index} may not be keyboard accessible`);
      }
    });
    
    return issues;
  }
}

// Setup function to be called before each test
export function setupTest() {
  // Reset all mocks
  vi.clearAllMocks();
  
  // Setup DOM mocks
  TestUtils.mockLocalStorage();
  TestUtils.mockSessionStorage();
  TestUtils.mockIntersectionObserver();
  TestUtils.mockResizeObserver();
  TestUtils.mockMediaDevices();
  
  // Setup global mocks
  global.fetch = vi.fn();
  global.URL.createObjectURL = vi.fn();
  global.URL.revokeObjectURL = vi.fn();
  
  // Mock Chart.js
  vi.mock('chart.js', () => ({
    Chart: vi.fn().mockImplementation(() => ({
      destroy: vi.fn(),
      update: vi.fn(),
      render: vi.fn()
    })),
    registerables: []
  }));
  
  // Mock video player libraries
  vi.mock('@vidstack/react', () => ({
    MediaPlayer: vi.fn(),
    MediaProvider: vi.fn(),
    Poster: vi.fn(),
    Track: vi.fn()
  }));
}

// Cleanup function to be called after each test
export function cleanupTest() {
  vi.clearAllMocks();
  vi.resetAllMocks();
}

export default {
  TestDataFactory,
  TestUtils,
  PerformanceTestUtils,
  AccessibilityTestUtils,
  setupTest,
  cleanupTest,
  mockSupabase,
  mockGlobalStore,
  mockCurrentBatch
};
