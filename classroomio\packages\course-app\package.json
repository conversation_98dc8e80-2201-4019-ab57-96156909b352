{"name": "@classroomio/course-app", "version": "0.0.16", "private": false, "main": "dist/create-template.js", "scripts": {"dev:run": "pnpm build && node dist/create-template.js", "build": "vite build && node scripts/postbuild.js"}, "bin": {"@classroomio/course-app": "./dist/create-template.js"}, "repository": {"type": "git", "url": "git+https://github.com/classroomio/classroomio.git"}, "bugs": {"url": "https://github.com/classroomio/classroomio/issues"}, "homepage": "https://github.com/classroomio/classroomio/blob/main/packages/course-app#readme", "author": "ClassroomIO <<EMAIL>>", "license": "MIT", "files": ["dist", "src"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "typescript": "^5.0.0", "vite": "^5.0.3"}, "type": "module", "dependencies": {"chalk": "^5.3.0", "commander": "^12.1.0", "figlet": "^1.8.0", "fs-extra": "^11.2.0", "inquirer": "^11.1.0", "ora": "^8.1.1"}}