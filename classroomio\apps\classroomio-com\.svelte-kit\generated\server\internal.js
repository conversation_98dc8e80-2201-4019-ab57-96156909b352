
import root from '../root.svelte';
import { set_building, set_prerendering } from '__sveltekit/environment';
import { set_assets } from '__sveltekit/paths';
import { set_manifest, set_read_implementation } from '__sveltekit/server';
import { set_private_env, set_public_env, set_safe_public_env } from '../../../../../node_modules/.pnpm/@sveltejs+kit@2.21.5_@sveltejs+vite-plugin-svelte@3.1.2_svelte@4.2.20_vite@5.4.19/node_modules/@sveltejs/kit/src/runtime/shared-server.js';

export const options = {
	app_template_contains_nonce: false,
	csp: {"mode":"auto","directives":{"upgrade-insecure-requests":false,"block-all-mixed-content":false},"reportOnly":{"upgrade-insecure-requests":false,"block-all-mixed-content":false}},
	csrf_check_origin: true,
	embedded: false,
	env_public_prefix: 'PUBLIC_',
	env_private_prefix: '',
	hash_routing: false,
	hooks: null, // added lazily, via `get_hooks`
	preload_strategy: "modulepreload",
	root,
	service_worker: false,
	templates: {
		app: ({ head, body, assets, nonce, env }) => "<!doctype html>\r\n<html lang=\"en\" class=\"scroll-smooth\">\r\n\r\n<head>\r\n  <meta charset=\"utf-8\" />\r\n  <meta name=\"viewport\" content=\"width=device-width,initial-scale=1.0\" />\r\n  <meta name=\"theme-color\" content=\"#333333\" />\r\n\r\n  <!-- <title>ClassroomIO –&nbsp;The Open Source Learning Management System for Companies</title>\r\n  <meta name=\"description\" content=\"ClassroomIO\" />\r\n  <meta name=\"application-name\" content=\"ClassroomIO\" />\r\n  <meta name=\"apple-mobile-web-app-title\" content=\"ClassroomIO\" />\r\n  <meta name=\"theme-color\" content=\"#ffffff\" />\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" /> -->\r\n\r\n  <!-- Facebook Meta Tags -->\r\n  <!-- <meta property=\"og:url\" content=\"https://www.classroomio.com/\" />\r\n  <meta property=\"og:image\" itemprop=\"image\" content=\"https://brand.cdn.clsrio.com/og/classroomio-og.png\" />\r\n  <meta property=\"og:title\" content=\"ClassroomIO –&nbsp;The Open Source Learning Management System for Companies\" />\r\n  <meta property=\"og:description\"\r\n    content=\"A flexible, user-friendly platform for creating, managing, and delivering courses for companies and training organisations\" />\r\n\r\n  <meta property=\"og:type\" content=\"website\" />\r\n  <meta property=\"og:image:type\" content=\"image/png\" />\r\n  <meta property=\"og:image:width\" content=\"1920\" />\r\n  <meta property=\"og:image:height\" content=\"1080\" />\r\n  <meta property=\"og:image:secure_url\" itemprop=\"image\" content=\"https://brand.cdn.clsrio.com/og/classroomio-og.png\" />\r\n\r\n  <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n  <meta property=\"twitter:domain\" content=\"classroomio.com\" />\r\n  <meta property=\"twitter:url\" content=\"https://www.classroomio.com/\" />\r\n  <meta name=\"twitter:title\" content=\"ClassroomIO –&nbsp;The Open Source Learning Management System for Companies\" />\r\n  <meta name=\"twitter:description\"\r\n    content=\"Launch your bootcamp quichttps://brand.cdn.clsrio.com/og/classroomio-og.pngnline teaching platform.\" />\r\n  <meta name=\"twitter:image\" content=\"https://classroomio.com/classroomio-opengraph-image.png\" /> -->\r\n\r\n  <!-- <link rel=\"stylesheet\" href=\"global.css\"> -->\r\n  <link rel=\"manifest\" href=\"/manifest.json\" crossorigin=\"use-credentials\" />\r\n  <link rel=\"icon\" type=\"image/png\" href=\"/favicon.ico\" />\r\n  <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/logo-32.png\" />\r\n\r\n\r\n  <!-- <link rel=\"stylesheet\" href=\"global.css\"> -->\r\n  <link rel=\"manifest\" href=\"/manifest.json\" crossorigin=\"use-credentials\" />\r\n  <link rel=\"icon\" type=\"image/png\" href=\"/favicon.ico\" />\r\n  <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/logo-32.png\" />\r\n\r\n  <script defer src=\"https://umami.hz.classroomio.com/script.js\"\r\n    data-website-id=\"5321d227-751a-4d43-8556-f516fba60891\"></script>\r\n\r\n  <script defer type=\"text/javascript\">\r\n    window.$crisp = [];\r\n    window.CRISP_WEBSITE_ID = '69176b47-5f63-42cc-bb9b-68d4927b6276';\r\n    (function () {\r\n      d = document;\r\n      s = d.createElement('script');\r\n      s.src = 'https://client.crisp.chat/l.js';\r\n      s.async = 1;\r\n      d.getElementsByTagName('head')[0].appendChild(s);\r\n    })();\r\n  </script>\r\n\r\n  <script defer type=\"text/javascript\">\r\n    (function (C, A, L) {\r\n      let p = function (a, ar) {\r\n        a.q.push(ar);\r\n      };\r\n      let d = C.document;\r\n      C.Cal =\r\n        C.Cal ||\r\n        function () {\r\n          let cal = C.Cal;\r\n          let ar = arguments;\r\n          if (!cal.loaded) {\r\n            cal.ns = {};\r\n            cal.q = cal.q || [];\r\n            d.head.appendChild(d.createElement('script')).src = A;\r\n            cal.loaded = true;\r\n          }\r\n          if (ar[0] === L) {\r\n            const api = function () {\r\n              p(api, arguments);\r\n            };\r\n            const namespace = ar[1];\r\n            api.q = api.q || [];\r\n            typeof namespace === 'string' ? (cal.ns[namespace] = api) && p(api, ar) : p(cal, ar);\r\n            return;\r\n          }\r\n          p(cal, ar);\r\n        };\r\n    })(window, 'https://app.cal.com/embed/embed.js', 'init');\r\n    Cal('init', { origin: 'https://cal.com' });\r\n\r\n    Cal('ui', {\r\n      theme: 'light',\r\n      styles: { branding: { brandColor: '#1D4EE2' } },\r\n      hideEventTypeDetails: false,\r\n      layout: 'month_view'\r\n    });\r\n  </script>\r\n  " + head + "\r\n</head>\r\n\r\n<body data-sveltekit-preload-data=\"hover\">\r\n  <div style=\"display: contents\">" + body + "</div>\r\n</body>\r\n\r\n</html>",
		error: ({ status, message }) => "<!doctype html>\n<html lang=\"en\">\n\t<head>\n\t\t<meta charset=\"utf-8\" />\n\t\t<title>" + message + "</title>\n\n\t\t<style>\n\t\t\tbody {\n\t\t\t\t--bg: white;\n\t\t\t\t--fg: #222;\n\t\t\t\t--divider: #ccc;\n\t\t\t\tbackground: var(--bg);\n\t\t\t\tcolor: var(--fg);\n\t\t\t\tfont-family:\n\t\t\t\t\tsystem-ui,\n\t\t\t\t\t-apple-system,\n\t\t\t\t\tBlinkMacSystemFont,\n\t\t\t\t\t'Segoe UI',\n\t\t\t\t\tRoboto,\n\t\t\t\t\tOxygen,\n\t\t\t\t\tUbuntu,\n\t\t\t\t\tCantarell,\n\t\t\t\t\t'Open Sans',\n\t\t\t\t\t'Helvetica Neue',\n\t\t\t\t\tsans-serif;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\theight: 100vh;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t.error {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmax-width: 32rem;\n\t\t\t\tmargin: 0 1rem;\n\t\t\t}\n\n\t\t\t.status {\n\t\t\t\tfont-weight: 200;\n\t\t\t\tfont-size: 3rem;\n\t\t\t\tline-height: 1;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -0.05rem;\n\t\t\t}\n\n\t\t\t.message {\n\t\t\t\tborder-left: 1px solid var(--divider);\n\t\t\t\tpadding: 0 0 0 1rem;\n\t\t\t\tmargin: 0 0 0 1rem;\n\t\t\t\tmin-height: 2.5rem;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.message h1 {\n\t\t\t\tfont-weight: 400;\n\t\t\t\tfont-size: 1em;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t@media (prefers-color-scheme: dark) {\n\t\t\t\tbody {\n\t\t\t\t\t--bg: #222;\n\t\t\t\t\t--fg: #ddd;\n\t\t\t\t\t--divider: #666;\n\t\t\t\t}\n\t\t\t}\n\t\t</style>\n\t</head>\n\t<body>\n\t\t<div class=\"error\">\n\t\t\t<span class=\"status\">" + status + "</span>\n\t\t\t<div class=\"message\">\n\t\t\t\t<h1>" + message + "</h1>\n\t\t\t</div>\n\t\t</div>\n\t</body>\n</html>\n"
	},
	version_hash: "6ug7mm"
};

export async function get_hooks() {
	let handle;
	let handleFetch;
	let handleError;
	let init;
	

	let reroute;
	let transport;
	

	return {
		handle,
		handleFetch,
		handleError,
		init,
		reroute,
		transport
	};
}

export { set_assets, set_building, set_manifest, set_prerendering, set_private_env, set_public_env, set_read_implementation, set_safe_public_env };
