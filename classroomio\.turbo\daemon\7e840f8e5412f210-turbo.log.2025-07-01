2025-07-01T06:14:10.889256Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-01T06:14:22.074331Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\classroomio-com\\vite.config.js.timestamp-1751350462028-50a72cb4a8fbf.mjs"), AnchoredSystemPathBuf("apps\\dashboard"), AnchoredSystemPathBuf("apps\\classroomio-com"), AnchoredSystemPathBuf("packages\\course-app\\src\\template")}
2025-07-01T06:14:22.074542Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("courseapp"), path: AnchoredSystemPathBuf("packages\\course-app\\src\\template") }, WorkspacePackage { name: Other("dashboard"), path: AnchoredSystemPathBuf("apps\\dashboard") }, WorkspacePackage { name: Other("classroomio-com"), path: AnchoredSystemPathBuf("apps\\classroomio-com") }}))
2025-07-01T06:14:26.370227Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\dashboard")}
2025-07-01T06:14:26.370317Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("dashboard"), path: AnchoredSystemPathBuf("apps\\dashboard") }}))
2025-07-01T06:14:27.712508Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:27.713621Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\classroomio-com\\vite.config.js.timestamp-1751350462028-50a72cb4a8fbf.mjs"), AnchoredSystemPathBuf("apps\\classroomio-com")}
2025-07-01T06:14:27.713652Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("classroomio-com"), path: AnchoredSystemPathBuf("apps\\classroomio-com") }}))
2025-07-01T06:14:28.388477Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:31.224883Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\dashboard\\.svelte-kit")}
2025-07-01T06:14:31.224996Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("dashboard"), path: AnchoredSystemPathBuf("apps\\dashboard") }}))
2025-07-01T06:14:37.820230Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:37.828813Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\course-app\\src\\template"), AnchoredSystemPathBuf("apps\\classroomio-com\\.svelte-kit")}
2025-07-01T06:14:37.828869Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("courseapp"), path: AnchoredSystemPathBuf("packages\\course-app\\src\\template") }, WorkspacePackage { name: Other("classroomio-com"), path: AnchoredSystemPathBuf("apps\\classroomio-com") }}))
2025-07-01T06:14:37.829314Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:37.829711Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:38.275336Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\course-app"), AnchoredSystemPathBuf("apps\\course-app\\.svelte-kit")}
2025-07-01T06:14:38.275379Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("course-app"), path: AnchoredSystemPathBuf("apps\\course-app") }}))
2025-07-01T06:14:41.409624Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\course-app\\.svelte-kit"), AnchoredSystemPathBuf("packages\\course-app\\src\\template\\.svelte-kit"), AnchoredSystemPathBuf("packages\\course-app\\src\\template")}
2025-07-01T06:14:41.409670Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("courseapp"), path: AnchoredSystemPathBuf("packages\\course-app\\src\\template") }, WorkspacePackage { name: Other("course-app"), path: AnchoredSystemPathBuf("apps\\course-app") }}))
2025-07-01T06:14:42.629147Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:42.629377Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:42.630354Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\course-app\\src\\template\\.svelte-kit")}
2025-07-01T06:14:42.630371Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("courseapp"), path: AnchoredSystemPathBuf("packages\\course-app\\src\\template") }}))
2025-07-01T06:14:42.630600Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:45.672891Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-WcooC8"), AnchoredSystemPathBuf("apps\\api\\.wrangler"), AnchoredSystemPathBuf("apps\\api"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp")}
2025-07-01T06:14:45.672933Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:14:46.960583Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX\\strip-cf-connecting-ip-header.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX\\middleware-insertion-facade.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX\\middleware-loader.entry.ts"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp")}
2025-07-01T06:14:46.960728Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:14:46.960942Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:47.578508Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-WcooC8\\index.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-WcooC8\\index.js.map"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-WcooC8")}
2025-07-01T06:14:47.578547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:14:48.602755Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:14:48.603360Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX\\middleware-insertion-facade.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\state\\v3"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-WcooC8\\index.js.map"), AnchoredSystemPathBuf("apps\\api\\.wrangler"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\state\\v3\\cache"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX\\middleware-loader.entry.ts"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\state"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\state\\v3\\workflows"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-WcooC8"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-WcooC8\\index.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-Y4IrFX\\strip-cf-connecting-ip-header.js")}
2025-07-01T06:14:48.603385Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:14:48.603436Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:15:24.892200Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\docs\\.next"), AnchoredSystemPathBuf("apps\\docs")}
2025-07-01T06:15:24.892240Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("docs"), path: AnchoredSystemPathBuf("apps\\docs") }}))
2025-07-01T06:15:28.396262Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\docs\\.next")}
2025-07-01T06:15:28.396301Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("docs"), path: AnchoredSystemPathBuf("apps\\docs") }}))
2025-07-01T06:15:28.744751Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:15:28.984478Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\docs\\.next")}
2025-07-01T06:15:28.984516Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("docs"), path: AnchoredSystemPathBuf("apps\\docs") }}))
2025-07-01T06:15:29.621886Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:32:53.210935Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-07-01T06:32:59.823962Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\course-app\\src\\template")}
2025-07-01T06:32:59.824465Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("courseapp"), path: AnchoredSystemPathBuf("packages\\course-app\\src\\template") }}))
2025-07-01T06:33:00.344370Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\classroomio-com"), AnchoredSystemPathBuf("apps\\classroomio-com\\vite.config.js.timestamp-1751351579850-995ebfc67c49a.mjs")}
2025-07-01T06:33:00.344402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("classroomio-com"), path: AnchoredSystemPathBuf("apps\\classroomio-com") }}))
2025-07-01T06:33:01.319624Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\dashboard")}
2025-07-01T06:33:01.319666Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("dashboard"), path: AnchoredSystemPathBuf("apps\\dashboard") }}))
2025-07-01T06:33:10.632505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-p3gdr1"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp")}
2025-07-01T06:33:10.632582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:33:11.328702Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT\\middleware-insertion-facade.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT\\strip-cf-connecting-ip-header.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT\\middleware-loader.entry.ts")}
2025-07-01T06:33:11.328789Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:33:11.328845Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:11.426500Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-p3gdr1\\index.js.map"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-p3gdr1"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-p3gdr1\\index.js")}
2025-07-01T06:33:11.426552Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:33:11.713013Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:12.021795Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-p3gdr1\\index.js.map"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT\\middleware-loader.entry.ts"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT\\middleware-insertion-facade.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\bundle-YxPjQT\\strip-cf-connecting-ip-header.js"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-p3gdr1"), AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp\\dev-p3gdr1\\index.js")}
2025-07-01T06:33:12.021850Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:33:12.291041Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:13.028091Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\docs\\.next")}
2025-07-01T06:33:13.028126Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("docs"), path: AnchoredSystemPathBuf("apps\\docs") }}))
2025-07-01T06:33:14.920712Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\docs\\.next")}
2025-07-01T06:33:14.920750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("docs"), path: AnchoredSystemPathBuf("apps\\docs") }}))
2025-07-01T06:33:15.187453Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:15.732780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\docs\\.next")}
2025-07-01T06:33:15.732924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("docs"), path: AnchoredSystemPathBuf("apps\\docs") }}))
2025-07-01T06:33:15.953748Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:18.919659Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\classroomio-com\\vite.config.js.timestamp-1751351579850-995ebfc67c49a.mjs")}
2025-07-01T06:33:18.919689Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("classroomio-com"), path: AnchoredSystemPathBuf("apps\\classroomio-com") }}))
2025-07-01T06:33:19.352635Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:19.352905Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\course-app\\src\\template"), AnchoredSystemPathBuf("apps\\classroomio-com")}
2025-07-01T06:33:19.352926Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("classroomio-com"), path: AnchoredSystemPathBuf("apps\\classroomio-com") }, WorkspacePackage { name: Other("courseapp"), path: AnchoredSystemPathBuf("packages\\course-app\\src\\template") }}))
2025-07-01T06:33:19.353061Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:19.353276Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:20.619792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\dashboard")}
2025-07-01T06:33:20.619833Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("dashboard"), path: AnchoredSystemPathBuf("apps\\dashboard") }}))
2025-07-01T06:33:21.590088Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-01T06:33:35.726855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\api\\.wrangler\\tmp")}
2025-07-01T06:33:35.726897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps\\api") }}))
2025-07-01T06:33:36.630688Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
