<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { 
    currentBatch, 
    currentSubject, 
    currentChapter,
    batchActions,
    subjectActions,
    chapterActions
  } from '$lib/components/Batch/store';
  import { 
    batchService, 
    subjectService, 
    chapterService 
  } from '$lib/utils/services/batch';
  import EnhancedLessonPlayer from '$lib/components/Lesson/EnhancedLessonPlayer.svelte';
  import { PageBody, PageNav } from '$lib/components/Page';
  import { t } from '$lib/utils/functions/translations';
  import { globalStore } from '$lib/utils/store/app';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    ChevronLeft, 
    ChevronRight, 
    CheckmarkFilled, 
    CheckmarkOutline,
    BookOpen,
    Video
  } from 'carbon-icons-svelte';

  export let data;

  let batchId: string;
  let subjectId: string;
  let chapterId: string;
  let lessonId: string;
  let lesson: any = null;
  let loading = true;
  let error: string | null = null;
  let isMarkingComplete = false;

  $: canEdit = $globalStore.isOrgAdmin || $globalStore.isOrgTeacher;
  $: isStudent = $globalStore.role === 'student';
  $: userId = $globalStore.user?.id;

  onMount(async () => {
    // Extract route parameters
    batchId = $page.params.id;
    subjectId = $page.params.subjectId;
    chapterId = $page.params.chapterId;
    lessonId = $page.params.lessonId;

    await loadLessonData();
  });

  async function loadLessonData() {
    try {
      loading = true;
      error = null;

      // Load batch hierarchy if not already loaded
      if (!$currentBatch || $currentBatch.id !== batchId) {
        const batch = await batchService.getBatch(batchId);
        if (!batch) throw new Error('Batch not found');
        batchActions.setBatch(batch);
      }

      if (!$currentSubject || $currentSubject.id !== subjectId) {
        const subjects = await subjectService.getSubjects(batchId);
        const subject = subjects.find(s => s.id === subjectId);
        if (!subject) throw new Error('Subject not found');
        subjectActions.setSubject(subject);
      }

      if (!$currentChapter || $currentChapter.id !== chapterId) {
        const chapters = await chapterService.getChapters(subjectId);
        const chapter = chapters.find(c => c.id === chapterId);
        if (!chapter) throw new Error('Chapter not found');
        chapterActions.setChapter(chapter);
      }

      // Load lesson details (this would need to be implemented)
      // For now, we'll create a mock lesson object
      lesson = {
        id: lessonId,
        title: 'Sample Lesson',
        description: 'This is a sample lesson with enhanced video player',
        order: 1,
        is_active: true,
        section_id: 'mock-section-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

    } catch (err) {
      console.error('Error loading lesson data:', err);
      error = err.message || 'Failed to load lesson data';
    } finally {
      loading = false;
    }
  }

  function handleVideoCompleted(event: CustomEvent) {
    const { videoId, lessonId: completedLessonId } = event.detail;
    console.log('Video completed:', { videoId, completedLessonId });
    
    // Here you could implement automatic lesson completion
    // or show a completion notification
  }

  function handleProgressUpdated(event: CustomEvent) {
    const { videoId, progress } = event.detail;
    console.log('Progress updated:', { videoId, progress });
  }

  function handleVideoAdded(event: CustomEvent) {
    const { videoContent } = event.detail;
    console.log('Video added:', videoContent);
    // Refresh lesson data or update UI as needed
  }

  async function markLessonComplete() {
    if (!userId || isMarkingComplete) return;

    try {
      isMarkingComplete = true;
      // Implement lesson completion logic here
      // This would update the progress_tracking table
      console.log('Marking lesson complete:', lessonId);
    } catch (err) {
      console.error('Error marking lesson complete:', err);
    } finally {
      isMarkingComplete = false;
    }
  }

  function goToPreviousLesson() {
    // Implement navigation to previous lesson
    console.log('Go to previous lesson');
  }

  function goToNextLesson() {
    // Implement navigation to next lesson
    console.log('Go to next lesson');
  }

  function goToChapter() {
    goto(`/batches/${batchId}/subjects/${subjectId}/chapters/${chapterId}`);
  }

  function goToSubject() {
    goto(`/batches/${batchId}/subjects/${subjectId}`);
  }

  function goToBatch() {
    goto(`/batches/${batchId}`);
  }
</script>

<svelte:head>
  <title>
    {lesson?.title || 'Lesson'} - {$currentChapter?.title} - {$currentSubject?.name} - ClassroomIO
  </title>
</svelte:head>

{#if loading}
  <PageBody>
    <Box className="w-full">
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-400">
          {$t('lesson.loading', { default: 'Loading lesson...' })}
        </span>
      </div>
    </Box>
  </PageBody>

{:else if error}
  <PageBody>
    <Box className="w-full">
      <div class="text-center py-12">
        <div class="text-red-500 mb-4">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {$t('lesson.error.title', { default: 'Error Loading Lesson' })}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <PrimaryButton onClick={loadLessonData}>
          {$t('lesson.retry', { default: 'Try Again' })}
        </PrimaryButton>
      </div>
    </Box>
  </PageBody>

{:else if lesson}
  <PageNav title={lesson.title}>
    <!-- Breadcrumb Navigation -->
    <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
      <button on:click={goToBatch} class="hover:text-primary-600">
        {$currentBatch?.name}
      </button>
      <span>/</span>
      <button on:click={goToSubject} class="hover:text-primary-600">
        {$currentSubject?.name}
      </button>
      <span>/</span>
      <button on:click={goToChapter} class="hover:text-primary-600">
        {$currentChapter?.title}
      </button>
      <span>/</span>
      <span class="text-gray-900 dark:text-white font-medium">
        {lesson.title}
      </span>
    </div>
  </PageNav>

  <PageBody>
    <!-- Enhanced Lesson Player -->
    <EnhancedLessonPlayer
      {lessonId}
      {lesson}
      {canEdit}
      on:videoCompleted={handleVideoCompleted}
      on:progressUpdated={handleProgressUpdated}
      on:videoAdded={handleVideoAdded}
    />

    <!-- Lesson Navigation -->
    <Box className="mt-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <PrimaryButton
            variant={VARIANTS.OUTLINED}
            onClick={goToPreviousLesson}
            disabled={false}
          >
            <ChevronLeft size={20} class="mr-2" />
            {$t('lesson.previous', { default: 'Previous Lesson' })}
          </PrimaryButton>

          <PrimaryButton
            variant={VARIANTS.OUTLINED}
            onClick={goToNextLesson}
            disabled={false}
          >
            {$t('lesson.next', { default: 'Next Lesson' })}
            <ChevronRight size={20} class="ml-2" />
          </PrimaryButton>
        </div>

        <div class="flex items-center space-x-4">
          <button
            on:click={goToChapter}
            class="flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600"
          >
            <BookOpen size={20} class="mr-2" />
            {$t('lesson.back_to_chapter', { default: 'Back to Chapter' })}
          </button>

          {#if isStudent}
            <PrimaryButton
              variant={VARIANTS.CONTAINED}
              onClick={markLessonComplete}
              disabled={isMarkingComplete}
            >
              {#if isMarkingComplete}
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {:else}
                <CheckmarkFilled size={20} class="mr-2" />
              {/if}
              {$t('lesson.mark_complete', { default: 'Mark Complete' })}
            </PrimaryButton>
          {/if}
        </div>
      </div>
    </Box>
  </PageBody>
{/if}

<style>
  /* Add any custom styles here */
</style>
