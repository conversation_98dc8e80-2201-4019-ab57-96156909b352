#!/bin/bash

# ClassroomIO Production Deployment Script
# Comprehensive deployment automation with zero-downtime deployment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOY_ENV="${DEPLOY_ENV:-production}"
BACKUP_BEFORE_DEPLOY="${BACKUP_BEFORE_DEPLOY:-true}"
RUN_MIGRATIONS="${RUN_MIGRATIONS:-true}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Deployment failed with exit code $exit_code"
        if [ "${ROLLBACK_ON_FAILURE:-true}" = "true" ]; then
            log_info "Initiating rollback..."
            rollback_deployment
        fi
    fi
    exit $exit_code
}

trap cleanup EXIT

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if environment file exists
    if [ ! -f "$PROJECT_ROOT/.env.production" ]; then
        log_error "Production environment file (.env.production) not found"
        exit 1
    fi
    
    # Check if SSL certificates exist
    if [ ! -f "$PROJECT_ROOT/ssl/classroomio.crt" ] || [ ! -f "$PROJECT_ROOT/ssl/classroomio.key" ]; then
        log_warning "SSL certificates not found. HTTPS will not be available."
    fi
    
    log_success "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    log_info "Loading environment variables..."
    
    if [ -f "$PROJECT_ROOT/.env.production" ]; then
        export $(grep -v '^#' "$PROJECT_ROOT/.env.production" | xargs)
        log_success "Environment variables loaded"
    else
        log_error "Production environment file not found"
        exit 1
    fi
}

# Create backup
create_backup() {
    if [ "$BACKUP_BEFORE_DEPLOY" = "true" ]; then
        log_info "Creating backup before deployment..."
        
        local backup_timestamp=$(date +%Y%m%d_%H%M%S)
        local backup_dir="$PROJECT_ROOT/backups/$backup_timestamp"
        
        mkdir -p "$backup_dir"
        
        # Backup database
        docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" exec -T postgres \
            pg_dump -U "$POSTGRES_USER" -d "$POSTGRES_DB" > "$backup_dir/database.sql"
        
        # Backup uploaded files
        if [ -d "$PROJECT_ROOT/uploads" ]; then
            tar -czf "$backup_dir/uploads.tar.gz" -C "$PROJECT_ROOT" uploads/
        fi
        
        # Backup configuration
        cp "$PROJECT_ROOT/.env.production" "$backup_dir/"
        
        echo "$backup_timestamp" > "$PROJECT_ROOT/.last_backup"
        
        log_success "Backup created: $backup_dir"
    fi
}

# Build Docker images
build_images() {
    log_info "Building Docker images..."
    
    cd "$PROJECT_ROOT"
    
    # Build main application image
    docker build -f Dockerfile.production -t classroomio/app:latest .
    
    # Build worker image
    docker build -f Dockerfile.worker -t classroomio/worker:latest .
    
    log_success "Docker images built successfully"
}

# Run database migrations
run_migrations() {
    if [ "$RUN_MIGRATIONS" = "true" ]; then
        log_info "Running database migrations..."
        
        # Start database if not running
        docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" up -d postgres
        
        # Wait for database to be ready
        log_info "Waiting for database to be ready..."
        timeout 60 bash -c 'until docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" exec postgres pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB"; do sleep 2; done'
        
        # Run migrations
        docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" run --rm classroomio-app npm run migrate
        
        log_success "Database migrations completed"
    fi
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."
    
    cd "$PROJECT_ROOT"
    
    # Pull latest images (if using registry)
    if [ "${USE_REGISTRY:-false}" = "true" ]; then
        docker-compose -f docker-compose.production.yml pull
    fi
    
    # Start services with zero-downtime deployment
    docker-compose -f docker-compose.production.yml up -d --remove-orphans
    
    log_success "Application deployed"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    local start_time=$(date +%s)
    local timeout=$HEALTH_CHECK_TIMEOUT
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [ $elapsed -gt $timeout ]; then
            log_error "Health check timeout after ${timeout}s"
            return 1
        fi
        
        # Check main application
        if curl -f -s http://localhost/health > /dev/null; then
            log_success "Main application is healthy"
            break
        fi
        
        log_info "Waiting for application to be ready... (${elapsed}s/${timeout}s)"
        sleep 5
    done
    
    # Check individual services
    local services=("classroomio-app" "postgres" "redis" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" ps "$service" | grep -q "Up"; then
            log_success "$service is running"
        else
            log_error "$service is not running"
            return 1
        fi
    done
    
    log_success "All health checks passed"
}

# Rollback deployment
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    if [ -f "$PROJECT_ROOT/.last_backup" ]; then
        local backup_timestamp=$(cat "$PROJECT_ROOT/.last_backup")
        local backup_dir="$PROJECT_ROOT/backups/$backup_timestamp"
        
        if [ -d "$backup_dir" ]; then
            log_info "Restoring from backup: $backup_timestamp"
            
            # Stop current services
            docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" down
            
            # Restore database
            if [ -f "$backup_dir/database.sql" ]; then
                docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" up -d postgres
                sleep 10
                docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" exec -T postgres \
                    psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" < "$backup_dir/database.sql"
            fi
            
            # Restore files
            if [ -f "$backup_dir/uploads.tar.gz" ]; then
                tar -xzf "$backup_dir/uploads.tar.gz" -C "$PROJECT_ROOT"
            fi
            
            # Start services
            docker-compose -f "$PROJECT_ROOT/docker-compose.production.yml" up -d
            
            log_success "Rollback completed"
        else
            log_error "Backup directory not found: $backup_dir"
        fi
    else
        log_error "No backup information found"
    fi
}

# Cleanup old resources
cleanup_old_resources() {
    log_info "Cleaning up old resources..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove old backups (keep last 7 days)
    find "$PROJECT_ROOT/backups" -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    
    # Remove old logs (keep last 30 days)
    find "$PROJECT_ROOT/logs" -name "*.log" -mtime +30 -delete 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Send deployment notification
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
        local color="good"
        if [ "$status" = "error" ]; then
            color="danger"
        elif [ "$status" = "warning" ]; then
            color="warning"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK_URL" || true
    fi
    
    if [ -n "${EMAIL_NOTIFICATION:-}" ]; then
        echo "$message" | mail -s "ClassroomIO Deployment $status" "$EMAIL_NOTIFICATION" || true
    fi
}

# Main deployment function
main() {
    local start_time=$(date +%s)
    
    log_info "Starting ClassroomIO deployment..."
    log_info "Environment: $DEPLOY_ENV"
    log_info "Timestamp: $(date)"
    
    # Run deployment steps
    check_prerequisites
    load_environment
    create_backup
    build_images
    run_migrations
    deploy_application
    health_check
    cleanup_old_resources
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "Deployment completed successfully in ${duration}s"
    send_notification "success" "ClassroomIO deployment completed successfully in ${duration}s"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
