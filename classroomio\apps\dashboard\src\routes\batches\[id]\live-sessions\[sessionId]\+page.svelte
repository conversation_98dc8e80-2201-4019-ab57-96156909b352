<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import LiveSessionManager from '$lib/components/LiveStreaming/LiveSessionManager.svelte';
  import { securityPolicyService } from '$lib/utils/services/security';
  import type { SecurityPolicy } from '$lib/utils/types/security';
  import { 
    currentBatch, 
    batchActions 
  } from '$lib/components/Batch/store';
  import { batchService } from '$lib/utils/services/batch';
  import { PageBody, PageNav } from '$lib/components/Page';
  import { t } from '$lib/utils/functions/translations';
  import { globalStore } from '$lib/utils/store/app';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    ArrowLeft, 
    Video, 
    Warning 
  } from 'carbon-icons-svelte';

  let batchId: string;
  let sessionId: string;
  let securityPolicy: SecurityPolicy | null = null;
  let loading = true;
  let error: string | null = null;

  $: organizationId = $globalStore.org?.id;

  onMount(async () => {
    batchId = $page.params.id;
    sessionId = $page.params.sessionId;
    
    await loadBatchData();
    await loadSecurityPolicy();
  });

  async function loadBatchData() {
    try {
      if (!$currentBatch || $currentBatch.id !== batchId) {
        const batch = await batchService.getBatch(batchId);
        if (!batch) {
          throw new Error('Batch not found');
        }
        batchActions.setBatch(batch);
      }
    } catch (err) {
      console.error('Error loading batch:', err);
      error = err.message || 'Failed to load batch data';
    }
  }

  async function loadSecurityPolicy() {
    try {
      loading = true;
      error = null;

      if (organizationId) {
        // Load video protection policy for live sessions
        securityPolicy = await securityPolicyService.getPolicyByType(
          organizationId, 
          'video_protection'
        );
      }

    } catch (err) {
      console.error('Error loading security policy:', err);
      // Don't fail the entire page if security policy fails to load
      securityPolicy = null;
    } finally {
      loading = false;
    }
  }

  function goBack() {
    goto(`/batches/${batchId}/live-sessions`);
  }

  function handleSessionJoined(event: CustomEvent) {
    const { sessionId: joinedSessionId, meetingUrl } = event.detail;
    console.log('Session joined:', { joinedSessionId, meetingUrl });
    // The meeting URL will be opened in a new window by the LiveSessionManager
  }

  function handleSessionEnded(event: CustomEvent) {
    const { sessionId: endedSessionId } = event.detail;
    console.log('Session ended:', endedSessionId);
    // Optionally redirect back to sessions list
    // goto(`/batches/${batchId}/live-sessions`);
  }

  function handleParticipantUpdate(event: CustomEvent) {
    const { participant } = event.detail;
    console.log('Participant updated:', participant);
  }
</script>

<svelte:head>
  <title>
    {$t('live_session.title', { default: 'Live Session' })} - {$currentBatch?.name} - ClassroomIO
  </title>
</svelte:head>

<PageNav title={$t('live_session.title', { default: 'Live Session' })}>
  <div class="flex items-center space-x-4">
    <PrimaryButton
      variant={VARIANTS.OUTLINED}
      onClick={goBack}
    >
      <ArrowLeft size={20} class="mr-2" />
      {$t('live_session.back_to_sessions', { default: 'Back to Sessions' })}
    </PrimaryButton>
  </div>
</PageNav>

<PageBody>
  {#if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('live_session.loading', { default: 'Loading Live Session' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('live_session.error', { default: 'Error Loading Session' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadBatchData}>
        {$t('live_session.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else}
    <!-- Live Session Manager -->
    <LiveSessionManager
      {batchId}
      {sessionId}
      {securityPolicy}
      on:sessionJoined={handleSessionJoined}
      on:sessionEnded={handleSessionEnded}
      on:participantUpdate={handleParticipantUpdate}
    />

    <!-- Additional Information -->
    <div class="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Session Guidelines -->
      <Box className="lg:col-span-2">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {$t('live_session.guidelines', { default: 'Session Guidelines' })}
        </h3>
        
        <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
          <div class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>{$t('live_session.guideline_1', { default: 'Ensure you have a stable internet connection before joining' })}</span>
          </div>
          
          <div class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>{$t('live_session.guideline_2', { default: 'Test your camera and microphone beforehand' })}</span>
          </div>
          
          <div class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>{$t('live_session.guideline_3', { default: 'Join from a quiet environment to minimize distractions' })}</span>
          </div>
          
          <div class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>{$t('live_session.guideline_4', { default: 'Mute your microphone when not speaking' })}</span>
          </div>
          
          <div class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>{$t('live_session.guideline_5', { default: 'Use the chat feature for questions and interactions' })}</span>
          </div>
          
          <div class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>{$t('live_session.guideline_6', { default: 'Be respectful and professional during the session' })}</span>
          </div>
        </div>
      </Box>

      <!-- Technical Requirements -->
      <Box>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {$t('live_session.tech_requirements', { default: 'Technical Requirements' })}
        </h3>
        
        <div class="space-y-3 text-sm">
          <div>
            <h4 class="font-medium text-gray-900 dark:text-white mb-1">
              {$t('live_session.browser', { default: 'Browser' })}
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
              {$t('live_session.browser_req', { default: 'Chrome, Firefox, Safari, or Edge (latest version)' })}
            </p>
          </div>
          
          <div>
            <h4 class="font-medium text-gray-900 dark:text-white mb-1">
              {$t('live_session.internet', { default: 'Internet Speed' })}
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
              {$t('live_session.internet_req', { default: 'Minimum 1 Mbps upload/download' })}
            </p>
          </div>
          
          <div>
            <h4 class="font-medium text-gray-900 dark:text-white mb-1">
              {$t('live_session.hardware', { default: 'Hardware' })}
            </h4>
            <p class="text-gray-600 dark:text-gray-400">
              {$t('live_session.hardware_req', { default: 'Webcam and microphone (built-in or external)' })}
            </p>
          </div>
          
          {#if securityPolicy?.settings.device_verification_required}
            <div>
              <h4 class="font-medium text-gray-900 dark:text-white mb-1">
                {$t('live_session.security', { default: 'Security' })}
              </h4>
              <p class="text-gray-600 dark:text-gray-400">
                {$t('live_session.security_req', { default: 'Device verification required for this session' })}
              </p>
            </div>
          {/if}
        </div>
      </Box>
    </div>

    <!-- Troubleshooting -->
    <Box className="mt-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        {$t('live_session.troubleshooting', { default: 'Troubleshooting' })}
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
        <div>
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">
            {$t('live_session.audio_issues', { default: 'Audio Issues' })}
          </h4>
          <ul class="space-y-1 text-gray-600 dark:text-gray-400">
            <li>• {$t('live_session.audio_fix_1', { default: 'Check microphone permissions in browser' })}</li>
            <li>• {$t('live_session.audio_fix_2', { default: 'Ensure microphone is not muted' })}</li>
            <li>• {$t('live_session.audio_fix_3', { default: 'Try refreshing the page' })}</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">
            {$t('live_session.video_issues', { default: 'Video Issues' })}
          </h4>
          <ul class="space-y-1 text-gray-600 dark:text-gray-400">
            <li>• {$t('live_session.video_fix_1', { default: 'Check camera permissions in browser' })}</li>
            <li>• {$t('live_session.video_fix_2', { default: 'Ensure camera is not being used by another app' })}</li>
            <li>• {$t('live_session.video_fix_3', { default: 'Try switching to a different browser' })}</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">
            {$t('live_session.connection_issues', { default: 'Connection Issues' })}
          </h4>
          <ul class="space-y-1 text-gray-600 dark:text-gray-400">
            <li>• {$t('live_session.connection_fix_1', { default: 'Check your internet connection' })}</li>
            <li>• {$t('live_session.connection_fix_2', { default: 'Close other bandwidth-heavy applications' })}</li>
            <li>• {$t('live_session.connection_fix_3', { default: 'Try joining from a different network' })}</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">
            {$t('live_session.general_issues', { default: 'General Issues' })}
          </h4>
          <ul class="space-y-1 text-gray-600 dark:text-gray-400">
            <li>• {$t('live_session.general_fix_1', { default: 'Clear browser cache and cookies' })}</li>
            <li>• {$t('live_session.general_fix_2', { default: 'Disable browser extensions temporarily' })}</li>
            <li>• {$t('live_session.general_fix_3', { default: 'Contact support if issues persist' })}</li>
          </ul>
        </div>
      </div>
    </Box>
  {/if}
</PageBody>
