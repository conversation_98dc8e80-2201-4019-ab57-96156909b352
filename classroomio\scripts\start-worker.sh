#!/bin/bash

# ClassroomIO Worker Startup Script
# Handles background job processing and scheduled tasks

set -euo pipefail

# Configuration
WORKER_NAME="ClassroomIO-Worker"
WORKER_CONCURRENCY="${WORKER_CONCURRENCY:-5}"
WORKER_TIMEOUT="${WORKER_TIMEOUT:-300000}"
NODE_ENV="${NODE_ENV:-production}"
HEALTH_CHECK_PORT="${WORKER_PORT:-9464}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[WORKER-INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[WORKER-SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WORKER-WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[WORKER-ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Signal handlers
cleanup() {
    local exit_code=$?
    log_info "Received shutdown signal, cleaning up worker processes..."
    
    if [ -n "${WORKER_PID:-}" ]; then
        log_info "Stopping worker (PID: $WORKER_PID)..."
        kill -TERM "$WORKER_PID" 2>/dev/null || true
        
        # Wait for graceful shutdown
        local count=0
        while kill -0 "$WORKER_PID" 2>/dev/null && [ $count -lt 30 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # Force kill if still running
        if kill -0 "$WORKER_PID" 2>/dev/null; then
            log_warning "Force killing worker..."
            kill -KILL "$WORKER_PID" 2>/dev/null || true
        fi
    fi
    
    # Stop health check server if running
    if [ -n "${HEALTH_SERVER_PID:-}" ]; then
        kill -TERM "$HEALTH_SERVER_PID" 2>/dev/null || true
    fi
    
    log_info "Worker cleanup completed"
    exit $exit_code
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT SIGQUIT

# Pre-flight checks for worker
worker_preflight_checks() {
    log_info "Running worker pre-flight checks..."
    
    # Check Node.js version
    local node_version=$(node --version)
    log_info "Node.js version: $node_version"
    
    # Check environment variables
    local required_vars=("NODE_ENV" "REDIS_URL" "DATABASE_URL")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "Missing required environment variables: ${missing_vars[*]}"
        exit 1
    fi
    
    # Check Redis connectivity
    log_info "Checking Redis connectivity..."
    if ! timeout 10 bash -c "echo 'PING' | nc -w 1 \$(echo \$REDIS_URL | sed 's/redis:\/\///g' | cut -d'@' -f2 | cut -d':' -f1) \$(echo \$REDIS_URL | sed 's/redis:\/\///g' | cut -d'@' -f2 | cut -d':' -f2 | cut -d'/' -f1) 2>/dev/null" 2>/dev/null; then
        log_warning "Redis connectivity check failed (continuing anyway)"
    else
        log_success "Redis is accessible"
    fi
    
    log_success "Worker pre-flight checks completed"
}

# Start health check server
start_health_server() {
    log_info "Starting worker health check server on port $HEALTH_CHECK_PORT..."
    
    # Create simple health check server
    node -e "
        const http = require('http');
        const { performWorkerHealthChecks } = require('./health-check.js');
        
        const server = http.createServer(async (req, res) => {
            if (req.url === '/health' || req.url === '/') {
                try {
                    const health = await performWorkerHealthChecks();
                    res.writeHead(health.status === 'healthy' ? 200 : 503, {
                        'Content-Type': 'application/json'
                    });
                    res.end(JSON.stringify(health, null, 2));
                } catch (error) {
                    res.writeHead(500, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ status: 'error', message: error.message }));
                }
            } else {
                res.writeHead(404, { 'Content-Type': 'text/plain' });
                res.end('Not Found');
            }
        });
        
        server.listen($HEALTH_CHECK_PORT, '0.0.0.0', () => {
            console.log('Worker health server listening on port $HEALTH_CHECK_PORT');
        });
        
        process.on('SIGTERM', () => {
            server.close(() => process.exit(0));
        });
    " &
    
    HEALTH_SERVER_PID=$!
    log_success "Health check server started with PID: $HEALTH_SERVER_PID"
}

# Start worker processes
start_worker() {
    log_info "Starting $WORKER_NAME..."
    log_info "Environment: $NODE_ENV"
    log_info "Concurrency: $WORKER_CONCURRENCY"
    log_info "Timeout: ${WORKER_TIMEOUT}ms"
    
    # Create worker script if it doesn't exist
    if [ ! -f "apps/worker/index.js" ]; then
        log_info "Creating worker entry point..."
        mkdir -p apps/worker
        cat > apps/worker/index.js << 'EOF'
// ClassroomIO Background Worker
const { Worker } = require('worker_threads');

console.log('ClassroomIO Worker starting...');

// Mock worker implementation
class JobProcessor {
    constructor(concurrency = 5) {
        this.concurrency = concurrency;
        this.workers = [];
        this.jobQueue = [];
        this.processing = false;
    }
    
    async start() {
        console.log(`Starting ${this.concurrency} worker threads...`);
        
        // Simulate job processing
        setInterval(() => {
            this.processJobs();
        }, 5000);
        
        console.log('Worker is ready to process jobs');
    }
    
    processJobs() {
        // Mock job processing
        const jobTypes = ['email', 'video-processing', 'analytics', 'notifications'];
        const randomJob = jobTypes[Math.floor(Math.random() * jobTypes.length)];
        
        console.log(`Processing ${randomJob} job...`);
        
        // Simulate job completion
        setTimeout(() => {
            console.log(`Completed ${randomJob} job`);
        }, Math.random() * 3000);
    }
    
    async stop() {
        console.log('Stopping worker...');
        // Cleanup logic here
    }
}

const processor = new JobProcessor(process.env.WORKER_CONCURRENCY || 5);

// Start processing
processor.start().catch(console.error);

// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    await processor.stop();
    process.exit(0);
});

process.on('SIGINT', async () => {
    console.log('Received SIGINT, shutting down gracefully...');
    await processor.stop();
    process.exit(0);
});

// Keep process alive
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
EOF
    fi
    
    # Start the worker
    node apps/worker/index.js &
    WORKER_PID=$!
    
    log_info "Worker started with PID: $WORKER_PID"
    
    # Wait for worker to be ready
    sleep 3
    
    # Check if worker is still running
    if ! kill -0 "$WORKER_PID" 2>/dev/null; then
        log_error "Worker failed to start"
        exit 1
    fi
    
    log_success "$WORKER_NAME is ready to process jobs"
}

# Monitor worker health
monitor_worker() {
    log_info "Monitoring worker health..."
    
    while true; do
        # Check if worker process is still running
        if ! kill -0 "$WORKER_PID" 2>/dev/null; then
            log_error "Worker process died unexpectedly"
            exit 1
        fi
        
        # Periodic health check
        if ! node health-check.js >/dev/null 2>&1; then
            log_warning "Worker health check failed"
            # Don't exit immediately, just log the warning
        fi
        
        sleep 30
    done
}

# Main execution
main() {
    log_info "Starting $WORKER_NAME..."
    
    # Run pre-flight checks
    worker_preflight_checks
    
    # Start health check server
    start_health_server
    
    # Start worker
    start_worker
    
    # Monitor worker
    monitor_worker
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
