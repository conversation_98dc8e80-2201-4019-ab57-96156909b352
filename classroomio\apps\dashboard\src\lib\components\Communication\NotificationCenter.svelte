<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { Notification } from '$lib/utils/types/communication';
  import { notificationService } from '$lib/utils/services/notifications';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Notification as NotificationIcon,
    CheckmarkFilled,
    Close,
    Filter,
    Settings,
    Email,
    Phone,
    Mobile,
    Chat,
    Warning,
    Information,
    CheckmarkOutline,
    TrashCan
  } from 'carbon-icons-svelte';

  export let className: string = '';

  const dispatch = createEventDispatcher<{
    notificationRead: { notificationId: string };
    notificationDeleted: { notificationId: string };
  }>();

  let notifications = writable<Notification[]>([]);
  let unreadCount = writable<number>(0);
  let loading = true;
  let error: string | null = null;
  let selectedFilter = 'all';
  let selectedChannel = 'all';

  $: userId = $globalStore.user?.id;

  onMount(async () => {
    await loadNotifications();
    await loadUnreadCount();
  });

  async function loadNotifications() {
    if (!userId) return;

    try {
      loading = true;
      error = null;

      const filters: any = {};
      if (selectedChannel !== 'all') filters.channel = selectedChannel;
      if (selectedFilter === 'unread') filters.is_read = false;
      if (selectedFilter === 'read') filters.is_read = true;

      const notificationsData = await notificationService.getUserNotifications(userId, filters);
      notifications.set(notificationsData);

    } catch (err) {
      console.error('Error loading notifications:', err);
      error = err.message || 'Failed to load notifications';
    } finally {
      loading = false;
    }
  }

  async function loadUnreadCount() {
    if (!userId) return;

    try {
      const count = await notificationService.getUnreadCount(userId);
      unreadCount.set(count);
    } catch (err) {
      console.error('Error loading unread count:', err);
    }
  }

  async function markAsRead(notificationId: string) {
    try {
      await notificationService.markAsRead(notificationId);
      
      // Update local state
      notifications.update(current => 
        current.map(n => n.id === notificationId ? { ...n, is_read: true, read_at: new Date().toISOString() } : n)
      );
      
      await loadUnreadCount();
      dispatch('notificationRead', { notificationId });

    } catch (err) {
      console.error('Error marking notification as read:', err);
      error = err.message || 'Failed to mark notification as read';
    }
  }

  async function markAllAsRead() {
    if (!userId) return;

    try {
      await notificationService.markAllAsRead(userId);
      
      // Update local state
      notifications.update(current => 
        current.map(n => ({ ...n, is_read: true, read_at: new Date().toISOString() }))
      );
      
      unreadCount.set(0);

    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      error = err.message || 'Failed to mark all notifications as read';
    }
  }

  async function deleteNotification(notificationId: string) {
    try {
      await notificationService.deleteNotification(notificationId);
      
      // Update local state
      notifications.update(current => current.filter(n => n.id !== notificationId));
      
      await loadUnreadCount();
      dispatch('notificationDeleted', { notificationId });

    } catch (err) {
      console.error('Error deleting notification:', err);
      error = err.message || 'Failed to delete notification';
    }
  }

  function getNotificationIcon(type: string, channel: string) {
    if (channel === 'email') return Email;
    if (channel === 'sms') return Phone;
    if (channel === 'push') return Mobile;
    if (channel === 'whatsapp' || channel === 'telegram') return Chat;
    
    switch (type) {
      case 'warning':
      case 'error':
        return Warning;
      case 'success':
        return CheckmarkOutline;
      default:
        return Information;
    }
  }

  function getNotificationColor(type: string, isRead: boolean) {
    const opacity = isRead ? 'opacity-60' : '';
    
    switch (type) {
      case 'warning':
      case 'error':
        return `text-red-600 ${opacity}`;
      case 'success':
        return `text-green-600 ${opacity}`;
      case 'info':
        return `text-blue-600 ${opacity}`;
      default:
        return `text-gray-600 ${opacity}`;
    }
  }

  function formatTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  }

  function handleNotificationClick(notification: Notification) {
    if (!notification.is_read) {
      markAsRead(notification.id);
    }
    
    if (notification.action_url) {
      window.open(notification.action_url, '_blank');
    }
  }

  $: filteredNotifications = $notifications;
</script>

<div class="notification-center {className}">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div class="flex items-center">
      <NotificationIcon size={24} class="text-primary-600 mr-3" />
      <div>
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('notifications.title', { default: 'Notifications' })}
        </h2>
        {#if $unreadCount > 0}
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {$unreadCount} {$t('notifications.unread', { default: 'unread' })}
          </p>
        {/if}
      </div>
    </div>

    <div class="flex items-center space-x-2">
      {#if $unreadCount > 0}
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={markAllAsRead}
          size="sm"
        >
          <CheckmarkFilled size={16} class="mr-1" />
          {$t('notifications.mark_all_read', { default: 'Mark All Read' })}
        </PrimaryButton>
      {/if}
      
      <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
        <Settings size={20} />
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div class="flex items-center space-x-4 mb-6">
    <select 
      bind:value={selectedFilter}
      on:change={loadNotifications}
      class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
    >
      <option value="all">{$t('notifications.all', { default: 'All' })}</option>
      <option value="unread">{$t('notifications.unread_only', { default: 'Unread' })}</option>
      <option value="read">{$t('notifications.read_only', { default: 'Read' })}</option>
    </select>

    <select 
      bind:value={selectedChannel}
      on:change={loadNotifications}
      class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
    >
      <option value="all">{$t('notifications.all_channels', { default: 'All Channels' })}</option>
      <option value="in_app">{$t('notifications.in_app', { default: 'In-App' })}</option>
      <option value="email">{$t('notifications.email', { default: 'Email' })}</option>
      <option value="sms">{$t('notifications.sms', { default: 'SMS' })}</option>
      <option value="push">{$t('notifications.push', { default: 'Push' })}</option>
      <option value="whatsapp">{$t('notifications.whatsapp', { default: 'WhatsApp' })}</option>
      <option value="telegram">{$t('notifications.telegram', { default: 'Telegram' })}</option>
    </select>
  </div>

  <!-- Notifications List -->
  {#if loading}
    <div class="space-y-3">
      {#each Array(5) as _}
        <Box className="animate-pulse">
          <div class="p-4">
            <div class="flex items-start space-x-3">
              <div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </Box>
      {/each}
    </div>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('notifications.error', { default: 'Error Loading Notifications' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadNotifications}>
        {$t('notifications.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if filteredNotifications.length === 0}
    <Box className="text-center py-12">
      <NotificationIcon size={48} class="text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {$t('notifications.no_notifications', { default: 'No Notifications' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {$t('notifications.no_notifications_desc', { default: 'You\'re all caught up!' })}
      </p>
    </Box>

  {:else}
    <div class="space-y-2">
      {#each filteredNotifications as notification (notification.id)}
        <Box className="cursor-pointer hover:shadow-md transition-shadow {!notification.is_read ? 'border-l-4 border-l-primary-500' : ''}" 
             onClick={() => handleNotificationClick(notification)}>
          <div class="p-4">
            <div class="flex items-start justify-between">
              <div class="flex items-start space-x-3 flex-1">
                <div class="flex-shrink-0">
                  <svelte:component 
                    this={getNotificationIcon(notification.notification_type, notification.channel)} 
                    size={20} 
                    class={getNotificationColor(notification.notification_type, notification.is_read)}
                  />
                </div>
                
                <div class="flex-1 min-w-0">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white {notification.is_read ? 'opacity-60' : ''}">
                    {notification.title}
                  </h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 {notification.is_read ? 'opacity-60' : ''}">
                    {notification.content}
                  </p>
                  <div class="flex items-center space-x-2 mt-2">
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {formatTime(notification.created_at)}
                    </span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">•</span>
                    <span class="text-xs text-gray-500 dark:text-gray-400 capitalize">
                      {notification.channel}
                    </span>
                    {#if notification.delivery_status === 'failed'}
                      <span class="text-xs text-red-500">Failed</span>
                    {:else if notification.delivery_status === 'delivered'}
                      <span class="text-xs text-green-500">Delivered</span>
                    {/if}
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-2 ml-4">
                {#if !notification.is_read}
                  <button
                    on:click|stopPropagation={() => markAsRead(notification.id)}
                    class="text-gray-400 hover:text-green-600"
                    title={$t('notifications.mark_read', { default: 'Mark as read' })}
                  >
                    <CheckmarkFilled size={16} />
                  </button>
                {/if}
                
                <button
                  on:click|stopPropagation={() => deleteNotification(notification.id)}
                  class="text-gray-400 hover:text-red-600"
                  title={$t('notifications.delete', { default: 'Delete' })}
                >
                  <TrashCan size={16} />
                </button>
              </div>
            </div>
          </div>
        </Box>
      {/each}
    </div>
  {/if}
</div>
