# Production Docker Compose Configuration
# Comprehensive production deployment setup for ClassroomIO

version: '3.8'

services:
  # Main Application
  classroomio-app:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        - NODE_ENV=production
    image: classroomio/app:latest
    container_name: classroomio-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=${REDIS_URL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET}
      - CLOUDFLARE_STREAM_API_TOKEN=${CLOUDFLARE_STREAM_API_TOKEN}
      - CLOUDFLARE_ACCOUNT_ID=${CLOUDFLARE_ACCOUNT_ID}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DAILY_API_KEY=${DAILY_API_KEY}
      - METABASE_URL=${METABASE_URL}
      - METABASE_API_KEY=${METABASE_API_KEY}
    volumes:
      - app-uploads:/app/uploads
      - app-logs:/app/logs
    networks:
      - classroomio-network
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: classroomio-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    networks:
      - classroomio-network
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: classroomio-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
    networks:
      - classroomio-network
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: classroomio-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - classroomio-network
    depends_on:
      - classroomio-app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Metabase Analytics
  metabase:
    image: metabase/metabase:latest
    container_name: classroomio-metabase
    restart: unless-stopped
    environment:
      - MB_DB_TYPE=postgres
      - MB_DB_DBNAME=${METABASE_DB_NAME}
      - MB_DB_PORT=5432
      - MB_DB_USER=${METABASE_DB_USER}
      - MB_DB_PASS=${METABASE_DB_PASS}
      - MB_DB_HOST=postgres
      - JAVA_TIMEZONE=${TIMEZONE}
    volumes:
      - metabase-data:/metabase-data
    networks:
      - classroomio-network
    ports:
      - "3001:3000"
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s

  # Background Job Worker
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    image: classroomio/worker:latest
    container_name: classroomio-worker
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - app-logs:/app/logs
    networks:
      - classroomio-network
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "node", "health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: classroomio-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - classroomio-network
    ports:
      - "9090:9090"

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: classroomio-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - classroomio-network
    ports:
      - "3002:3000"
    depends_on:
      - prometheus

  # Log Aggregation with Loki
  loki:
    image: grafana/loki:latest
    container_name: classroomio-loki
    restart: unless-stopped
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./monitoring/loki-config.yaml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    networks:
      - classroomio-network
    ports:
      - "3100:3100"

  # Log Shipping with Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: classroomio-promtail
    restart: unless-stopped
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./monitoring/promtail-config.yml:/etc/promtail/config.yml:ro
      - app-logs:/var/log/app:ro
      - nginx-logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    networks:
      - classroomio-network
    depends_on:
      - loki

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: classroomio-backup
    restart: "no"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - BACKUP_S3_BUCKET=${BACKUP_S3_BUCKET}
    volumes:
      - ./scripts/backup.sh:/backup.sh:ro
      - backup-data:/backup
    networks:
      - classroomio-network
    depends_on:
      - postgres
    entrypoint: ["/backup.sh"]

networks:
  classroomio-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  metabase-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  loki-data:
    driver: local
  app-uploads:
    driver: local
  app-logs:
    driver: local
  nginx-logs:
    driver: local
  backup-data:
    driver: local
