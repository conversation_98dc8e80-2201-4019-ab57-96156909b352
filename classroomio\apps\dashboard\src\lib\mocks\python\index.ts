export const PYTHON_IDS: { [key: string]: string } = {
  PYTHON_SYNTAX: 'PYTHON_SYNTAX',
  PYTHON_COMMENTS: 'PYTHON_COMMENTS',
  PYTHON_VARIABLES: 'PYTHON_VARIABLES',
  PYTHON_DATATYPES: 'PYTHON_DATATYPES',
  PYTHON_NUMBERS: 'PYTHON_NUMBERS',
  PYTHON_CASTING: 'PYTHON_CASTING',
  PYTHON_STRINGS: 'PYTHON_STRINGS',
  PYTHON_BOOLEANS: 'PYTHON_BOOLEANS',
  PYTHON_OPERATORS: 'PYTHON_OPERATORS',
  PYTHON_LISTS: 'PYTHON_LISTS',
  PYTHON_TUPLES: 'PYTHON_TUPLES',
  PYTHON_SETS: 'PYTHON_SETS',
  PYTHON_DICTIONARIES: 'PYTHON_DICTIONARIES',
  PYTHON_CONDITIONALS: 'PYTHON_CONDITIONALS',
  PYTHON_WHILELOOPS: 'PYTHON_WHILELOOPS',
  PYTHON_FORLOOPS: 'PYTHON_FORLOOPS',
  PYTHON_FUNCTIONS: 'PYTHON_FUNCTIONS',
  PYTHON_LAMBDA: 'PYTHON_LAMBDA',
  PYTHON_ARRAYS: 'PYTHON_ARRAYS',
  PYTHON_CLASSES: 'PYTHON_CLASSES'
};

export const PYTHON_TEMPLATES = {
  [PYTHON_IDS.PYTHON_SYNTAX]: async () => (await import('./001_python_syntax')).default,
  [PYTHON_IDS.PYTHON_COMMENTS]: async () => (await import('./002_python_comments')).default,
  [PYTHON_IDS.PYTHON_VARIABLES]: async () => (await import('./003_python_variables')).default,
  [PYTHON_IDS.PYTHON_DATATYPES]: async () => (await import('./004_python_dataTypes')).default,
  [PYTHON_IDS.PYTHON_NUMBERS]: async () => (await import('./005_python_numbers')).default,
  [PYTHON_IDS.PYTHON_CASTING]: async () => (await import('./006_python_casting')).default,
  [PYTHON_IDS.PYTHON_STRINGS]: async () => (await import('./007_python_strings')).default,
  [PYTHON_IDS.PYTHON_BOOLEANS]: async () => (await import('./008_python_booleans')).default,
  [PYTHON_IDS.PYTHON_OPERATORS]: async () => (await import('./009_python_operators')).default,
  [PYTHON_IDS.PYTHON_LISTS]: async () => (await import('./010_python_lists')).default,
  [PYTHON_IDS.PYTHON_TUPLES]: async () => (await import('./011_python_tuples')).default,
  [PYTHON_IDS.PYTHON_SETS]: async () => (await import('./012_python_sets')).default,
  [PYTHON_IDS.PYTHON_DICTIONARIES]: async () => (await import('./013_python_dictionaries')).default,
  [PYTHON_IDS.PYTHON_CONDITIONALS]: async () => (await import('./014_python_conditionals')).default,
  [PYTHON_IDS.PYTHON_WHILELOOPS]: async () => (await import('./015_python_whileLoops')).default,
  [PYTHON_IDS.PYTHON_FORLOOPS]: async () => (await import('./016_python_forLoops')).default,
  [PYTHON_IDS.PYTHON_FUNCTIONS]: async () => (await import('./017_python_functions')).default,
  [PYTHON_IDS.PYTHON_LAMBDA]: async () => (await import('./018_python_lambda')).default,
  [PYTHON_IDS.PYTHON_ARRAYS]: async () => (await import('./019_python_arrays')).default,
  [PYTHON_IDS.PYTHON_CLASSES]: async () => (await import('./020_python_classes')).default
};
