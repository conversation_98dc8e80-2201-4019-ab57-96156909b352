// Analytics Services
// Comprehensive analytics and reporting service layer

import { supabase } from '$lib/utils/functions/supabase';
import type {
  AnalyticsEvent,
  LearningAnalytics,
  VideoAnalytics,
  AssessmentAnalytics,
  CommunicationAnalytics,
  LiveSessionAnalytics,
  SystemAnalytics,
  DashboardConfig,
  ReportTemplate,
  ScheduledReport,
  AnalyticsCache,
  StudentEngagementMetrics,
  BatchPerformanceMetrics,
  RealTimeMetric,
  LiveDashboardData
} from '$lib/utils/types/analytics';

export class AnalyticsService {
  // Event Tracking
  async trackEvent(eventData: Partial<AnalyticsEvent>): Promise<string> {
    const { data, error } = await supabase.rpc('track_analytics_event', {
      p_user_id: eventData.user_id,
      p_session_id: eventData.session_id,
      p_organization_id: eventData.organization_id,
      p_batch_id: eventData.batch_id,
      p_event_type: eventData.event_type,
      p_event_category: eventData.event_category,
      p_event_action: eventData.event_action,
      p_event_label: eventData.event_label,
      p_event_value: eventData.event_value,
      p_properties: eventData.properties || {},
      p_context: eventData.context || {}
    });

    if (error) throw error;
    return data;
  }

  async getEvents(
    organizationId: string,
    filters: {
      user_id?: string;
      batch_id?: string;
      event_type?: string;
      event_category?: string;
      date_from?: string;
      date_to?: string;
      limit?: number;
    } = {}
  ): Promise<AnalyticsEvent[]> {
    let query = supabase
      .from('analytics_events')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (filters.user_id) query = query.eq('user_id', filters.user_id);
    if (filters.batch_id) query = query.eq('batch_id', filters.batch_id);
    if (filters.event_type) query = query.eq('event_type', filters.event_type);
    if (filters.event_category) query = query.eq('event_category', filters.event_category);
    if (filters.date_from) query = query.gte('created_at', filters.date_from);
    if (filters.date_to) query = query.lte('created_at', filters.date_to);
    if (filters.limit) query = query.limit(filters.limit);

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  // Learning Analytics
  async updateLearningAnalytics(
    userId: string,
    organizationId: string,
    batchId: string,
    metrics: {
      subject_id?: string;
      chapter_id?: string;
      lesson_id?: string;
      study_time_minutes?: number;
      video_watch_time_minutes?: number;
      videos_completed?: number;
      assignments_completed?: number;
      quizzes_completed?: number;
      forum_posts?: number;
      doubts_submitted?: number;
      live_sessions_attended?: number;
    }
  ): Promise<void> {
    const { error } = await supabase.rpc('update_learning_analytics', {
      p_user_id: userId,
      p_organization_id: organizationId,
      p_batch_id: batchId,
      p_subject_id: metrics.subject_id,
      p_chapter_id: metrics.chapter_id,
      p_lesson_id: metrics.lesson_id,
      p_study_time_minutes: metrics.study_time_minutes || 0,
      p_video_watch_time_minutes: metrics.video_watch_time_minutes || 0,
      p_videos_completed: metrics.videos_completed || 0,
      p_assignments_completed: metrics.assignments_completed || 0,
      p_quizzes_completed: metrics.quizzes_completed || 0,
      p_forum_posts: metrics.forum_posts || 0,
      p_doubts_submitted: metrics.doubts_submitted || 0,
      p_live_sessions_attended: metrics.live_sessions_attended || 0
    });

    if (error) throw error;
  }

  async getLearningAnalytics(
    userId: string,
    batchId: string,
    filters: {
      subject_id?: string;
      date_from?: string;
      date_to?: string;
    } = {}
  ): Promise<LearningAnalytics[]> {
    let query = supabase
      .from('learning_analytics')
      .select('*')
      .eq('user_id', userId)
      .eq('batch_id', batchId)
      .order('date', { ascending: false });

    if (filters.subject_id) query = query.eq('subject_id', filters.subject_id);
    if (filters.date_from) query = query.gte('date', filters.date_from);
    if (filters.date_to) query = query.lte('date', filters.date_to);

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async getStudentEngagementMetrics(
    userId: string,
    batchId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<StudentEngagementMetrics> {
    const { data, error } = await supabase.rpc('get_student_engagement_metrics', {
      p_user_id: userId,
      p_batch_id: batchId,
      p_date_from: dateFrom,
      p_date_to: dateTo
    });

    if (error) throw error;
    return data || {};
  }

  async getBatchPerformanceMetrics(
    batchId: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<BatchPerformanceMetrics> {
    const { data, error } = await supabase.rpc('get_batch_performance_analytics', {
      p_batch_id: batchId,
      p_date_from: dateFrom,
      p_date_to: dateTo
    });

    if (error) throw error;
    return data || {};
  }

  // Video Analytics
  async trackVideoAnalytics(videoData: Partial<VideoAnalytics>): Promise<VideoAnalytics> {
    const { data, error } = await supabase
      .from('video_analytics')
      .upsert({
        user_id: videoData.user_id,
        video_id: videoData.video_id,
        session_id: videoData.session_id,
        total_watch_time_seconds: videoData.total_watch_time_seconds || 0,
        completion_percentage: videoData.completion_percentage || 0,
        play_count: videoData.play_count || 0,
        pause_count: videoData.pause_count || 0,
        seek_count: videoData.seek_count || 0,
        replay_count: videoData.replay_count || 0,
        speed_changes: videoData.speed_changes || 0,
        quality_changes: videoData.quality_changes || 0,
        fullscreen_toggles: videoData.fullscreen_toggles || 0,
        watch_segments: videoData.watch_segments || [],
        engagement_events: videoData.engagement_events || [],
        device_info: videoData.device_info || {},
        network_info: videoData.network_info || {},
        last_position_seconds: videoData.last_position_seconds || 0,
        completed_at: videoData.completion_percentage === 100 ? new Date().toISOString() : null
      }, {
        onConflict: 'user_id,video_id,session_id'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getVideoAnalytics(
    videoId: string,
    filters: {
      user_id?: string;
      date_from?: string;
      date_to?: string;
    } = {}
  ): Promise<VideoAnalytics[]> {
    let query = supabase
      .from('video_analytics')
      .select('*')
      .eq('video_id', videoId)
      .order('created_at', { ascending: false });

    if (filters.user_id) query = query.eq('user_id', filters.user_id);
    if (filters.date_from) query = query.gte('created_at', filters.date_from);
    if (filters.date_to) query = query.lte('created_at', filters.date_to);

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  // Assessment Analytics
  async trackAssessmentAnalytics(assessmentData: Partial<AssessmentAnalytics>): Promise<AssessmentAnalytics> {
    const { data, error } = await supabase
      .from('assessment_analytics')
      .insert({
        user_id: assessmentData.user_id,
        assessment_id: assessmentData.assessment_id,
        assessment_type: assessmentData.assessment_type,
        batch_id: assessmentData.batch_id,
        subject_id: assessmentData.subject_id,
        chapter_id: assessmentData.chapter_id,
        attempt_number: assessmentData.attempt_number || 1,
        start_time: assessmentData.start_time || new Date().toISOString(),
        end_time: assessmentData.end_time,
        duration_minutes: assessmentData.duration_minutes,
        score: assessmentData.score,
        max_score: assessmentData.max_score,
        percentage: assessmentData.percentage,
        questions_attempted: assessmentData.questions_attempted || 0,
        questions_correct: assessmentData.questions_correct || 0,
        questions_total: assessmentData.questions_total || 0,
        time_per_question: assessmentData.time_per_question || [],
        answer_changes: assessmentData.answer_changes || 0,
        hints_used: assessmentData.hints_used || 0,
        status: assessmentData.status || 'in_progress',
        submission_data: assessmentData.submission_data || {},
        grading_data: assessmentData.grading_data || {}
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getAssessmentAnalytics(
    batchId: string,
    filters: {
      user_id?: string;
      assessment_type?: string;
      subject_id?: string;
      date_from?: string;
      date_to?: string;
    } = {}
  ): Promise<AssessmentAnalytics[]> {
    let query = supabase
      .from('assessment_analytics')
      .select('*')
      .eq('batch_id', batchId)
      .order('created_at', { ascending: false });

    if (filters.user_id) query = query.eq('user_id', filters.user_id);
    if (filters.assessment_type) query = query.eq('assessment_type', filters.assessment_type);
    if (filters.subject_id) query = query.eq('subject_id', filters.subject_id);
    if (filters.date_from) query = query.gte('created_at', filters.date_from);
    if (filters.date_to) query = query.lte('created_at', filters.date_to);

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  // Communication Analytics
  async updateCommunicationAnalytics(
    userId: string,
    organizationId: string,
    batchId: string,
    metrics: Partial<CommunicationAnalytics>
  ): Promise<void> {
    const currentDate = new Date().toISOString().split('T')[0];

    const { error } = await supabase
      .from('communication_analytics')
      .upsert({
        user_id: userId,
        organization_id: organizationId,
        batch_id: batchId,
        date: currentDate,
        forum_posts_created: metrics.forum_posts_created || 0,
        forum_replies_created: metrics.forum_replies_created || 0,
        forum_posts_viewed: metrics.forum_posts_viewed || 0,
        forum_upvotes_given: metrics.forum_upvotes_given || 0,
        forum_upvotes_received: metrics.forum_upvotes_received || 0,
        doubts_submitted: metrics.doubts_submitted || 0,
        doubts_resolved: metrics.doubts_resolved || 0,
        doubt_responses_given: metrics.doubt_responses_given || 0,
        messages_sent: metrics.messages_sent || 0,
        messages_received: metrics.messages_received || 0,
        channels_active: metrics.channels_active || 0,
        voice_messages_sent: metrics.voice_messages_sent || 0,
        files_shared: metrics.files_shared || 0,
        reputation_points_earned: metrics.reputation_points_earned || 0,
        badges_earned: metrics.badges_earned || 0
      }, {
        onConflict: 'user_id,organization_id,batch_id,date'
      });

    if (error) throw error;
  }

  // Cache Management
  async getCachedData(
    cacheKey: string,
    organizationId?: string,
    batchId?: string,
    userId?: string
  ): Promise<any> {
    const { data, error } = await supabase.rpc('get_cached_analytics_data', {
      p_cache_key: cacheKey,
      p_organization_id: organizationId,
      p_batch_id: batchId,
      p_user_id: userId
    });

    if (error) throw error;
    return data;
  }

  async setCachedData(
    cacheKey: string,
    cacheType: string,
    data: any,
    organizationId?: string,
    batchId?: string,
    userId?: string,
    expiresInHours: number = 24
  ): Promise<void> {
    const { error } = await supabase.rpc('cache_analytics_data', {
      p_cache_key: cacheKey,
      p_cache_type: cacheType,
      p_organization_id: organizationId,
      p_batch_id: batchId,
      p_user_id: userId,
      p_data: data,
      p_expires_in_hours: expiresInHours
    });

    if (error) throw error;
  }
}

export const analyticsService = new AnalyticsService();
