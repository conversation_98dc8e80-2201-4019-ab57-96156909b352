<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { 
    batches, 
    batchLoading, 
    batchError,
    batchActions 
  } from './store';
  import { batchService } from '$lib/utils/services/batch';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import IconButton from '$lib/components/IconButton/index.svelte';
  import { Add, Edit, Delete, People, BookOpen } from 'carbon-icons-svelte';
  import { StructuredList, StructuredListHead, StructuredListRow, StructuredListCell, StructuredListBody, Tag } from 'carbon-components-svelte';

  export let showCreateButton = true;
  export let onBatchSelect: ((batch: any) => void) | null = null;

  let createBatchModal = false;

  onMount(async () => {
    await loadBatches();
  });

  async function loadBatches() {
    try {
      batchActions.setLoading(true);
      batchActions.setError(null);
      
      const organizationId = $globalStore.org?.id;
      if (!organizationId) {
        throw new Error('No organization found');
      }

      const batchList = await batchService.getBatches(organizationId);
      batchActions.setBatches(batchList);
    } catch (error) {
      console.error('Error loading batches:', error);
      batchActions.setError(error.message || 'Failed to load batches');
    } finally {
      batchActions.setLoading(false);
    }
  }

  function handleBatchClick(batch: any) {
    if (onBatchSelect) {
      onBatchSelect(batch);
    } else {
      goto(`/batches/${batch.id}`);
    }
  }

  function handleCreateBatch() {
    createBatchModal = true;
  }

  function handleEditBatch(batch: any, event: Event) {
    event.stopPropagation();
    goto(`/batches/${batch.id}/settings`);
  }

  function handleDeleteBatch(batch: any, event: Event) {
    event.stopPropagation();
    // TODO: Implement delete confirmation modal
    console.log('Delete batch:', batch);
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString();
  }

  function getBatchStatus(batch: any) {
    const now = new Date();
    const startDate = batch.start_date ? new Date(batch.start_date) : null;
    const endDate = batch.end_date ? new Date(batch.end_date) : null;

    if (!startDate) return 'draft';
    if (now < startDate) return 'upcoming';
    if (endDate && now > endDate) return 'completed';
    return 'active';
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'active': return 'green';
      case 'upcoming': return 'blue';
      case 'completed': return 'gray';
      case 'draft': return 'yellow';
      default: return 'gray';
    }
  }
</script>

<div class="batch-list-container">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        {$t('batch.title', { default: 'Batches' })}
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">
        {$t('batch.subtitle', { default: 'Manage your educational batches and student groups' })}
      </p>
    </div>
    
    {#if showCreateButton}
      <PrimaryButton
        variant={VARIANTS.CONTAINED}
        onClick={handleCreateBatch}
      >
        <Add size={20} class="mr-2" />
        {$t('batch.create', { default: 'Create Batch' })}
      </PrimaryButton>
    {/if}
  </div>

  <!-- Loading State -->
  {#if $batchLoading}
    <Box className="w-full">
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-400">
          {$t('batch.loading', { default: 'Loading batches...' })}
        </span>
      </div>
    </Box>
  
  <!-- Error State -->
  {:else if $batchError}
    <Box className="w-full">
      <div class="text-center py-12">
        <div class="text-red-500 mb-4">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {$t('batch.error.title', { default: 'Error Loading Batches' })}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{$batchError}</p>
        <PrimaryButton onClick={loadBatches}>
          {$t('batch.retry', { default: 'Try Again' })}
        </PrimaryButton>
      </div>
    </Box>

  <!-- Empty State -->
  {:else if $batches.length === 0}
    <Box className="w-full">
      <div class="text-center py-12">
        <div class="text-gray-400 mb-4">
          <BookOpen size={48} class="mx-auto" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {$t('batch.empty.title', { default: 'No Batches Yet' })}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          {$t('batch.empty.description', { default: 'Create your first batch to start organizing students and courses.' })}
        </p>
        {#if showCreateButton}
          <PrimaryButton
            variant={VARIANTS.CONTAINED}
            onClick={handleCreateBatch}
          >
            <Add size={20} class="mr-2" />
            {$t('batch.create_first', { default: 'Create Your First Batch' })}
          </PrimaryButton>
        {/if}
      </div>
    </Box>

  <!-- Batch List -->
  {:else}
    <Box className="w-full">
      <StructuredList selection>
        <StructuredListHead>
          <StructuredListRow head>
            <StructuredListCell head>
              {$t('batch.table.name', { default: 'Batch Name' })}
            </StructuredListCell>
            <StructuredListCell head>
              {$t('batch.table.code', { default: 'Code' })}
            </StructuredListCell>
            <StructuredListCell head>
              {$t('batch.table.students', { default: 'Students' })}
            </StructuredListCell>
            <StructuredListCell head>
              {$t('batch.table.subjects', { default: 'Subjects' })}
            </StructuredListCell>
            <StructuredListCell head>
              {$t('batch.table.duration', { default: 'Duration' })}
            </StructuredListCell>
            <StructuredListCell head>
              {$t('batch.table.status', { default: 'Status' })}
            </StructuredListCell>
            <StructuredListCell head>
              {$t('batch.table.actions', { default: 'Actions' })}
            </StructuredListCell>
          </StructuredListRow>
        </StructuredListHead>
        
        <StructuredListBody>
          {#each $batches as batch (batch.id)}
            {@const status = getBatchStatus(batch)}
            <StructuredListRow 
              label 
              for="batch-{batch.id}"
              class="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
              on:click={() => handleBatchClick(batch)}
            >
              <StructuredListCell>
                <div>
                  <p class="font-semibold text-gray-900 dark:text-white">
                    {batch.name}
                  </p>
                  {#if batch.description}
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">
                      {batch.description}
                    </p>
                  {/if}
                </div>
              </StructuredListCell>
              
              <StructuredListCell>
                <code class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                  {batch.batch_code || 'N/A'}
                </code>
              </StructuredListCell>
              
              <StructuredListCell>
                <div class="flex items-center">
                  <People size={16} class="mr-1 text-gray-500" />
                  <span>{batch.total_students || 0}/{batch.max_students}</span>
                </div>
              </StructuredListCell>
              
              <StructuredListCell>
                <span>{batch.total_subjects || 0}</span>
              </StructuredListCell>
              
              <StructuredListCell>
                <div class="text-sm">
                  {#if batch.start_date}
                    <div>{formatDate(batch.start_date)}</div>
                    {#if batch.end_date}
                      <div class="text-gray-500">to {formatDate(batch.end_date)}</div>
                    {/if}
                  {:else}
                    <span class="text-gray-500">Not scheduled</span>
                  {/if}
                </div>
              </StructuredListCell>
              
              <StructuredListCell>
                <Tag type={getStatusColor(status)}>
                  {$t(`batch.status.${status}`, { default: status })}
                </Tag>
              </StructuredListCell>
              
              <StructuredListCell>
                <div class="flex items-center space-x-2">
                  <IconButton
                    onClick={(e) => handleEditBatch(batch, e)}
                    title={$t('batch.edit', { default: 'Edit Batch' })}
                  >
                    <Edit size={16} />
                  </IconButton>
                  
                  <IconButton
                    onClick={(e) => handleDeleteBatch(batch, e)}
                    title={$t('batch.delete', { default: 'Delete Batch' })}
                  >
                    <Delete size={16} />
                  </IconButton>
                </div>
              </StructuredListCell>
            </StructuredListRow>
          {/each}
        </StructuredListBody>
      </StructuredList>
    </Box>
  {/if}
</div>

<style>
  .batch-list-container {
    @apply w-full max-w-7xl mx-auto p-4;
  }
  
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
