
import root from '../root.svelte';
import { set_building } from '__sveltekit/environment';
import { set_assets } from '__sveltekit/paths';
import { set_private_env, set_public_env } from '../../../../../node_modules/.pnpm/@sveltejs+kit@1.30.4_svelte@4.2.20_vite@4.5.14/node_modules/@sveltejs/kit/src/runtime/shared-server.js';

export const options = {
	app_template_contains_nonce: false,
	csp: {"mode":"auto","directives":{"upgrade-insecure-requests":false,"block-all-mixed-content":false},"reportOnly":{"upgrade-insecure-requests":false,"block-all-mixed-content":false}},
	csrf_check_origin: true,
	track_server_fetches: false,
	embedded: false,
	env_public_prefix: 'PUBLIC_',
	env_private_prefix: '',
	hooks: null, // added lazily, via `get_hooks`
	preload_strategy: "modulepreload",
	root,
	service_worker: false,
	templates: {
		app: ({ head, body, assets, nonce, env }) => "<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n  <meta charset=\"utf-8\" />\r\n  <meta name=\"viewport\" content=\"width=device-width,initial-scale=1.0\" />\r\n  <meta name=\"theme-color\" content=\"#333333\" />\r\n\r\n  <link rel=\"manifest\" href=\"/manifest.json\" crossorigin=\"use-credentials\" />\r\n  <link rel=\"icon\" type=\"image/png\" href=\"/favicon.ico\" />\r\n  <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/logo-32.png\" />\r\n  <link rel=\"stylesheet\" href=\"https://cdn.plyr.io/3.7.3/plyr.css\" />\r\n\r\n  <link rel=\"manifest\" href=\"/manifest.json\" crossorigin=\"use-credentials\" />\r\n  <link rel=\"icon\" type=\"image/png\" href=\"/favicon.ico\" />\r\n  <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/logo-32.png\" />\r\n  <link rel=\"stylesheet\" href=\"https://cdn.plyr.io/3.7.3/plyr.css\" />\r\n  <link rel=\"stylesheet\" href=\"https://assets.cdn.clsrio.com/carbon.css\" />\r\n\r\n  <script defer src=\"https://umami.hz.classroomio.com/script.js\"\r\n    data-website-id=\"80a9544a-4dda-4c91-b62f-b6be7a8a3b5c\"></script>\r\n\r\n  <script src=\"https://cdn.plyr.io/3.7.3/plyr.js\"></script>\r\n\r\n  " + head + "\r\n</head>\r\n\r\n<body data-sveltekit-preload-data=\"hover\" class=\"\">\r\n  <div style=\"display: contents\">" + body + "</div>\r\n</body>\r\n\r\n</html>\r\n",
		error: ({ status, message }) => "<div class=\"error-container\">\r\n  <div class=\"error-content\">\r\n    <img src=\"/logo-512.png\" class=\"logo\" alt=\"ClassroomIO - Logo\" />\r\n    <h2>Something unexpected occured.</h2>\r\n    <p>\r\n      Don't worry, your learning is safe. It isn't your fault, it is ours. We have gotten the error\r\n      notification and will push a fix ASAP. In the meantime, take a short break and come back a bit\r\n      later.\r\n    </p>\r\n    <a href=\"mailto:<EMAIL>\">Contact Us</a>\r\n  </div>\r\n\r\n  <div class=\"error-icon\">\r\n    <img src=\"/500-error.svg\" alt=\"Error\" />\r\n  </div>\r\n</div>\r\n\r\n<style>\r\n  body {\r\n    margin: 0;\r\n  }\r\n  .error-container {\r\n    max-width: 964px;\r\n    margin: auto;\r\n    display: flex;\r\n    flex-direction: column-reverse;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100vh;\r\n    width: 100vw;\r\n    padding: 0;\r\n  }\r\n\r\n  :global(.dark) .error-container {\r\n    background-color: black;\r\n  }\r\n\r\n  .error-content {\r\n    padding: 0.5rem;\r\n    width: 100%;\r\n  }\r\n\r\n  .error-content h2 {\r\n    width: 100%;\r\n    font-size: 1.125rem;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .error-content p {\r\n    width: 100%;\r\n    font-size: 1rem;\r\n    font-weight: 400;\r\n    margin: 1.5rem 0;\r\n    color: rgb(75, 85, 99);\r\n  }\r\n\r\n  :global(.dark) .error-content p {\r\n    color: white;\r\n  }\r\n\r\n  .error-content .logo {\r\n    width: 5rem;\r\n  }\r\n\r\n  .error-content a {\r\n    background-color: rgb(29, 78, 216);\r\n    padding: 0.75rem 2.25rem;\r\n    border-radius: 0.75rem;\r\n    color: white;\r\n    margin-top: 0.75rem;\r\n    transition: all 0.2s;\r\n    display: inline-block;\r\n  }\r\n\r\n  .error-content a:hover {\r\n    background-color: rgb(30, 58, 138);\r\n    text-decoration: none;\r\n  }\r\n\r\n  .error-icon {\r\n    width: 18rem;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  @media (min-width: 768px) {\r\n    .error-content {\r\n      width: 90%;\r\n    }\r\n  }\r\n\r\n  @media (min-width: 1024px) {\r\n    .error-container {\r\n      flex-direction: row;\r\n    }\r\n\r\n    .error-content {\r\n      width: 50%;\r\n    }\r\n\r\n    .error-content h2 {\r\n      font-size: 1.875rem;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .error-icon {\r\n      width: 50%;\r\n    }\r\n  }\r\n</style>\r\n"
	},
	version_hash: "rz3bm"
};

export function get_hooks() {
	return import("../../../src/hooks.server.ts");
}

export { set_assets, set_building, set_private_env, set_public_env };
