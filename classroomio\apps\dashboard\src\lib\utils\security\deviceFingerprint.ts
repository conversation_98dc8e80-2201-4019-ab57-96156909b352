// Device Fingerprinting Utility
// Date: 2025-06-30

import type { DeviceFingerprint } from '$lib/utils/types/security';

export class DeviceFingerprintGenerator {
  private static instance: Device<PERSON>ingerprintGenerator;
  private fingerprint: Device<PERSON>ingerprint | null = null;

  static getInstance(): Device<PERSON>ingerprintGenerator {
    if (!DeviceFingerprintGenerator.instance) {
      DeviceFingerprintGenerator.instance = new DeviceFingerprintGenerator();
    }
    return DeviceFingerprintGenerator.instance;
  }

  async generateFingerprint(): Promise<DeviceFingerprint> {
    if (this.fingerprint) {
      return this.fingerprint;
    }

    const fingerprint: DeviceFingerprint = {
      // Basic browser information
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}x${screen.colorDepth}`,
      color_depth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      platform: navigator.platform,
      cookie_enabled: navigator.cookieEnabled,
      do_not_track: navigator.doNotTrack === '1',

      // Hardware information
      device_memory: (navigator as any).deviceMemory || 0,
      hardware_concurrency: navigator.hardwareConcurrency || 0,
      max_touch_points: navigator.maxTouchPoints || 0,

      // Advanced fingerprinting
      canvas_fingerprint: await this.generateCanvasFingerprint(),
      webgl_fingerprint: await this.generateWebGLFingerprint(),
      audio_fingerprint: await this.generateAudioFingerprint(),
      available_fonts: await this.detectFonts(),
      plugins: this.getPluginInfo(),

      // Network information
      connection_type: (navigator as any).connection?.type || 'unknown',
      effective_type: (navigator as any).connection?.effectiveType || 'unknown',

      // Composite hash (calculated from all values)
      composite_hash: '',
      confidence_score: 0
    };

    // Calculate composite hash
    fingerprint.composite_hash = await this.calculateCompositeHash(fingerprint);
    fingerprint.confidence_score = this.calculateConfidenceScore(fingerprint);

    this.fingerprint = fingerprint;
    return fingerprint;
  }

  private async generateCanvasFingerprint(): Promise<string> {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) return 'canvas_not_supported';

      canvas.width = 200;
      canvas.height = 50;

      // Draw text with various styles
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillStyle = '#f60';
      ctx.fillRect(125, 1, 62, 20);
      ctx.fillStyle = '#069';
      ctx.fillText('Device fingerprint 🔒', 2, 15);
      ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
      ctx.fillText('Security test', 4, 35);

      // Draw some shapes
      ctx.globalCompositeOperation = 'multiply';
      ctx.fillStyle = 'rgb(255,0,255)';
      ctx.beginPath();
      ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
      ctx.closePath();
      ctx.fill();

      return canvas.toDataURL();
    } catch (error) {
      return 'canvas_error';
    }
  }

  private async generateWebGLFingerprint(): Promise<string> {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) return 'webgl_not_supported';

      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      const vendor = debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'unknown';
      const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown';

      return `${vendor}|${renderer}`;
    } catch (error) {
      return 'webgl_error';
    }
  }

  private async generateAudioFingerprint(): Promise<string> {
    try {
      if (!window.AudioContext && !(window as any).webkitAudioContext) {
        return 'audio_not_supported';
      }

      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      const context = new AudioContext();
      
      const oscillator = context.createOscillator();
      const analyser = context.createAnalyser();
      const gainNode = context.createGain();
      const scriptProcessor = context.createScriptProcessor(4096, 1, 1);

      oscillator.type = 'triangle';
      oscillator.frequency.setValueAtTime(10000, context.currentTime);

      gainNode.gain.setValueAtTime(0, context.currentTime);

      oscillator.connect(analyser);
      analyser.connect(scriptProcessor);
      scriptProcessor.connect(gainNode);
      gainNode.connect(context.destination);

      oscillator.start(0);

      return new Promise((resolve) => {
        let samples: number[] = [];
        
        scriptProcessor.onaudioprocess = (event) => {
          const buffer = event.inputBuffer.getChannelData(0);
          samples = samples.concat(Array.from(buffer));
          
          if (samples.length >= 4096) {
            oscillator.stop();
            context.close();
            
            // Calculate hash from samples
            const hash = samples.slice(0, 100).reduce((acc, val) => acc + val, 0).toString();
            resolve(hash);
          }
        };

        // Fallback timeout
        setTimeout(() => {
          oscillator.stop();
          context.close();
          resolve('audio_timeout');
        }, 1000);
      });
    } catch (error) {
      return 'audio_error';
    }
  }

  private async detectFonts(): Promise<string[]> {
    const testFonts = [
      'Arial', 'Arial Black', 'Arial Narrow', 'Arial Rounded MT Bold',
      'Calibri', 'Cambria', 'Comic Sans MS', 'Consolas', 'Courier',
      'Courier New', 'Georgia', 'Helvetica', 'Impact', 'Lucida Console',
      'Lucida Sans Unicode', 'Microsoft Sans Serif', 'Palatino Linotype',
      'Segoe UI', 'Tahoma', 'Times', 'Times New Roman', 'Trebuchet MS',
      'Verdana', 'Webdings', 'Wingdings'
    ];

    const detectedFonts: string[] = [];
    const testString = 'mmmmmmmmmmlli';
    const testSize = '72px';
    const baseFonts = ['monospace', 'sans-serif', 'serif'];

    // Create a canvas for font detection
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return [];

    // Measure baseline fonts
    const baselines: Record<string, number> = {};
    for (const baseFont of baseFonts) {
      context.font = `${testSize} ${baseFont}`;
      baselines[baseFont] = context.measureText(testString).width;
    }

    // Test each font
    for (const font of testFonts) {
      let detected = false;
      
      for (const baseFont of baseFonts) {
        context.font = `${testSize} ${font}, ${baseFont}`;
        const width = context.measureText(testString).width;
        
        if (width !== baselines[baseFont]) {
          detected = true;
          break;
        }
      }
      
      if (detected) {
        detectedFonts.push(font);
      }
    }

    return detectedFonts;
  }

  private getPluginInfo(): Array<{ name: string; version: string }> {
    const plugins: Array<{ name: string; version: string }> = [];
    
    if (navigator.plugins) {
      for (let i = 0; i < navigator.plugins.length; i++) {
        const plugin = navigator.plugins[i];
        plugins.push({
          name: plugin.name,
          version: plugin.version || 'unknown'
        });
      }
    }

    return plugins;
  }

  private async calculateCompositeHash(fingerprint: Omit<DeviceFingerprint, 'composite_hash' | 'confidence_score'>): Promise<string> {
    const data = JSON.stringify(fingerprint);
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private calculateConfidenceScore(fingerprint: DeviceFingerprint): number {
    let score = 0;
    let maxScore = 0;

    // User agent (weight: 10)
    maxScore += 10;
    if (fingerprint.user_agent && fingerprint.user_agent.length > 50) score += 10;
    else if (fingerprint.user_agent) score += 5;

    // Screen resolution (weight: 15)
    maxScore += 15;
    if (fingerprint.screen_resolution && fingerprint.screen_resolution !== '0x0x0') score += 15;

    // Canvas fingerprint (weight: 20)
    maxScore += 20;
    if (fingerprint.canvas_fingerprint && 
        fingerprint.canvas_fingerprint !== 'canvas_not_supported' && 
        fingerprint.canvas_fingerprint !== 'canvas_error') {
      score += 20;
    }

    // WebGL fingerprint (weight: 15)
    maxScore += 15;
    if (fingerprint.webgl_fingerprint && 
        fingerprint.webgl_fingerprint !== 'webgl_not_supported' && 
        fingerprint.webgl_fingerprint !== 'webgl_error') {
      score += 15;
    }

    // Audio fingerprint (weight: 10)
    maxScore += 10;
    if (fingerprint.audio_fingerprint && 
        fingerprint.audio_fingerprint !== 'audio_not_supported' && 
        fingerprint.audio_fingerprint !== 'audio_error') {
      score += 10;
    }

    // Fonts (weight: 10)
    maxScore += 10;
    if (fingerprint.available_fonts && fingerprint.available_fonts.length > 5) score += 10;
    else if (fingerprint.available_fonts && fingerprint.available_fonts.length > 0) score += 5;

    // Hardware info (weight: 10)
    maxScore += 10;
    if (fingerprint.hardware_concurrency && fingerprint.hardware_concurrency > 0) score += 5;
    if (fingerprint.device_memory && fingerprint.device_memory > 0) score += 5;

    // Plugins (weight: 5)
    maxScore += 5;
    if (fingerprint.plugins && fingerprint.plugins.length > 0) score += 5;

    // Timezone and language (weight: 5)
    maxScore += 5;
    if (fingerprint.timezone && fingerprint.language) score += 5;

    return Math.round((score / maxScore) * 100);
  }

  // Get a simplified fingerprint for quick identification
  getSimpleFingerprint(): string {
    if (!this.fingerprint) {
      throw new Error('Fingerprint not generated yet. Call generateFingerprint() first.');
    }

    const simple = [
      this.fingerprint.screen_resolution,
      this.fingerprint.timezone,
      this.fingerprint.language,
      this.fingerprint.platform,
      this.fingerprint.hardware_concurrency?.toString() || '0'
    ].join('|');

    return btoa(simple).substring(0, 16);
  }

  // Check if two fingerprints are similar
  static calculateSimilarity(fp1: DeviceFingerprint, fp2: DeviceFingerprint): number {
    let matches = 0;
    let total = 0;

    // Compare basic properties
    const basicProps = ['user_agent', 'screen_resolution', 'timezone', 'language', 'platform'];
    for (const prop of basicProps) {
      total++;
      if (fp1[prop as keyof DeviceFingerprint] === fp2[prop as keyof DeviceFingerprint]) {
        matches++;
      }
    }

    // Compare hardware properties
    const hardwareProps = ['device_memory', 'hardware_concurrency', 'max_touch_points'];
    for (const prop of hardwareProps) {
      total++;
      if (fp1[prop as keyof DeviceFingerprint] === fp2[prop as keyof DeviceFingerprint]) {
        matches++;
      }
    }

    // Compare fingerprints
    const fingerprintProps = ['canvas_fingerprint', 'webgl_fingerprint', 'audio_fingerprint'];
    for (const prop of fingerprintProps) {
      total++;
      if (fp1[prop as keyof DeviceFingerprint] === fp2[prop as keyof DeviceFingerprint]) {
        matches++;
      }
    }

    return (matches / total) * 100;
  }
}
