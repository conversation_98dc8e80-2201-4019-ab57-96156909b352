// Communication Services for Educational Platform
// Date: 2025-06-30

import { supabase } from '$lib/utils/functions/supabase';
import type { 
  DoubtSubmission, 
  DoubtResponse, 
  ForumCategory,
  ForumPost,
  ForumReply,
  MessagingChannel,
  Message,
  Notification,
  NotificationTemplate,
  ExternalIntegration,
  CommunicationAnalytics
} from '$lib/utils/types/communication';

// Doubt Clearing Service
export const doubtService = {
  // Submit a new doubt
  async submitDoubt(doubtData: Omit<DoubtSubmission, 'id' | 'created_at' | 'updated_at' | 'status' | 'upvotes' | 'views' | 'ai_category' | 'ai_confidence' | 'ai_suggested_faqs'>): Promise<string> {
    const { data, error } = await supabase
      .rpc('submit_doubt', {
        p_student_id: doubtData.student_id,
        p_batch_id: doubtData.batch_id,
        p_title: doubtData.title,
        p_description: doubtData.description,
        p_subject_id: doubtData.subject_id,
        p_chapter_id: doubtData.chapter_id,
        p_lesson_id: doubtData.lesson_id,
        p_doubt_type: doubtData.doubt_type,
        p_tags: doubtData.tags,
        p_attachments: doubtData.attachments
      });

    if (error) throw error;
    return data;
  },

  // Get doubts for a batch
  async getBatchDoubts(batchId: string, filters?: {
    status?: string;
    priority?: string;
    assigned_to?: string;
    subject_id?: string;
    limit?: number;
    offset?: number;
  }): Promise<DoubtSubmission[]> {
    let query = supabase
      .from('doubt_submissions')
      .select(`
        *,
        student:student_id(id, fullname, email, avatar_url),
        assigned_instructor:assigned_to(id, fullname, email, avatar_url),
        subject:subject_id(id, name),
        chapter:chapter_id(id, name),
        lesson:lesson_id(id, name)
      `)
      .eq('batch_id', batchId);

    if (filters?.status) query = query.eq('status', filters.status);
    if (filters?.priority) query = query.eq('priority', filters.priority);
    if (filters?.assigned_to) query = query.eq('assigned_to', filters.assigned_to);
    if (filters?.subject_id) query = query.eq('subject_id', filters.subject_id);

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(filters?.limit || 50)
      .range(filters?.offset || 0, (filters?.offset || 0) + (filters?.limit || 50) - 1);

    if (error) throw error;
    return data || [];
  },

  // Get doubt by ID
  async getDoubt(doubtId: string): Promise<DoubtSubmission | null> {
    const { data, error } = await supabase
      .from('doubt_submissions')
      .select(`
        *,
        student:student_id(id, fullname, email, avatar_url),
        assigned_instructor:assigned_to(id, fullname, email, avatar_url),
        subject:subject_id(id, name),
        chapter:chapter_id(id, name),
        lesson:lesson_id(id, name)
      `)
      .eq('id', doubtId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  },

  // Assign doubt to instructor
  async assignDoubt(doubtId: string, instructorId: string): Promise<void> {
    const { error } = await supabase
      .from('doubt_submissions')
      .update({
        assigned_to: instructorId,
        assigned_at: new Date().toISOString(),
        status: 'assigned',
        updated_at: new Date().toISOString()
      })
      .eq('id', doubtId);

    if (error) throw error;
  },

  // Respond to doubt
  async respondToDoubt(responseData: Omit<DoubtResponse, 'id' | 'created_at' | 'updated_at' | 'helpful_votes'>): Promise<DoubtResponse> {
    const { data, error } = await supabase
      .from('doubt_responses')
      .insert(responseData)
      .select()
      .single();

    if (error) throw error;

    // Update doubt status if this is a solution
    if (responseData.is_solution) {
      await supabase
        .from('doubt_submissions')
        .update({
          status: 'resolved',
          resolved_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', responseData.doubt_id);
    }

    return data;
  },

  // Get doubt responses
  async getDoubtResponses(doubtId: string): Promise<DoubtResponse[]> {
    const { data, error } = await supabase
      .from('doubt_responses')
      .select(`
        *,
        responder:responder_id(id, fullname, email, avatar_url)
      `)
      .eq('doubt_id', doubtId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Update doubt views
  async incrementViews(doubtId: string): Promise<void> {
    const { error } = await supabase
      .from('doubt_submissions')
      .update({
        views: supabase.raw('views + 1')
      })
      .eq('id', doubtId);

    if (error) throw error;
  },

  // Upvote doubt
  async upvoteDoubt(doubtId: string): Promise<void> {
    const { error } = await supabase
      .from('doubt_submissions')
      .update({
        upvotes: supabase.raw('upvotes + 1')
      })
      .eq('id', doubtId);

    if (error) throw error;
  }
};

// Forum Service
export const forumService = {
  // Get forum categories
  async getCategories(organizationId: string, batchId?: string): Promise<ForumCategory[]> {
    let query = supabase
      .from('forum_categories')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('is_active', true);

    if (batchId) {
      query = query.eq('batch_id', batchId);
    }

    const { data, error } = await query.order('sort_order', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Create forum post
  async createPost(postData: Omit<ForumPost, 'id' | 'created_at' | 'updated_at' | 'upvotes' | 'downvotes' | 'views' | 'reply_count' | 'moderation_status'>): Promise<string> {
    const { data, error } = await supabase
      .rpc('create_forum_post', {
        p_category_id: postData.category_id,
        p_author_id: postData.author_id,
        p_title: postData.title,
        p_content: postData.content,
        p_post_type: postData.post_type,
        p_tags: postData.tags,
        p_attachments: postData.attachments
      });

    if (error) throw error;
    return data;
  },

  // Get forum posts
  async getPosts(categoryId: string, filters?: {
    post_type?: string;
    is_pinned?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<ForumPost[]> {
    let query = supabase
      .from('forum_posts')
      .select(`
        *,
        author:author_id(id, fullname, email, avatar_url),
        category:category_id(id, name, color)
      `)
      .eq('category_id', categoryId)
      .eq('moderation_status', 'approved');

    if (filters?.post_type) query = query.eq('post_type', filters.post_type);
    if (filters?.is_pinned !== undefined) query = query.eq('is_pinned', filters.is_pinned);

    const { data, error } = await query
      .order('is_pinned', { ascending: false })
      .order('last_reply_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })
      .limit(filters?.limit || 20)
      .range(filters?.offset || 0, (filters?.offset || 0) + (filters?.limit || 20) - 1);

    if (error) throw error;
    return data || [];
  },

  // Get forum post by ID
  async getPost(postId: string): Promise<ForumPost | null> {
    const { data, error } = await supabase
      .from('forum_posts')
      .select(`
        *,
        author:author_id(id, fullname, email, avatar_url),
        category:category_id(id, name, color)
      `)
      .eq('id', postId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;

    // Increment views
    if (data) {
      await supabase
        .from('forum_posts')
        .update({ views: supabase.raw('views + 1') })
        .eq('id', postId);
    }

    return data;
  },

  // Create forum reply
  async createReply(replyData: Omit<ForumReply, 'id' | 'created_at' | 'updated_at' | 'upvotes' | 'downvotes' | 'moderation_status'>): Promise<ForumReply> {
    const { data, error } = await supabase
      .from('forum_replies')
      .insert(replyData)
      .select()
      .single();

    if (error) throw error;

    // Update post reply count and last reply info
    await supabase
      .from('forum_posts')
      .update({
        reply_count: supabase.raw('reply_count + 1'),
        last_reply_at: new Date().toISOString(),
        last_reply_by: replyData.author_id
      })
      .eq('id', replyData.post_id);

    return data;
  },

  // Get forum replies
  async getReplies(postId: string): Promise<ForumReply[]> {
    const { data, error } = await supabase
      .from('forum_replies')
      .select(`
        *,
        author:author_id(id, fullname, email, avatar_url)
      `)
      .eq('post_id', postId)
      .eq('moderation_status', 'approved')
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Search forum posts
  async searchPosts(organizationId: string, query: string, filters?: {
    category_id?: string;
    batch_id?: string;
    limit?: number;
  }): Promise<ForumPost[]> {
    const { data, error } = await supabase
      .from('forum_posts')
      .select(`
        *,
        author:author_id(id, fullname, email, avatar_url),
        category:category_id(id, name, color, organization_id, batch_id)
      `)
      .textSearch('title', query)
      .eq('moderation_status', 'approved')
      .limit(filters?.limit || 20);

    if (error) throw error;

    // Filter by organization and batch if needed
    let filteredData = data?.filter(post => 
      post.category?.organization_id === organizationId
    ) || [];

    if (filters?.batch_id) {
      filteredData = filteredData.filter(post => 
        post.category?.batch_id === filters.batch_id
      );
    }

    if (filters?.category_id) {
      filteredData = filteredData.filter(post => 
        post.category_id === filters.category_id
      );
    }

    return filteredData;
  }
};

// Messaging Service
export const messagingService = {
  // Create messaging channel
  async createChannel(channelData: Omit<MessagingChannel, 'id' | 'created_at' | 'updated_at' | 'member_count' | 'last_message_at' | 'last_message_by'>): Promise<string> {
    const { data, error } = await supabase
      .rpc('create_messaging_channel', {
        p_name: channelData.name,
        p_channel_type: channelData.channel_type,
        p_created_by: channelData.created_by,
        p_batch_id: channelData.batch_id,
        p_subject_id: channelData.subject_id,
        p_description: channelData.description
      });

    if (error) throw error;
    return data;
  },

  // Get user channels
  async getUserChannels(userId: string): Promise<MessagingChannel[]> {
    const { data, error } = await supabase
      .from('messaging_channels')
      .select(`
        *,
        messaging_channel_members!inner(user_id, role, last_read_at)
      `)
      .eq('messaging_channel_members.user_id', userId)
      .eq('is_active', true)
      .order('last_message_at', { ascending: false, nullsFirst: false });

    if (error) throw error;
    return data || [];
  },

  // Send message
  async sendMessage(messageData: Omit<Message, 'id' | 'created_at' | 'updated_at' | 'is_edited' | 'is_deleted' | 'reactions' | 'read_by' | 'delivery_status'>): Promise<Message> {
    const { data, error } = await supabase
      .from('messages')
      .insert({
        ...messageData,
        delivery_status: 'sent'
      })
      .select()
      .single();

    if (error) throw error;

    // Update channel last message info
    if (messageData.channel_id) {
      await supabase
        .from('messaging_channels')
        .update({
          last_message_at: new Date().toISOString(),
          last_message_by: messageData.sender_id
        })
        .eq('id', messageData.channel_id);
    }

    return data;
  },

  // Get channel messages
  async getChannelMessages(channelId: string, limit: number = 50, before?: string): Promise<Message[]> {
    let query = supabase
      .from('messages')
      .select(`
        *,
        sender:sender_id(id, fullname, avatar_url)
      `)
      .eq('channel_id', channelId)
      .eq('is_deleted', false);

    if (before) {
      query = query.lt('created_at', before);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data?.reverse() || [];
  },

  // Mark messages as read
  async markAsRead(channelId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('messaging_channel_members')
      .update({
        last_read_at: new Date().toISOString()
      })
      .eq('channel_id', channelId)
      .eq('user_id', userId);

    if (error) throw error;
  }
};
