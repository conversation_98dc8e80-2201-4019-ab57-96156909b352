// Analytics and Reporting Types
// Comprehensive type definitions for the analytics and reporting system

// Legacy types (keeping for backward compatibility)
interface User {
  id: string;
  fullName: string;
  avatarUrl: string;
  email: string;
  lastSeen: string | undefined;
}

export interface UserCourseWithStats {
  id: string;
  org_id: string;
  title: string;
  slug: string;
  description: string;
  logo: string;
  banner_image: string;
  cost: number;
  currency: string;
  is_published: boolean;
  total_lessons: number;
  total_students: number;
  progress_rate: number;
  progress_percentage: number;
  type: string;
  member_profile_id: string;
  lessons_count: number;
  lessons_completed: number;
  exercises_count: number;
  exercises_completed: number;
  average_grade: number;
}

export interface UserAnalytics {
  user: User;
  courses: UserCourseWithStats[];
  overallCourseProgress: number;
  overallAverageGrade: number;
}

export interface OrganisationAnalytics {
  revenue: number;
  numberOfCourses: number;
  totalStudents: number;
  topCourses: {
    id: string;
    title: string;
    enrollments: number;
    completion: number;
  }[];
  enrollments: {
    id: string;
    avatarUrl: string;
    name: string;
    courseId: string;
    course: string;
    date: string;
  }[];
}

export interface UserCourseAnalytics {
  user: User;
  averageGrade: number;
  userExercisesStats: UserExercisesStats[];
  totalExercises: number;
  completedExercises: number;
  progressPercentage: number;
}

export interface UserExercisesStats {
  id: string;
  lessonId: string;
  lessonTitle: string;
  title: string;
  status: number | string | undefined;
  score: number;
  totalPoints: number;
  isCompleted: boolean;
}

export interface UserExerciseStatsQuery {
  lesson: {
    title: string;
    exercise: {
      id: string;
      title: string;
      lesson_id: string;
      created_at: string;
      question: { points: number }[];
      submission: {
        groupmember: { id: string; profile_id: string };
        total: number;
        id: string;
        status_id: string;
      }[];
    }[];
  }[];
}

// New comprehensive analytics types
export interface AnalyticsEvent {
  id: string;
  created_at: string;
  user_id?: string;
  session_id?: string;
  organization_id: string;
  batch_id?: string;
  event_type: string;
  event_category: 'learning' | 'communication' | 'assessment' | 'system';
  event_action: string;
  event_label?: string;
  event_value?: number;
  properties: Record<string, any>;
  context: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
  page_url?: string;
  processed: boolean;
  processed_at?: string;
}

export interface LearningAnalytics {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  organization_id: string;
  batch_id: string;
  subject_id?: string;
  chapter_id?: string;
  lesson_id?: string;
  date: string;
  total_study_time_minutes: number;
  video_watch_time_minutes: number;
  videos_completed: number;
  assignments_completed: number;
  quizzes_completed: number;
  forum_posts: number;
  doubts_submitted: number;
  live_sessions_attended: number;
  engagement_score: number;
  progress_percentage: number;
  last_activity_at?: string;
  metadata: Record<string, any>;
}

export interface VideoAnalytics {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  video_id: string;
  session_id: string;
  total_watch_time_seconds: number;
  completion_percentage: number;
  play_count: number;
  pause_count: number;
  seek_count: number;
  replay_count: number;
  speed_changes: number;
  quality_changes: number;
  fullscreen_toggles: number;
  watch_segments: WatchSegment[];
  engagement_events: EngagementEvent[];
  device_info: DeviceInfo;
  network_info: NetworkInfo;
  completed_at?: string;
  last_position_seconds: number;
}

export interface WatchSegment {
  start: number;
  end: number;
  duration: number;
}

export interface EngagementEvent {
  timestamp: number;
  event_type: 'play' | 'pause' | 'seek' | 'speed_change' | 'quality_change' | 'fullscreen';
  value?: any;
}

export interface DeviceInfo {
  device_type: 'desktop' | 'tablet' | 'mobile';
  os: string;
  browser: string;
  screen_resolution: string;
  viewport_size: string;
}

export interface NetworkInfo {
  connection_type: string;
  effective_type: string;
  downlink: number;
  rtt: number;
}

export interface AssessmentAnalytics {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  assessment_id: string;
  assessment_type: 'quiz' | 'assignment' | 'exam';
  batch_id: string;
  subject_id?: string;
  chapter_id?: string;
  attempt_number: number;
  start_time: string;
  end_time?: string;
  duration_minutes?: number;
  score?: number;
  max_score?: number;
  percentage?: number;
  questions_attempted: number;
  questions_correct: number;
  questions_total: number;
  time_per_question: number[];
  answer_changes: number;
  hints_used: number;
  status: 'in_progress' | 'completed' | 'abandoned';
  submission_data: Record<string, any>;
  grading_data: Record<string, any>;
}

export interface CommunicationAnalytics {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  organization_id: string;
  batch_id?: string;
  date: string;
  forum_posts_created: number;
  forum_replies_created: number;
  forum_posts_viewed: number;
  forum_upvotes_given: number;
  forum_upvotes_received: number;
  doubts_submitted: number;
  doubts_resolved: number;
  doubt_responses_given: number;
  messages_sent: number;
  messages_received: number;
  channels_active: number;
  voice_messages_sent: number;
  files_shared: number;
  reputation_points_earned: number;
  badges_earned: number;
}

export interface LiveSessionAnalytics {
  id: string;
  created_at: string;
  updated_at: string;
  session_id: string;
  user_id: string;
  join_time: string;
  leave_time?: string;
  duration_minutes?: number;
  attendance_percentage: number;
  camera_on_duration: number;
  microphone_on_duration: number;
  chat_messages_sent: number;
  polls_participated: number;
  screen_shares: number;
  reactions_sent: number;
  breakout_rooms_joined: number;
  connection_quality: 'excellent' | 'good' | 'fair' | 'poor';
  technical_issues: TechnicalIssue[];
  engagement_score: number;
  device_info: DeviceInfo;
  network_stats: NetworkStats;
}

export interface TechnicalIssue {
  timestamp: string;
  issue_type: 'connection' | 'audio' | 'video' | 'screen_share';
  description: string;
  resolved: boolean;
}

export interface NetworkStats {
  avg_bitrate: number;
  packet_loss: number;
  jitter: number;
  latency: number;
}

export interface SystemAnalytics {
  id: string;
  created_at: string;
  metric_name: string;
  metric_category: 'performance' | 'usage' | 'security' | 'error';
  metric_value: number;
  metric_unit: 'ms' | 'count' | 'percentage' | 'bytes';
  organization_id?: string;
  batch_id?: string;
  user_id?: string;
  session_id?: string;
  tags: Record<string, any>;
  metadata: Record<string, any>;
}

export interface DashboardConfig {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  dashboard_type: 'student' | 'instructor' | 'admin' | 'custom';
  organization_id: string;
  created_by: string;
  is_public: boolean;
  is_default: boolean;
  config: DashboardLayout;
  filters: Record<string, any>;
  permissions: Record<string, any>;
  usage_count: number;
  last_used_at?: string;
}

export interface DashboardLayout {
  widgets: DashboardWidget[];
  layout: LayoutConfig;
  theme: ThemeConfig;
}

export interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'text' | 'image';
  title: string;
  position: WidgetPosition;
  size: WidgetSize;
  config: WidgetConfig;
  data_source: DataSourceConfig;
  filters: FilterConfig[];
}

export interface WidgetPosition {
  x: number;
  y: number;
}

export interface WidgetSize {
  width: number;
  height: number;
}

export interface WidgetConfig {
  chart_type?: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'gauge';
  colors?: string[];
  show_legend?: boolean;
  show_grid?: boolean;
  animation?: boolean;
  refresh_interval?: number;
}

export interface DataSourceConfig {
  type: 'sql' | 'api' | 'function';
  query?: string;
  endpoint?: string;
  function_name?: string;
  parameters?: Record<string, any>;
  cache_duration?: number;
}

export interface FilterConfig {
  field: string;
  type: 'date' | 'select' | 'multiselect' | 'text' | 'number';
  label: string;
  default_value?: any;
  options?: FilterOption[];
}

export interface FilterOption {
  label: string;
  value: any;
}

export interface LayoutConfig {
  columns: number;
  row_height: number;
  margin: number[];
  container_padding: number[];
}

export interface ThemeConfig {
  primary_color: string;
  secondary_color: string;
  background_color: string;
  text_color: string;
  font_family: string;
}

export interface ReportTemplate {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  report_type: 'student_progress' | 'batch_performance' | 'engagement' | 'custom';
  organization_id: string;
  created_by: string;
  is_public: boolean;
  query_config: QueryConfig;
  visualization_config: VisualizationConfig;
  filters: FilterConfig[];
  schedule_config: ScheduleConfig;
  export_formats: ExportFormat[];
  permissions: Record<string, any>;
  usage_count: number;
  last_generated_at?: string;
}

export interface QueryConfig {
  sql?: string;
  data_source: string;
  parameters: QueryParameter[];
}

export interface QueryParameter {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  required: boolean;
  default_value?: any;
}

export interface VisualizationConfig {
  charts: ChartConfig[];
  tables: TableConfig[];
  layout: 'single' | 'grid' | 'tabs';
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  title: string;
  x_axis: string;
  y_axis: string;
  series: string[];
  colors: string[];
}

export interface TableConfig {
  title: string;
  columns: TableColumn[];
  sorting: boolean;
  pagination: boolean;
}

export interface TableColumn {
  field: string;
  title: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  format?: string;
  sortable: boolean;
}

export interface ScheduleConfig {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly' | 'custom';
  cron_expression?: string;
  timezone: string;
  recipients: string[];
}

export type ExportFormat = 'pdf' | 'excel' | 'csv' | 'json';

export interface ScheduledReport {
  id: string;
  created_at: string;
  updated_at: string;
  template_id: string;
  name: string;
  schedule_expression: string;
  timezone: string;
  recipients: string[];
  filters: Record<string, any>;
  format: ExportFormat;
  is_active: boolean;
  last_run_at?: string;
  next_run_at?: string;
  run_count: number;
  error_count: number;
  last_error?: string;
  created_by: string;
}

export interface AnalyticsCache {
  id: string;
  created_at: string;
  updated_at: string;
  cache_key: string;
  cache_type: 'dashboard' | 'report' | 'metric';
  organization_id?: string;
  batch_id?: string;
  user_id?: string;
  date_range_start?: string;
  date_range_end?: string;
  data: Record<string, any>;
  expires_at: string;
  hit_count: number;
  last_accessed_at: string;
}

// Analytics API Response Types
export interface StudentEngagementMetrics {
  total_study_time_minutes: number;
  video_watch_time_minutes: number;
  videos_completed: number;
  assignments_completed: number;
  quizzes_completed: number;
  forum_posts: number;
  doubts_submitted: number;
  live_sessions_attended: number;
  average_engagement_score: number;
  days_active: number;
  streak_days: number;
}

export interface BatchPerformanceMetrics {
  total_students: number;
  active_students: number;
  average_engagement_score: number;
  total_study_time_hours: number;
  total_videos_completed: number;
  total_assignments_completed: number;
  total_quizzes_completed: number;
  total_forum_posts: number;
  total_doubts_submitted: number;
  attendance_rate: number;
  top_performers: TopPerformer[];
  subject_performance: Record<string, SubjectPerformance>;
}

export interface TopPerformer {
  user_id: string;
  engagement_score: number;
}

export interface SubjectPerformance {
  average_engagement: number;
  total_study_time: number;
  videos_completed: number;
  assignments_completed: number;
}

// Real-time Analytics Types
export interface RealTimeMetric {
  metric_name: string;
  current_value: number;
  previous_value: number;
  change_percentage: number;
  trend: 'up' | 'down' | 'stable';
  timestamp: string;
}

export interface LiveDashboardData {
  active_users: number;
  live_sessions: number;
  system_health: SystemHealth;
  recent_activities: RecentActivity[];
  alerts: Alert[];
}

export interface SystemHealth {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  response_time: number;
  error_rate: number;
  status: 'healthy' | 'warning' | 'critical';
}

export interface RecentActivity {
  id: string;
  user_id: string;
  activity_type: string;
  description: string;
  timestamp: string;
}

export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: string;
  acknowledged: boolean;
}
