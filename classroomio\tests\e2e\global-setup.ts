// Global Setup for Playwright E2E Tests
import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test global setup...');
  
  const { baseURL } = config.projects[0].use;
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(baseURL || 'http://localhost:5173');
    await page.waitForLoadState('networkidle');
    
    // Set up test data
    await setupTestData(page);
    
    // Set up authentication state
    await setupAuthState(page);
    
    console.log('✅ E2E test global setup completed');
    
  } catch (error) {
    console.error('❌ E2E test global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestData(page: any) {
  console.log('📊 Setting up test data...');
  
  // Create test organization
  const testOrg = {
    id: 'test-org-e2e',
    name: 'E2E Test Organization',
    description: 'Organization for E2E testing'
  };
  
  // Create test users
  const testUsers = [
    {
      id: 'test-admin-e2e',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      role: 'admin',
      fullname: 'Test Admin'
    },
    {
      id: 'test-instructor-e2e',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      role: 'instructor',
      fullname: 'Test Instructor'
    },
    {
      id: 'test-student-e2e',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      role: 'student',
      fullname: 'Test Student'
    }
  ];
  
  // Create test batch
  const testBatch = {
    id: 'test-batch-e2e',
    name: 'E2E Test Batch',
    description: 'Batch for E2E testing',
    organization_id: testOrg.id
  };
  
  // Create test course
  const testCourse = {
    id: 'test-course-e2e',
    title: 'E2E Test Course',
    description: 'Course for E2E testing',
    batch_id: testBatch.id
  };
  
  // In a real implementation, this would make API calls to create test data
  // For now, we'll store the test data in localStorage for the tests to use
  await page.evaluate((data) => {
    localStorage.setItem('e2e-test-data', JSON.stringify(data));
  }, {
    organization: testOrg,
    users: testUsers,
    batch: testBatch,
    course: testCourse
  });
  
  console.log('✅ Test data setup completed');
}

async function setupAuthState(page: any) {
  console.log('🔐 Setting up authentication states...');
  
  // Set up admin authentication state
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'TestPassword123!');
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login
  await page.waitForURL('/dashboard', { timeout: 10000 });
  
  // Save admin auth state
  await page.context().storageState({ path: 'tests/e2e/auth/admin-auth.json' });
  
  // Set up instructor authentication state
  await page.goto('/logout');
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'TestPassword123!');
  await page.click('[data-testid="login-button"]');
  
  await page.waitForURL('/dashboard', { timeout: 10000 });
  await page.context().storageState({ path: 'tests/e2e/auth/instructor-auth.json' });
  
  // Set up student authentication state
  await page.goto('/logout');
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'TestPassword123!');
  await page.click('[data-testid="login-button"]');
  
  await page.waitForURL('/dashboard', { timeout: 10000 });
  await page.context().storageState({ path: 'tests/e2e/auth/student-auth.json' });
  
  console.log('✅ Authentication states setup completed');
}

export default globalSetup;
