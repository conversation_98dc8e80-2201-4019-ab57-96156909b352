import {
  require_baseKeys,
  require_getTag
} from "./chunk-ECBLFDQZ.js";
import {
  require_isArguments,
  require_isArrayLike,
  require_isBuffer,
  require_isPrototype,
  require_isTypedArray
} from "./chunk-LKNOVVY6.js";
import {
  require_isArray
} from "./chunk-EEOWAMMZ.js";
import "./chunk-O36BFTIB.js";
import "./chunk-BEY4UYWZ.js";
import {
  __commonJS
} from "./chunk-2GTGKKMZ.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js
var require_isEmpty = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js"(exports, module) {
    var baseKeys = require_baseKeys();
    var getTag = require_getTag();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var isArrayLike = require_isArrayLike();
    var isBuffer = require_isBuffer();
    var isPrototype = require_isPrototype();
    var isTypedArray = require_isTypedArray();
    var mapTag = "[object Map]";
    var setTag = "[object Set]";
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function isEmpty(value) {
      if (value == null) {
        return true;
      }
      if (isArrayLike(value) && (isArray(value) || typeof value == "string" || typeof value.splice == "function" || isBuffer(value) || isTypedArray(value) || isArguments(value))) {
        return !value.length;
      }
      var tag = getTag(value);
      if (tag == mapTag || tag == setTag) {
        return !value.size;
      }
      if (isPrototype(value)) {
        return !baseKeys(value).length;
      }
      for (var key in value) {
        if (hasOwnProperty.call(value, key)) {
          return false;
        }
      }
      return true;
    }
    module.exports = isEmpty;
  }
});
export default require_isEmpty();
//# sourceMappingURL=lodash_isEmpty.js.map
