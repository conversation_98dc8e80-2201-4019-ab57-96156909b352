<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import type { DashboardWidget, ThemeConfig } from '$lib/utils/types/analytics';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import ChartWidget from './widgets/ChartWidget.svelte';
  import MetricWidget from './widgets/MetricWidget.svelte';
  import TableWidget from './widgets/TableWidget.svelte';
  import TextWidget from './widgets/TextWidget.svelte';
  import { 
    Warning,
    Refresh,
    View,
    ChartLine,
    DataTable,
    TextAlignLeft
  } from 'carbon-icons-svelte';

  export let widget: DashboardWidget;
  export let data: any = null;
  export let theme: ThemeConfig;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    click: { widget: DashboardWidget };
    refresh: { widgetId: string };
    edit: { widget: DashboardWidget };
  }>();

  let loading = false;
  let error: string | null = null;

  $: hasError = data?.error || error;
  $: isLoading = loading || (!data && !hasError);

  onMount(() => {
    // Auto-refresh if configured
    if (widget.config.refresh_interval && widget.config.refresh_interval > 0) {
      const interval = setInterval(() => {
        dispatch('refresh', { widgetId: widget.id });
      }, widget.config.refresh_interval * 1000);

      return () => clearInterval(interval);
    }
  });

  function handleWidgetClick() {
    dispatch('click', { widget });
  }

  function handleRefresh() {
    dispatch('refresh', { widgetId: widget.id });
  }

  function getWidgetIcon(type: string) {
    switch (type) {
      case 'chart': return ChartLine;
      case 'table': return DataTable;
      case 'text': return TextAlignLeft;
      case 'metric': return View;
      default: return View;
    }
  }

  function getWidgetTypeLabel(type: string): string {
    switch (type) {
      case 'chart': return $t('analytics.chart', { default: 'Chart' });
      case 'metric': return $t('analytics.metric', { default: 'Metric' });
      case 'table': return $t('analytics.table', { default: 'Table' });
      case 'text': return $t('analytics.text', { default: 'Text' });
      case 'image': return $t('analytics.image', { default: 'Image' });
      default: return $t('analytics.widget', { default: 'Widget' });
    }
  }
</script>

<Box 
  className="widget-renderer h-full cursor-pointer hover:shadow-lg transition-shadow {className}"
  onClick={handleWidgetClick}
  style="
    border-color: {theme?.primary_color || '#3B82F6'};
    background-color: {theme?.background_color || '#FFFFFF'};
    color: {theme?.text_color || '#111827'};
    font-family: {theme?.font_family || 'Inter'};
  "
>
  <!-- Widget Header -->
  <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center space-x-2">
      <svelte:component 
        this={getWidgetIcon(widget.type)} 
        size={20} 
        class="text-gray-600 dark:text-gray-400" 
      />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        {widget.title}
      </h3>
    </div>

    <div class="flex items-center space-x-2">
      {#if widget.config.refresh_interval}
        <span class="text-xs text-gray-500 dark:text-gray-400">
          {$t('analytics.auto_refresh', { default: 'Auto-refresh' })}
        </span>
      {/if}
      
      <button
        on:click|stopPropagation={handleRefresh}
        class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        title={$t('analytics.refresh_widget', { default: 'Refresh widget' })}
      >
        <Refresh size={16} />
      </button>
    </div>
  </div>

  <!-- Widget Content -->
  <div class="flex-1 p-4">
    {#if isLoading}
      <!-- Loading State -->
      <div class="flex items-center justify-center h-full min-h-[200px]">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {$t('analytics.loading_widget', { default: 'Loading...' })}
          </p>
        </div>
      </div>

    {:else if hasError}
      <!-- Error State -->
      <div class="flex items-center justify-center h-full min-h-[200px]">
        <div class="text-center">
          <Warning size={32} class="text-red-500 mx-auto mb-2" />
          <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">
            {$t('analytics.widget_error', { default: 'Widget Error' })}
          </p>
          <p class="text-xs text-red-600 dark:text-red-400">
            {hasError === true ? $t('analytics.unknown_error', { default: 'Unknown error' }) : hasError}
          </p>
          <button
            on:click|stopPropagation={handleRefresh}
            class="mt-2 text-xs text-primary-600 hover:text-primary-700 underline"
          >
            {$t('analytics.retry', { default: 'Retry' })}
          </button>
        </div>
      </div>

    {:else if !data}
      <!-- No Data State -->
      <div class="flex items-center justify-center h-full min-h-[200px]">
        <div class="text-center">
          <svelte:component 
            this={getWidgetIcon(widget.type)} 
            size={32} 
            class="text-gray-400 mx-auto mb-2" 
          />
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {$t('analytics.no_data', { default: 'No data available' })}
          </p>
        </div>
      </div>

    {:else}
      <!-- Widget Content Based on Type -->
      {#if widget.type === 'chart'}
        <ChartWidget 
          {widget} 
          {data} 
          {theme}
          on:click={() => dispatch('click', { widget })}
        />

      {:else if widget.type === 'metric'}
        <MetricWidget 
          {widget} 
          {data} 
          {theme}
          on:click={() => dispatch('click', { widget })}
        />

      {:else if widget.type === 'table'}
        <TableWidget 
          {widget} 
          {data} 
          {theme}
          on:click={() => dispatch('click', { widget })}
        />

      {:else if widget.type === 'text'}
        <TextWidget 
          {widget} 
          {data} 
          {theme}
          on:click={() => dispatch('click', { widget })}
        />

      {:else if widget.type === 'image'}
        <!-- Image Widget -->
        <div class="flex items-center justify-center h-full">
          {#if data.url}
            <img 
              src={data.url} 
              alt={data.alt || widget.title}
              class="max-w-full max-h-full object-contain"
            />
          {:else}
            <div class="text-center">
              <View size={32} class="text-gray-400 mx-auto mb-2" />
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {$t('analytics.no_image', { default: 'No image available' })}
              </p>
            </div>
          {/if}
        </div>

      {:else}
        <!-- Unknown Widget Type -->
        <div class="flex items-center justify-center h-full min-h-[200px]">
          <div class="text-center">
            <Warning size={32} class="text-yellow-500 mx-auto mb-2" />
            <p class="text-sm font-medium text-gray-900 dark:text-white mb-1">
              {$t('analytics.unsupported_widget', { default: 'Unsupported Widget Type' })}
            </p>
            <p class="text-xs text-gray-600 dark:text-gray-400">
              {getWidgetTypeLabel(widget.type)}: {widget.type}
            </p>
          </div>
        </div>
      {/if}
    {/if}
  </div>

  <!-- Widget Footer (Optional) -->
  {#if data && !hasError && !isLoading}
    <div class="px-4 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
      <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
        <span>
          {#if data.last_updated}
            {$t('analytics.last_updated', { default: 'Updated' })}: {new Date(data.last_updated).toLocaleTimeString()}
          {:else}
            {$t('analytics.data_loaded', { default: 'Data loaded' })}
          {/if}
        </span>
        
        {#if data.record_count !== undefined}
          <span>
            {data.record_count} {$t('analytics.records', { default: 'records' })}
          </span>
        {/if}
      </div>
    </div>
  {/if}
</Box>

<style>
  .widget-renderer {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 200px;
  }

  .widget-renderer:hover {
    transform: translateY(-1px);
  }

  /* Ensure widget content fills available space */
  .widget-renderer > div:nth-child(2) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
</style>
