#!/bin/bash

# ClassroomIO Production Readiness Check
# Comprehensive validation script for production deployment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((PASSED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
    ((WARNING_CHECKS++))
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((FAILED_CHECKS++))
}

check_file() {
    local file_path=$1
    local description=$2
    ((TOTAL_CHECKS++))
    
    if [ -f "$file_path" ]; then
        log_success "$description: $file_path"
    else
        log_error "$description: $file_path (MISSING)"
    fi
}

check_directory() {
    local dir_path=$1
    local description=$2
    ((TOTAL_CHECKS++))
    
    if [ -d "$dir_path" ]; then
        log_success "$description: $dir_path"
    else
        log_error "$description: $dir_path (MISSING)"
    fi
}

check_env_var() {
    local var_name=$1
    local description=$2
    local required=${3:-true}
    ((TOTAL_CHECKS++))
    
    if [ -n "${!var_name:-}" ]; then
        log_success "$description: $var_name is set"
    elif [ "$required" = "true" ]; then
        log_error "$description: $var_name is not set (REQUIRED)"
    else
        log_warning "$description: $var_name is not set (OPTIONAL)"
    fi
}

# Main execution
main() {
    log_info "Starting ClassroomIO Production Readiness Check..."
    log_info "Timestamp: $(date)"
    echo ""

    # Check critical files
    log_info "=== Checking Critical Files ==="
    check_file "package.json" "Root package.json"
    check_file "apps/dashboard/package.json" "Dashboard package.json"
    check_file "apps/worker/package.json" "Worker package.json"
    check_file ".env.example" "Environment template"
    check_file ".env.production" "Production environment"
    check_file "docker-compose.production.yml" "Production Docker Compose"
    check_file "Dockerfile.production" "Production Dockerfile"
    check_file "Dockerfile.worker" "Worker Dockerfile"
    echo ""

    # Check configuration files
    log_info "=== Checking Configuration Files ==="
    check_file "vitest.config.ts" "Vitest configuration"
    check_file "playwright.config.ts" "Playwright configuration"
    check_file "lighthouserc.js" "Lighthouse CI configuration"
    check_file "tsconfig.json" "TypeScript configuration"
    check_file "supabase/config.toml" "Supabase configuration"
    check_file "monitoring/prometheus.yml" "Prometheus configuration"
    echo ""

    # Check scripts
    log_info "=== Checking Scripts ==="
    check_file "scripts/health-check.js" "Health check script"
    check_file "scripts/worker-health-check.js" "Worker health check script"
    check_file "scripts/start-production.sh" "Production startup script"
    check_file "scripts/start-worker.sh" "Worker startup script"
    check_file "scripts/backup.sh" "Backup script"
    echo ""

    # Check test files
    log_info "=== Checking Test Infrastructure ==="
    check_file "tests/e2e/auth.spec.ts" "E2E authentication tests"
    check_file "tests/load/load-test.js" "Load test configuration"
    check_file "tests/smoke/smoke.spec.ts" "Smoke tests"
    check_file "tests/health/health-check.js" "Health check tests"
    check_directory "tests/e2e/auth" "E2E auth directory"
    echo ""

    # Check health endpoints
    log_info "=== Checking Health Endpoints ==="
    check_file "apps/dashboard/src/routes/health/+server.ts" "Main health endpoint"
    check_file "apps/dashboard/src/routes/api/health/+server.ts" "API health endpoint"
    echo ""

    # Check monitoring files
    log_info "=== Checking Monitoring Configuration ==="
    check_file "monitoring/grafana/provisioning/dashboards/dashboard.yml" "Grafana dashboard config"
    check_file "monitoring/grafana/provisioning/datasources/prometheus.yml" "Grafana datasource config"
    check_file "monitoring/loki-config.yaml" "Loki configuration"
    check_file "monitoring/promtail-config.yml" "Promtail configuration"
    echo ""

    # Check SSL directory
    log_info "=== Checking SSL Configuration ==="
    check_directory "ssl" "SSL certificates directory"
    check_file "ssl/README.md" "SSL setup documentation"
    echo ""

    # Check documentation
    log_info "=== Checking Documentation ==="
    check_file "README.md" "Main README"
    check_file "DOCUMENTATION.md" "Comprehensive documentation"
    check_file "MAINTENANCE_GUIDE.md" "Maintenance guide"
    check_file "API_DOCUMENTATION.md" "API documentation"
    check_file "TEST_SUMMARY.md" "Test summary"
    check_file "CONTRIBUTING.md" "Contributing guide"
    echo ""

    # Check environment variables (if .env.production exists)
    if [ -f ".env.production" ]; then
        log_info "=== Checking Environment Variables ==="
        source .env.production 2>/dev/null || true
        
        # Critical environment variables
        check_env_var "NODE_ENV" "Node environment"
        check_env_var "DATABASE_URL" "Database URL"
        check_env_var "SUPABASE_URL" "Supabase URL"
        check_env_var "SUPABASE_ANON_KEY" "Supabase anonymous key"
        check_env_var "JWT_SECRET" "JWT secret"
        
        # Optional but recommended
        check_env_var "REDIS_URL" "Redis URL" false
        check_env_var "OPENAI_API_KEY" "OpenAI API key" false
        check_env_var "STRIPE_SECRET_KEY" "Stripe secret key" false
        check_env_var "AWS_ACCESS_KEY_ID" "AWS access key" false
        echo ""
    else
        log_warning "Skipping environment variable check - .env.production not found"
        echo ""
    fi

    # Check Node.js version compatibility
    log_info "=== Checking Node.js Compatibility ==="
    ((TOTAL_CHECKS++))
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | sed 's/v//')
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d. -f1)
        
        if [ "$MAJOR_VERSION" -ge 18 ]; then
            log_success "Node.js version: v$NODE_VERSION (compatible)"
        else
            log_error "Node.js version: v$NODE_VERSION (requires >= 18.17.0)"
        fi
    else
        log_error "Node.js not found"
    fi
    echo ""

    # Check Docker availability
    log_info "=== Checking Docker Availability ==="
    ((TOTAL_CHECKS++))
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | sed 's/,//')
        log_success "Docker version: $DOCKER_VERSION"
    else
        log_error "Docker not found"
    fi

    ((TOTAL_CHECKS++))
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | sed 's/,//')
        log_success "Docker Compose version: $COMPOSE_VERSION"
    else
        log_error "Docker Compose not found"
    fi
    echo ""

    # Check package manager
    log_info "=== Checking Package Manager ==="
    ((TOTAL_CHECKS++))
    if command -v pnpm &> /dev/null; then
        PNPM_VERSION=$(pnpm --version)
        log_success "pnpm version: $PNPM_VERSION"
    elif command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        log_warning "npm version: $NPM_VERSION (pnpm recommended)"
    else
        log_error "No package manager found (npm or pnpm required)"
    fi
    echo ""

    # Summary
    log_info "=== Production Readiness Summary ==="
    echo "Total checks: $TOTAL_CHECKS"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    
    if [ $WARNING_CHECKS -gt 0 ]; then
        echo -e "Warnings: ${YELLOW}$WARNING_CHECKS${NC}"
    fi
    
    if [ $FAILED_CHECKS -gt 0 ]; then
        echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    fi
    
    echo ""
    
    # Final assessment
    if [ $FAILED_CHECKS -eq 0 ]; then
        if [ $WARNING_CHECKS -eq 0 ]; then
            log_success "🎉 ClassroomIO is FULLY PRODUCTION READY!"
            echo "All critical components are in place and properly configured."
        else
            log_warning "⚠️ ClassroomIO is MOSTLY PRODUCTION READY with warnings"
            echo "Address the warnings above for optimal production deployment."
        fi
        exit 0
    else
        log_error "❌ ClassroomIO is NOT PRODUCTION READY"
        echo "Please address the failed checks above before deploying to production."
        exit 1
    fi
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
