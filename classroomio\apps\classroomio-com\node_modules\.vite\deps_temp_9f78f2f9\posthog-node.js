import {
  __commonJS,
  __toESM
} from "./chunk-EQCVQC35.js";

// ../../node_modules/.pnpm/rusha@0.8.14/node_modules/rusha/dist/rusha.js
var require_rusha = __commonJS({
  "../../node_modules/.pnpm/rusha@0.8.14/node_modules/rusha/dist/rusha.js"(exports, module) {
    (function webpackUniversalModuleDefinition(root, factory) {
      if (typeof exports === "object" && typeof module === "object")
        module.exports = factory();
      else if (typeof define === "function" && define.amd)
        define([], factory);
      else if (typeof exports === "object")
        exports["Rusha"] = factory();
      else
        root["Rusha"] = factory();
    })(typeof self !== "undefined" ? self : exports, function() {
      return (
        /******/
        function(modules) {
          var installedModules = {};
          function __webpack_require__(moduleId) {
            if (installedModules[moduleId]) {
              return installedModules[moduleId].exports;
            }
            var module2 = installedModules[moduleId] = {
              /******/
              i: moduleId,
              /******/
              l: false,
              /******/
              exports: {}
              /******/
            };
            modules[moduleId].call(module2.exports, module2, module2.exports, __webpack_require__);
            module2.l = true;
            return module2.exports;
          }
          __webpack_require__.m = modules;
          __webpack_require__.c = installedModules;
          __webpack_require__.d = function(exports2, name, getter) {
            if (!__webpack_require__.o(exports2, name)) {
              Object.defineProperty(exports2, name, {
                /******/
                configurable: false,
                /******/
                enumerable: true,
                /******/
                get: getter
                /******/
              });
            }
          };
          __webpack_require__.n = function(module2) {
            var getter = module2 && module2.__esModule ? (
              /******/
              function getDefault() {
                return module2["default"];
              }
            ) : (
              /******/
              function getModuleExports() {
                return module2;
              }
            );
            __webpack_require__.d(getter, "a", getter);
            return getter;
          };
          __webpack_require__.o = function(object, property) {
            return Object.prototype.hasOwnProperty.call(object, property);
          };
          __webpack_require__.p = "";
          return __webpack_require__(__webpack_require__.s = 3);
        }([
          /* 0 */
          /***/
          function(module2, exports2, __webpack_require__) {
            function _classCallCheck(instance, Constructor) {
              if (!(instance instanceof Constructor)) {
                throw new TypeError("Cannot call a class as a function");
              }
            }
            var RushaCore = __webpack_require__(5);
            var _require = __webpack_require__(1), toHex = _require.toHex, ceilHeapSize = _require.ceilHeapSize;
            var conv = __webpack_require__(6);
            var padlen = function(len) {
              for (len += 9; len % 64 > 0; len += 1) {
              }
              return len;
            };
            var padZeroes = function(bin, len) {
              var h8 = new Uint8Array(bin.buffer);
              var om = len % 4, align = len - om;
              switch (om) {
                case 0:
                  h8[align + 3] = 0;
                case 1:
                  h8[align + 2] = 0;
                case 2:
                  h8[align + 1] = 0;
                case 3:
                  h8[align + 0] = 0;
              }
              for (var i = (len >> 2) + 1; i < bin.length; i++) {
                bin[i] = 0;
              }
            };
            var padData = function(bin, chunkLen, msgLen) {
              bin[chunkLen >> 2] |= 128 << 24 - (chunkLen % 4 << 3);
              bin[((chunkLen >> 2) + 2 & ~15) + 14] = msgLen / (1 << 29) | 0;
              bin[((chunkLen >> 2) + 2 & ~15) + 15] = msgLen << 3;
            };
            var getRawDigest = function(heap, padMaxChunkLen) {
              var io = new Int32Array(heap, padMaxChunkLen + 320, 5);
              var out = new Int32Array(5);
              var arr = new DataView(out.buffer);
              arr.setInt32(0, io[0], false);
              arr.setInt32(4, io[1], false);
              arr.setInt32(8, io[2], false);
              arr.setInt32(12, io[3], false);
              arr.setInt32(16, io[4], false);
              return out;
            };
            var Rusha = function() {
              function Rusha2(chunkSize) {
                _classCallCheck(this, Rusha2);
                chunkSize = chunkSize || 64 * 1024;
                if (chunkSize % 64 > 0) {
                  throw new Error("Chunk size must be a multiple of 128 bit");
                }
                this._offset = 0;
                this._maxChunkLen = chunkSize;
                this._padMaxChunkLen = padlen(chunkSize);
                this._heap = new ArrayBuffer(ceilHeapSize(this._padMaxChunkLen + 320 + 20));
                this._h32 = new Int32Array(this._heap);
                this._h8 = new Int8Array(this._heap);
                this._core = new RushaCore({ Int32Array }, {}, this._heap);
              }
              Rusha2.prototype._initState = function _initState(heap, padMsgLen) {
                this._offset = 0;
                var io = new Int32Array(heap, padMsgLen + 320, 5);
                io[0] = 1732584193;
                io[1] = -271733879;
                io[2] = -1732584194;
                io[3] = 271733878;
                io[4] = -1009589776;
              };
              Rusha2.prototype._padChunk = function _padChunk(chunkLen, msgLen) {
                var padChunkLen = padlen(chunkLen);
                var view = new Int32Array(this._heap, 0, padChunkLen >> 2);
                padZeroes(view, chunkLen);
                padData(view, chunkLen, msgLen);
                return padChunkLen;
              };
              Rusha2.prototype._write = function _write(data, chunkOffset, chunkLen, off) {
                conv(data, this._h8, this._h32, chunkOffset, chunkLen, off || 0);
              };
              Rusha2.prototype._coreCall = function _coreCall(data, chunkOffset, chunkLen, msgLen, finalize) {
                var padChunkLen = chunkLen;
                this._write(data, chunkOffset, chunkLen);
                if (finalize) {
                  padChunkLen = this._padChunk(chunkLen, msgLen);
                }
                this._core.hash(padChunkLen, this._padMaxChunkLen);
              };
              Rusha2.prototype.rawDigest = function rawDigest(str) {
                var msgLen = str.byteLength || str.length || str.size || 0;
                this._initState(this._heap, this._padMaxChunkLen);
                var chunkOffset = 0, chunkLen = this._maxChunkLen;
                for (chunkOffset = 0; msgLen > chunkOffset + chunkLen; chunkOffset += chunkLen) {
                  this._coreCall(str, chunkOffset, chunkLen, msgLen, false);
                }
                this._coreCall(str, chunkOffset, msgLen - chunkOffset, msgLen, true);
                return getRawDigest(this._heap, this._padMaxChunkLen);
              };
              Rusha2.prototype.digest = function digest(str) {
                return toHex(this.rawDigest(str).buffer);
              };
              Rusha2.prototype.digestFromString = function digestFromString(str) {
                return this.digest(str);
              };
              Rusha2.prototype.digestFromBuffer = function digestFromBuffer(str) {
                return this.digest(str);
              };
              Rusha2.prototype.digestFromArrayBuffer = function digestFromArrayBuffer(str) {
                return this.digest(str);
              };
              Rusha2.prototype.resetState = function resetState() {
                this._initState(this._heap, this._padMaxChunkLen);
                return this;
              };
              Rusha2.prototype.append = function append(chunk) {
                var chunkOffset = 0;
                var chunkLen = chunk.byteLength || chunk.length || chunk.size || 0;
                var turnOffset = this._offset % this._maxChunkLen;
                var inputLen = void 0;
                this._offset += chunkLen;
                while (chunkOffset < chunkLen) {
                  inputLen = Math.min(chunkLen - chunkOffset, this._maxChunkLen - turnOffset);
                  this._write(chunk, chunkOffset, inputLen, turnOffset);
                  turnOffset += inputLen;
                  chunkOffset += inputLen;
                  if (turnOffset === this._maxChunkLen) {
                    this._core.hash(this._maxChunkLen, this._padMaxChunkLen);
                    turnOffset = 0;
                  }
                }
                return this;
              };
              Rusha2.prototype.getState = function getState() {
                var turnOffset = this._offset % this._maxChunkLen;
                var heap = void 0;
                if (!turnOffset) {
                  var io = new Int32Array(this._heap, this._padMaxChunkLen + 320, 5);
                  heap = io.buffer.slice(io.byteOffset, io.byteOffset + io.byteLength);
                } else {
                  heap = this._heap.slice(0);
                }
                return {
                  offset: this._offset,
                  heap
                };
              };
              Rusha2.prototype.setState = function setState(state) {
                this._offset = state.offset;
                if (state.heap.byteLength === 20) {
                  var io = new Int32Array(this._heap, this._padMaxChunkLen + 320, 5);
                  io.set(new Int32Array(state.heap));
                } else {
                  this._h32.set(new Int32Array(state.heap));
                }
                return this;
              };
              Rusha2.prototype.rawEnd = function rawEnd() {
                var msgLen = this._offset;
                var chunkLen = msgLen % this._maxChunkLen;
                var padChunkLen = this._padChunk(chunkLen, msgLen);
                this._core.hash(padChunkLen, this._padMaxChunkLen);
                var result = getRawDigest(this._heap, this._padMaxChunkLen);
                this._initState(this._heap, this._padMaxChunkLen);
                return result;
              };
              Rusha2.prototype.end = function end() {
                return toHex(this.rawEnd().buffer);
              };
              return Rusha2;
            }();
            module2.exports = Rusha;
            module2.exports._core = RushaCore;
          },
          /* 1 */
          /***/
          function(module2, exports2) {
            var precomputedHex = new Array(256);
            for (var i = 0; i < 256; i++) {
              precomputedHex[i] = (i < 16 ? "0" : "") + i.toString(16);
            }
            module2.exports.toHex = function(arrayBuffer) {
              var binarray = new Uint8Array(arrayBuffer);
              var res = new Array(arrayBuffer.byteLength);
              for (var _i = 0; _i < res.length; _i++) {
                res[_i] = precomputedHex[binarray[_i]];
              }
              return res.join("");
            };
            module2.exports.ceilHeapSize = function(v) {
              var p = 0;
              if (v <= 65536) return 65536;
              if (v < 16777216) {
                for (p = 1; p < v; p = p << 1) {
                }
              } else {
                for (p = 16777216; p < v; p += 16777216) {
                }
              }
              return p;
            };
            module2.exports.isDedicatedWorkerScope = function(self2) {
              var isRunningInWorker = "WorkerGlobalScope" in self2 && self2 instanceof self2.WorkerGlobalScope;
              var isRunningInSharedWorker = "SharedWorkerGlobalScope" in self2 && self2 instanceof self2.SharedWorkerGlobalScope;
              var isRunningInServiceWorker = "ServiceWorkerGlobalScope" in self2 && self2 instanceof self2.ServiceWorkerGlobalScope;
              return isRunningInWorker && !isRunningInSharedWorker && !isRunningInServiceWorker;
            };
          },
          /* 2 */
          /***/
          function(module2, exports2, __webpack_require__) {
            module2.exports = function() {
              var Rusha = __webpack_require__(0);
              var hashData = function(hasher, data, cb) {
                try {
                  return cb(null, hasher.digest(data));
                } catch (e) {
                  return cb(e);
                }
              };
              var hashFile = function(hasher, readTotal, blockSize, file, cb) {
                var reader = new self.FileReader();
                reader.onloadend = function onloadend() {
                  if (reader.error) {
                    return cb(reader.error);
                  }
                  var buffer = reader.result;
                  readTotal += reader.result.byteLength;
                  try {
                    hasher.append(buffer);
                  } catch (e) {
                    cb(e);
                    return;
                  }
                  if (readTotal < file.size) {
                    hashFile(hasher, readTotal, blockSize, file, cb);
                  } else {
                    cb(null, hasher.end());
                  }
                };
                reader.readAsArrayBuffer(file.slice(readTotal, readTotal + blockSize));
              };
              var workerBehaviourEnabled = true;
              self.onmessage = function(event) {
                if (!workerBehaviourEnabled) {
                  return;
                }
                var data = event.data.data, file = event.data.file, id = event.data.id;
                if (typeof id === "undefined") return;
                if (!file && !data) return;
                var blockSize = event.data.blockSize || 4 * 1024 * 1024;
                var hasher = new Rusha(blockSize);
                hasher.resetState();
                var done = function(err, hash) {
                  if (!err) {
                    self.postMessage({ id, hash });
                  } else {
                    self.postMessage({ id, error: err.name });
                  }
                };
                if (data) hashData(hasher, data, done);
                if (file) hashFile(hasher, 0, blockSize, file, done);
              };
              return function() {
                workerBehaviourEnabled = false;
              };
            };
          },
          /* 3 */
          /***/
          function(module2, exports2, __webpack_require__) {
            var work = __webpack_require__(4);
            var Rusha = __webpack_require__(0);
            var createHash2 = __webpack_require__(7);
            var runWorker = __webpack_require__(2);
            var _require = __webpack_require__(1), isDedicatedWorkerScope = _require.isDedicatedWorkerScope;
            var isRunningInDedicatedWorker = typeof self !== "undefined" && isDedicatedWorkerScope(self);
            Rusha.disableWorkerBehaviour = isRunningInDedicatedWorker ? runWorker() : function() {
            };
            Rusha.createWorker = function() {
              var worker = work(
                /*require.resolve*/
                2
              );
              var terminate = worker.terminate;
              worker.terminate = function() {
                URL.revokeObjectURL(worker.objectURL);
                terminate.call(worker);
              };
              return worker;
            };
            Rusha.createHash = createHash2;
            module2.exports = Rusha;
          },
          /* 4 */
          /***/
          function(module2, exports2, __webpack_require__) {
            function webpackBootstrapFunc(modules) {
              var installedModules = {};
              function __webpack_require__2(moduleId) {
                if (installedModules[moduleId])
                  return installedModules[moduleId].exports;
                var module3 = installedModules[moduleId] = {
                  /******/
                  i: moduleId,
                  /******/
                  l: false,
                  /******/
                  exports: {}
                  /******/
                };
                modules[moduleId].call(module3.exports, module3, module3.exports, __webpack_require__2);
                module3.l = true;
                return module3.exports;
              }
              __webpack_require__2.m = modules;
              __webpack_require__2.c = installedModules;
              __webpack_require__2.i = function(value) {
                return value;
              };
              __webpack_require__2.d = function(exports3, name, getter) {
                if (!__webpack_require__2.o(exports3, name)) {
                  Object.defineProperty(exports3, name, {
                    /******/
                    configurable: false,
                    /******/
                    enumerable: true,
                    /******/
                    get: getter
                    /******/
                  });
                }
              };
              __webpack_require__2.r = function(exports3) {
                Object.defineProperty(exports3, "__esModule", { value: true });
              };
              __webpack_require__2.n = function(module3) {
                var getter = module3 && module3.__esModule ? (
                  /******/
                  function getDefault() {
                    return module3["default"];
                  }
                ) : (
                  /******/
                  function getModuleExports() {
                    return module3;
                  }
                );
                __webpack_require__2.d(getter, "a", getter);
                return getter;
              };
              __webpack_require__2.o = function(object, property) {
                return Object.prototype.hasOwnProperty.call(object, property);
              };
              __webpack_require__2.p = "/";
              __webpack_require__2.oe = function(err) {
                console.error(err);
                throw err;
              };
              var f2 = __webpack_require__2(__webpack_require__2.s = ENTRY_MODULE);
              return f2.default || f2;
            }
            var moduleNameReqExp = "[\\.|\\-|\\+|\\w|/|@]+";
            var dependencyRegExp = "\\((/\\*.*?\\*/)?s?.*?(" + moduleNameReqExp + ").*?\\)";
            function quoteRegExp(str) {
              return (str + "").replace(/[.?*+^$[\]\\(){}|-]/g, "\\$&");
            }
            function getModuleDependencies(sources, module3, queueName) {
              var retval = {};
              retval[queueName] = [];
              var fnString = module3.toString();
              var wrapperSignature = fnString.match(/^function\s?\(\w+,\s*\w+,\s*(\w+)\)/);
              if (!wrapperSignature) return retval;
              var webpackRequireName = wrapperSignature[1];
              var re = new RegExp("(\\\\n|\\W)" + quoteRegExp(webpackRequireName) + dependencyRegExp, "g");
              var match;
              while (match = re.exec(fnString)) {
                if (match[3] === "dll-reference") continue;
                retval[queueName].push(match[3]);
              }
              re = new RegExp("\\(" + quoteRegExp(webpackRequireName) + '\\("(dll-reference\\s(' + moduleNameReqExp + '))"\\)\\)' + dependencyRegExp, "g");
              while (match = re.exec(fnString)) {
                if (!sources[match[2]]) {
                  retval[queueName].push(match[1]);
                  sources[match[2]] = __webpack_require__(match[1]).m;
                }
                retval[match[2]] = retval[match[2]] || [];
                retval[match[2]].push(match[4]);
              }
              return retval;
            }
            function hasValuesInQueues(queues) {
              var keys = Object.keys(queues);
              return keys.reduce(function(hasValues, key) {
                return hasValues || queues[key].length > 0;
              }, false);
            }
            function getRequiredModules(sources, moduleId) {
              var modulesQueue = {
                main: [moduleId]
              };
              var requiredModules = {
                main: []
              };
              var seenModules = {
                main: {}
              };
              while (hasValuesInQueues(modulesQueue)) {
                var queues = Object.keys(modulesQueue);
                for (var i = 0; i < queues.length; i++) {
                  var queueName = queues[i];
                  var queue = modulesQueue[queueName];
                  var moduleToCheck = queue.pop();
                  seenModules[queueName] = seenModules[queueName] || {};
                  if (seenModules[queueName][moduleToCheck] || !sources[queueName][moduleToCheck]) continue;
                  seenModules[queueName][moduleToCheck] = true;
                  requiredModules[queueName] = requiredModules[queueName] || [];
                  requiredModules[queueName].push(moduleToCheck);
                  var newModules = getModuleDependencies(sources, sources[queueName][moduleToCheck], queueName);
                  var newModulesKeys = Object.keys(newModules);
                  for (var j = 0; j < newModulesKeys.length; j++) {
                    modulesQueue[newModulesKeys[j]] = modulesQueue[newModulesKeys[j]] || [];
                    modulesQueue[newModulesKeys[j]] = modulesQueue[newModulesKeys[j]].concat(newModules[newModulesKeys[j]]);
                  }
                }
              }
              return requiredModules;
            }
            module2.exports = function(moduleId, options) {
              options = options || {};
              var sources = {
                main: __webpack_require__.m
              };
              var requiredModules = options.all ? { main: Object.keys(sources) } : getRequiredModules(sources, moduleId);
              var src = "";
              Object.keys(requiredModules).filter(function(m) {
                return m !== "main";
              }).forEach(function(module3) {
                var entryModule = 0;
                while (requiredModules[module3][entryModule]) {
                  entryModule++;
                }
                requiredModules[module3].push(entryModule);
                sources[module3][entryModule] = "(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })";
                src = src + "var " + module3 + " = (" + webpackBootstrapFunc.toString().replace("ENTRY_MODULE", JSON.stringify(entryModule)) + ")({" + requiredModules[module3].map(function(id) {
                  return "" + JSON.stringify(id) + ": " + sources[module3][id].toString();
                }).join(",") + "});\n";
              });
              src = src + "(" + webpackBootstrapFunc.toString().replace("ENTRY_MODULE", JSON.stringify(moduleId)) + ")({" + requiredModules.main.map(function(id) {
                return "" + JSON.stringify(id) + ": " + sources.main[id].toString();
              }).join(",") + "})(self);";
              var blob = new window.Blob([src], { type: "text/javascript" });
              if (options.bare) {
                return blob;
              }
              var URL2 = window.URL || window.webkitURL || window.mozURL || window.msURL;
              var workerUrl = URL2.createObjectURL(blob);
              var worker = new window.Worker(workerUrl);
              worker.objectURL = workerUrl;
              return worker;
            };
          },
          /* 5 */
          /***/
          function(module2, exports2) {
            module2.exports = function RushaCore(stdlib$840, foreign$841, heap$842) {
              ;
              var H$843 = new stdlib$840.Int32Array(heap$842);
              function hash$844(k$845, x$846) {
                k$845 = k$845 | 0;
                x$846 = x$846 | 0;
                var i$847 = 0, j$848 = 0, y0$849 = 0, z0$850 = 0, y1$851 = 0, z1$852 = 0, y2$853 = 0, z2$854 = 0, y3$855 = 0, z3$856 = 0, y4$857 = 0, z4$858 = 0, t0$859 = 0, t1$860 = 0;
                y0$849 = H$843[x$846 + 320 >> 2] | 0;
                y1$851 = H$843[x$846 + 324 >> 2] | 0;
                y2$853 = H$843[x$846 + 328 >> 2] | 0;
                y3$855 = H$843[x$846 + 332 >> 2] | 0;
                y4$857 = H$843[x$846 + 336 >> 2] | 0;
                for (i$847 = 0; (i$847 | 0) < (k$845 | 0); i$847 = i$847 + 64 | 0) {
                  z0$850 = y0$849;
                  z1$852 = y1$851;
                  z2$854 = y2$853;
                  z3$856 = y3$855;
                  z4$858 = y4$857;
                  for (j$848 = 0; (j$848 | 0) < 64; j$848 = j$848 + 4 | 0) {
                    t1$860 = H$843[i$847 + j$848 >> 2] | 0;
                    t0$859 = ((y0$849 << 5 | y0$849 >>> 27) + (y1$851 & y2$853 | ~y1$851 & y3$855) | 0) + ((t1$860 + y4$857 | 0) + 1518500249 | 0) | 0;
                    y4$857 = y3$855;
                    y3$855 = y2$853;
                    y2$853 = y1$851 << 30 | y1$851 >>> 2;
                    y1$851 = y0$849;
                    y0$849 = t0$859;
                    H$843[k$845 + j$848 >> 2] = t1$860;
                  }
                  for (j$848 = k$845 + 64 | 0; (j$848 | 0) < (k$845 + 80 | 0); j$848 = j$848 + 4 | 0) {
                    t1$860 = (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) << 1 | (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) >>> 31;
                    t0$859 = ((y0$849 << 5 | y0$849 >>> 27) + (y1$851 & y2$853 | ~y1$851 & y3$855) | 0) + ((t1$860 + y4$857 | 0) + 1518500249 | 0) | 0;
                    y4$857 = y3$855;
                    y3$855 = y2$853;
                    y2$853 = y1$851 << 30 | y1$851 >>> 2;
                    y1$851 = y0$849;
                    y0$849 = t0$859;
                    H$843[j$848 >> 2] = t1$860;
                  }
                  for (j$848 = k$845 + 80 | 0; (j$848 | 0) < (k$845 + 160 | 0); j$848 = j$848 + 4 | 0) {
                    t1$860 = (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) << 1 | (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) >>> 31;
                    t0$859 = ((y0$849 << 5 | y0$849 >>> 27) + (y1$851 ^ y2$853 ^ y3$855) | 0) + ((t1$860 + y4$857 | 0) + 1859775393 | 0) | 0;
                    y4$857 = y3$855;
                    y3$855 = y2$853;
                    y2$853 = y1$851 << 30 | y1$851 >>> 2;
                    y1$851 = y0$849;
                    y0$849 = t0$859;
                    H$843[j$848 >> 2] = t1$860;
                  }
                  for (j$848 = k$845 + 160 | 0; (j$848 | 0) < (k$845 + 240 | 0); j$848 = j$848 + 4 | 0) {
                    t1$860 = (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) << 1 | (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) >>> 31;
                    t0$859 = ((y0$849 << 5 | y0$849 >>> 27) + (y1$851 & y2$853 | y1$851 & y3$855 | y2$853 & y3$855) | 0) + ((t1$860 + y4$857 | 0) - 1894007588 | 0) | 0;
                    y4$857 = y3$855;
                    y3$855 = y2$853;
                    y2$853 = y1$851 << 30 | y1$851 >>> 2;
                    y1$851 = y0$849;
                    y0$849 = t0$859;
                    H$843[j$848 >> 2] = t1$860;
                  }
                  for (j$848 = k$845 + 240 | 0; (j$848 | 0) < (k$845 + 320 | 0); j$848 = j$848 + 4 | 0) {
                    t1$860 = (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) << 1 | (H$843[j$848 - 12 >> 2] ^ H$843[j$848 - 32 >> 2] ^ H$843[j$848 - 56 >> 2] ^ H$843[j$848 - 64 >> 2]) >>> 31;
                    t0$859 = ((y0$849 << 5 | y0$849 >>> 27) + (y1$851 ^ y2$853 ^ y3$855) | 0) + ((t1$860 + y4$857 | 0) - 899497514 | 0) | 0;
                    y4$857 = y3$855;
                    y3$855 = y2$853;
                    y2$853 = y1$851 << 30 | y1$851 >>> 2;
                    y1$851 = y0$849;
                    y0$849 = t0$859;
                    H$843[j$848 >> 2] = t1$860;
                  }
                  y0$849 = y0$849 + z0$850 | 0;
                  y1$851 = y1$851 + z1$852 | 0;
                  y2$853 = y2$853 + z2$854 | 0;
                  y3$855 = y3$855 + z3$856 | 0;
                  y4$857 = y4$857 + z4$858 | 0;
                }
                H$843[x$846 + 320 >> 2] = y0$849;
                H$843[x$846 + 324 >> 2] = y1$851;
                H$843[x$846 + 328 >> 2] = y2$853;
                H$843[x$846 + 332 >> 2] = y3$855;
                H$843[x$846 + 336 >> 2] = y4$857;
              }
              return { hash: hash$844 };
            };
          },
          /* 6 */
          /***/
          function(module2, exports2) {
            var _this = this;
            var reader = void 0;
            if (typeof self !== "undefined" && typeof self.FileReaderSync !== "undefined") {
              reader = new self.FileReaderSync();
            }
            var convStr = function(str, H8, H32, start, len, off) {
              var i = void 0, om = off % 4, lm = (len + om) % 4, j = len - lm;
              switch (om) {
                case 0:
                  H8[off] = str.charCodeAt(start + 3);
                case 1:
                  H8[off + 1 - (om << 1) | 0] = str.charCodeAt(start + 2);
                case 2:
                  H8[off + 2 - (om << 1) | 0] = str.charCodeAt(start + 1);
                case 3:
                  H8[off + 3 - (om << 1) | 0] = str.charCodeAt(start);
              }
              if (len < lm + (4 - om)) {
                return;
              }
              for (i = 4 - om; i < j; i = i + 4 | 0) {
                H32[off + i >> 2] = str.charCodeAt(start + i) << 24 | str.charCodeAt(start + i + 1) << 16 | str.charCodeAt(start + i + 2) << 8 | str.charCodeAt(start + i + 3);
              }
              switch (lm) {
                case 3:
                  H8[off + j + 1 | 0] = str.charCodeAt(start + j + 2);
                case 2:
                  H8[off + j + 2 | 0] = str.charCodeAt(start + j + 1);
                case 1:
                  H8[off + j + 3 | 0] = str.charCodeAt(start + j);
              }
            };
            var convBuf = function(buf, H8, H32, start, len, off) {
              var i = void 0, om = off % 4, lm = (len + om) % 4, j = len - lm;
              switch (om) {
                case 0:
                  H8[off] = buf[start + 3];
                case 1:
                  H8[off + 1 - (om << 1) | 0] = buf[start + 2];
                case 2:
                  H8[off + 2 - (om << 1) | 0] = buf[start + 1];
                case 3:
                  H8[off + 3 - (om << 1) | 0] = buf[start];
              }
              if (len < lm + (4 - om)) {
                return;
              }
              for (i = 4 - om; i < j; i = i + 4 | 0) {
                H32[off + i >> 2 | 0] = buf[start + i] << 24 | buf[start + i + 1] << 16 | buf[start + i + 2] << 8 | buf[start + i + 3];
              }
              switch (lm) {
                case 3:
                  H8[off + j + 1 | 0] = buf[start + j + 2];
                case 2:
                  H8[off + j + 2 | 0] = buf[start + j + 1];
                case 1:
                  H8[off + j + 3 | 0] = buf[start + j];
              }
            };
            var convBlob = function(blob, H8, H32, start, len, off) {
              var i = void 0, om = off % 4, lm = (len + om) % 4, j = len - lm;
              var buf = new Uint8Array(reader.readAsArrayBuffer(blob.slice(start, start + len)));
              switch (om) {
                case 0:
                  H8[off] = buf[3];
                case 1:
                  H8[off + 1 - (om << 1) | 0] = buf[2];
                case 2:
                  H8[off + 2 - (om << 1) | 0] = buf[1];
                case 3:
                  H8[off + 3 - (om << 1) | 0] = buf[0];
              }
              if (len < lm + (4 - om)) {
                return;
              }
              for (i = 4 - om; i < j; i = i + 4 | 0) {
                H32[off + i >> 2 | 0] = buf[i] << 24 | buf[i + 1] << 16 | buf[i + 2] << 8 | buf[i + 3];
              }
              switch (lm) {
                case 3:
                  H8[off + j + 1 | 0] = buf[j + 2];
                case 2:
                  H8[off + j + 2 | 0] = buf[j + 1];
                case 1:
                  H8[off + j + 3 | 0] = buf[j];
              }
            };
            module2.exports = function(data, H8, H32, start, len, off) {
              if (typeof data === "string") {
                return convStr(data, H8, H32, start, len, off);
              }
              if (data instanceof Array) {
                return convBuf(data, H8, H32, start, len, off);
              }
              if (_this && _this.Buffer && _this.Buffer.isBuffer(data)) {
                return convBuf(data, H8, H32, start, len, off);
              }
              if (data instanceof ArrayBuffer) {
                return convBuf(new Uint8Array(data), H8, H32, start, len, off);
              }
              if (data.buffer instanceof ArrayBuffer) {
                return convBuf(new Uint8Array(data.buffer, data.byteOffset, data.byteLength), H8, H32, start, len, off);
              }
              if (data instanceof Blob) {
                return convBlob(data, H8, H32, start, len, off);
              }
              throw new Error("Unsupported data type.");
            };
          },
          /* 7 */
          /***/
          function(module2, exports2, __webpack_require__) {
            var _createClass = /* @__PURE__ */ function() {
              function defineProperties(target, props) {
                for (var i = 0; i < props.length; i++) {
                  var descriptor = props[i];
                  descriptor.enumerable = descriptor.enumerable || false;
                  descriptor.configurable = true;
                  if ("value" in descriptor) descriptor.writable = true;
                  Object.defineProperty(target, descriptor.key, descriptor);
                }
              }
              return function(Constructor, protoProps, staticProps) {
                if (protoProps) defineProperties(Constructor.prototype, protoProps);
                if (staticProps) defineProperties(Constructor, staticProps);
                return Constructor;
              };
            }();
            function _classCallCheck(instance, Constructor) {
              if (!(instance instanceof Constructor)) {
                throw new TypeError("Cannot call a class as a function");
              }
            }
            var Rusha = __webpack_require__(0);
            var _require = __webpack_require__(1), toHex = _require.toHex;
            var Hash = function() {
              function Hash2() {
                _classCallCheck(this, Hash2);
                this._rusha = new Rusha();
                this._rusha.resetState();
              }
              Hash2.prototype.update = function update(data) {
                this._rusha.append(data);
                return this;
              };
              Hash2.prototype.digest = function digest(encoding) {
                var digest2 = this._rusha.rawEnd().buffer;
                if (!encoding) {
                  return digest2;
                }
                if (encoding === "hex") {
                  return toHex(digest2);
                }
                throw new Error("unsupported digest encoding");
              };
              _createClass(Hash2, [{
                key: "state",
                get: function() {
                  return this._rusha.getState();
                },
                set: function(state) {
                  this._rusha.setState(state);
                }
              }]);
              return Hash2;
            }();
            module2.exports = function() {
              return new Hash();
            };
          }
          /******/
        ])
      );
    });
  }
});

// ../../node_modules/.pnpm/axios@1.7.7/node_modules/axios/dist/browser/axios.cjs
var require_axios = __commonJS({
  "../../node_modules/.pnpm/axios@1.7.7/node_modules/axios/dist/browser/axios.cjs"(exports, module) {
    "use strict";
    function bind(fn, thisArg) {
      return function wrap() {
        return fn.apply(thisArg, arguments);
      };
    }
    var { toString } = Object.prototype;
    var { getPrototypeOf } = Object;
    var kindOf = /* @__PURE__ */ ((cache) => (thing) => {
      const str = toString.call(thing);
      return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());
    })(/* @__PURE__ */ Object.create(null));
    var kindOfTest = (type) => {
      type = type.toLowerCase();
      return (thing) => kindOf(thing) === type;
    };
    var typeOfTest = (type) => (thing) => typeof thing === type;
    var { isArray } = Array;
    var isUndefined = typeOfTest("undefined");
    function isBuffer(val) {
      return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);
    }
    var isArrayBuffer = kindOfTest("ArrayBuffer");
    function isArrayBufferView(val) {
      let result;
      if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
        result = ArrayBuffer.isView(val);
      } else {
        result = val && val.buffer && isArrayBuffer(val.buffer);
      }
      return result;
    }
    var isString = typeOfTest("string");
    var isFunction = typeOfTest("function");
    var isNumber = typeOfTest("number");
    var isObject = (thing) => thing !== null && typeof thing === "object";
    var isBoolean = (thing) => thing === true || thing === false;
    var isPlainObject = (val) => {
      if (kindOf(val) !== "object") {
        return false;
      }
      const prototype2 = getPrototypeOf(val);
      return (prototype2 === null || prototype2 === Object.prototype || Object.getPrototypeOf(prototype2) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);
    };
    var isDate = kindOfTest("Date");
    var isFile = kindOfTest("File");
    var isBlob = kindOfTest("Blob");
    var isFileList = kindOfTest("FileList");
    var isStream = (val) => isObject(val) && isFunction(val.pipe);
    var isFormData = (thing) => {
      let kind;
      return thing && (typeof FormData === "function" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === "formdata" || // detect form-data instance
      kind === "object" && isFunction(thing.toString) && thing.toString() === "[object FormData]"));
    };
    var isURLSearchParams = kindOfTest("URLSearchParams");
    var [isReadableStream, isRequest, isResponse, isHeaders] = ["ReadableStream", "Request", "Response", "Headers"].map(kindOfTest);
    var trim = (str) => str.trim ? str.trim() : str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
    function forEach(obj, fn, { allOwnKeys = false } = {}) {
      if (obj === null || typeof obj === "undefined") {
        return;
      }
      let i;
      let l;
      if (typeof obj !== "object") {
        obj = [obj];
      }
      if (isArray(obj)) {
        for (i = 0, l = obj.length; i < l; i++) {
          fn.call(null, obj[i], i, obj);
        }
      } else {
        const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);
        const len = keys.length;
        let key;
        for (i = 0; i < len; i++) {
          key = keys[i];
          fn.call(null, obj[key], key, obj);
        }
      }
    }
    function findKey(obj, key) {
      key = key.toLowerCase();
      const keys = Object.keys(obj);
      let i = keys.length;
      let _key;
      while (i-- > 0) {
        _key = keys[i];
        if (key === _key.toLowerCase()) {
          return _key;
        }
      }
      return null;
    }
    var _global = (() => {
      if (typeof globalThis !== "undefined") return globalThis;
      return typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : global;
    })();
    var isContextDefined = (context) => !isUndefined(context) && context !== _global;
    function merge() {
      const { caseless } = isContextDefined(this) && this || {};
      const result = {};
      const assignValue = (val, key) => {
        const targetKey = caseless && findKey(result, key) || key;
        if (isPlainObject(result[targetKey]) && isPlainObject(val)) {
          result[targetKey] = merge(result[targetKey], val);
        } else if (isPlainObject(val)) {
          result[targetKey] = merge({}, val);
        } else if (isArray(val)) {
          result[targetKey] = val.slice();
        } else {
          result[targetKey] = val;
        }
      };
      for (let i = 0, l = arguments.length; i < l; i++) {
        arguments[i] && forEach(arguments[i], assignValue);
      }
      return result;
    }
    var extend = (a, b, thisArg, { allOwnKeys } = {}) => {
      forEach(b, (val, key) => {
        if (thisArg && isFunction(val)) {
          a[key] = bind(val, thisArg);
        } else {
          a[key] = val;
        }
      }, { allOwnKeys });
      return a;
    };
    var stripBOM = (content) => {
      if (content.charCodeAt(0) === 65279) {
        content = content.slice(1);
      }
      return content;
    };
    var inherits = (constructor, superConstructor, props, descriptors2) => {
      constructor.prototype = Object.create(superConstructor.prototype, descriptors2);
      constructor.prototype.constructor = constructor;
      Object.defineProperty(constructor, "super", {
        value: superConstructor.prototype
      });
      props && Object.assign(constructor.prototype, props);
    };
    var toFlatObject = (sourceObj, destObj, filter, propFilter) => {
      let props;
      let i;
      let prop;
      const merged = {};
      destObj = destObj || {};
      if (sourceObj == null) return destObj;
      do {
        props = Object.getOwnPropertyNames(sourceObj);
        i = props.length;
        while (i-- > 0) {
          prop = props[i];
          if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {
            destObj[prop] = sourceObj[prop];
            merged[prop] = true;
          }
        }
        sourceObj = filter !== false && getPrototypeOf(sourceObj);
      } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);
      return destObj;
    };
    var endsWith = (str, searchString, position) => {
      str = String(str);
      if (position === void 0 || position > str.length) {
        position = str.length;
      }
      position -= searchString.length;
      const lastIndex = str.indexOf(searchString, position);
      return lastIndex !== -1 && lastIndex === position;
    };
    var toArray = (thing) => {
      if (!thing) return null;
      if (isArray(thing)) return thing;
      let i = thing.length;
      if (!isNumber(i)) return null;
      const arr = new Array(i);
      while (i-- > 0) {
        arr[i] = thing[i];
      }
      return arr;
    };
    var isTypedArray = /* @__PURE__ */ ((TypedArray) => {
      return (thing) => {
        return TypedArray && thing instanceof TypedArray;
      };
    })(typeof Uint8Array !== "undefined" && getPrototypeOf(Uint8Array));
    var forEachEntry = (obj, fn) => {
      const generator = obj && obj[Symbol.iterator];
      const iterator = generator.call(obj);
      let result;
      while ((result = iterator.next()) && !result.done) {
        const pair = result.value;
        fn.call(obj, pair[0], pair[1]);
      }
    };
    var matchAll = (regExp, str) => {
      let matches;
      const arr = [];
      while ((matches = regExp.exec(str)) !== null) {
        arr.push(matches);
      }
      return arr;
    };
    var isHTMLForm = kindOfTest("HTMLFormElement");
    var toCamelCase = (str) => {
      return str.toLowerCase().replace(
        /[-_\s]([a-z\d])(\w*)/g,
        function replacer(m, p1, p2) {
          return p1.toUpperCase() + p2;
        }
      );
    };
    var hasOwnProperty = (({ hasOwnProperty: hasOwnProperty2 }) => (obj, prop) => hasOwnProperty2.call(obj, prop))(Object.prototype);
    var isRegExp = kindOfTest("RegExp");
    var reduceDescriptors = (obj, reducer) => {
      const descriptors2 = Object.getOwnPropertyDescriptors(obj);
      const reducedDescriptors = {};
      forEach(descriptors2, (descriptor, name) => {
        let ret;
        if ((ret = reducer(descriptor, name, obj)) !== false) {
          reducedDescriptors[name] = ret || descriptor;
        }
      });
      Object.defineProperties(obj, reducedDescriptors);
    };
    var freezeMethods = (obj) => {
      reduceDescriptors(obj, (descriptor, name) => {
        if (isFunction(obj) && ["arguments", "caller", "callee"].indexOf(name) !== -1) {
          return false;
        }
        const value = obj[name];
        if (!isFunction(value)) return;
        descriptor.enumerable = false;
        if ("writable" in descriptor) {
          descriptor.writable = false;
          return;
        }
        if (!descriptor.set) {
          descriptor.set = () => {
            throw Error("Can not rewrite read-only method '" + name + "'");
          };
        }
      });
    };
    var toObjectSet = (arrayOrString, delimiter) => {
      const obj = {};
      const define2 = (arr) => {
        arr.forEach((value) => {
          obj[value] = true;
        });
      };
      isArray(arrayOrString) ? define2(arrayOrString) : define2(String(arrayOrString).split(delimiter));
      return obj;
    };
    var noop = () => {
    };
    var toFiniteNumber = (value, defaultValue) => {
      return value != null && Number.isFinite(value = +value) ? value : defaultValue;
    };
    var ALPHA = "abcdefghijklmnopqrstuvwxyz";
    var DIGIT = "0123456789";
    var ALPHABET = {
      DIGIT,
      ALPHA,
      ALPHA_DIGIT: ALPHA + ALPHA.toUpperCase() + DIGIT
    };
    var generateString = (size = 16, alphabet = ALPHABET.ALPHA_DIGIT) => {
      let str = "";
      const { length } = alphabet;
      while (size--) {
        str += alphabet[Math.random() * length | 0];
      }
      return str;
    };
    function isSpecCompliantForm(thing) {
      return !!(thing && isFunction(thing.append) && thing[Symbol.toStringTag] === "FormData" && thing[Symbol.iterator]);
    }
    var toJSONObject = (obj) => {
      const stack = new Array(10);
      const visit = (source, i) => {
        if (isObject(source)) {
          if (stack.indexOf(source) >= 0) {
            return;
          }
          if (!("toJSON" in source)) {
            stack[i] = source;
            const target = isArray(source) ? [] : {};
            forEach(source, (value, key) => {
              const reducedValue = visit(value, i + 1);
              !isUndefined(reducedValue) && (target[key] = reducedValue);
            });
            stack[i] = void 0;
            return target;
          }
        }
        return source;
      };
      return visit(obj, 0);
    };
    var isAsyncFn = kindOfTest("AsyncFunction");
    var isThenable = (thing) => thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);
    var _setImmediate = ((setImmediateSupported, postMessageSupported) => {
      if (setImmediateSupported) {
        return setImmediate;
      }
      return postMessageSupported ? ((token, callbacks) => {
        _global.addEventListener("message", ({ source, data }) => {
          if (source === _global && data === token) {
            callbacks.length && callbacks.shift()();
          }
        }, false);
        return (cb) => {
          callbacks.push(cb);
          _global.postMessage(token, "*");
        };
      })(`axios@${Math.random()}`, []) : (cb) => setTimeout(cb);
    })(
      typeof setImmediate === "function",
      isFunction(_global.postMessage)
    );
    var asap = typeof queueMicrotask !== "undefined" ? queueMicrotask.bind(_global) : typeof process !== "undefined" && process.nextTick || _setImmediate;
    var utils$1 = {
      isArray,
      isArrayBuffer,
      isBuffer,
      isFormData,
      isArrayBufferView,
      isString,
      isNumber,
      isBoolean,
      isObject,
      isPlainObject,
      isReadableStream,
      isRequest,
      isResponse,
      isHeaders,
      isUndefined,
      isDate,
      isFile,
      isBlob,
      isRegExp,
      isFunction,
      isStream,
      isURLSearchParams,
      isTypedArray,
      isFileList,
      forEach,
      merge,
      extend,
      trim,
      stripBOM,
      inherits,
      toFlatObject,
      kindOf,
      kindOfTest,
      endsWith,
      toArray,
      forEachEntry,
      matchAll,
      isHTMLForm,
      hasOwnProperty,
      hasOwnProp: hasOwnProperty,
      // an alias to avoid ESLint no-prototype-builtins detection
      reduceDescriptors,
      freezeMethods,
      toObjectSet,
      toCamelCase,
      noop,
      toFiniteNumber,
      findKey,
      global: _global,
      isContextDefined,
      ALPHABET,
      generateString,
      isSpecCompliantForm,
      toJSONObject,
      isAsyncFn,
      isThenable,
      setImmediate: _setImmediate,
      asap
    };
    function AxiosError(message, code, config, request, response) {
      Error.call(this);
      if (Error.captureStackTrace) {
        Error.captureStackTrace(this, this.constructor);
      } else {
        this.stack = new Error().stack;
      }
      this.message = message;
      this.name = "AxiosError";
      code && (this.code = code);
      config && (this.config = config);
      request && (this.request = request);
      if (response) {
        this.response = response;
        this.status = response.status ? response.status : null;
      }
    }
    utils$1.inherits(AxiosError, Error, {
      toJSON: function toJSON() {
        return {
          // Standard
          message: this.message,
          name: this.name,
          // Microsoft
          description: this.description,
          number: this.number,
          // Mozilla
          fileName: this.fileName,
          lineNumber: this.lineNumber,
          columnNumber: this.columnNumber,
          stack: this.stack,
          // Axios
          config: utils$1.toJSONObject(this.config),
          code: this.code,
          status: this.status
        };
      }
    });
    var prototype$1 = AxiosError.prototype;
    var descriptors = {};
    [
      "ERR_BAD_OPTION_VALUE",
      "ERR_BAD_OPTION",
      "ECONNABORTED",
      "ETIMEDOUT",
      "ERR_NETWORK",
      "ERR_FR_TOO_MANY_REDIRECTS",
      "ERR_DEPRECATED",
      "ERR_BAD_RESPONSE",
      "ERR_BAD_REQUEST",
      "ERR_CANCELED",
      "ERR_NOT_SUPPORT",
      "ERR_INVALID_URL"
      // eslint-disable-next-line func-names
    ].forEach((code) => {
      descriptors[code] = { value: code };
    });
    Object.defineProperties(AxiosError, descriptors);
    Object.defineProperty(prototype$1, "isAxiosError", { value: true });
    AxiosError.from = (error, code, config, request, response, customProps) => {
      const axiosError = Object.create(prototype$1);
      utils$1.toFlatObject(error, axiosError, function filter(obj) {
        return obj !== Error.prototype;
      }, (prop) => {
        return prop !== "isAxiosError";
      });
      AxiosError.call(axiosError, error.message, code, config, request, response);
      axiosError.cause = error;
      axiosError.name = error.name;
      customProps && Object.assign(axiosError, customProps);
      return axiosError;
    };
    var httpAdapter = null;
    function isVisitable(thing) {
      return utils$1.isPlainObject(thing) || utils$1.isArray(thing);
    }
    function removeBrackets(key) {
      return utils$1.endsWith(key, "[]") ? key.slice(0, -2) : key;
    }
    function renderKey(path, key, dots) {
      if (!path) return key;
      return path.concat(key).map(function each(token, i) {
        token = removeBrackets(token);
        return !dots && i ? "[" + token + "]" : token;
      }).join(dots ? "." : "");
    }
    function isFlatArray(arr) {
      return utils$1.isArray(arr) && !arr.some(isVisitable);
    }
    var predicates = utils$1.toFlatObject(utils$1, {}, null, function filter(prop) {
      return /^is[A-Z]/.test(prop);
    });
    function toFormData(obj, formData, options) {
      if (!utils$1.isObject(obj)) {
        throw new TypeError("target must be an object");
      }
      formData = formData || new FormData();
      options = utils$1.toFlatObject(options, {
        metaTokens: true,
        dots: false,
        indexes: false
      }, false, function defined(option, source) {
        return !utils$1.isUndefined(source[option]);
      });
      const metaTokens = options.metaTokens;
      const visitor = options.visitor || defaultVisitor;
      const dots = options.dots;
      const indexes = options.indexes;
      const _Blob = options.Blob || typeof Blob !== "undefined" && Blob;
      const useBlob = _Blob && utils$1.isSpecCompliantForm(formData);
      if (!utils$1.isFunction(visitor)) {
        throw new TypeError("visitor must be a function");
      }
      function convertValue(value) {
        if (value === null) return "";
        if (utils$1.isDate(value)) {
          return value.toISOString();
        }
        if (!useBlob && utils$1.isBlob(value)) {
          throw new AxiosError("Blob is not supported. Use a Buffer instead.");
        }
        if (utils$1.isArrayBuffer(value) || utils$1.isTypedArray(value)) {
          return useBlob && typeof Blob === "function" ? new Blob([value]) : Buffer.from(value);
        }
        return value;
      }
      function defaultVisitor(value, key, path) {
        let arr = value;
        if (value && !path && typeof value === "object") {
          if (utils$1.endsWith(key, "{}")) {
            key = metaTokens ? key : key.slice(0, -2);
            value = JSON.stringify(value);
          } else if (utils$1.isArray(value) && isFlatArray(value) || (utils$1.isFileList(value) || utils$1.endsWith(key, "[]")) && (arr = utils$1.toArray(value))) {
            key = removeBrackets(key);
            arr.forEach(function each(el, index) {
              !(utils$1.isUndefined(el) || el === null) && formData.append(
                // eslint-disable-next-line no-nested-ternary
                indexes === true ? renderKey([key], index, dots) : indexes === null ? key : key + "[]",
                convertValue(el)
              );
            });
            return false;
          }
        }
        if (isVisitable(value)) {
          return true;
        }
        formData.append(renderKey(path, key, dots), convertValue(value));
        return false;
      }
      const stack = [];
      const exposedHelpers = Object.assign(predicates, {
        defaultVisitor,
        convertValue,
        isVisitable
      });
      function build(value, path) {
        if (utils$1.isUndefined(value)) return;
        if (stack.indexOf(value) !== -1) {
          throw Error("Circular reference detected in " + path.join("."));
        }
        stack.push(value);
        utils$1.forEach(value, function each(el, key) {
          const result = !(utils$1.isUndefined(el) || el === null) && visitor.call(
            formData,
            el,
            utils$1.isString(key) ? key.trim() : key,
            path,
            exposedHelpers
          );
          if (result === true) {
            build(el, path ? path.concat(key) : [key]);
          }
        });
        stack.pop();
      }
      if (!utils$1.isObject(obj)) {
        throw new TypeError("data must be an object");
      }
      build(obj);
      return formData;
    }
    function encode$1(str) {
      const charMap = {
        "!": "%21",
        "'": "%27",
        "(": "%28",
        ")": "%29",
        "~": "%7E",
        "%20": "+",
        "%00": "\0"
      };
      return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {
        return charMap[match];
      });
    }
    function AxiosURLSearchParams(params, options) {
      this._pairs = [];
      params && toFormData(params, this, options);
    }
    var prototype = AxiosURLSearchParams.prototype;
    prototype.append = function append(name, value) {
      this._pairs.push([name, value]);
    };
    prototype.toString = function toString2(encoder) {
      const _encode = encoder ? function(value) {
        return encoder.call(this, value, encode$1);
      } : encode$1;
      return this._pairs.map(function each(pair) {
        return _encode(pair[0]) + "=" + _encode(pair[1]);
      }, "").join("&");
    };
    function encode(val) {
      return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
    }
    function buildURL(url, params, options) {
      if (!params) {
        return url;
      }
      const _encode = options && options.encode || encode;
      const serializeFn = options && options.serialize;
      let serializedParams;
      if (serializeFn) {
        serializedParams = serializeFn(params, options);
      } else {
        serializedParams = utils$1.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);
      }
      if (serializedParams) {
        const hashmarkIndex = url.indexOf("#");
        if (hashmarkIndex !== -1) {
          url = url.slice(0, hashmarkIndex);
        }
        url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
      }
      return url;
    }
    var InterceptorManager = class {
      constructor() {
        this.handlers = [];
      }
      /**
       * Add a new interceptor to the stack
       *
       * @param {Function} fulfilled The function to handle `then` for a `Promise`
       * @param {Function} rejected The function to handle `reject` for a `Promise`
       *
       * @return {Number} An ID used to remove interceptor later
       */
      use(fulfilled, rejected, options) {
        this.handlers.push({
          fulfilled,
          rejected,
          synchronous: options ? options.synchronous : false,
          runWhen: options ? options.runWhen : null
        });
        return this.handlers.length - 1;
      }
      /**
       * Remove an interceptor from the stack
       *
       * @param {Number} id The ID that was returned by `use`
       *
       * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise
       */
      eject(id) {
        if (this.handlers[id]) {
          this.handlers[id] = null;
        }
      }
      /**
       * Clear all interceptors from the stack
       *
       * @returns {void}
       */
      clear() {
        if (this.handlers) {
          this.handlers = [];
        }
      }
      /**
       * Iterate over all the registered interceptors
       *
       * This method is particularly useful for skipping over any
       * interceptors that may have become `null` calling `eject`.
       *
       * @param {Function} fn The function to call for each interceptor
       *
       * @returns {void}
       */
      forEach(fn) {
        utils$1.forEach(this.handlers, function forEachHandler(h) {
          if (h !== null) {
            fn(h);
          }
        });
      }
    };
    var InterceptorManager$1 = InterceptorManager;
    var transitionalDefaults = {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    };
    var URLSearchParams$1 = typeof URLSearchParams !== "undefined" ? URLSearchParams : AxiosURLSearchParams;
    var FormData$1 = typeof FormData !== "undefined" ? FormData : null;
    var Blob$1 = typeof Blob !== "undefined" ? Blob : null;
    var platform$1 = {
      isBrowser: true,
      classes: {
        URLSearchParams: URLSearchParams$1,
        FormData: FormData$1,
        Blob: Blob$1
      },
      protocols: ["http", "https", "file", "blob", "url", "data"]
    };
    var hasBrowserEnv = typeof window !== "undefined" && typeof document !== "undefined";
    var _navigator = typeof navigator === "object" && navigator || void 0;
    var hasStandardBrowserEnv = hasBrowserEnv && (!_navigator || ["ReactNative", "NativeScript", "NS"].indexOf(_navigator.product) < 0);
    var hasStandardBrowserWebWorkerEnv = (() => {
      return typeof WorkerGlobalScope !== "undefined" && // eslint-disable-next-line no-undef
      self instanceof WorkerGlobalScope && typeof self.importScripts === "function";
    })();
    var origin = hasBrowserEnv && window.location.href || "http://localhost";
    var utils = Object.freeze({
      __proto__: null,
      hasBrowserEnv,
      hasStandardBrowserWebWorkerEnv,
      hasStandardBrowserEnv,
      navigator: _navigator,
      origin
    });
    var platform = {
      ...utils,
      ...platform$1
    };
    function toURLEncodedForm(data, options) {
      return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({
        visitor: function(value, key, path, helpers) {
          if (platform.isNode && utils$1.isBuffer(value)) {
            this.append(key, value.toString("base64"));
            return false;
          }
          return helpers.defaultVisitor.apply(this, arguments);
        }
      }, options));
    }
    function parsePropPath(name) {
      return utils$1.matchAll(/\w+|\[(\w*)]/g, name).map((match) => {
        return match[0] === "[]" ? "" : match[1] || match[0];
      });
    }
    function arrayToObject(arr) {
      const obj = {};
      const keys = Object.keys(arr);
      let i;
      const len = keys.length;
      let key;
      for (i = 0; i < len; i++) {
        key = keys[i];
        obj[key] = arr[key];
      }
      return obj;
    }
    function formDataToJSON(formData) {
      function buildPath(path, value, target, index) {
        let name = path[index++];
        if (name === "__proto__") return true;
        const isNumericKey = Number.isFinite(+name);
        const isLast = index >= path.length;
        name = !name && utils$1.isArray(target) ? target.length : name;
        if (isLast) {
          if (utils$1.hasOwnProp(target, name)) {
            target[name] = [target[name], value];
          } else {
            target[name] = value;
          }
          return !isNumericKey;
        }
        if (!target[name] || !utils$1.isObject(target[name])) {
          target[name] = [];
        }
        const result = buildPath(path, value, target[name], index);
        if (result && utils$1.isArray(target[name])) {
          target[name] = arrayToObject(target[name]);
        }
        return !isNumericKey;
      }
      if (utils$1.isFormData(formData) && utils$1.isFunction(formData.entries)) {
        const obj = {};
        utils$1.forEachEntry(formData, (name, value) => {
          buildPath(parsePropPath(name), value, obj, 0);
        });
        return obj;
      }
      return null;
    }
    function stringifySafely(rawValue, parser, encoder) {
      if (utils$1.isString(rawValue)) {
        try {
          (parser || JSON.parse)(rawValue);
          return utils$1.trim(rawValue);
        } catch (e) {
          if (e.name !== "SyntaxError") {
            throw e;
          }
        }
      }
      return (encoder || JSON.stringify)(rawValue);
    }
    var defaults = {
      transitional: transitionalDefaults,
      adapter: ["xhr", "http", "fetch"],
      transformRequest: [function transformRequest(data, headers) {
        const contentType = headers.getContentType() || "";
        const hasJSONContentType = contentType.indexOf("application/json") > -1;
        const isObjectPayload = utils$1.isObject(data);
        if (isObjectPayload && utils$1.isHTMLForm(data)) {
          data = new FormData(data);
        }
        const isFormData2 = utils$1.isFormData(data);
        if (isFormData2) {
          return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;
        }
        if (utils$1.isArrayBuffer(data) || utils$1.isBuffer(data) || utils$1.isStream(data) || utils$1.isFile(data) || utils$1.isBlob(data) || utils$1.isReadableStream(data)) {
          return data;
        }
        if (utils$1.isArrayBufferView(data)) {
          return data.buffer;
        }
        if (utils$1.isURLSearchParams(data)) {
          headers.setContentType("application/x-www-form-urlencoded;charset=utf-8", false);
          return data.toString();
        }
        let isFileList2;
        if (isObjectPayload) {
          if (contentType.indexOf("application/x-www-form-urlencoded") > -1) {
            return toURLEncodedForm(data, this.formSerializer).toString();
          }
          if ((isFileList2 = utils$1.isFileList(data)) || contentType.indexOf("multipart/form-data") > -1) {
            const _FormData = this.env && this.env.FormData;
            return toFormData(
              isFileList2 ? { "files[]": data } : data,
              _FormData && new _FormData(),
              this.formSerializer
            );
          }
        }
        if (isObjectPayload || hasJSONContentType) {
          headers.setContentType("application/json", false);
          return stringifySafely(data);
        }
        return data;
      }],
      transformResponse: [function transformResponse(data) {
        const transitional = this.transitional || defaults.transitional;
        const forcedJSONParsing = transitional && transitional.forcedJSONParsing;
        const JSONRequested = this.responseType === "json";
        if (utils$1.isResponse(data) || utils$1.isReadableStream(data)) {
          return data;
        }
        if (data && utils$1.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {
          const silentJSONParsing = transitional && transitional.silentJSONParsing;
          const strictJSONParsing = !silentJSONParsing && JSONRequested;
          try {
            return JSON.parse(data);
          } catch (e) {
            if (strictJSONParsing) {
              if (e.name === "SyntaxError") {
                throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);
              }
              throw e;
            }
          }
        }
        return data;
      }],
      /**
       * A timeout in milliseconds to abort a request. If set to 0 (default) a
       * timeout is not created.
       */
      timeout: 0,
      xsrfCookieName: "XSRF-TOKEN",
      xsrfHeaderName: "X-XSRF-TOKEN",
      maxContentLength: -1,
      maxBodyLength: -1,
      env: {
        FormData: platform.classes.FormData,
        Blob: platform.classes.Blob
      },
      validateStatus: function validateStatus(status) {
        return status >= 200 && status < 300;
      },
      headers: {
        common: {
          "Accept": "application/json, text/plain, */*",
          "Content-Type": void 0
        }
      }
    };
    utils$1.forEach(["delete", "get", "head", "post", "put", "patch"], (method) => {
      defaults.headers[method] = {};
    });
    var defaults$1 = defaults;
    var ignoreDuplicateOf = utils$1.toObjectSet([
      "age",
      "authorization",
      "content-length",
      "content-type",
      "etag",
      "expires",
      "from",
      "host",
      "if-modified-since",
      "if-unmodified-since",
      "last-modified",
      "location",
      "max-forwards",
      "proxy-authorization",
      "referer",
      "retry-after",
      "user-agent"
    ]);
    var parseHeaders = (rawHeaders) => {
      const parsed = {};
      let key;
      let val;
      let i;
      rawHeaders && rawHeaders.split("\n").forEach(function parser(line) {
        i = line.indexOf(":");
        key = line.substring(0, i).trim().toLowerCase();
        val = line.substring(i + 1).trim();
        if (!key || parsed[key] && ignoreDuplicateOf[key]) {
          return;
        }
        if (key === "set-cookie") {
          if (parsed[key]) {
            parsed[key].push(val);
          } else {
            parsed[key] = [val];
          }
        } else {
          parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
        }
      });
      return parsed;
    };
    var $internals = Symbol("internals");
    function normalizeHeader(header) {
      return header && String(header).trim().toLowerCase();
    }
    function normalizeValue(value) {
      if (value === false || value == null) {
        return value;
      }
      return utils$1.isArray(value) ? value.map(normalizeValue) : String(value);
    }
    function parseTokens(str) {
      const tokens = /* @__PURE__ */ Object.create(null);
      const tokensRE = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
      let match;
      while (match = tokensRE.exec(str)) {
        tokens[match[1]] = match[2];
      }
      return tokens;
    }
    var isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());
    function matchHeaderValue(context, value, header, filter, isHeaderNameFilter) {
      if (utils$1.isFunction(filter)) {
        return filter.call(this, value, header);
      }
      if (isHeaderNameFilter) {
        value = header;
      }
      if (!utils$1.isString(value)) return;
      if (utils$1.isString(filter)) {
        return value.indexOf(filter) !== -1;
      }
      if (utils$1.isRegExp(filter)) {
        return filter.test(value);
      }
    }
    function formatHeader(header) {
      return header.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (w, char, str) => {
        return char.toUpperCase() + str;
      });
    }
    function buildAccessors(obj, header) {
      const accessorName = utils$1.toCamelCase(" " + header);
      ["get", "set", "has"].forEach((methodName) => {
        Object.defineProperty(obj, methodName + accessorName, {
          value: function(arg1, arg2, arg3) {
            return this[methodName].call(this, header, arg1, arg2, arg3);
          },
          configurable: true
        });
      });
    }
    var AxiosHeaders = class {
      constructor(headers) {
        headers && this.set(headers);
      }
      set(header, valueOrRewrite, rewrite) {
        const self2 = this;
        function setHeader(_value, _header, _rewrite) {
          const lHeader = normalizeHeader(_header);
          if (!lHeader) {
            throw new Error("header name must be a non-empty string");
          }
          const key = utils$1.findKey(self2, lHeader);
          if (!key || self2[key] === void 0 || _rewrite === true || _rewrite === void 0 && self2[key] !== false) {
            self2[key || _header] = normalizeValue(_value);
          }
        }
        const setHeaders = (headers, _rewrite) => utils$1.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));
        if (utils$1.isPlainObject(header) || header instanceof this.constructor) {
          setHeaders(header, valueOrRewrite);
        } else if (utils$1.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {
          setHeaders(parseHeaders(header), valueOrRewrite);
        } else if (utils$1.isHeaders(header)) {
          for (const [key, value] of header.entries()) {
            setHeader(value, key, rewrite);
          }
        } else {
          header != null && setHeader(valueOrRewrite, header, rewrite);
        }
        return this;
      }
      get(header, parser) {
        header = normalizeHeader(header);
        if (header) {
          const key = utils$1.findKey(this, header);
          if (key) {
            const value = this[key];
            if (!parser) {
              return value;
            }
            if (parser === true) {
              return parseTokens(value);
            }
            if (utils$1.isFunction(parser)) {
              return parser.call(this, value, key);
            }
            if (utils$1.isRegExp(parser)) {
              return parser.exec(value);
            }
            throw new TypeError("parser must be boolean|regexp|function");
          }
        }
      }
      has(header, matcher) {
        header = normalizeHeader(header);
        if (header) {
          const key = utils$1.findKey(this, header);
          return !!(key && this[key] !== void 0 && (!matcher || matchHeaderValue(this, this[key], key, matcher)));
        }
        return false;
      }
      delete(header, matcher) {
        const self2 = this;
        let deleted = false;
        function deleteHeader(_header) {
          _header = normalizeHeader(_header);
          if (_header) {
            const key = utils$1.findKey(self2, _header);
            if (key && (!matcher || matchHeaderValue(self2, self2[key], key, matcher))) {
              delete self2[key];
              deleted = true;
            }
          }
        }
        if (utils$1.isArray(header)) {
          header.forEach(deleteHeader);
        } else {
          deleteHeader(header);
        }
        return deleted;
      }
      clear(matcher) {
        const keys = Object.keys(this);
        let i = keys.length;
        let deleted = false;
        while (i--) {
          const key = keys[i];
          if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {
            delete this[key];
            deleted = true;
          }
        }
        return deleted;
      }
      normalize(format) {
        const self2 = this;
        const headers = {};
        utils$1.forEach(this, (value, header) => {
          const key = utils$1.findKey(headers, header);
          if (key) {
            self2[key] = normalizeValue(value);
            delete self2[header];
            return;
          }
          const normalized = format ? formatHeader(header) : String(header).trim();
          if (normalized !== header) {
            delete self2[header];
          }
          self2[normalized] = normalizeValue(value);
          headers[normalized] = true;
        });
        return this;
      }
      concat(...targets) {
        return this.constructor.concat(this, ...targets);
      }
      toJSON(asStrings) {
        const obj = /* @__PURE__ */ Object.create(null);
        utils$1.forEach(this, (value, header) => {
          value != null && value !== false && (obj[header] = asStrings && utils$1.isArray(value) ? value.join(", ") : value);
        });
        return obj;
      }
      [Symbol.iterator]() {
        return Object.entries(this.toJSON())[Symbol.iterator]();
      }
      toString() {
        return Object.entries(this.toJSON()).map(([header, value]) => header + ": " + value).join("\n");
      }
      get [Symbol.toStringTag]() {
        return "AxiosHeaders";
      }
      static from(thing) {
        return thing instanceof this ? thing : new this(thing);
      }
      static concat(first, ...targets) {
        const computed = new this(first);
        targets.forEach((target) => computed.set(target));
        return computed;
      }
      static accessor(header) {
        const internals = this[$internals] = this[$internals] = {
          accessors: {}
        };
        const accessors = internals.accessors;
        const prototype2 = this.prototype;
        function defineAccessor(_header) {
          const lHeader = normalizeHeader(_header);
          if (!accessors[lHeader]) {
            buildAccessors(prototype2, _header);
            accessors[lHeader] = true;
          }
        }
        utils$1.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);
        return this;
      }
    };
    AxiosHeaders.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
    utils$1.reduceDescriptors(AxiosHeaders.prototype, ({ value }, key) => {
      let mapped = key[0].toUpperCase() + key.slice(1);
      return {
        get: () => value,
        set(headerValue) {
          this[mapped] = headerValue;
        }
      };
    });
    utils$1.freezeMethods(AxiosHeaders);
    var AxiosHeaders$1 = AxiosHeaders;
    function transformData(fns, response) {
      const config = this || defaults$1;
      const context = response || config;
      const headers = AxiosHeaders$1.from(context.headers);
      let data = context.data;
      utils$1.forEach(fns, function transform(fn) {
        data = fn.call(config, data, headers.normalize(), response ? response.status : void 0);
      });
      headers.normalize();
      return data;
    }
    function isCancel(value) {
      return !!(value && value.__CANCEL__);
    }
    function CanceledError(message, config, request) {
      AxiosError.call(this, message == null ? "canceled" : message, AxiosError.ERR_CANCELED, config, request);
      this.name = "CanceledError";
    }
    utils$1.inherits(CanceledError, AxiosError, {
      __CANCEL__: true
    });
    function settle(resolve, reject, response) {
      const validateStatus = response.config.validateStatus;
      if (!response.status || !validateStatus || validateStatus(response.status)) {
        resolve(response);
      } else {
        reject(new AxiosError(
          "Request failed with status code " + response.status,
          [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],
          response.config,
          response.request,
          response
        ));
      }
    }
    function parseProtocol(url) {
      const match = /^([-+\w]{1,25})(:?\/\/|:)/.exec(url);
      return match && match[1] || "";
    }
    function speedometer(samplesCount, min) {
      samplesCount = samplesCount || 10;
      const bytes = new Array(samplesCount);
      const timestamps = new Array(samplesCount);
      let head = 0;
      let tail = 0;
      let firstSampleTS;
      min = min !== void 0 ? min : 1e3;
      return function push(chunkLength) {
        const now = Date.now();
        const startedAt = timestamps[tail];
        if (!firstSampleTS) {
          firstSampleTS = now;
        }
        bytes[head] = chunkLength;
        timestamps[head] = now;
        let i = tail;
        let bytesCount = 0;
        while (i !== head) {
          bytesCount += bytes[i++];
          i = i % samplesCount;
        }
        head = (head + 1) % samplesCount;
        if (head === tail) {
          tail = (tail + 1) % samplesCount;
        }
        if (now - firstSampleTS < min) {
          return;
        }
        const passed = startedAt && now - startedAt;
        return passed ? Math.round(bytesCount * 1e3 / passed) : void 0;
      };
    }
    function throttle(fn, freq) {
      let timestamp = 0;
      let threshold = 1e3 / freq;
      let lastArgs;
      let timer;
      const invoke = (args, now = Date.now()) => {
        timestamp = now;
        lastArgs = null;
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        fn.apply(null, args);
      };
      const throttled = (...args) => {
        const now = Date.now();
        const passed = now - timestamp;
        if (passed >= threshold) {
          invoke(args, now);
        } else {
          lastArgs = args;
          if (!timer) {
            timer = setTimeout(() => {
              timer = null;
              invoke(lastArgs);
            }, threshold - passed);
          }
        }
      };
      const flush = () => lastArgs && invoke(lastArgs);
      return [throttled, flush];
    }
    var progressEventReducer = (listener, isDownloadStream, freq = 3) => {
      let bytesNotified = 0;
      const _speedometer = speedometer(50, 250);
      return throttle((e) => {
        const loaded = e.loaded;
        const total = e.lengthComputable ? e.total : void 0;
        const progressBytes = loaded - bytesNotified;
        const rate = _speedometer(progressBytes);
        const inRange = loaded <= total;
        bytesNotified = loaded;
        const data = {
          loaded,
          total,
          progress: total ? loaded / total : void 0,
          bytes: progressBytes,
          rate: rate ? rate : void 0,
          estimated: rate && total && inRange ? (total - loaded) / rate : void 0,
          event: e,
          lengthComputable: total != null,
          [isDownloadStream ? "download" : "upload"]: true
        };
        listener(data);
      }, freq);
    };
    var progressEventDecorator = (total, throttled) => {
      const lengthComputable = total != null;
      return [(loaded) => throttled[0]({
        lengthComputable,
        total,
        loaded
      }), throttled[1]];
    };
    var asyncDecorator = (fn) => (...args) => utils$1.asap(() => fn(...args));
    var isURLSameOrigin = platform.hasStandardBrowserEnv ? (
      // Standard browser envs have full support of the APIs needed to test
      // whether the request URL is of the same origin as current location.
      function standardBrowserEnv() {
        const msie = platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent);
        const urlParsingNode = document.createElement("a");
        let originURL;
        function resolveURL(url) {
          let href = url;
          if (msie) {
            urlParsingNode.setAttribute("href", href);
            href = urlParsingNode.href;
          }
          urlParsingNode.setAttribute("href", href);
          return {
            href: urlParsingNode.href,
            protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, "") : "",
            host: urlParsingNode.host,
            search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, "") : "",
            hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, "") : "",
            hostname: urlParsingNode.hostname,
            port: urlParsingNode.port,
            pathname: urlParsingNode.pathname.charAt(0) === "/" ? urlParsingNode.pathname : "/" + urlParsingNode.pathname
          };
        }
        originURL = resolveURL(window.location.href);
        return function isURLSameOrigin2(requestURL) {
          const parsed = utils$1.isString(requestURL) ? resolveURL(requestURL) : requestURL;
          return parsed.protocol === originURL.protocol && parsed.host === originURL.host;
        };
      }()
    ) : (
      // Non standard browser envs (web workers, react-native) lack needed support.
      /* @__PURE__ */ function nonStandardBrowserEnv() {
        return function isURLSameOrigin2() {
          return true;
        };
      }()
    );
    var cookies = platform.hasStandardBrowserEnv ? (
      // Standard browser envs support document.cookie
      {
        write(name, value, expires, path, domain, secure) {
          const cookie = [name + "=" + encodeURIComponent(value)];
          utils$1.isNumber(expires) && cookie.push("expires=" + new Date(expires).toGMTString());
          utils$1.isString(path) && cookie.push("path=" + path);
          utils$1.isString(domain) && cookie.push("domain=" + domain);
          secure === true && cookie.push("secure");
          document.cookie = cookie.join("; ");
        },
        read(name) {
          const match = document.cookie.match(new RegExp("(^|;\\s*)(" + name + ")=([^;]*)"));
          return match ? decodeURIComponent(match[3]) : null;
        },
        remove(name) {
          this.write(name, "", Date.now() - 864e5);
        }
      }
    ) : (
      // Non-standard browser env (web workers, react-native) lack needed support.
      {
        write() {
        },
        read() {
          return null;
        },
        remove() {
        }
      }
    );
    function isAbsoluteURL(url) {
      return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
    }
    function combineURLs(baseURL, relativeURL) {
      return relativeURL ? baseURL.replace(/\/?\/$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
    }
    function buildFullPath(baseURL, requestedURL) {
      if (baseURL && !isAbsoluteURL(requestedURL)) {
        return combineURLs(baseURL, requestedURL);
      }
      return requestedURL;
    }
    var headersToObject = (thing) => thing instanceof AxiosHeaders$1 ? { ...thing } : thing;
    function mergeConfig(config1, config2) {
      config2 = config2 || {};
      const config = {};
      function getMergedValue(target, source, caseless) {
        if (utils$1.isPlainObject(target) && utils$1.isPlainObject(source)) {
          return utils$1.merge.call({ caseless }, target, source);
        } else if (utils$1.isPlainObject(source)) {
          return utils$1.merge({}, source);
        } else if (utils$1.isArray(source)) {
          return source.slice();
        }
        return source;
      }
      function mergeDeepProperties(a, b, caseless) {
        if (!utils$1.isUndefined(b)) {
          return getMergedValue(a, b, caseless);
        } else if (!utils$1.isUndefined(a)) {
          return getMergedValue(void 0, a, caseless);
        }
      }
      function valueFromConfig2(a, b) {
        if (!utils$1.isUndefined(b)) {
          return getMergedValue(void 0, b);
        }
      }
      function defaultToConfig2(a, b) {
        if (!utils$1.isUndefined(b)) {
          return getMergedValue(void 0, b);
        } else if (!utils$1.isUndefined(a)) {
          return getMergedValue(void 0, a);
        }
      }
      function mergeDirectKeys(a, b, prop) {
        if (prop in config2) {
          return getMergedValue(a, b);
        } else if (prop in config1) {
          return getMergedValue(void 0, a);
        }
      }
      const mergeMap = {
        url: valueFromConfig2,
        method: valueFromConfig2,
        data: valueFromConfig2,
        baseURL: defaultToConfig2,
        transformRequest: defaultToConfig2,
        transformResponse: defaultToConfig2,
        paramsSerializer: defaultToConfig2,
        timeout: defaultToConfig2,
        timeoutMessage: defaultToConfig2,
        withCredentials: defaultToConfig2,
        withXSRFToken: defaultToConfig2,
        adapter: defaultToConfig2,
        responseType: defaultToConfig2,
        xsrfCookieName: defaultToConfig2,
        xsrfHeaderName: defaultToConfig2,
        onUploadProgress: defaultToConfig2,
        onDownloadProgress: defaultToConfig2,
        decompress: defaultToConfig2,
        maxContentLength: defaultToConfig2,
        maxBodyLength: defaultToConfig2,
        beforeRedirect: defaultToConfig2,
        transport: defaultToConfig2,
        httpAgent: defaultToConfig2,
        httpsAgent: defaultToConfig2,
        cancelToken: defaultToConfig2,
        socketPath: defaultToConfig2,
        responseEncoding: defaultToConfig2,
        validateStatus: mergeDirectKeys,
        headers: (a, b) => mergeDeepProperties(headersToObject(a), headersToObject(b), true)
      };
      utils$1.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {
        const merge2 = mergeMap[prop] || mergeDeepProperties;
        const configValue = merge2(config1[prop], config2[prop], prop);
        utils$1.isUndefined(configValue) && merge2 !== mergeDirectKeys || (config[prop] = configValue);
      });
      return config;
    }
    var resolveConfig = (config) => {
      const newConfig = mergeConfig({}, config);
      let { data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth } = newConfig;
      newConfig.headers = headers = AxiosHeaders$1.from(headers);
      newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url), config.params, config.paramsSerializer);
      if (auth) {
        headers.set(
          "Authorization",
          "Basic " + btoa((auth.username || "") + ":" + (auth.password ? unescape(encodeURIComponent(auth.password)) : ""))
        );
      }
      let contentType;
      if (utils$1.isFormData(data)) {
        if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {
          headers.setContentType(void 0);
        } else if ((contentType = headers.getContentType()) !== false) {
          const [type, ...tokens] = contentType ? contentType.split(";").map((token) => token.trim()).filter(Boolean) : [];
          headers.setContentType([type || "multipart/form-data", ...tokens].join("; "));
        }
      }
      if (platform.hasStandardBrowserEnv) {
        withXSRFToken && utils$1.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));
        if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {
          const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);
          if (xsrfValue) {
            headers.set(xsrfHeaderName, xsrfValue);
          }
        }
      }
      return newConfig;
    };
    var isXHRAdapterSupported = typeof XMLHttpRequest !== "undefined";
    var xhrAdapter = isXHRAdapterSupported && function(config) {
      return new Promise(function dispatchXhrRequest(resolve, reject) {
        const _config = resolveConfig(config);
        let requestData = _config.data;
        const requestHeaders = AxiosHeaders$1.from(_config.headers).normalize();
        let { responseType, onUploadProgress, onDownloadProgress } = _config;
        let onCanceled;
        let uploadThrottled, downloadThrottled;
        let flushUpload, flushDownload;
        function done() {
          flushUpload && flushUpload();
          flushDownload && flushDownload();
          _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);
          _config.signal && _config.signal.removeEventListener("abort", onCanceled);
        }
        let request = new XMLHttpRequest();
        request.open(_config.method.toUpperCase(), _config.url, true);
        request.timeout = _config.timeout;
        function onloadend() {
          if (!request) {
            return;
          }
          const responseHeaders = AxiosHeaders$1.from(
            "getAllResponseHeaders" in request && request.getAllResponseHeaders()
          );
          const responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
          const response = {
            data: responseData,
            status: request.status,
            statusText: request.statusText,
            headers: responseHeaders,
            config,
            request
          };
          settle(function _resolve(value) {
            resolve(value);
            done();
          }, function _reject(err) {
            reject(err);
            done();
          }, response);
          request = null;
        }
        if ("onloadend" in request) {
          request.onloadend = onloadend;
        } else {
          request.onreadystatechange = function handleLoad() {
            if (!request || request.readyState !== 4) {
              return;
            }
            if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
              return;
            }
            setTimeout(onloadend);
          };
        }
        request.onabort = function handleAbort() {
          if (!request) {
            return;
          }
          reject(new AxiosError("Request aborted", AxiosError.ECONNABORTED, config, request));
          request = null;
        };
        request.onerror = function handleError() {
          reject(new AxiosError("Network Error", AxiosError.ERR_NETWORK, config, request));
          request = null;
        };
        request.ontimeout = function handleTimeout() {
          let timeoutErrorMessage = _config.timeout ? "timeout of " + _config.timeout + "ms exceeded" : "timeout exceeded";
          const transitional = _config.transitional || transitionalDefaults;
          if (_config.timeoutErrorMessage) {
            timeoutErrorMessage = _config.timeoutErrorMessage;
          }
          reject(new AxiosError(
            timeoutErrorMessage,
            transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,
            config,
            request
          ));
          request = null;
        };
        requestData === void 0 && requestHeaders.setContentType(null);
        if ("setRequestHeader" in request) {
          utils$1.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {
            request.setRequestHeader(key, val);
          });
        }
        if (!utils$1.isUndefined(_config.withCredentials)) {
          request.withCredentials = !!_config.withCredentials;
        }
        if (responseType && responseType !== "json") {
          request.responseType = _config.responseType;
        }
        if (onDownloadProgress) {
          [downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true);
          request.addEventListener("progress", downloadThrottled);
        }
        if (onUploadProgress && request.upload) {
          [uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress);
          request.upload.addEventListener("progress", uploadThrottled);
          request.upload.addEventListener("loadend", flushUpload);
        }
        if (_config.cancelToken || _config.signal) {
          onCanceled = (cancel) => {
            if (!request) {
              return;
            }
            reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);
            request.abort();
            request = null;
          };
          _config.cancelToken && _config.cancelToken.subscribe(onCanceled);
          if (_config.signal) {
            _config.signal.aborted ? onCanceled() : _config.signal.addEventListener("abort", onCanceled);
          }
        }
        const protocol = parseProtocol(_config.url);
        if (protocol && platform.protocols.indexOf(protocol) === -1) {
          reject(new AxiosError("Unsupported protocol " + protocol + ":", AxiosError.ERR_BAD_REQUEST, config));
          return;
        }
        request.send(requestData || null);
      });
    };
    var composeSignals = (signals, timeout) => {
      const { length } = signals = signals ? signals.filter(Boolean) : [];
      if (timeout || length) {
        let controller = new AbortController();
        let aborted;
        const onabort = function(reason) {
          if (!aborted) {
            aborted = true;
            unsubscribe();
            const err = reason instanceof Error ? reason : this.reason;
            controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));
          }
        };
        let timer = timeout && setTimeout(() => {
          timer = null;
          onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));
        }, timeout);
        const unsubscribe = () => {
          if (signals) {
            timer && clearTimeout(timer);
            timer = null;
            signals.forEach((signal2) => {
              signal2.unsubscribe ? signal2.unsubscribe(onabort) : signal2.removeEventListener("abort", onabort);
            });
            signals = null;
          }
        };
        signals.forEach((signal2) => signal2.addEventListener("abort", onabort));
        const { signal } = controller;
        signal.unsubscribe = () => utils$1.asap(unsubscribe);
        return signal;
      }
    };
    var composeSignals$1 = composeSignals;
    var streamChunk = function* (chunk, chunkSize) {
      let len = chunk.byteLength;
      if (!chunkSize || len < chunkSize) {
        yield chunk;
        return;
      }
      let pos = 0;
      let end;
      while (pos < len) {
        end = pos + chunkSize;
        yield chunk.slice(pos, end);
        pos = end;
      }
    };
    var readBytes = async function* (iterable, chunkSize) {
      for await (const chunk of readStream(iterable)) {
        yield* streamChunk(chunk, chunkSize);
      }
    };
    var readStream = async function* (stream) {
      if (stream[Symbol.asyncIterator]) {
        yield* stream;
        return;
      }
      const reader = stream.getReader();
      try {
        for (; ; ) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }
          yield value;
        }
      } finally {
        await reader.cancel();
      }
    };
    var trackStream = (stream, chunkSize, onProgress, onFinish) => {
      const iterator = readBytes(stream, chunkSize);
      let bytes = 0;
      let done;
      let _onFinish = (e) => {
        if (!done) {
          done = true;
          onFinish && onFinish(e);
        }
      };
      return new ReadableStream({
        async pull(controller) {
          try {
            const { done: done2, value } = await iterator.next();
            if (done2) {
              _onFinish();
              controller.close();
              return;
            }
            let len = value.byteLength;
            if (onProgress) {
              let loadedBytes = bytes += len;
              onProgress(loadedBytes);
            }
            controller.enqueue(new Uint8Array(value));
          } catch (err) {
            _onFinish(err);
            throw err;
          }
        },
        cancel(reason) {
          _onFinish(reason);
          return iterator.return();
        }
      }, {
        highWaterMark: 2
      });
    };
    var isFetchSupported = typeof fetch === "function" && typeof Request === "function" && typeof Response === "function";
    var isReadableStreamSupported = isFetchSupported && typeof ReadableStream === "function";
    var encodeText = isFetchSupported && (typeof TextEncoder === "function" ? /* @__PURE__ */ ((encoder) => (str) => encoder.encode(str))(new TextEncoder()) : async (str) => new Uint8Array(await new Response(str).arrayBuffer()));
    var test = (fn, ...args) => {
      try {
        return !!fn(...args);
      } catch (e) {
        return false;
      }
    };
    var supportsRequestStream = isReadableStreamSupported && test(() => {
      let duplexAccessed = false;
      const hasContentType = new Request(platform.origin, {
        body: new ReadableStream(),
        method: "POST",
        get duplex() {
          duplexAccessed = true;
          return "half";
        }
      }).headers.has("Content-Type");
      return duplexAccessed && !hasContentType;
    });
    var DEFAULT_CHUNK_SIZE = 64 * 1024;
    var supportsResponseStream = isReadableStreamSupported && test(() => utils$1.isReadableStream(new Response("").body));
    var resolvers = {
      stream: supportsResponseStream && ((res) => res.body)
    };
    isFetchSupported && ((res) => {
      ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((type) => {
        !resolvers[type] && (resolvers[type] = utils$1.isFunction(res[type]) ? (res2) => res2[type]() : (_, config) => {
          throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);
        });
      });
    })(new Response());
    var getBodyLength = async (body) => {
      if (body == null) {
        return 0;
      }
      if (utils$1.isBlob(body)) {
        return body.size;
      }
      if (utils$1.isSpecCompliantForm(body)) {
        const _request = new Request(platform.origin, {
          method: "POST",
          body
        });
        return (await _request.arrayBuffer()).byteLength;
      }
      if (utils$1.isArrayBufferView(body) || utils$1.isArrayBuffer(body)) {
        return body.byteLength;
      }
      if (utils$1.isURLSearchParams(body)) {
        body = body + "";
      }
      if (utils$1.isString(body)) {
        return (await encodeText(body)).byteLength;
      }
    };
    var resolveBodyLength = async (headers, body) => {
      const length = utils$1.toFiniteNumber(headers.getContentLength());
      return length == null ? getBodyLength(body) : length;
    };
    var fetchAdapter = isFetchSupported && (async (config) => {
      let {
        url,
        method,
        data,
        signal,
        cancelToken,
        timeout,
        onDownloadProgress,
        onUploadProgress,
        responseType,
        headers,
        withCredentials = "same-origin",
        fetchOptions
      } = resolveConfig(config);
      responseType = responseType ? (responseType + "").toLowerCase() : "text";
      let composedSignal = composeSignals$1([signal, cancelToken && cancelToken.toAbortSignal()], timeout);
      let request;
      const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {
        composedSignal.unsubscribe();
      });
      let requestContentLength;
      try {
        if (onUploadProgress && supportsRequestStream && method !== "get" && method !== "head" && (requestContentLength = await resolveBodyLength(headers, data)) !== 0) {
          let _request = new Request(url, {
            method: "POST",
            body: data,
            duplex: "half"
          });
          let contentTypeHeader;
          if (utils$1.isFormData(data) && (contentTypeHeader = _request.headers.get("content-type"))) {
            headers.setContentType(contentTypeHeader);
          }
          if (_request.body) {
            const [onProgress, flush] = progressEventDecorator(
              requestContentLength,
              progressEventReducer(asyncDecorator(onUploadProgress))
            );
            data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);
          }
        }
        if (!utils$1.isString(withCredentials)) {
          withCredentials = withCredentials ? "include" : "omit";
        }
        const isCredentialsSupported = "credentials" in Request.prototype;
        request = new Request(url, {
          ...fetchOptions,
          signal: composedSignal,
          method: method.toUpperCase(),
          headers: headers.normalize().toJSON(),
          body: data,
          duplex: "half",
          credentials: isCredentialsSupported ? withCredentials : void 0
        });
        let response = await fetch(request);
        const isStreamResponse = supportsResponseStream && (responseType === "stream" || responseType === "response");
        if (supportsResponseStream && (onDownloadProgress || isStreamResponse && unsubscribe)) {
          const options = {};
          ["status", "statusText", "headers"].forEach((prop) => {
            options[prop] = response[prop];
          });
          const responseContentLength = utils$1.toFiniteNumber(response.headers.get("content-length"));
          const [onProgress, flush] = onDownloadProgress && progressEventDecorator(
            responseContentLength,
            progressEventReducer(asyncDecorator(onDownloadProgress), true)
          ) || [];
          response = new Response(
            trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {
              flush && flush();
              unsubscribe && unsubscribe();
            }),
            options
          );
        }
        responseType = responseType || "text";
        let responseData = await resolvers[utils$1.findKey(resolvers, responseType) || "text"](response, config);
        !isStreamResponse && unsubscribe && unsubscribe();
        return await new Promise((resolve, reject) => {
          settle(resolve, reject, {
            data: responseData,
            headers: AxiosHeaders$1.from(response.headers),
            status: response.status,
            statusText: response.statusText,
            config,
            request
          });
        });
      } catch (err) {
        unsubscribe && unsubscribe();
        if (err && err.name === "TypeError" && /fetch/i.test(err.message)) {
          throw Object.assign(
            new AxiosError("Network Error", AxiosError.ERR_NETWORK, config, request),
            {
              cause: err.cause || err
            }
          );
        }
        throw AxiosError.from(err, err && err.code, config, request);
      }
    });
    var knownAdapters = {
      http: httpAdapter,
      xhr: xhrAdapter,
      fetch: fetchAdapter
    };
    utils$1.forEach(knownAdapters, (fn, value) => {
      if (fn) {
        try {
          Object.defineProperty(fn, "name", { value });
        } catch (e) {
        }
        Object.defineProperty(fn, "adapterName", { value });
      }
    });
    var renderReason = (reason) => `- ${reason}`;
    var isResolvedHandle = (adapter) => utils$1.isFunction(adapter) || adapter === null || adapter === false;
    var adapters = {
      getAdapter: (adapters2) => {
        adapters2 = utils$1.isArray(adapters2) ? adapters2 : [adapters2];
        const { length } = adapters2;
        let nameOrAdapter;
        let adapter;
        const rejectedReasons = {};
        for (let i = 0; i < length; i++) {
          nameOrAdapter = adapters2[i];
          let id;
          adapter = nameOrAdapter;
          if (!isResolvedHandle(nameOrAdapter)) {
            adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];
            if (adapter === void 0) {
              throw new AxiosError(`Unknown adapter '${id}'`);
            }
          }
          if (adapter) {
            break;
          }
          rejectedReasons[id || "#" + i] = adapter;
        }
        if (!adapter) {
          const reasons = Object.entries(rejectedReasons).map(
            ([id, state]) => `adapter ${id} ` + (state === false ? "is not supported by the environment" : "is not available in the build")
          );
          let s = length ? reasons.length > 1 ? "since :\n" + reasons.map(renderReason).join("\n") : " " + renderReason(reasons[0]) : "as no adapter specified";
          throw new AxiosError(
            `There is no suitable adapter to dispatch the request ` + s,
            "ERR_NOT_SUPPORT"
          );
        }
        return adapter;
      },
      adapters: knownAdapters
    };
    function throwIfCancellationRequested(config) {
      if (config.cancelToken) {
        config.cancelToken.throwIfRequested();
      }
      if (config.signal && config.signal.aborted) {
        throw new CanceledError(null, config);
      }
    }
    function dispatchRequest(config) {
      throwIfCancellationRequested(config);
      config.headers = AxiosHeaders$1.from(config.headers);
      config.data = transformData.call(
        config,
        config.transformRequest
      );
      if (["post", "put", "patch"].indexOf(config.method) !== -1) {
        config.headers.setContentType("application/x-www-form-urlencoded", false);
      }
      const adapter = adapters.getAdapter(config.adapter || defaults$1.adapter);
      return adapter(config).then(function onAdapterResolution(response) {
        throwIfCancellationRequested(config);
        response.data = transformData.call(
          config,
          config.transformResponse,
          response
        );
        response.headers = AxiosHeaders$1.from(response.headers);
        return response;
      }, function onAdapterRejection(reason) {
        if (!isCancel(reason)) {
          throwIfCancellationRequested(config);
          if (reason && reason.response) {
            reason.response.data = transformData.call(
              config,
              config.transformResponse,
              reason.response
            );
            reason.response.headers = AxiosHeaders$1.from(reason.response.headers);
          }
        }
        return Promise.reject(reason);
      });
    }
    var VERSION = "1.7.7";
    var validators$1 = {};
    ["object", "boolean", "number", "function", "string", "symbol"].forEach((type, i) => {
      validators$1[type] = function validator2(thing) {
        return typeof thing === type || "a" + (i < 1 ? "n " : " ") + type;
      };
    });
    var deprecatedWarnings = {};
    validators$1.transitional = function transitional(validator2, version2, message) {
      function formatMessage(opt, desc) {
        return "[Axios v" + VERSION + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
      }
      return (value, opt, opts) => {
        if (validator2 === false) {
          throw new AxiosError(
            formatMessage(opt, " has been removed" + (version2 ? " in " + version2 : "")),
            AxiosError.ERR_DEPRECATED
          );
        }
        if (version2 && !deprecatedWarnings[opt]) {
          deprecatedWarnings[opt] = true;
          console.warn(
            formatMessage(
              opt,
              " has been deprecated since v" + version2 + " and will be removed in the near future"
            )
          );
        }
        return validator2 ? validator2(value, opt, opts) : true;
      };
    };
    function assertOptions(options, schema, allowUnknown) {
      if (typeof options !== "object") {
        throw new AxiosError("options must be an object", AxiosError.ERR_BAD_OPTION_VALUE);
      }
      const keys = Object.keys(options);
      let i = keys.length;
      while (i-- > 0) {
        const opt = keys[i];
        const validator2 = schema[opt];
        if (validator2) {
          const value = options[opt];
          const result = value === void 0 || validator2(value, opt, options);
          if (result !== true) {
            throw new AxiosError("option " + opt + " must be " + result, AxiosError.ERR_BAD_OPTION_VALUE);
          }
          continue;
        }
        if (allowUnknown !== true) {
          throw new AxiosError("Unknown option " + opt, AxiosError.ERR_BAD_OPTION);
        }
      }
    }
    var validator = {
      assertOptions,
      validators: validators$1
    };
    var validators = validator.validators;
    var Axios = class {
      constructor(instanceConfig) {
        this.defaults = instanceConfig;
        this.interceptors = {
          request: new InterceptorManager$1(),
          response: new InterceptorManager$1()
        };
      }
      /**
       * Dispatch a request
       *
       * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)
       * @param {?Object} config
       *
       * @returns {Promise} The Promise to be fulfilled
       */
      async request(configOrUrl, config) {
        try {
          return await this._request(configOrUrl, config);
        } catch (err) {
          if (err instanceof Error) {
            let dummy;
            Error.captureStackTrace ? Error.captureStackTrace(dummy = {}) : dummy = new Error();
            const stack = dummy.stack ? dummy.stack.replace(/^.+\n/, "") : "";
            try {
              if (!err.stack) {
                err.stack = stack;
              } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\n.+\n/, ""))) {
                err.stack += "\n" + stack;
              }
            } catch (e) {
            }
          }
          throw err;
        }
      }
      _request(configOrUrl, config) {
        if (typeof configOrUrl === "string") {
          config = config || {};
          config.url = configOrUrl;
        } else {
          config = configOrUrl || {};
        }
        config = mergeConfig(this.defaults, config);
        const { transitional, paramsSerializer, headers } = config;
        if (transitional !== void 0) {
          validator.assertOptions(transitional, {
            silentJSONParsing: validators.transitional(validators.boolean),
            forcedJSONParsing: validators.transitional(validators.boolean),
            clarifyTimeoutError: validators.transitional(validators.boolean)
          }, false);
        }
        if (paramsSerializer != null) {
          if (utils$1.isFunction(paramsSerializer)) {
            config.paramsSerializer = {
              serialize: paramsSerializer
            };
          } else {
            validator.assertOptions(paramsSerializer, {
              encode: validators.function,
              serialize: validators.function
            }, true);
          }
        }
        config.method = (config.method || this.defaults.method || "get").toLowerCase();
        let contextHeaders = headers && utils$1.merge(
          headers.common,
          headers[config.method]
        );
        headers && utils$1.forEach(
          ["delete", "get", "head", "post", "put", "patch", "common"],
          (method) => {
            delete headers[method];
          }
        );
        config.headers = AxiosHeaders$1.concat(contextHeaders, headers);
        const requestInterceptorChain = [];
        let synchronousRequestInterceptors = true;
        this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
          if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
            return;
          }
          synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
          requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
        });
        const responseInterceptorChain = [];
        this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
          responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
        });
        let promise;
        let i = 0;
        let len;
        if (!synchronousRequestInterceptors) {
          const chain = [dispatchRequest.bind(this), void 0];
          chain.unshift.apply(chain, requestInterceptorChain);
          chain.push.apply(chain, responseInterceptorChain);
          len = chain.length;
          promise = Promise.resolve(config);
          while (i < len) {
            promise = promise.then(chain[i++], chain[i++]);
          }
          return promise;
        }
        len = requestInterceptorChain.length;
        let newConfig = config;
        i = 0;
        while (i < len) {
          const onFulfilled = requestInterceptorChain[i++];
          const onRejected = requestInterceptorChain[i++];
          try {
            newConfig = onFulfilled(newConfig);
          } catch (error) {
            onRejected.call(this, error);
            break;
          }
        }
        try {
          promise = dispatchRequest.call(this, newConfig);
        } catch (error) {
          return Promise.reject(error);
        }
        i = 0;
        len = responseInterceptorChain.length;
        while (i < len) {
          promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);
        }
        return promise;
      }
      getUri(config) {
        config = mergeConfig(this.defaults, config);
        const fullPath = buildFullPath(config.baseURL, config.url);
        return buildURL(fullPath, config.params, config.paramsSerializer);
      }
    };
    utils$1.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method) {
      Axios.prototype[method] = function(url, config) {
        return this.request(mergeConfig(config || {}, {
          method,
          url,
          data: (config || {}).data
        }));
      };
    });
    utils$1.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
      function generateHTTPMethod(isForm) {
        return function httpMethod(url, data, config) {
          return this.request(mergeConfig(config || {}, {
            method,
            headers: isForm ? {
              "Content-Type": "multipart/form-data"
            } : {},
            url,
            data
          }));
        };
      }
      Axios.prototype[method] = generateHTTPMethod();
      Axios.prototype[method + "Form"] = generateHTTPMethod(true);
    });
    var Axios$1 = Axios;
    var CancelToken = class _CancelToken {
      constructor(executor) {
        if (typeof executor !== "function") {
          throw new TypeError("executor must be a function.");
        }
        let resolvePromise;
        this.promise = new Promise(function promiseExecutor(resolve) {
          resolvePromise = resolve;
        });
        const token = this;
        this.promise.then((cancel) => {
          if (!token._listeners) return;
          let i = token._listeners.length;
          while (i-- > 0) {
            token._listeners[i](cancel);
          }
          token._listeners = null;
        });
        this.promise.then = (onfulfilled) => {
          let _resolve;
          const promise = new Promise((resolve) => {
            token.subscribe(resolve);
            _resolve = resolve;
          }).then(onfulfilled);
          promise.cancel = function reject() {
            token.unsubscribe(_resolve);
          };
          return promise;
        };
        executor(function cancel(message, config, request) {
          if (token.reason) {
            return;
          }
          token.reason = new CanceledError(message, config, request);
          resolvePromise(token.reason);
        });
      }
      /**
       * Throws a `CanceledError` if cancellation has been requested.
       */
      throwIfRequested() {
        if (this.reason) {
          throw this.reason;
        }
      }
      /**
       * Subscribe to the cancel signal
       */
      subscribe(listener) {
        if (this.reason) {
          listener(this.reason);
          return;
        }
        if (this._listeners) {
          this._listeners.push(listener);
        } else {
          this._listeners = [listener];
        }
      }
      /**
       * Unsubscribe from the cancel signal
       */
      unsubscribe(listener) {
        if (!this._listeners) {
          return;
        }
        const index = this._listeners.indexOf(listener);
        if (index !== -1) {
          this._listeners.splice(index, 1);
        }
      }
      toAbortSignal() {
        const controller = new AbortController();
        const abort = (err) => {
          controller.abort(err);
        };
        this.subscribe(abort);
        controller.signal.unsubscribe = () => this.unsubscribe(abort);
        return controller.signal;
      }
      /**
       * Returns an object that contains a new `CancelToken` and a function that, when called,
       * cancels the `CancelToken`.
       */
      static source() {
        let cancel;
        const token = new _CancelToken(function executor(c) {
          cancel = c;
        });
        return {
          token,
          cancel
        };
      }
    };
    var CancelToken$1 = CancelToken;
    function spread(callback) {
      return function wrap(arr) {
        return callback.apply(null, arr);
      };
    }
    function isAxiosError(payload) {
      return utils$1.isObject(payload) && payload.isAxiosError === true;
    }
    var HttpStatusCode = {
      Continue: 100,
      SwitchingProtocols: 101,
      Processing: 102,
      EarlyHints: 103,
      Ok: 200,
      Created: 201,
      Accepted: 202,
      NonAuthoritativeInformation: 203,
      NoContent: 204,
      ResetContent: 205,
      PartialContent: 206,
      MultiStatus: 207,
      AlreadyReported: 208,
      ImUsed: 226,
      MultipleChoices: 300,
      MovedPermanently: 301,
      Found: 302,
      SeeOther: 303,
      NotModified: 304,
      UseProxy: 305,
      Unused: 306,
      TemporaryRedirect: 307,
      PermanentRedirect: 308,
      BadRequest: 400,
      Unauthorized: 401,
      PaymentRequired: 402,
      Forbidden: 403,
      NotFound: 404,
      MethodNotAllowed: 405,
      NotAcceptable: 406,
      ProxyAuthenticationRequired: 407,
      RequestTimeout: 408,
      Conflict: 409,
      Gone: 410,
      LengthRequired: 411,
      PreconditionFailed: 412,
      PayloadTooLarge: 413,
      UriTooLong: 414,
      UnsupportedMediaType: 415,
      RangeNotSatisfiable: 416,
      ExpectationFailed: 417,
      ImATeapot: 418,
      MisdirectedRequest: 421,
      UnprocessableEntity: 422,
      Locked: 423,
      FailedDependency: 424,
      TooEarly: 425,
      UpgradeRequired: 426,
      PreconditionRequired: 428,
      TooManyRequests: 429,
      RequestHeaderFieldsTooLarge: 431,
      UnavailableForLegalReasons: 451,
      InternalServerError: 500,
      NotImplemented: 501,
      BadGateway: 502,
      ServiceUnavailable: 503,
      GatewayTimeout: 504,
      HttpVersionNotSupported: 505,
      VariantAlsoNegotiates: 506,
      InsufficientStorage: 507,
      LoopDetected: 508,
      NotExtended: 510,
      NetworkAuthenticationRequired: 511
    };
    Object.entries(HttpStatusCode).forEach(([key, value]) => {
      HttpStatusCode[value] = key;
    });
    var HttpStatusCode$1 = HttpStatusCode;
    function createInstance(defaultConfig) {
      const context = new Axios$1(defaultConfig);
      const instance = bind(Axios$1.prototype.request, context);
      utils$1.extend(instance, Axios$1.prototype, context, { allOwnKeys: true });
      utils$1.extend(instance, context, null, { allOwnKeys: true });
      instance.create = function create(instanceConfig) {
        return createInstance(mergeConfig(defaultConfig, instanceConfig));
      };
      return instance;
    }
    var axios = createInstance(defaults$1);
    axios.Axios = Axios$1;
    axios.CanceledError = CanceledError;
    axios.CancelToken = CancelToken$1;
    axios.isCancel = isCancel;
    axios.VERSION = VERSION;
    axios.toFormData = toFormData;
    axios.AxiosError = AxiosError;
    axios.Cancel = axios.CanceledError;
    axios.all = function all(promises) {
      return Promise.all(promises);
    };
    axios.spread = spread;
    axios.isAxiosError = isAxiosError;
    axios.mergeConfig = mergeConfig;
    axios.AxiosHeaders = AxiosHeaders$1;
    axios.formToJSON = (thing) => formDataToJSON(utils$1.isHTMLForm(thing) ? new FormData(thing) : thing);
    axios.getAdapter = adapters.getAdapter;
    axios.HttpStatusCode = HttpStatusCode$1;
    axios.default = axios;
    module.exports = axios;
  }
});

// ../../node_modules/.pnpm/posthog-node@3.6.3/node_modules/posthog-node/lib/index.esm.js
var import_rusha = __toESM(require_rusha());
var extendStatics = function(d, b) {
  extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
    d2.__proto__ = b2;
  } || function(d2, b2) {
    for (var p in b2)
      if (Object.prototype.hasOwnProperty.call(b2, p))
        d2[p] = b2[p];
  };
  return extendStatics(d, b);
};
function __extends(d, b) {
  if (typeof b !== "function" && b !== null)
    throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
  extendStatics(d, b);
  function __() {
    this.constructor = d;
  }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
var __assign = function() {
  __assign = Object.assign || function __assign2(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s)
        if (Object.prototype.hasOwnProperty.call(s, p))
          t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
function __rest(s, e) {
  var t = {};
  for (var p in s)
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
      t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
}
function __awaiter(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
}
function __generator(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1)
      throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2)
      throw new TypeError("Generator is already executing.");
    while (_)
      try {
        if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done)
          return t;
        if (y = 0, t)
          op = [op[0] & 2, t.value];
        switch (op[0]) {
          case 0:
          case 1:
            t = op;
            break;
          case 4:
            _.label++;
            return { value: op[1], done: false };
          case 5:
            _.label++;
            y = op[1];
            op = [0];
            continue;
          case 7:
            op = _.ops.pop();
            _.trys.pop();
            continue;
          default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
              _ = 0;
              continue;
            }
            if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }
            if (op[0] === 6 && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }
            if (t && _.label < t[2]) {
              _.label = t[2];
              _.ops.push(op);
              break;
            }
            if (t[2])
              _.ops.pop();
            _.trys.pop();
            continue;
        }
        op = body.call(thisArg, _);
      } catch (e) {
        op = [6, e];
        y = 0;
      } finally {
        f2 = t = 0;
      }
    if (op[0] & 5)
      throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
}
function __spreadArray(to, from, pack) {
  if (pack || arguments.length === 2)
    for (var i = 0, l = from.length, ar; i < l; i++) {
      if (ar || !(i in from)) {
        if (!ar)
          ar = Array.prototype.slice.call(from, 0, i);
        ar[i] = from[i];
      }
    }
  return to.concat(ar || Array.prototype.slice.call(from));
}
var version = "3.6.3";
var PostHogPersistedProperty;
(function(PostHogPersistedProperty2) {
  PostHogPersistedProperty2["AnonymousId"] = "anonymous_id";
  PostHogPersistedProperty2["DistinctId"] = "distinct_id";
  PostHogPersistedProperty2["Props"] = "props";
  PostHogPersistedProperty2["FeatureFlags"] = "feature_flags";
  PostHogPersistedProperty2["FeatureFlagPayloads"] = "feature_flag_payloads";
  PostHogPersistedProperty2["OverrideFeatureFlags"] = "override_feature_flags";
  PostHogPersistedProperty2["Queue"] = "queue";
  PostHogPersistedProperty2["OptedOut"] = "opted_out";
  PostHogPersistedProperty2["SessionId"] = "session_id";
  PostHogPersistedProperty2["SessionLastTimestamp"] = "session_timestamp";
  PostHogPersistedProperty2["PersonProperties"] = "person_properties";
  PostHogPersistedProperty2["GroupProperties"] = "group_properties";
  PostHogPersistedProperty2["InstalledAppBuild"] = "installed_app_build";
  PostHogPersistedProperty2["InstalledAppVersion"] = "installed_app_version";
})(PostHogPersistedProperty || (PostHogPersistedProperty = {}));
function assert(truthyValue, message) {
  if (!truthyValue) {
    throw new Error(message);
  }
}
function removeTrailingSlash(url) {
  return url === null || url === void 0 ? void 0 : url.replace(/\/+$/, "");
}
function retriable(fn, props) {
  if (props === void 0) {
    props = {};
  }
  return __awaiter(this, void 0, void 0, function() {
    var _a, retryCount, _b, retryDelay, _c, retryCheck, lastError, i, res, e_1;
    return __generator(this, function(_d) {
      switch (_d.label) {
        case 0:
          _a = props.retryCount, retryCount = _a === void 0 ? 3 : _a, _b = props.retryDelay, retryDelay = _b === void 0 ? 5e3 : _b, _c = props.retryCheck, retryCheck = _c === void 0 ? function() {
            return true;
          } : _c;
          lastError = null;
          i = 0;
          _d.label = 1;
        case 1:
          if (!(i < retryCount + 1)) return [3, 7];
          if (!(i > 0)) return [3, 3];
          return [4, new Promise(function(r) {
            return setTimeout(r, retryDelay);
          })];
        case 2:
          _d.sent();
          _d.label = 3;
        case 3:
          _d.trys.push([3, 5, , 6]);
          return [4, fn()];
        case 4:
          res = _d.sent();
          return [2, res];
        case 5:
          e_1 = _d.sent();
          lastError = e_1;
          if (!retryCheck(e_1)) {
            throw e_1;
          }
          return [3, 6];
        case 6:
          i++;
          return [3, 1];
        case 7:
          throw lastError;
      }
    });
  });
}
function currentTimestamp() {
  return (/* @__PURE__ */ new Date()).getTime();
}
function currentISOTime() {
  return (/* @__PURE__ */ new Date()).toISOString();
}
function safeSetTimeout(fn, timeout) {
  var t = setTimeout(fn, timeout);
  (t === null || t === void 0 ? void 0 : t.unref) && (t === null || t === void 0 ? void 0 : t.unref());
  return t;
}
var f = String.fromCharCode;
var keyStrBase64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
var baseReverseDic = {};
function getBaseValue(alphabet, character) {
  if (!baseReverseDic[alphabet]) {
    baseReverseDic[alphabet] = {};
    for (var i = 0; i < alphabet.length; i++) {
      baseReverseDic[alphabet][alphabet.charAt(i)] = i;
    }
  }
  return baseReverseDic[alphabet][character];
}
var LZString = {
  compressToBase64: function(input) {
    if (input == null) {
      return "";
    }
    var res = LZString._compress(input, 6, function(a) {
      return keyStrBase64.charAt(a);
    });
    switch (res.length % 4) {
      default:
      case 0:
        return res;
      case 1:
        return res + "===";
      case 2:
        return res + "==";
      case 3:
        return res + "=";
    }
  },
  decompressFromBase64: function(input) {
    if (input == null) {
      return "";
    }
    if (input == "") {
      return null;
    }
    return LZString._decompress(input.length, 32, function(index) {
      return getBaseValue(keyStrBase64, input.charAt(index));
    });
  },
  compress: function(uncompressed) {
    return LZString._compress(uncompressed, 16, function(a) {
      return f(a);
    });
  },
  _compress: function(uncompressed, bitsPerChar, getCharFromInt) {
    if (uncompressed == null) {
      return "";
    }
    var context_dictionary = {}, context_dictionaryToCreate = {}, context_data = [];
    var i, value, context_c = "", context_wc = "", context_w = "", context_enlargeIn = 2, context_dictSize = 3, context_numBits = 2, context_data_val = 0, context_data_position = 0, ii;
    for (ii = 0; ii < uncompressed.length; ii += 1) {
      context_c = uncompressed.charAt(ii);
      if (!Object.prototype.hasOwnProperty.call(context_dictionary, context_c)) {
        context_dictionary[context_c] = context_dictSize++;
        context_dictionaryToCreate[context_c] = true;
      }
      context_wc = context_w + context_c;
      if (Object.prototype.hasOwnProperty.call(context_dictionary, context_wc)) {
        context_w = context_wc;
      } else {
        if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {
          if (context_w.charCodeAt(0) < 256) {
            for (i = 0; i < context_numBits; i++) {
              context_data_val = context_data_val << 1;
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
            }
            value = context_w.charCodeAt(0);
            for (i = 0; i < 8; i++) {
              context_data_val = context_data_val << 1 | value & 1;
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = value >> 1;
            }
          } else {
            value = 1;
            for (i = 0; i < context_numBits; i++) {
              context_data_val = context_data_val << 1 | value;
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = 0;
            }
            value = context_w.charCodeAt(0);
            for (i = 0; i < 16; i++) {
              context_data_val = context_data_val << 1 | value & 1;
              if (context_data_position == bitsPerChar - 1) {
                context_data_position = 0;
                context_data.push(getCharFromInt(context_data_val));
                context_data_val = 0;
              } else {
                context_data_position++;
              }
              value = value >> 1;
            }
          }
          context_enlargeIn--;
          if (context_enlargeIn == 0) {
            context_enlargeIn = Math.pow(2, context_numBits);
            context_numBits++;
          }
          delete context_dictionaryToCreate[context_w];
        } else {
          value = context_dictionary[context_w];
          for (i = 0; i < context_numBits; i++) {
            context_data_val = context_data_val << 1 | value & 1;
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        }
        context_enlargeIn--;
        if (context_enlargeIn == 0) {
          context_enlargeIn = Math.pow(2, context_numBits);
          context_numBits++;
        }
        context_dictionary[context_wc] = context_dictSize++;
        context_w = String(context_c);
      }
    }
    if (context_w !== "") {
      if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {
        if (context_w.charCodeAt(0) < 256) {
          for (i = 0; i < context_numBits; i++) {
            context_data_val = context_data_val << 1;
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
          }
          value = context_w.charCodeAt(0);
          for (i = 0; i < 8; i++) {
            context_data_val = context_data_val << 1 | value & 1;
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        } else {
          value = 1;
          for (i = 0; i < context_numBits; i++) {
            context_data_val = context_data_val << 1 | value;
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = 0;
          }
          value = context_w.charCodeAt(0);
          for (i = 0; i < 16; i++) {
            context_data_val = context_data_val << 1 | value & 1;
            if (context_data_position == bitsPerChar - 1) {
              context_data_position = 0;
              context_data.push(getCharFromInt(context_data_val));
              context_data_val = 0;
            } else {
              context_data_position++;
            }
            value = value >> 1;
          }
        }
        context_enlargeIn--;
        if (context_enlargeIn == 0) {
          context_enlargeIn = Math.pow(2, context_numBits);
          context_numBits++;
        }
        delete context_dictionaryToCreate[context_w];
      } else {
        value = context_dictionary[context_w];
        for (i = 0; i < context_numBits; i++) {
          context_data_val = context_data_val << 1 | value & 1;
          if (context_data_position == bitsPerChar - 1) {
            context_data_position = 0;
            context_data.push(getCharFromInt(context_data_val));
            context_data_val = 0;
          } else {
            context_data_position++;
          }
          value = value >> 1;
        }
      }
      context_enlargeIn--;
      if (context_enlargeIn == 0) {
        context_enlargeIn = Math.pow(2, context_numBits);
        context_numBits++;
      }
    }
    value = 2;
    for (i = 0; i < context_numBits; i++) {
      context_data_val = context_data_val << 1 | value & 1;
      if (context_data_position == bitsPerChar - 1) {
        context_data_position = 0;
        context_data.push(getCharFromInt(context_data_val));
        context_data_val = 0;
      } else {
        context_data_position++;
      }
      value = value >> 1;
    }
    while (true) {
      context_data_val = context_data_val << 1;
      if (context_data_position == bitsPerChar - 1) {
        context_data.push(getCharFromInt(context_data_val));
        break;
      } else {
        context_data_position++;
      }
    }
    return context_data.join("");
  },
  decompress: function(compressed) {
    if (compressed == null) {
      return "";
    }
    if (compressed == "") {
      return null;
    }
    return LZString._decompress(compressed.length, 32768, function(index) {
      return compressed.charCodeAt(index);
    });
  },
  _decompress: function(length, resetValue, getNextValue) {
    var dictionary = [], result = [], data = { val: getNextValue(0), position: resetValue, index: 1 };
    var enlargeIn = 4, dictSize = 4, numBits = 3, entry = "", i, w, bits, resb, maxpower, power, c;
    for (i = 0; i < 3; i += 1) {
      dictionary[i] = i;
    }
    bits = 0;
    maxpower = Math.pow(2, 2);
    power = 1;
    while (power != maxpower) {
      resb = data.val & data.position;
      data.position >>= 1;
      if (data.position == 0) {
        data.position = resetValue;
        data.val = getNextValue(data.index++);
      }
      bits |= (resb > 0 ? 1 : 0) * power;
      power <<= 1;
    }
    switch (bits) {
      case 0:
        bits = 0;
        maxpower = Math.pow(2, 8);
        power = 1;
        while (power != maxpower) {
          resb = data.val & data.position;
          data.position >>= 1;
          if (data.position == 0) {
            data.position = resetValue;
            data.val = getNextValue(data.index++);
          }
          bits |= (resb > 0 ? 1 : 0) * power;
          power <<= 1;
        }
        c = f(bits);
        break;
      case 1:
        bits = 0;
        maxpower = Math.pow(2, 16);
        power = 1;
        while (power != maxpower) {
          resb = data.val & data.position;
          data.position >>= 1;
          if (data.position == 0) {
            data.position = resetValue;
            data.val = getNextValue(data.index++);
          }
          bits |= (resb > 0 ? 1 : 0) * power;
          power <<= 1;
        }
        c = f(bits);
        break;
      case 2:
        return "";
    }
    dictionary[3] = c;
    w = c;
    result.push(c);
    while (true) {
      if (data.index > length) {
        return "";
      }
      bits = 0;
      maxpower = Math.pow(2, numBits);
      power = 1;
      while (power != maxpower) {
        resb = data.val & data.position;
        data.position >>= 1;
        if (data.position == 0) {
          data.position = resetValue;
          data.val = getNextValue(data.index++);
        }
        bits |= (resb > 0 ? 1 : 0) * power;
        power <<= 1;
      }
      switch (c = bits) {
        case 0:
          bits = 0;
          maxpower = Math.pow(2, 8);
          power = 1;
          while (power != maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb > 0 ? 1 : 0) * power;
            power <<= 1;
          }
          dictionary[dictSize++] = f(bits);
          c = dictSize - 1;
          enlargeIn--;
          break;
        case 1:
          bits = 0;
          maxpower = Math.pow(2, 16);
          power = 1;
          while (power != maxpower) {
            resb = data.val & data.position;
            data.position >>= 1;
            if (data.position == 0) {
              data.position = resetValue;
              data.val = getNextValue(data.index++);
            }
            bits |= (resb > 0 ? 1 : 0) * power;
            power <<= 1;
          }
          dictionary[dictSize++] = f(bits);
          c = dictSize - 1;
          enlargeIn--;
          break;
        case 2:
          return result.join("");
      }
      if (enlargeIn == 0) {
        enlargeIn = Math.pow(2, numBits);
        numBits++;
      }
      if (dictionary[c]) {
        entry = dictionary[c];
      } else {
        if (c === dictSize) {
          entry = w + w.charAt(0);
        } else {
          return null;
        }
      }
      result.push(entry);
      dictionary[dictSize++] = w + entry.charAt(0);
      enlargeIn--;
      w = entry;
      if (enlargeIn == 0) {
        enlargeIn = Math.pow(2, numBits);
        numBits++;
      }
    }
  }
};
var SimpleEventEmitter = (
  /** @class */
  function() {
    function SimpleEventEmitter2() {
      this.events = {};
      this.events = {};
    }
    SimpleEventEmitter2.prototype.on = function(event, listener) {
      var _this = this;
      if (!this.events[event]) {
        this.events[event] = [];
      }
      this.events[event].push(listener);
      return function() {
        _this.events[event] = _this.events[event].filter(function(x) {
          return x !== listener;
        });
      };
    };
    SimpleEventEmitter2.prototype.emit = function(event, payload) {
      for (var _i = 0, _a = this.events[event] || []; _i < _a.length; _i++) {
        var listener = _a[_i];
        listener(payload);
      }
      for (var _b = 0, _c = this.events["*"] || []; _b < _c.length; _b++) {
        var listener = _c[_b];
        listener(event, payload);
      }
    };
    return SimpleEventEmitter2;
  }()
);
var DIGITS = "0123456789abcdef";
var UUID = (
  /** @class */
  function() {
    function UUID2(bytes) {
      this.bytes = bytes;
    }
    UUID2.ofInner = function(bytes) {
      if (bytes.length !== 16) {
        throw new TypeError("not 128-bit length");
      } else {
        return new UUID2(bytes);
      }
    };
    UUID2.fromFieldsV7 = function(unixTsMs, randA, randBHi, randBLo) {
      if (!Number.isInteger(unixTsMs) || !Number.isInteger(randA) || !Number.isInteger(randBHi) || !Number.isInteger(randBLo) || unixTsMs < 0 || randA < 0 || randBHi < 0 || randBLo < 0 || unixTsMs > 281474976710655 || randA > 4095 || randBHi > 1073741823 || randBLo > 4294967295) {
        throw new RangeError("invalid field value");
      }
      var bytes = new Uint8Array(16);
      bytes[0] = unixTsMs / Math.pow(2, 40);
      bytes[1] = unixTsMs / Math.pow(2, 32);
      bytes[2] = unixTsMs / Math.pow(2, 24);
      bytes[3] = unixTsMs / Math.pow(2, 16);
      bytes[4] = unixTsMs / Math.pow(2, 8);
      bytes[5] = unixTsMs;
      bytes[6] = 112 | randA >>> 8;
      bytes[7] = randA;
      bytes[8] = 128 | randBHi >>> 24;
      bytes[9] = randBHi >>> 16;
      bytes[10] = randBHi >>> 8;
      bytes[11] = randBHi;
      bytes[12] = randBLo >>> 24;
      bytes[13] = randBLo >>> 16;
      bytes[14] = randBLo >>> 8;
      bytes[15] = randBLo;
      return new UUID2(bytes);
    };
    UUID2.parse = function(uuid) {
      var _a, _b, _c, _d;
      var hex = void 0;
      switch (uuid.length) {
        case 32:
          hex = (_a = /^[0-9a-f]{32}$/i.exec(uuid)) === null || _a === void 0 ? void 0 : _a[0];
          break;
        case 36:
          hex = (_b = /^([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(uuid)) === null || _b === void 0 ? void 0 : _b.slice(1, 6).join("");
          break;
        case 38:
          hex = (_c = /^\{([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})\}$/i.exec(uuid)) === null || _c === void 0 ? void 0 : _c.slice(1, 6).join("");
          break;
        case 45:
          hex = (_d = /^urn:uuid:([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(uuid)) === null || _d === void 0 ? void 0 : _d.slice(1, 6).join("");
          break;
      }
      if (hex) {
        var inner = new Uint8Array(16);
        for (var i = 0; i < 16; i += 4) {
          var n = parseInt(hex.substring(2 * i, 2 * i + 8), 16);
          inner[i + 0] = n >>> 24;
          inner[i + 1] = n >>> 16;
          inner[i + 2] = n >>> 8;
          inner[i + 3] = n;
        }
        return new UUID2(inner);
      } else {
        throw new SyntaxError("could not parse UUID string");
      }
    };
    UUID2.prototype.toString = function() {
      var text = "";
      for (var i = 0; i < this.bytes.length; i++) {
        text += DIGITS.charAt(this.bytes[i] >>> 4);
        text += DIGITS.charAt(this.bytes[i] & 15);
        if (i === 3 || i === 5 || i === 7 || i === 9) {
          text += "-";
        }
      }
      return text;
    };
    UUID2.prototype.toHex = function() {
      var text = "";
      for (var i = 0; i < this.bytes.length; i++) {
        text += DIGITS.charAt(this.bytes[i] >>> 4);
        text += DIGITS.charAt(this.bytes[i] & 15);
      }
      return text;
    };
    UUID2.prototype.toJSON = function() {
      return this.toString();
    };
    UUID2.prototype.getVariant = function() {
      var n = this.bytes[8] >>> 4;
      if (n < 0) {
        throw new Error("unreachable");
      } else if (n <= 7) {
        return this.bytes.every(function(e) {
          return e === 0;
        }) ? "NIL" : "VAR_0";
      } else if (n <= 11) {
        return "VAR_10";
      } else if (n <= 13) {
        return "VAR_110";
      } else if (n <= 15) {
        return this.bytes.every(function(e) {
          return e === 255;
        }) ? "MAX" : "VAR_RESERVED";
      } else {
        throw new Error("unreachable");
      }
    };
    UUID2.prototype.getVersion = function() {
      return this.getVariant() === "VAR_10" ? this.bytes[6] >>> 4 : void 0;
    };
    UUID2.prototype.clone = function() {
      return new UUID2(this.bytes.slice(0));
    };
    UUID2.prototype.equals = function(other) {
      return this.compareTo(other) === 0;
    };
    UUID2.prototype.compareTo = function(other) {
      for (var i = 0; i < 16; i++) {
        var diff = this.bytes[i] - other.bytes[i];
        if (diff !== 0) {
          return Math.sign(diff);
        }
      }
      return 0;
    };
    return UUID2;
  }()
);
var V7Generator = (
  /** @class */
  function() {
    function V7Generator2(randomNumberGenerator) {
      this.timestamp = 0;
      this.counter = 0;
      this.random = randomNumberGenerator !== null && randomNumberGenerator !== void 0 ? randomNumberGenerator : getDefaultRandom();
    }
    V7Generator2.prototype.generate = function() {
      return this.generateOrResetCore(Date.now(), 1e4);
    };
    V7Generator2.prototype.generateOrAbort = function() {
      return this.generateOrAbortCore(Date.now(), 1e4);
    };
    V7Generator2.prototype.generateOrResetCore = function(unixTsMs, rollbackAllowance) {
      var value = this.generateOrAbortCore(unixTsMs, rollbackAllowance);
      if (value === void 0) {
        this.timestamp = 0;
        value = this.generateOrAbortCore(unixTsMs, rollbackAllowance);
      }
      return value;
    };
    V7Generator2.prototype.generateOrAbortCore = function(unixTsMs, rollbackAllowance) {
      var MAX_COUNTER = 4398046511103;
      if (!Number.isInteger(unixTsMs) || unixTsMs < 1 || unixTsMs > 281474976710655) {
        throw new RangeError("`unixTsMs` must be a 48-bit positive integer");
      } else if (rollbackAllowance < 0 || rollbackAllowance > 281474976710655) {
        throw new RangeError("`rollbackAllowance` out of reasonable range");
      }
      if (unixTsMs > this.timestamp) {
        this.timestamp = unixTsMs;
        this.resetCounter();
      } else if (unixTsMs + rollbackAllowance >= this.timestamp) {
        this.counter++;
        if (this.counter > MAX_COUNTER) {
          this.timestamp++;
          this.resetCounter();
        }
      } else {
        return void 0;
      }
      return UUID.fromFieldsV7(this.timestamp, Math.trunc(this.counter / Math.pow(2, 30)), this.counter & Math.pow(2, 30) - 1, this.random.nextUint32());
    };
    V7Generator2.prototype.resetCounter = function() {
      this.counter = this.random.nextUint32() * 1024 + (this.random.nextUint32() & 1023);
    };
    V7Generator2.prototype.generateV4 = function() {
      var bytes = new Uint8Array(Uint32Array.of(this.random.nextUint32(), this.random.nextUint32(), this.random.nextUint32(), this.random.nextUint32()).buffer);
      bytes[6] = 64 | bytes[6] >>> 4;
      bytes[8] = 128 | bytes[8] >>> 2;
      return UUID.ofInner(bytes);
    };
    return V7Generator2;
  }()
);
var getDefaultRandom = function() {
  return {
    nextUint32: function() {
      return Math.trunc(Math.random() * 65536) * 65536 + Math.trunc(Math.random() * 65536);
    }
  };
};
var defaultGenerator;
var uuidv7 = function() {
  return uuidv7obj().toString();
};
var uuidv7obj = function() {
  return (defaultGenerator || (defaultGenerator = new V7Generator())).generate();
};
var PostHogFetchHttpError = (
  /** @class */
  function(_super) {
    __extends(PostHogFetchHttpError2, _super);
    function PostHogFetchHttpError2(response) {
      var _this = _super.call(this, "HTTP error while fetching PostHog: " + response.status) || this;
      _this.response = response;
      _this.name = "PostHogFetchHttpError";
      return _this;
    }
    return PostHogFetchHttpError2;
  }(Error)
);
var PostHogFetchNetworkError = (
  /** @class */
  function(_super) {
    __extends(PostHogFetchNetworkError2, _super);
    function PostHogFetchNetworkError2(error) {
      var _this = (
        // TRICKY: "cause" is a newer property but is just ignored otherwise. Cast to any to ignore the type issue.
        // @ts-ignore
        _super.call(this, "Network error while fetching PostHog", error instanceof Error ? { cause: error } : {}) || this
      );
      _this.error = error;
      _this.name = "PostHogFetchNetworkError";
      return _this;
    }
    return PostHogFetchNetworkError2;
  }(Error)
);
function isPostHogFetchError(err) {
  return typeof err === "object" && (err.name === "PostHogFetchHttpError" || err.name === "PostHogFetchNetworkError");
}
var PostHogCoreStateless = (
  /** @class */
  function() {
    function PostHogCoreStateless2(apiKey, options) {
      var _a, _b, _c, _d, _e;
      this.debugMode = false;
      this.disableGeoip = true;
      this.pendingPromises = {};
      this._events = new SimpleEventEmitter();
      assert(apiKey, "You must pass your PostHog project's api key.");
      this.apiKey = apiKey;
      this.host = removeTrailingSlash((options === null || options === void 0 ? void 0 : options.host) || "https://app.posthog.com");
      this.flushAt = (options === null || options === void 0 ? void 0 : options.flushAt) ? Math.max(options === null || options === void 0 ? void 0 : options.flushAt, 1) : 20;
      this.flushInterval = (_a = options === null || options === void 0 ? void 0 : options.flushInterval) !== null && _a !== void 0 ? _a : 1e4;
      this.captureMode = (options === null || options === void 0 ? void 0 : options.captureMode) || "form";
      this._optoutOverride = (options === null || options === void 0 ? void 0 : options.enable) === false;
      this._retryOptions = {
        retryCount: (_b = options === null || options === void 0 ? void 0 : options.fetchRetryCount) !== null && _b !== void 0 ? _b : 3,
        retryDelay: (_c = options === null || options === void 0 ? void 0 : options.fetchRetryDelay) !== null && _c !== void 0 ? _c : 3e3,
        retryCheck: isPostHogFetchError
      };
      this.requestTimeout = (_d = options === null || options === void 0 ? void 0 : options.requestTimeout) !== null && _d !== void 0 ? _d : 1e4;
      this.disableGeoip = (_e = options === null || options === void 0 ? void 0 : options.disableGeoip) !== null && _e !== void 0 ? _e : true;
    }
    PostHogCoreStateless2.prototype.getCommonEventProperties = function() {
      return {
        $lib: this.getLibraryId(),
        $lib_version: this.getLibraryVersion()
      };
    };
    Object.defineProperty(PostHogCoreStateless2.prototype, "optedOut", {
      get: function() {
        var _a, _b;
        return (_b = (_a = this.getPersistedProperty(PostHogPersistedProperty.OptedOut)) !== null && _a !== void 0 ? _a : this._optoutOverride) !== null && _b !== void 0 ? _b : false;
      },
      enumerable: false,
      configurable: true
    });
    PostHogCoreStateless2.prototype.optIn = function() {
      this.setPersistedProperty(PostHogPersistedProperty.OptedOut, false);
    };
    PostHogCoreStateless2.prototype.optOut = function() {
      this.setPersistedProperty(PostHogPersistedProperty.OptedOut, true);
    };
    PostHogCoreStateless2.prototype.on = function(event, cb) {
      return this._events.on(event, cb);
    };
    PostHogCoreStateless2.prototype.debug = function(enabled) {
      var _a;
      if (enabled === void 0) {
        enabled = true;
      }
      (_a = this.removeDebugCallback) === null || _a === void 0 ? void 0 : _a.call(this);
      this.debugMode = enabled;
      if (enabled) {
        this.removeDebugCallback = this.on("*", function(event, payload) {
          return console.log("PostHog Debug", event, payload);
        });
      }
    };
    PostHogCoreStateless2.prototype.buildPayload = function(payload) {
      return {
        distinct_id: payload.distinct_id,
        event: payload.event,
        properties: __assign(__assign({}, payload.properties || {}), this.getCommonEventProperties())
      };
    };
    PostHogCoreStateless2.prototype.addPendingPromise = function(promise) {
      var _this = this;
      var promiseUUID = uuidv7();
      this.pendingPromises[promiseUUID] = promise;
      promise.finally(function() {
        delete _this.pendingPromises[promiseUUID];
      });
    };
    PostHogCoreStateless2.prototype.identifyStateless = function(distinctId, properties, options) {
      var payload = __assign({}, this.buildPayload({
        distinct_id: distinctId,
        event: "$identify",
        properties
      }));
      this.enqueue("identify", payload, options);
      return this;
    };
    PostHogCoreStateless2.prototype.captureStateless = function(distinctId, event, properties, options) {
      var payload = this.buildPayload({ distinct_id: distinctId, event, properties });
      this.enqueue("capture", payload, options);
      return this;
    };
    PostHogCoreStateless2.prototype.aliasStateless = function(alias, distinctId, properties, options) {
      var payload = this.buildPayload({
        event: "$create_alias",
        distinct_id: distinctId,
        properties: __assign(__assign({}, properties || {}), { distinct_id: distinctId, alias })
      });
      this.enqueue("alias", payload, options);
      return this;
    };
    PostHogCoreStateless2.prototype.groupIdentifyStateless = function(groupType, groupKey, groupProperties, options, distinctId, eventProperties) {
      var payload = this.buildPayload({
        distinct_id: distinctId || "$".concat(groupType, "_").concat(groupKey),
        event: "$groupidentify",
        properties: __assign({ $group_type: groupType, $group_key: groupKey, $group_set: groupProperties || {} }, eventProperties || {})
      });
      this.enqueue("capture", payload, options);
      return this;
    };
    PostHogCoreStateless2.prototype.getDecide = function(distinctId, groups, personProperties, groupProperties, extraPayload) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      if (extraPayload === void 0) {
        extraPayload = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        var url, fetchOptions;
        var _this = this;
        return __generator(this, function(_a) {
          url = "".concat(this.host, "/decide/?v=3");
          fetchOptions = {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(__assign({ token: this.apiKey, distinct_id: distinctId, groups, person_properties: personProperties, group_properties: groupProperties }, extraPayload))
          };
          return [2, this.fetchWithRetry(url, fetchOptions).then(function(response) {
            return response.json();
          }).catch(function(error) {
            _this._events.emit("error", error);
            return void 0;
          })];
        });
      });
    };
    PostHogCoreStateless2.prototype.getFeatureFlagStateless = function(key, distinctId, groups, personProperties, groupProperties, disableGeoip) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        var featureFlags, response;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              return [4, this.getFeatureFlagsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip)];
            case 1:
              featureFlags = _a.sent();
              if (!featureFlags) {
                return [2, void 0];
              }
              response = featureFlags[key];
              if (response === void 0) {
                response = false;
              }
              return [2, response];
          }
        });
      });
    };
    PostHogCoreStateless2.prototype.getFeatureFlagPayloadStateless = function(key, distinctId, groups, personProperties, groupProperties, disableGeoip) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        var payloads, response;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              return [4, this.getFeatureFlagPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip)];
            case 1:
              payloads = _a.sent();
              if (!payloads) {
                return [2, void 0];
              }
              response = payloads[key];
              if (response === void 0) {
                return [2, null];
              }
              return [2, this._parsePayload(response)];
          }
        });
      });
    };
    PostHogCoreStateless2.prototype.getFeatureFlagPayloadsStateless = function(distinctId, groups, personProperties, groupProperties, disableGeoip) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        var payloads;
        var _this = this;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              return [4, this.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip)];
            case 1:
              payloads = _a.sent().payloads;
              if (payloads) {
                return [2, Object.fromEntries(Object.entries(payloads).map(function(_a2) {
                  var k = _a2[0], v = _a2[1];
                  return [k, _this._parsePayload(v)];
                }))];
              }
              return [2, payloads];
          }
        });
      });
    };
    PostHogCoreStateless2.prototype._parsePayload = function(response) {
      try {
        return JSON.parse(response);
      } catch (_a) {
        return response;
      }
    };
    PostHogCoreStateless2.prototype.getFeatureFlagsStateless = function(distinctId, groups, personProperties, groupProperties, disableGeoip) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              return [4, this.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip)];
            case 1:
              return [2, _a.sent().flags];
          }
        });
      });
    };
    PostHogCoreStateless2.prototype.getFeatureFlagsAndPayloadsStateless = function(distinctId, groups, personProperties, groupProperties, disableGeoip) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        var extraPayload, decideResponse, flags, payloads;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              extraPayload = {};
              if (disableGeoip !== null && disableGeoip !== void 0 ? disableGeoip : this.disableGeoip) {
                extraPayload["geoip_disable"] = true;
              }
              return [4, this.getDecide(distinctId, groups, personProperties, groupProperties, extraPayload)];
            case 1:
              decideResponse = _a.sent();
              flags = decideResponse === null || decideResponse === void 0 ? void 0 : decideResponse.featureFlags;
              payloads = decideResponse === null || decideResponse === void 0 ? void 0 : decideResponse.featureFlagPayloads;
              return [2, {
                flags,
                payloads
              }];
          }
        });
      });
    };
    PostHogCoreStateless2.prototype.enqueue = function(type, _message, options) {
      var _this = this;
      var _a;
      if (this.optedOut) {
        this._events.emit(type, "Library is disabled. Not sending event. To re-enable, call posthog.optIn()");
        return;
      }
      var message = __assign(__assign({}, _message), { type, library: this.getLibraryId(), library_version: this.getLibraryVersion(), timestamp: (options === null || options === void 0 ? void 0 : options.timestamp) ? options === null || options === void 0 ? void 0 : options.timestamp : currentISOTime(), uuid: (options === null || options === void 0 ? void 0 : options.uuid) ? options.uuid : uuidv7() });
      var addGeoipDisableProperty = (_a = options === null || options === void 0 ? void 0 : options.disableGeoip) !== null && _a !== void 0 ? _a : this.disableGeoip;
      if (addGeoipDisableProperty) {
        if (!message.properties) {
          message.properties = {};
        }
        message["properties"]["$geoip_disable"] = true;
      }
      if (message.distinctId) {
        message.distinct_id = message.distinctId;
        delete message.distinctId;
      }
      var queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];
      queue.push({ message });
      this.setPersistedProperty(PostHogPersistedProperty.Queue, queue);
      this._events.emit(type, message);
      if (queue.length >= this.flushAt) {
        this.flush();
      }
      if (this.flushInterval && !this._flushTimer) {
        this._flushTimer = safeSetTimeout(function() {
          return _this.flush();
        }, this.flushInterval);
      }
    };
    PostHogCoreStateless2.prototype.flushAsync = function() {
      var _this = this;
      return new Promise(function(resolve, reject) {
        _this.flush(function(err, data) {
          return err ? reject(err) : resolve(data);
        });
      });
    };
    PostHogCoreStateless2.prototype.flush = function(callback) {
      var _this = this;
      if (this._flushTimer) {
        clearTimeout(this._flushTimer);
        this._flushTimer = null;
      }
      var queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];
      if (!queue.length) {
        return callback === null || callback === void 0 ? void 0 : callback();
      }
      var items = queue.splice(0, this.flushAt);
      this.setPersistedProperty(PostHogPersistedProperty.Queue, queue);
      var messages = items.map(function(item) {
        return item.message;
      });
      var data = {
        api_key: this.apiKey,
        batch: messages,
        sent_at: currentISOTime()
      };
      var done = function(err) {
        if (err) {
          _this._events.emit("error", err);
        }
        callback === null || callback === void 0 ? void 0 : callback(err, messages);
        _this._events.emit("flush", messages);
      };
      this.getCustomUserAgent();
      var payload = JSON.stringify(data);
      var url = this.captureMode === "form" ? "".concat(this.host, "/e/?ip=1&_=").concat(currentTimestamp(), "&v=").concat(this.getLibraryVersion()) : "".concat(this.host, "/batch/");
      var fetchOptions = this.captureMode === "form" ? {
        method: "POST",
        mode: "no-cors",
        credentials: "omit",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: "data=".concat(encodeURIComponent(LZString.compressToBase64(payload)), "&compression=lz64")
      } : {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: payload
      };
      var requestPromise = this.fetchWithRetry(url, fetchOptions);
      this.addPendingPromise(requestPromise.then(function() {
        return done();
      }).catch(function(err) {
        done(err);
      }));
    };
    PostHogCoreStateless2.prototype.fetchWithRetry = function(url, options, retryOptions) {
      var _a;
      var _b;
      return __awaiter(this, void 0, void 0, function() {
        var _this = this;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              (_a = (_b = AbortSignal).timeout) !== null && _a !== void 0 ? _a : _b.timeout = function timeout(ms) {
                var ctrl = new AbortController();
                setTimeout(function() {
                  return ctrl.abort();
                }, ms);
                return ctrl.signal;
              };
              return [4, retriable(function() {
                return __awaiter(_this, void 0, void 0, function() {
                  var res, e_1, isNoCors;
                  return __generator(this, function(_a2) {
                    switch (_a2.label) {
                      case 0:
                        res = null;
                        _a2.label = 1;
                      case 1:
                        _a2.trys.push([1, 3, , 4]);
                        return [4, this.fetch(url, __assign({ signal: AbortSignal.timeout(this.requestTimeout) }, options))];
                      case 2:
                        res = _a2.sent();
                        return [3, 4];
                      case 3:
                        e_1 = _a2.sent();
                        throw new PostHogFetchNetworkError(e_1);
                      case 4:
                        isNoCors = options.mode === "no-cors";
                        if (!isNoCors && (res.status < 200 || res.status >= 400)) {
                          throw new PostHogFetchHttpError(res);
                        }
                        return [2, res];
                    }
                  });
                });
              }, __assign(__assign({}, this._retryOptions), retryOptions))];
            case 1:
              return [2, _c.sent()];
          }
        });
      });
    };
    PostHogCoreStateless2.prototype.shutdownAsync = function() {
      return __awaiter(this, void 0, void 0, function() {
        var e_2;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              clearTimeout(this._flushTimer);
              _a.label = 1;
            case 1:
              _a.trys.push([1, 5, , 6]);
              return [4, this.flushAsync()];
            case 2:
              _a.sent();
              return [
                4,
                Promise.all(Object.values(this.pendingPromises).map(function(x) {
                  return x.catch(function() {
                  });
                }))
                // flush again to make sure we send all events, some of which might've been added
                // while we were waiting for the pending promises to resolve
                // For example, see sendFeatureFlags in posthog-node/src/posthog-node.ts::capture
              ];
            case 3:
              _a.sent();
              return [4, this.flushAsync()];
            case 4:
              _a.sent();
              return [3, 6];
            case 5:
              e_2 = _a.sent();
              if (!isPostHogFetchError(e_2)) {
                throw e_2;
              }
              console.error("Error while shutting down PostHog", e_2);
              return [3, 6];
            case 6:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    PostHogCoreStateless2.prototype.shutdown = function() {
      void this.shutdownAsync();
    };
    return PostHogCoreStateless2;
  }()
);
(function(_super) {
  __extends(PostHogCore, _super);
  function PostHogCore(apiKey, options) {
    var _this = this;
    var _a, _b, _c;
    var disableGeoipOption = (_a = options === null || options === void 0 ? void 0 : options.disableGeoip) !== null && _a !== void 0 ? _a : false;
    _this = _super.call(this, apiKey, __assign(__assign({}, options), { disableGeoip: disableGeoipOption })) || this;
    _this.flagCallReported = {};
    _this.sessionProps = {};
    _this.sendFeatureFlagEvent = (_b = options === null || options === void 0 ? void 0 : options.sendFeatureFlagEvent) !== null && _b !== void 0 ? _b : true;
    _this._sessionExpirationTimeSeconds = (_c = options === null || options === void 0 ? void 0 : options.sessionExpirationTimeSeconds) !== null && _c !== void 0 ? _c : 1800;
    return _this;
  }
  PostHogCore.prototype.setupBootstrap = function(options) {
    var _a, _b, _c, _d;
    if ((_a = options === null || options === void 0 ? void 0 : options.bootstrap) === null || _a === void 0 ? void 0 : _a.distinctId) {
      if ((_b = options === null || options === void 0 ? void 0 : options.bootstrap) === null || _b === void 0 ? void 0 : _b.isIdentifiedId) {
        this.setPersistedProperty(PostHogPersistedProperty.DistinctId, options.bootstrap.distinctId);
      } else {
        this.setPersistedProperty(PostHogPersistedProperty.AnonymousId, options.bootstrap.distinctId);
      }
    }
    if ((_c = options === null || options === void 0 ? void 0 : options.bootstrap) === null || _c === void 0 ? void 0 : _c.featureFlags) {
      var activeFlags = Object.keys(((_d = options.bootstrap) === null || _d === void 0 ? void 0 : _d.featureFlags) || {}).filter(function(flag) {
        var _a2, _b2;
        return !!((_b2 = (_a2 = options.bootstrap) === null || _a2 === void 0 ? void 0 : _a2.featureFlags) === null || _b2 === void 0 ? void 0 : _b2[flag]);
      }).reduce(function(res, key) {
        var _a2, _b2;
        return res[key] = ((_b2 = (_a2 = options.bootstrap) === null || _a2 === void 0 ? void 0 : _a2.featureFlags) === null || _b2 === void 0 ? void 0 : _b2[key]) || false, res;
      }, {});
      this.setKnownFeatureFlags(activeFlags);
      (options === null || options === void 0 ? void 0 : options.bootstrap.featureFlagPayloads) && this.setKnownFeatureFlagPayloads(options === null || options === void 0 ? void 0 : options.bootstrap.featureFlagPayloads);
    }
  };
  Object.defineProperty(PostHogCore.prototype, "props", {
    // NOTE: Props are lazy loaded from localstorage hence the complex getter setter logic
    get: function() {
      if (!this._props) {
        this._props = this.getPersistedProperty(PostHogPersistedProperty.Props);
      }
      return this._props || {};
    },
    set: function(val) {
      this._props = val;
    },
    enumerable: false,
    configurable: true
  });
  PostHogCore.prototype.clearProps = function() {
    this.props = void 0;
    this.sessionProps = {};
  };
  PostHogCore.prototype.on = function(event, cb) {
    return this._events.on(event, cb);
  };
  PostHogCore.prototype.reset = function(propertiesToKeep) {
    var allPropertiesToKeep = __spreadArray([PostHogPersistedProperty.Queue], propertiesToKeep || [], true);
    this.clearProps();
    for (var _i = 0, _a = Object.keys(PostHogPersistedProperty); _i < _a.length; _i++) {
      var key = _a[_i];
      if (!allPropertiesToKeep.includes(PostHogPersistedProperty[key])) {
        this.setPersistedProperty(PostHogPersistedProperty[key], null);
      }
    }
  };
  PostHogCore.prototype.getCommonEventProperties = function() {
    var featureFlags = this.getFeatureFlags();
    var featureVariantProperties = {};
    if (featureFlags) {
      for (var _i = 0, _a = Object.entries(featureFlags); _i < _a.length; _i++) {
        var _b = _a[_i], feature = _b[0], variant = _b[1];
        featureVariantProperties["$feature/".concat(feature)] = variant;
      }
    }
    return __assign(__assign({ $active_feature_flags: featureFlags ? Object.keys(featureFlags) : void 0 }, featureVariantProperties), _super.prototype.getCommonEventProperties.call(this));
  };
  PostHogCore.prototype.enrichProperties = function(properties) {
    return __assign(__assign(__assign(__assign(__assign({}, this.props), this.sessionProps), properties || {}), this.getCommonEventProperties()), { $session_id: this.getSessionId() });
  };
  PostHogCore.prototype.getSessionId = function() {
    var sessionId = this.getPersistedProperty(PostHogPersistedProperty.SessionId);
    var sessionTimestamp = this.getPersistedProperty(PostHogPersistedProperty.SessionLastTimestamp) || 0;
    if (!sessionId || Date.now() - sessionTimestamp > this._sessionExpirationTimeSeconds * 1e3) {
      sessionId = uuidv7();
      this.setPersistedProperty(PostHogPersistedProperty.SessionId, sessionId);
    }
    this.setPersistedProperty(PostHogPersistedProperty.SessionLastTimestamp, Date.now());
    return sessionId;
  };
  PostHogCore.prototype.resetSessionId = function() {
    this.setPersistedProperty(PostHogPersistedProperty.SessionId, null);
  };
  PostHogCore.prototype.getAnonymousId = function() {
    var anonId = this.getPersistedProperty(PostHogPersistedProperty.AnonymousId);
    if (!anonId) {
      anonId = uuidv7();
      this.setPersistedProperty(PostHogPersistedProperty.AnonymousId, anonId);
    }
    return anonId;
  };
  PostHogCore.prototype.getDistinctId = function() {
    return this.getPersistedProperty(PostHogPersistedProperty.DistinctId) || this.getAnonymousId();
  };
  PostHogCore.prototype.unregister = function(property) {
    delete this.props[property];
    this.setPersistedProperty(PostHogPersistedProperty.Props, this.props);
  };
  PostHogCore.prototype.register = function(properties) {
    this.props = __assign(__assign({}, this.props), properties);
    this.setPersistedProperty(PostHogPersistedProperty.Props, this.props);
  };
  PostHogCore.prototype.registerForSession = function(properties) {
    this.sessionProps = __assign(__assign({}, this.sessionProps), properties);
  };
  PostHogCore.prototype.unregisterForSession = function(property) {
    delete this.sessionProps[property];
  };
  PostHogCore.prototype.identify = function(distinctId, properties, options) {
    var previousDistinctId = this.getDistinctId();
    distinctId = distinctId || previousDistinctId;
    if (properties === null || properties === void 0 ? void 0 : properties.$groups) {
      this.groups(properties.$groups);
    }
    var allProperties = this.enrichProperties(__assign(__assign({}, properties), { $anon_distinct_id: this.getAnonymousId(), $set: properties }));
    if (distinctId !== previousDistinctId) {
      this.setPersistedProperty(PostHogPersistedProperty.AnonymousId, previousDistinctId);
      this.setPersistedProperty(PostHogPersistedProperty.DistinctId, distinctId);
      this.reloadFeatureFlags();
    }
    _super.prototype.identifyStateless.call(this, distinctId, allProperties, options);
    return this;
  };
  PostHogCore.prototype.capture = function(event, properties, options) {
    var distinctId = this.getDistinctId();
    if (properties === null || properties === void 0 ? void 0 : properties.$groups) {
      this.groups(properties.$groups);
    }
    var allProperties = this.enrichProperties(properties);
    _super.prototype.captureStateless.call(this, distinctId, event, allProperties, options);
    return this;
  };
  PostHogCore.prototype.alias = function(alias) {
    var distinctId = this.getDistinctId();
    var allProperties = this.enrichProperties({});
    _super.prototype.aliasStateless.call(this, alias, distinctId, allProperties);
    return this;
  };
  PostHogCore.prototype.autocapture = function(eventType, elements, properties, options) {
    if (properties === void 0) {
      properties = {};
    }
    var distinctId = this.getDistinctId();
    var payload = {
      distinct_id: distinctId,
      event: "$autocapture",
      properties: __assign(__assign({}, this.enrichProperties(properties)), { $event_type: eventType, $elements: elements })
    };
    this.enqueue("autocapture", payload, options);
    return this;
  };
  PostHogCore.prototype.groups = function(groups) {
    var existingGroups = this.props.$groups || {};
    this.register({
      $groups: __assign(__assign({}, existingGroups), groups)
    });
    if (Object.keys(groups).find(function(type) {
      return existingGroups[type] !== groups[type];
    })) {
      this.reloadFeatureFlags();
    }
    return this;
  };
  PostHogCore.prototype.group = function(groupType, groupKey, groupProperties, options) {
    var _a;
    this.groups((_a = {}, _a[groupType] = groupKey, _a));
    if (groupProperties) {
      this.groupIdentify(groupType, groupKey, groupProperties, options);
    }
    return this;
  };
  PostHogCore.prototype.groupIdentify = function(groupType, groupKey, groupProperties, options) {
    var distinctId = this.getDistinctId();
    var eventProperties = this.enrichProperties({});
    _super.prototype.groupIdentifyStateless.call(this, groupType, groupKey, groupProperties, options, distinctId, eventProperties);
    return this;
  };
  PostHogCore.prototype.setPersonPropertiesForFlags = function(properties) {
    var existingProperties = this.getPersistedProperty(PostHogPersistedProperty.PersonProperties) || {};
    this.setPersistedProperty(PostHogPersistedProperty.PersonProperties, __assign(__assign({}, existingProperties), properties));
    return this;
  };
  PostHogCore.prototype.resetPersonPropertiesForFlags = function() {
    this.setPersistedProperty(PostHogPersistedProperty.PersonProperties, {});
  };
  PostHogCore.prototype.personProperties = function(properties) {
    return this.setPersonPropertiesForFlags(properties);
  };
  PostHogCore.prototype.setGroupPropertiesForFlags = function(properties) {
    var existingProperties = this.getPersistedProperty(PostHogPersistedProperty.GroupProperties) || {};
    if (Object.keys(existingProperties).length !== 0) {
      Object.keys(existingProperties).forEach(function(groupType) {
        existingProperties[groupType] = __assign(__assign({}, existingProperties[groupType]), properties[groupType]);
        delete properties[groupType];
      });
    }
    this.setPersistedProperty(PostHogPersistedProperty.GroupProperties, __assign(__assign({}, existingProperties), properties));
    return this;
  };
  PostHogCore.prototype.resetGroupPropertiesForFlags = function() {
    this.setPersistedProperty(PostHogPersistedProperty.GroupProperties, {});
  };
  PostHogCore.prototype.groupProperties = function(properties) {
    return this.setGroupPropertiesForFlags(properties);
  };
  PostHogCore.prototype.decideAsync = function(sendAnonDistinctId) {
    if (sendAnonDistinctId === void 0) {
      sendAnonDistinctId = true;
    }
    if (this._decideResponsePromise) {
      return this._decideResponsePromise;
    }
    return this._decideAsync(sendAnonDistinctId);
  };
  PostHogCore.prototype._decideAsync = function(sendAnonDistinctId) {
    if (sendAnonDistinctId === void 0) {
      sendAnonDistinctId = true;
    }
    return __awaiter(this, void 0, void 0, function() {
      var distinctId, groups, personProperties, groupProperties, extraProperties;
      var _this = this;
      return __generator(this, function(_a) {
        distinctId = this.getDistinctId();
        groups = this.props.$groups || {};
        personProperties = this.getPersistedProperty(PostHogPersistedProperty.PersonProperties) || {};
        groupProperties = this.getPersistedProperty(PostHogPersistedProperty.GroupProperties) || {};
        extraProperties = {
          $anon_distinct_id: sendAnonDistinctId ? this.getAnonymousId() : void 0
        };
        this._decideResponsePromise = _super.prototype.getDecide.call(this, distinctId, groups, personProperties, groupProperties, extraProperties).then(function(res) {
          if (res === null || res === void 0 ? void 0 : res.featureFlags) {
            var newFeatureFlags = res.featureFlags;
            var newFeatureFlagPayloads = res.featureFlagPayloads;
            if (res.errorsWhileComputingFlags) {
              var currentFlags = _this.getPersistedProperty(PostHogPersistedProperty.FeatureFlags);
              var currentFlagPayloads = _this.getPersistedProperty(PostHogPersistedProperty.FeatureFlagPayloads);
              newFeatureFlags = __assign(__assign({}, currentFlags), res.featureFlags);
              newFeatureFlagPayloads = __assign(__assign({}, currentFlagPayloads), res.featureFlagPayloads);
            }
            _this.setKnownFeatureFlags(newFeatureFlags);
            _this.setKnownFeatureFlagPayloads(newFeatureFlagPayloads);
          }
          return res;
        }).finally(function() {
          _this._decideResponsePromise = void 0;
        });
        return [2, this._decideResponsePromise];
      });
    });
  };
  PostHogCore.prototype.setKnownFeatureFlags = function(featureFlags) {
    this.setPersistedProperty(PostHogPersistedProperty.FeatureFlags, featureFlags);
    this._events.emit("featureflags", featureFlags);
  };
  PostHogCore.prototype.setKnownFeatureFlagPayloads = function(featureFlagPayloads) {
    this.setPersistedProperty(PostHogPersistedProperty.FeatureFlagPayloads, featureFlagPayloads);
  };
  PostHogCore.prototype.getFeatureFlag = function(key) {
    var featureFlags = this.getFeatureFlags();
    if (!featureFlags) {
      return void 0;
    }
    var response = featureFlags[key];
    if (response === void 0) {
      response = false;
    }
    if (this.sendFeatureFlagEvent && !this.flagCallReported[key]) {
      this.flagCallReported[key] = true;
      this.capture("$feature_flag_called", {
        $feature_flag: key,
        $feature_flag_response: response
      });
    }
    return response;
  };
  PostHogCore.prototype.getFeatureFlagPayload = function(key) {
    var payloads = this.getFeatureFlagPayloads();
    if (!payloads) {
      return void 0;
    }
    var response = payloads[key];
    if (response === void 0) {
      return null;
    }
    return this._parsePayload(response);
  };
  PostHogCore.prototype.getFeatureFlagPayloads = function() {
    var _this = this;
    var payloads = this.getPersistedProperty(PostHogPersistedProperty.FeatureFlagPayloads);
    if (payloads) {
      return Object.fromEntries(Object.entries(payloads).map(function(_a) {
        var k = _a[0], v = _a[1];
        return [k, _this._parsePayload(v)];
      }));
    }
    return payloads;
  };
  PostHogCore.prototype.getFeatureFlags = function() {
    var flags = this.getPersistedProperty(PostHogPersistedProperty.FeatureFlags);
    var overriddenFlags = this.getPersistedProperty(PostHogPersistedProperty.OverrideFeatureFlags);
    if (!overriddenFlags) {
      return flags;
    }
    flags = flags || {};
    for (var key in overriddenFlags) {
      if (!overriddenFlags[key]) {
        delete flags[key];
      } else {
        flags[key] = overriddenFlags[key];
      }
    }
    return flags;
  };
  PostHogCore.prototype.getFeatureFlagsAndPayloads = function() {
    var flags = this.getFeatureFlags();
    var payloads = this.getFeatureFlagPayloads();
    return {
      flags,
      payloads
    };
  };
  PostHogCore.prototype.isFeatureEnabled = function(key) {
    var response = this.getFeatureFlag(key);
    if (response === void 0) {
      return void 0;
    }
    return !!response;
  };
  PostHogCore.prototype.reloadFeatureFlags = function(cb) {
    this.decideAsync().then(function(res) {
      cb === null || cb === void 0 ? void 0 : cb(void 0, res === null || res === void 0 ? void 0 : res.featureFlags);
    }).catch(function(e) {
      cb === null || cb === void 0 ? void 0 : cb(e, void 0);
      if (!cb) {
        console.log("[PostHog] Error reloading feature flags", e);
      }
    });
  };
  PostHogCore.prototype.reloadFeatureFlagsAsync = function(sendAnonDistinctId) {
    var _a;
    if (sendAnonDistinctId === void 0) {
      sendAnonDistinctId = true;
    }
    return __awaiter(this, void 0, void 0, function() {
      return __generator(this, function(_b) {
        switch (_b.label) {
          case 0:
            return [4, this.decideAsync(sendAnonDistinctId)];
          case 1:
            return [2, (_a = _b.sent()) === null || _a === void 0 ? void 0 : _a.featureFlags];
        }
      });
    });
  };
  PostHogCore.prototype.onFeatureFlags = function(cb) {
    var _this = this;
    return this.on("featureflags", function() {
      return __awaiter(_this, void 0, void 0, function() {
        var flags;
        return __generator(this, function(_a) {
          flags = this.getFeatureFlags();
          if (flags) {
            cb(flags);
          }
          return [
            2
            /*return*/
          ];
        });
      });
    });
  };
  PostHogCore.prototype.onFeatureFlag = function(key, cb) {
    var _this = this;
    return this.on("featureflags", function() {
      return __awaiter(_this, void 0, void 0, function() {
        var flagResponse;
        return __generator(this, function(_a) {
          flagResponse = this.getFeatureFlag(key);
          if (flagResponse !== void 0) {
            cb(flagResponse);
          }
          return [
            2
            /*return*/
          ];
        });
      });
    });
  };
  PostHogCore.prototype.overrideFeatureFlag = function(flags) {
    if (flags === null) {
      return this.setPersistedProperty(PostHogPersistedProperty.OverrideFeatureFlags, null);
    }
    return this.setPersistedProperty(PostHogPersistedProperty.OverrideFeatureFlags, flags);
  };
  return PostHogCore;
})(PostHogCoreStateless);
var PostHogMemoryStorage = (
  /** @class */
  function() {
    function PostHogMemoryStorage2() {
      this._memoryStorage = {};
    }
    PostHogMemoryStorage2.prototype.getProperty = function(key) {
      return this._memoryStorage[key];
    };
    PostHogMemoryStorage2.prototype.setProperty = function(key, value) {
      this._memoryStorage[key] = value !== null ? value : void 0;
    };
    return PostHogMemoryStorage2;
  }()
);
var _fetch = (
  // eslint-disable-next-line @typescript-eslint/prefer-ts-expect-error
  // @ts-ignore
  typeof fetch !== "undefined" ? fetch : typeof global.fetch !== "undefined" ? global.fetch : void 0
);
if (!_fetch) {
  axios_1 = require_axios();
  _fetch = function(url, options) {
    return __awaiter(void 0, void 0, void 0, function() {
      var res;
      return __generator(this, function(_a) {
        switch (_a.label) {
          case 0:
            return [
              4,
              axios_1.request({
                url,
                headers: options.headers,
                method: options.method.toLowerCase(),
                data: options.body,
                signal: options.signal,
                // fetch only throws on network errors, not on HTTP errors
                validateStatus: function() {
                  return true;
                }
              })
            ];
          case 1:
            res = _a.sent();
            return [
              2,
              {
                status: res.status,
                text: function() {
                  return __awaiter(void 0, void 0, void 0, function() {
                    return __generator(this, function(_a2) {
                      return [
                        2,
                        res.data
                      ];
                    });
                  });
                },
                json: function() {
                  return __awaiter(void 0, void 0, void 0, function() {
                    return __generator(this, function(_a2) {
                      return [
                        2,
                        res.data
                      ];
                    });
                  });
                }
              }
            ];
        }
      });
    });
  };
}
var axios_1;
var fetch$1 = _fetch;
var LONG_SCALE = 1152921504606847e3;
var ClientError = (
  /** @class */
  function(_super) {
    __extends(ClientError2, _super);
    function ClientError2(message) {
      var _this = _super.call(this) || this;
      Error.captureStackTrace(_this, _this.constructor);
      _this.name = "ClientError";
      _this.message = message;
      Object.setPrototypeOf(_this, ClientError2.prototype);
      return _this;
    }
    return ClientError2;
  }(Error)
);
var InconclusiveMatchError = (
  /** @class */
  function(_super) {
    __extends(InconclusiveMatchError2, _super);
    function InconclusiveMatchError2(message) {
      var _this = _super.call(this, message) || this;
      _this.name = _this.constructor.name;
      Error.captureStackTrace(_this, _this.constructor);
      Object.setPrototypeOf(_this, InconclusiveMatchError2.prototype);
      return _this;
    }
    return InconclusiveMatchError2;
  }(Error)
);
var FeatureFlagsPoller = (
  /** @class */
  function() {
    function FeatureFlagsPoller2(_a) {
      var pollingInterval = _a.pollingInterval, personalApiKey = _a.personalApiKey, projectApiKey = _a.projectApiKey, timeout = _a.timeout, host = _a.host, options = __rest(_a, ["pollingInterval", "personalApiKey", "projectApiKey", "timeout", "host"]);
      this.debugMode = false;
      this.pollingInterval = pollingInterval;
      this.personalApiKey = personalApiKey;
      this.featureFlags = [];
      this.featureFlagsByKey = {};
      this.groupTypeMapping = {};
      this.cohorts = {};
      this.loadedSuccessfullyOnce = false;
      this.timeout = timeout;
      this.projectApiKey = projectApiKey;
      this.host = host;
      this.poller = void 0;
      this.fetch = options.fetch || fetch$1;
      this.onError = options.onError;
      void this.loadFeatureFlags();
    }
    FeatureFlagsPoller2.prototype.debug = function(enabled) {
      if (enabled === void 0) {
        enabled = true;
      }
      this.debugMode = enabled;
    };
    FeatureFlagsPoller2.prototype.getFeatureFlag = function(key, distinctId, groups, personProperties, groupProperties) {
      var _a;
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        var response, featureFlag, _i, _b, flag;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              return [
                4,
                this.loadFeatureFlags()
              ];
            case 1:
              _c.sent();
              response = void 0;
              featureFlag = void 0;
              if (!this.loadedSuccessfullyOnce) {
                return [
                  2,
                  response
                ];
              }
              for (_i = 0, _b = this.featureFlags; _i < _b.length; _i++) {
                flag = _b[_i];
                if (key === flag.key) {
                  featureFlag = flag;
                  break;
                }
              }
              if (featureFlag !== void 0) {
                try {
                  response = this.computeFlagLocally(featureFlag, distinctId, groups, personProperties, groupProperties);
                  if (this.debugMode) {
                    console.debug("Successfully computed flag locally: ".concat(key, " -> ").concat(response));
                  }
                } catch (e) {
                  if (e instanceof InconclusiveMatchError) {
                    if (this.debugMode) {
                      console.debug("InconclusiveMatchError when computing flag locally: ".concat(key, ": ").concat(e));
                    }
                  } else if (e instanceof Error) {
                    (_a = this.onError) === null || _a === void 0 ? void 0 : _a.call(this, new Error("Error computing flag locally: ".concat(key, ": ").concat(e)));
                  }
                }
              }
              return [
                2,
                response
              ];
          }
        });
      });
    };
    FeatureFlagsPoller2.prototype.computeFeatureFlagPayloadLocally = function(key, matchValue) {
      var _a, _b, _c, _d, _e, _f, _g, _h;
      return __awaiter(this, void 0, void 0, function() {
        var response;
        return __generator(this, function(_j) {
          switch (_j.label) {
            case 0:
              return [
                4,
                this.loadFeatureFlags()
              ];
            case 1:
              _j.sent();
              response = void 0;
              if (!this.loadedSuccessfullyOnce) {
                return [
                  2,
                  void 0
                ];
              }
              if (typeof matchValue == "boolean") {
                response = (_d = (_c = (_b = (_a = this.featureFlagsByKey) === null || _a === void 0 ? void 0 : _a[key]) === null || _b === void 0 ? void 0 : _b.filters) === null || _c === void 0 ? void 0 : _c.payloads) === null || _d === void 0 ? void 0 : _d[matchValue.toString()];
              } else if (typeof matchValue == "string") {
                response = (_h = (_g = (_f = (_e = this.featureFlagsByKey) === null || _e === void 0 ? void 0 : _e[key]) === null || _f === void 0 ? void 0 : _f.filters) === null || _g === void 0 ? void 0 : _g.payloads) === null || _h === void 0 ? void 0 : _h[matchValue];
              }
              if (response === void 0) {
                return [
                  2,
                  null
                ];
              }
              return [
                2,
                response
              ];
          }
        });
      });
    };
    FeatureFlagsPoller2.prototype.getAllFlagsAndPayloads = function(distinctId, groups, personProperties, groupProperties) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      return __awaiter(this, void 0, void 0, function() {
        var response, payloads, fallbackToDecide;
        var _this = this;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              return [
                4,
                this.loadFeatureFlags()
              ];
            case 1:
              _a.sent();
              response = {};
              payloads = {};
              fallbackToDecide = this.featureFlags.length == 0;
              this.featureFlags.map(function(flag) {
                return __awaiter(_this, void 0, void 0, function() {
                  var matchValue, matchPayload, e_1;
                  var _a2;
                  return __generator(this, function(_b) {
                    switch (_b.label) {
                      case 0:
                        _b.trys.push([0, 2, , 3]);
                        matchValue = this.computeFlagLocally(flag, distinctId, groups, personProperties, groupProperties);
                        response[flag.key] = matchValue;
                        return [
                          4,
                          this.computeFeatureFlagPayloadLocally(flag.key, matchValue)
                        ];
                      case 1:
                        matchPayload = _b.sent();
                        if (matchPayload) {
                          payloads[flag.key] = matchPayload;
                        }
                        return [
                          3,
                          3
                        ];
                      case 2:
                        e_1 = _b.sent();
                        if (e_1 instanceof InconclusiveMatchError) ;
                        else if (e_1 instanceof Error) {
                          (_a2 = this.onError) === null || _a2 === void 0 ? void 0 : _a2.call(this, new Error("Error computing flag locally: ".concat(flag.key, ": ").concat(e_1)));
                        }
                        fallbackToDecide = true;
                        return [
                          3,
                          3
                        ];
                      case 3:
                        return [
                          2
                          /*return*/
                        ];
                    }
                  });
                });
              });
              return [
                2,
                {
                  response,
                  payloads,
                  fallbackToDecide
                }
              ];
          }
        });
      });
    };
    FeatureFlagsPoller2.prototype.computeFlagLocally = function(flag, distinctId, groups, personProperties, groupProperties) {
      if (groups === void 0) {
        groups = {};
      }
      if (personProperties === void 0) {
        personProperties = {};
      }
      if (groupProperties === void 0) {
        groupProperties = {};
      }
      if (flag.ensure_experience_continuity) {
        throw new InconclusiveMatchError("Flag has experience continuity enabled");
      }
      if (!flag.active) {
        return false;
      }
      var flagFilters = flag.filters || {};
      var aggregation_group_type_index = flagFilters.aggregation_group_type_index;
      if (aggregation_group_type_index != void 0) {
        var groupName = this.groupTypeMapping[String(aggregation_group_type_index)];
        if (!groupName) {
          if (this.debugMode) {
            console.warn("[FEATURE FLAGS] Unknown group type index ".concat(aggregation_group_type_index, " for feature flag ").concat(flag.key));
          }
          throw new InconclusiveMatchError("Flag has unknown group type index");
        }
        if (!(groupName in groups)) {
          if (this.debugMode) {
            console.warn("[FEATURE FLAGS] Can't compute group feature flag: ".concat(flag.key, " without group names passed in"));
          }
          return false;
        }
        var focusedGroupProperties = groupProperties[groupName];
        return this.matchFeatureFlagProperties(flag, groups[groupName], focusedGroupProperties);
      } else {
        return this.matchFeatureFlagProperties(flag, distinctId, personProperties);
      }
    };
    FeatureFlagsPoller2.prototype.matchFeatureFlagProperties = function(flag, distinctId, properties) {
      var _a;
      var flagFilters = flag.filters || {};
      var flagConditions = flagFilters.groups || [];
      var isInconclusive = false;
      var result = void 0;
      var sortedFlagConditions = __spreadArray([], flagConditions, true).sort(function(conditionA, conditionB) {
        var AHasVariantOverride = !!conditionA.variant;
        var BHasVariantOverride = !!conditionB.variant;
        if (AHasVariantOverride && BHasVariantOverride) {
          return 0;
        } else if (AHasVariantOverride) {
          return -1;
        } else if (BHasVariantOverride) {
          return 1;
        } else {
          return 0;
        }
      });
      var _loop_1 = function(condition2) {
        try {
          if (this_1.isConditionMatch(flag, distinctId, condition2, properties)) {
            var variantOverride_1 = condition2.variant;
            var flagVariants = ((_a = flagFilters.multivariate) === null || _a === void 0 ? void 0 : _a.variants) || [];
            if (variantOverride_1 && flagVariants.some(function(variant) {
              return variant.key === variantOverride_1;
            })) {
              result = variantOverride_1;
            } else {
              result = this_1.getMatchingVariant(flag, distinctId) || true;
            }
            return "break";
          }
        } catch (e) {
          if (e instanceof InconclusiveMatchError) {
            isInconclusive = true;
          } else {
            throw e;
          }
        }
      };
      var this_1 = this;
      for (var _i = 0, sortedFlagConditions_1 = sortedFlagConditions; _i < sortedFlagConditions_1.length; _i++) {
        var condition = sortedFlagConditions_1[_i];
        var state_1 = _loop_1(condition);
        if (state_1 === "break") break;
      }
      if (result !== void 0) {
        return result;
      } else if (isInconclusive) {
        throw new InconclusiveMatchError("Can't determine if feature flag is enabled or not with given properties");
      }
      return false;
    };
    FeatureFlagsPoller2.prototype.isConditionMatch = function(flag, distinctId, condition, properties) {
      var rolloutPercentage = condition.rollout_percentage;
      if ((condition.properties || []).length > 0) {
        for (var _i = 0, _a = condition.properties; _i < _a.length; _i++) {
          var prop = _a[_i];
          var propertyType = prop.type;
          var matches = false;
          if (propertyType === "cohort") {
            matches = matchCohort(prop, properties, this.cohorts);
          } else {
            matches = matchProperty(prop, properties);
          }
          if (!matches) {
            return false;
          }
        }
        if (rolloutPercentage == void 0) {
          return true;
        }
      }
      if (rolloutPercentage != void 0 && _hash(flag.key, distinctId) > rolloutPercentage / 100) {
        return false;
      }
      return true;
    };
    FeatureFlagsPoller2.prototype.getMatchingVariant = function(flag, distinctId) {
      var hashValue = _hash(flag.key, distinctId, "variant");
      var matchingVariant = this.variantLookupTable(flag).find(function(variant) {
        return hashValue >= variant.valueMin && hashValue < variant.valueMax;
      });
      if (matchingVariant) {
        return matchingVariant.key;
      }
      return void 0;
    };
    FeatureFlagsPoller2.prototype.variantLookupTable = function(flag) {
      var _a;
      var lookupTable = [];
      var valueMin = 0;
      var valueMax = 0;
      var flagFilters = flag.filters || {};
      var multivariates = ((_a = flagFilters.multivariate) === null || _a === void 0 ? void 0 : _a.variants) || [];
      multivariates.forEach(function(variant) {
        valueMax = valueMin + variant.rollout_percentage / 100;
        lookupTable.push({
          valueMin,
          valueMax,
          key: variant.key
        });
        valueMin = valueMax;
      });
      return lookupTable;
    };
    FeatureFlagsPoller2.prototype.loadFeatureFlags = function(forceReload) {
      if (forceReload === void 0) {
        forceReload = false;
      }
      return __awaiter(this, void 0, void 0, function() {
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              if (!(!this.loadedSuccessfullyOnce || forceReload)) return [
                3,
                2
              ];
              return [
                4,
                this._loadFeatureFlags()
              ];
            case 1:
              _a.sent();
              _a.label = 2;
            case 2:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    FeatureFlagsPoller2.prototype._loadFeatureFlags = function() {
      var _a, _b;
      return __awaiter(this, void 0, void 0, function() {
        var res, responseJson, err_1;
        var _this = this;
        return __generator(this, function(_c) {
          switch (_c.label) {
            case 0:
              if (this.poller) {
                clearTimeout(this.poller);
                this.poller = void 0;
              }
              this.poller = setTimeout(function() {
                return _this._loadFeatureFlags();
              }, this.pollingInterval);
              _c.label = 1;
            case 1:
              _c.trys.push([1, 4, , 5]);
              return [
                4,
                this._requestFeatureFlagDefinitions()
              ];
            case 2:
              res = _c.sent();
              if (res && res.status === 401) {
                throw new ClientError("Your personalApiKey is invalid. Are you sure you're not using your Project API key? More information: https://posthog.com/docs/api/overview");
              }
              if (res && res.status !== 200) {
                return [
                  2
                  /*return*/
                ];
              }
              return [
                4,
                res.json()
              ];
            case 3:
              responseJson = _c.sent();
              if (!("flags" in responseJson)) {
                (_a = this.onError) === null || _a === void 0 ? void 0 : _a.call(this, new Error("Invalid response when getting feature flags: ".concat(JSON.stringify(responseJson))));
              }
              this.featureFlags = responseJson.flags || [];
              this.featureFlagsByKey = this.featureFlags.reduce(function(acc, curr) {
                return acc[curr.key] = curr, acc;
              }, {});
              this.groupTypeMapping = responseJson.group_type_mapping || {};
              this.cohorts = responseJson.cohorts || [];
              this.loadedSuccessfullyOnce = true;
              return [
                3,
                5
              ];
            case 4:
              err_1 = _c.sent();
              if (err_1 instanceof ClientError) {
                (_b = this.onError) === null || _b === void 0 ? void 0 : _b.call(this, err_1);
              }
              return [
                3,
                5
              ];
            case 5:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    FeatureFlagsPoller2.prototype._requestFeatureFlagDefinitions = function() {
      return __awaiter(this, void 0, void 0, function() {
        var url, options, abortTimeout, controller_1;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              url = "".concat(this.host, "/api/feature_flag/local_evaluation?token=").concat(this.projectApiKey, "&send_cohorts");
              options = {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: "Bearer ".concat(this.personalApiKey),
                  "user-agent": "posthog-node/".concat(version)
                }
              };
              abortTimeout = null;
              if (this.timeout && typeof this.timeout === "number") {
                controller_1 = new AbortController();
                abortTimeout = safeSetTimeout(function() {
                  controller_1.abort();
                }, this.timeout);
                options.signal = controller_1.signal;
              }
              _a.label = 1;
            case 1:
              _a.trys.push([1, , 3, 4]);
              return [
                4,
                this.fetch(url, options)
              ];
            case 2:
              return [
                2,
                _a.sent()
              ];
            case 3:
              clearTimeout(abortTimeout);
              return [
                7
                /*endfinally*/
              ];
            case 4:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    FeatureFlagsPoller2.prototype.stopPoller = function() {
      clearTimeout(this.poller);
    };
    return FeatureFlagsPoller2;
  }()
);
function _hash(key, distinctId, salt) {
  if (salt === void 0) {
    salt = "";
  }
  var sha1Hash = (0, import_rusha.createHash)();
  sha1Hash.update("".concat(key, ".").concat(distinctId).concat(salt));
  return parseInt(sha1Hash.digest("hex").slice(0, 15), 16) / LONG_SCALE;
}
function matchProperty(property, propertyValues) {
  var key = property.key;
  var value = property.value;
  var operator = property.operator || "exact";
  if (!(key in propertyValues)) {
    throw new InconclusiveMatchError("Property ".concat(key, " not found in propertyValues"));
  } else if (operator === "is_not_set") {
    throw new InconclusiveMatchError("Operator is_not_set is not supported");
  }
  var overrideValue = propertyValues[key];
  function computeExactMatch(value2, overrideValue2) {
    if (Array.isArray(value2)) {
      return value2.map(function(val) {
        return String(val).toLowerCase();
      }).includes(String(overrideValue2).toLowerCase());
    }
    return String(value2).toLowerCase() === String(overrideValue2).toLowerCase();
  }
  function compare(lhs, rhs, operator2) {
    if (operator2 === "gt") {
      return lhs > rhs;
    } else if (operator2 === "gte") {
      return lhs >= rhs;
    } else if (operator2 === "lt") {
      return lhs < rhs;
    } else if (operator2 === "lte") {
      return lhs <= rhs;
    } else {
      throw new Error("Invalid operator: ".concat(operator2));
    }
  }
  switch (operator) {
    case "exact":
      return computeExactMatch(value, overrideValue);
    case "is_not":
      return !computeExactMatch(value, overrideValue);
    case "is_set":
      return key in propertyValues;
    case "icontains":
      return String(overrideValue).toLowerCase().includes(String(value).toLowerCase());
    case "not_icontains":
      return !String(overrideValue).toLowerCase().includes(String(value).toLowerCase());
    case "regex":
      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) !== null;
    case "not_regex":
      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) === null;
    case "gt":
    case "gte":
    case "lt":
    case "lte": {
      var parsedValue = typeof value === "number" ? value : null;
      if (typeof value === "string") {
        try {
          parsedValue = parseFloat(value);
        } catch (err) {
        }
      }
      if (parsedValue != null && overrideValue != null) {
        if (typeof overrideValue === "string") {
          return compare(overrideValue, String(value), operator);
        } else {
          return compare(overrideValue, parsedValue, operator);
        }
      } else {
        return compare(String(overrideValue), String(value), operator);
      }
    }
    case "is_date_after":
    case "is_date_before": {
      var parsedDate = relativeDateParseForFeatureFlagMatching(String(value));
      if (parsedDate == null) {
        parsedDate = convertToDateTime(value);
      }
      if (parsedDate == null) {
        throw new InconclusiveMatchError("Invalid date: ".concat(value));
      }
      var overrideDate = convertToDateTime(overrideValue);
      if (["is_date_before"].includes(operator)) {
        return overrideDate < parsedDate;
      }
      return overrideDate > parsedDate;
    }
    default:
      throw new InconclusiveMatchError("Unknown operator: ".concat(operator));
  }
}
function matchCohort(property, propertyValues, cohortProperties) {
  var cohortId = String(property.value);
  if (!(cohortId in cohortProperties)) {
    throw new InconclusiveMatchError("can't match cohort without a given cohort property value");
  }
  var propertyGroup = cohortProperties[cohortId];
  return matchPropertyGroup(propertyGroup, propertyValues, cohortProperties);
}
function matchPropertyGroup(propertyGroup, propertyValues, cohortProperties) {
  if (!propertyGroup) {
    return true;
  }
  var propertyGroupType = propertyGroup.type;
  var properties = propertyGroup.values;
  if (!properties || properties.length === 0) {
    return true;
  }
  var errorMatchingLocally = false;
  if ("values" in properties[0]) {
    for (var _i = 0, _a = properties; _i < _a.length; _i++) {
      var prop = _a[_i];
      try {
        var matches = matchPropertyGroup(prop, propertyValues, cohortProperties);
        if (propertyGroupType === "AND") {
          if (!matches) {
            return false;
          }
        } else {
          if (matches) {
            return true;
          }
        }
      } catch (err) {
        if (err instanceof InconclusiveMatchError) {
          console.debug("Failed to compute property ".concat(prop, " locally: ").concat(err));
          errorMatchingLocally = true;
        } else {
          throw err;
        }
      }
    }
    if (errorMatchingLocally) {
      throw new InconclusiveMatchError("Can't match cohort without a given cohort property value");
    }
    return propertyGroupType === "AND";
  } else {
    for (var _b = 0, _c = properties; _b < _c.length; _b++) {
      var prop = _c[_b];
      try {
        var matches = void 0;
        if (prop.type === "cohort") {
          matches = matchCohort(prop, propertyValues, cohortProperties);
        } else {
          matches = matchProperty(prop, propertyValues);
        }
        var negation = prop.negation || false;
        if (propertyGroupType === "AND") {
          if (!matches && !negation) {
            return false;
          }
          if (matches && negation) {
            return false;
          }
        } else {
          if (matches && !negation) {
            return true;
          }
          if (!matches && negation) {
            return true;
          }
        }
      } catch (err) {
        if (err instanceof InconclusiveMatchError) {
          console.debug("Failed to compute property ".concat(prop, " locally: ").concat(err));
          errorMatchingLocally = true;
        } else {
          throw err;
        }
      }
    }
    if (errorMatchingLocally) {
      throw new InconclusiveMatchError("can't match cohort without a given cohort property value");
    }
    return propertyGroupType === "AND";
  }
}
function isValidRegex(regex) {
  try {
    new RegExp(regex);
    return true;
  } catch (err) {
    return false;
  }
}
function convertToDateTime(value) {
  if (value instanceof Date) {
    return value;
  } else if (typeof value === "string" || typeof value === "number") {
    var date = new Date(value);
    if (!isNaN(date.valueOf())) {
      return date;
    }
    throw new InconclusiveMatchError("".concat(value, " is in an invalid date format"));
  } else {
    throw new InconclusiveMatchError("The date provided ".concat(value, " must be a string, number, or date object"));
  }
}
function relativeDateParseForFeatureFlagMatching(value) {
  var regex = /^-?(?<number>[0-9]+)(?<interval>[a-z])$/;
  var match = value.match(regex);
  var parsedDt = new Date((/* @__PURE__ */ new Date()).toISOString());
  if (match) {
    if (!match.groups) {
      return null;
    }
    var number = parseInt(match.groups["number"]);
    if (number >= 1e4) {
      return null;
    }
    var interval = match.groups["interval"];
    if (interval == "h") {
      parsedDt.setUTCHours(parsedDt.getUTCHours() - number);
    } else if (interval == "d") {
      parsedDt.setUTCDate(parsedDt.getUTCDate() - number);
    } else if (interval == "w") {
      parsedDt.setUTCDate(parsedDt.getUTCDate() - number * 7);
    } else if (interval == "m") {
      parsedDt.setUTCMonth(parsedDt.getUTCMonth() - number);
    } else if (interval == "y") {
      parsedDt.setUTCFullYear(parsedDt.getUTCFullYear() - number);
    } else {
      return null;
    }
    return parsedDt;
  } else {
    return null;
  }
}
var THIRTY_SECONDS = 30 * 1e3;
var MAX_CACHE_SIZE = 50 * 1e3;
var PostHog = (
  /** @class */
  function(_super) {
    __extends(PostHog2, _super);
    function PostHog2(apiKey, options) {
      if (options === void 0) {
        options = {};
      }
      var _this = this;
      var _a;
      options.captureMode = (options === null || options === void 0 ? void 0 : options.captureMode) || "json";
      _this = _super.call(this, apiKey, options) || this;
      _this._memoryStorage = new PostHogMemoryStorage();
      _this.options = options;
      if (options.personalApiKey) {
        _this.featureFlagsPoller = new FeatureFlagsPoller({
          pollingInterval: typeof options.featureFlagsPollingInterval === "number" ? options.featureFlagsPollingInterval : THIRTY_SECONDS,
          personalApiKey: options.personalApiKey,
          projectApiKey: apiKey,
          timeout: (_a = options.requestTimeout) !== null && _a !== void 0 ? _a : 1e4,
          host: _this.host,
          fetch: options.fetch,
          onError: function(err) {
            _this._events.emit("error", err);
          }
        });
      }
      _this.distinctIdHasSentFlagCalls = {};
      _this.maxCacheSize = options.maxCacheSize || MAX_CACHE_SIZE;
      return _this;
    }
    PostHog2.prototype.getPersistedProperty = function(key) {
      return this._memoryStorage.getProperty(key);
    };
    PostHog2.prototype.setPersistedProperty = function(key, value) {
      return this._memoryStorage.setProperty(key, value);
    };
    PostHog2.prototype.fetch = function(url, options) {
      return this.options.fetch ? this.options.fetch(url, options) : fetch$1(url, options);
    };
    PostHog2.prototype.getLibraryId = function() {
      return "posthog-node";
    };
    PostHog2.prototype.getLibraryVersion = function() {
      return version;
    };
    PostHog2.prototype.getCustomUserAgent = function() {
      return "posthog-node/".concat(version);
    };
    PostHog2.prototype.enable = function() {
      return _super.prototype.optIn.call(this);
    };
    PostHog2.prototype.disable = function() {
      return _super.prototype.optOut.call(this);
    };
    PostHog2.prototype.debug = function(enabled) {
      var _a;
      if (enabled === void 0) {
        enabled = true;
      }
      _super.prototype.debug.call(this, enabled);
      (_a = this.featureFlagsPoller) === null || _a === void 0 ? void 0 : _a.debug(enabled);
    };
    PostHog2.prototype.capture = function(_a) {
      var _this = this;
      var distinctId = _a.distinctId, event = _a.event, properties = _a.properties, groups = _a.groups, sendFeatureFlags = _a.sendFeatureFlags, timestamp = _a.timestamp, disableGeoip = _a.disableGeoip, uuid = _a.uuid;
      var _capture = function(props) {
        _super.prototype.captureStateless.call(_this, distinctId, event, props, {
          timestamp,
          disableGeoip,
          uuid
        });
      };
      var capturePromise = Promise.resolve().then(function() {
        return __awaiter(_this, void 0, void 0, function() {
          var groupsWithStringValues, _i, _a2, _b, key, value;
          var _c, _d;
          return __generator(this, function(_e) {
            switch (_e.label) {
              case 0:
                if (!sendFeatureFlags) return [
                  3,
                  2
                ];
                return [
                  4,
                  _super.prototype.getFeatureFlagsStateless.call(this, distinctId, groups, void 0, void 0, disableGeoip)
                ];
              case 1:
                return [
                  2,
                  _e.sent()
                ];
              case 2:
                if (!((((_d = (_c = this.featureFlagsPoller) === null || _c === void 0 ? void 0 : _c.featureFlags) === null || _d === void 0 ? void 0 : _d.length) || 0) > 0)) return [
                  3,
                  4
                ];
                groupsWithStringValues = {};
                for (_i = 0, _a2 = Object.entries(groups || {}); _i < _a2.length; _i++) {
                  _b = _a2[_i], key = _b[0], value = _b[1];
                  groupsWithStringValues[key] = String(value);
                }
                return [
                  4,
                  this.getAllFlags(distinctId, {
                    groups: groupsWithStringValues,
                    disableGeoip,
                    onlyEvaluateLocally: true
                  })
                ];
              case 3:
                return [
                  2,
                  _e.sent()
                ];
              case 4:
                return [
                  2,
                  {}
                ];
            }
          });
        });
      }).then(function(flags) {
        var additionalProperties = {};
        if (flags) {
          for (var _i = 0, _a2 = Object.entries(flags); _i < _a2.length; _i++) {
            var _b = _a2[_i], feature = _b[0], variant = _b[1];
            additionalProperties["$feature/".concat(feature)] = variant;
          }
        }
        var activeFlags = Object.keys(flags || {}).filter(function(flag) {
          return (flags === null || flags === void 0 ? void 0 : flags[flag]) !== false;
        });
        if (activeFlags.length > 0) {
          additionalProperties["$active_feature_flags"] = activeFlags;
        }
        return additionalProperties;
      }).catch(function() {
        return {};
      }).then(function(additionalProperties) {
        _capture(__assign(__assign(__assign({}, additionalProperties), properties), {
          $groups: groups
        }));
      });
      this.addPendingPromise(capturePromise);
    };
    PostHog2.prototype.identify = function(_a) {
      var distinctId = _a.distinctId, properties = _a.properties, disableGeoip = _a.disableGeoip;
      var personProperties = (properties === null || properties === void 0 ? void 0 : properties.$set) || properties;
      _super.prototype.identifyStateless.call(this, distinctId, {
        $set: personProperties
      }, {
        disableGeoip
      });
    };
    PostHog2.prototype.alias = function(data) {
      _super.prototype.aliasStateless.call(this, data.alias, data.distinctId, void 0, {
        disableGeoip: data.disableGeoip
      });
    };
    PostHog2.prototype.getFeatureFlag = function(key, distinctId, options) {
      var _a;
      return __awaiter(this, void 0, void 0, function() {
        var _b, groups, disableGeoip, _c, onlyEvaluateLocally, sendFeatureFlagEvents, personProperties, groupProperties, adjustedProperties, response, flagWasLocallyEvaluated, featureFlagReportedKey;
        var _d;
        return __generator(this, function(_e) {
          switch (_e.label) {
            case 0:
              _b = options || {}, groups = _b.groups, disableGeoip = _b.disableGeoip;
              _c = options || {}, onlyEvaluateLocally = _c.onlyEvaluateLocally, sendFeatureFlagEvents = _c.sendFeatureFlagEvents, personProperties = _c.personProperties, groupProperties = _c.groupProperties;
              adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);
              personProperties = adjustedProperties.allPersonProperties;
              groupProperties = adjustedProperties.allGroupProperties;
              if (onlyEvaluateLocally == void 0) {
                onlyEvaluateLocally = false;
              }
              if (sendFeatureFlagEvents == void 0) {
                sendFeatureFlagEvents = true;
              }
              return [
                4,
                (_a = this.featureFlagsPoller) === null || _a === void 0 ? void 0 : _a.getFeatureFlag(key, distinctId, groups, personProperties, groupProperties)
              ];
            case 1:
              response = _e.sent();
              flagWasLocallyEvaluated = response !== void 0;
              if (!(!flagWasLocallyEvaluated && !onlyEvaluateLocally)) return [
                3,
                3
              ];
              return [
                4,
                _super.prototype.getFeatureFlagStateless.call(this, key, distinctId, groups, personProperties, groupProperties, disableGeoip)
              ];
            case 2:
              response = _e.sent();
              _e.label = 3;
            case 3:
              featureFlagReportedKey = "".concat(key, "_").concat(response);
              if (sendFeatureFlagEvents && (!(distinctId in this.distinctIdHasSentFlagCalls) || !this.distinctIdHasSentFlagCalls[distinctId].includes(featureFlagReportedKey))) {
                if (Object.keys(this.distinctIdHasSentFlagCalls).length >= this.maxCacheSize) {
                  this.distinctIdHasSentFlagCalls = {};
                }
                if (Array.isArray(this.distinctIdHasSentFlagCalls[distinctId])) {
                  this.distinctIdHasSentFlagCalls[distinctId].push(featureFlagReportedKey);
                } else {
                  this.distinctIdHasSentFlagCalls[distinctId] = [featureFlagReportedKey];
                }
                this.capture({
                  distinctId,
                  event: "$feature_flag_called",
                  properties: (_d = {
                    $feature_flag: key,
                    $feature_flag_response: response,
                    locally_evaluated: flagWasLocallyEvaluated
                  }, _d["$feature/".concat(key)] = response, _d),
                  groups,
                  disableGeoip
                });
              }
              return [
                2,
                response
              ];
          }
        });
      });
    };
    PostHog2.prototype.getFeatureFlagPayload = function(key, distinctId, matchValue, options) {
      var _a;
      return __awaiter(this, void 0, void 0, function() {
        var _b, groups, disableGeoip, _c, onlyEvaluateLocally, personProperties, groupProperties, adjustedProperties, response, payloadWasLocallyEvaluated;
        return __generator(this, function(_d) {
          switch (_d.label) {
            case 0:
              _b = options || {}, groups = _b.groups, disableGeoip = _b.disableGeoip;
              _c = options || {}, onlyEvaluateLocally = _c.onlyEvaluateLocally, _c.sendFeatureFlagEvents, personProperties = _c.personProperties, groupProperties = _c.groupProperties;
              adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);
              personProperties = adjustedProperties.allPersonProperties;
              groupProperties = adjustedProperties.allGroupProperties;
              response = void 0;
              if (!!matchValue) return [
                3,
                2
              ];
              return [
                4,
                this.getFeatureFlag(key, distinctId, __assign(__assign({}, options), {
                  onlyEvaluateLocally: true
                }))
              ];
            case 1:
              matchValue = _d.sent();
              _d.label = 2;
            case 2:
              if (!matchValue) return [
                3,
                4
              ];
              return [
                4,
                (_a = this.featureFlagsPoller) === null || _a === void 0 ? void 0 : _a.computeFeatureFlagPayloadLocally(key, matchValue)
              ];
            case 3:
              response = _d.sent();
              _d.label = 4;
            case 4:
              if (onlyEvaluateLocally == void 0) {
                onlyEvaluateLocally = false;
              }
              if (onlyEvaluateLocally == void 0) {
                onlyEvaluateLocally = false;
              }
              payloadWasLocallyEvaluated = response !== void 0;
              if (!(!payloadWasLocallyEvaluated && !onlyEvaluateLocally)) return [
                3,
                6
              ];
              return [
                4,
                _super.prototype.getFeatureFlagPayloadStateless.call(this, key, distinctId, groups, personProperties, groupProperties, disableGeoip)
              ];
            case 5:
              response = _d.sent();
              _d.label = 6;
            case 6:
              try {
                return [
                  2,
                  JSON.parse(response)
                ];
              } catch (_e) {
                return [
                  2,
                  response
                ];
              }
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    PostHog2.prototype.isFeatureEnabled = function(key, distinctId, options) {
      return __awaiter(this, void 0, void 0, function() {
        var feat;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              return [
                4,
                this.getFeatureFlag(key, distinctId, options)
              ];
            case 1:
              feat = _a.sent();
              if (feat === void 0) {
                return [
                  2,
                  void 0
                ];
              }
              return [
                2,
                !!feat || false
              ];
          }
        });
      });
    };
    PostHog2.prototype.getAllFlags = function(distinctId, options) {
      return __awaiter(this, void 0, void 0, function() {
        var response;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              return [
                4,
                this.getAllFlagsAndPayloads(distinctId, options)
              ];
            case 1:
              response = _a.sent();
              return [
                2,
                response.featureFlags
              ];
          }
        });
      });
    };
    PostHog2.prototype.getAllFlagsAndPayloads = function(distinctId, options) {
      var _a;
      return __awaiter(this, void 0, void 0, function() {
        var _b, groups, disableGeoip, _c, onlyEvaluateLocally, personProperties, groupProperties, adjustedProperties, localEvaluationResult, featureFlags, featureFlagPayloads, fallbackToDecide, remoteEvaluationResult;
        return __generator(this, function(_d) {
          switch (_d.label) {
            case 0:
              _b = options || {}, groups = _b.groups, disableGeoip = _b.disableGeoip;
              _c = options || {}, onlyEvaluateLocally = _c.onlyEvaluateLocally, personProperties = _c.personProperties, groupProperties = _c.groupProperties;
              adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);
              personProperties = adjustedProperties.allPersonProperties;
              groupProperties = adjustedProperties.allGroupProperties;
              if (onlyEvaluateLocally == void 0) {
                onlyEvaluateLocally = false;
              }
              return [
                4,
                (_a = this.featureFlagsPoller) === null || _a === void 0 ? void 0 : _a.getAllFlagsAndPayloads(distinctId, groups, personProperties, groupProperties)
              ];
            case 1:
              localEvaluationResult = _d.sent();
              featureFlags = {};
              featureFlagPayloads = {};
              fallbackToDecide = true;
              if (localEvaluationResult) {
                featureFlags = localEvaluationResult.response;
                featureFlagPayloads = localEvaluationResult.payloads;
                fallbackToDecide = localEvaluationResult.fallbackToDecide;
              }
              if (!(fallbackToDecide && !onlyEvaluateLocally)) return [
                3,
                3
              ];
              return [
                4,
                _super.prototype.getFeatureFlagsAndPayloadsStateless.call(this, distinctId, groups, personProperties, groupProperties, disableGeoip)
              ];
            case 2:
              remoteEvaluationResult = _d.sent();
              featureFlags = __assign(__assign({}, featureFlags), remoteEvaluationResult.flags || {});
              featureFlagPayloads = __assign(__assign({}, featureFlagPayloads), remoteEvaluationResult.payloads || {});
              _d.label = 3;
            case 3:
              return [
                2,
                {
                  featureFlags,
                  featureFlagPayloads
                }
              ];
          }
        });
      });
    };
    PostHog2.prototype.groupIdentify = function(_a) {
      var groupType = _a.groupType, groupKey = _a.groupKey, properties = _a.properties, distinctId = _a.distinctId, disableGeoip = _a.disableGeoip;
      _super.prototype.groupIdentifyStateless.call(this, groupType, groupKey, properties, {
        disableGeoip
      }, distinctId);
    };
    PostHog2.prototype.reloadFeatureFlags = function() {
      var _a;
      return __awaiter(this, void 0, void 0, function() {
        return __generator(this, function(_b) {
          switch (_b.label) {
            case 0:
              return [
                4,
                (_a = this.featureFlagsPoller) === null || _a === void 0 ? void 0 : _a.loadFeatureFlags(true)
              ];
            case 1:
              _b.sent();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    PostHog2.prototype.shutdown = function() {
      void this.shutdownAsync();
    };
    PostHog2.prototype.shutdownAsync = function() {
      var _a;
      return __awaiter(this, void 0, void 0, function() {
        return __generator(this, function(_b) {
          (_a = this.featureFlagsPoller) === null || _a === void 0 ? void 0 : _a.stopPoller();
          return [
            2,
            _super.prototype.shutdownAsync.call(this)
          ];
        });
      });
    };
    PostHog2.prototype.addLocalPersonAndGroupProperties = function(distinctId, groups, personProperties, groupProperties) {
      var allPersonProperties = __assign({
        distinct_id: distinctId
      }, personProperties || {});
      var allGroupProperties = {};
      if (groups) {
        for (var _i = 0, _a = Object.keys(groups); _i < _a.length; _i++) {
          var groupName = _a[_i];
          allGroupProperties[groupName] = __assign({
            $group_key: groups[groupName]
          }, (groupProperties === null || groupProperties === void 0 ? void 0 : groupProperties[groupName]) || {});
        }
      }
      return {
        allPersonProperties,
        allGroupProperties
      };
    };
    return PostHog2;
  }(PostHogCoreStateless)
);
var PostHogSentryIntegration = (
  /** @class */
  function() {
    function PostHogSentryIntegration2(posthog, posthogHost, organization, prefix) {
      var _a;
      this.posthog = posthog;
      this.posthogHost = posthogHost;
      this.organization = organization;
      this.prefix = prefix;
      this.name = "posthog-node";
      this.posthogHost = (_a = posthog.options.host) !== null && _a !== void 0 ? _a : "https://app.posthog.com";
    }
    PostHogSentryIntegration2.prototype.setupOnce = function(addGlobalEventProcessor, getCurrentHub) {
      var _this = this;
      addGlobalEventProcessor(function(event) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        if (((_a = event.exception) === null || _a === void 0 ? void 0 : _a.values) === void 0 || event.exception.values.length === 0) {
          return event;
        }
        if (!event.tags) {
          event.tags = {};
        }
        var sentry = getCurrentHub();
        var userId = event.tags[PostHogSentryIntegration2.POSTHOG_ID_TAG];
        if (userId === void 0) {
          return event;
        }
        event.tags["PostHog Person URL"] = new URL("/person/".concat(userId), _this.posthogHost).toString();
        var properties = {
          // PostHog Exception Properties
          $exception_message: (_b = event.exception.values[0]) === null || _b === void 0 ? void 0 : _b.value,
          $exception_type: (_c = event.exception.values[0]) === null || _c === void 0 ? void 0 : _c.type,
          $exception_personURL: event.tags["PostHog Person URL"],
          // Sentry Exception Properties
          $sentry_event_id: event.event_id,
          $sentry_exception: event.exception,
          $sentry_exception_message: (_d = event.exception.values[0]) === null || _d === void 0 ? void 0 : _d.value,
          $sentry_exception_type: (_e = event.exception.values[0]) === null || _e === void 0 ? void 0 : _e.type,
          $sentry_tags: event.tags
        };
        var projectId = (_g = (_f = sentry.getClient()) === null || _f === void 0 ? void 0 : _f.getDsn()) === null || _g === void 0 ? void 0 : _g.projectId;
        if (_this.organization !== void 0 && projectId !== void 0 && event.event_id !== void 0) {
          properties.$sentry_url = "".concat((_h = _this.prefix) !== null && _h !== void 0 ? _h : "https://sentry.io/organizations", "/").concat(_this.organization, "/issues/?project=").concat(projectId, "&query=").concat(event.event_id);
        }
        _this.posthog.capture({
          event: "$exception",
          distinctId: userId,
          properties
        });
        return event;
      });
    };
    PostHogSentryIntegration2.POSTHOG_ID_TAG = "posthog_distinct_id";
    return PostHogSentryIntegration2;
  }()
);
export {
  PostHog,
  PostHogSentryIntegration
};
/*! Bundled license information:

posthog-node/lib/index.esm.js:
  (**
   * uuidv7: An experimental implementation of the proposed UUID Version 7
   *
   * @license Apache-2.0
   * @copyright 2021-2023 LiosK
   * @packageDocumentation
   *)
*/
//# sourceMappingURL=posthog-node.js.map
