#!/usr/bin/env node

/**
 * Worker Health Check Script for ClassroomIO
 * Monitors background worker processes and job queues
 */

const { execSync } = require('child_process');

// Configuration
const config = {
  timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000,
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  workerPort: process.env.WORKER_PORT || 9464
};

// Health check results
const healthStatus = {
  status: 'unknown',
  timestamp: new Date().toISOString(),
  checks: {},
  uptime: process.uptime(),
  memory: process.memoryUsage(),
  version: process.env.npm_package_version || '1.0.0',
  worker: {
    type: 'background-worker',
    concurrency: process.env.WORKER_CONCURRENCY || 5
  }
};

/**
 * Check Redis connectivity for job queues
 */
async function checkRedisConnection() {
  try {
    // Simple Redis connectivity check
    // In production, this would use actual Redis client
    if (process.env.REDIS_URL) {
      return {
        status: 'healthy',
        url: process.env.REDIS_URL.replace(/\/\/.*@/, '//***:***@'), // Hide credentials
        message: 'Redis connection available'
      };
    } else {
      throw new Error('REDIS_URL not configured');
    }
  } catch (error) {
    throw new Error(`Redis connection failed: ${error.message}`);
  }
}

/**
 * Check job queue status
 */
async function checkJobQueues() {
  try {
    // Mock job queue check - in production this would check actual queue status
    const queues = [
      { name: 'email', pending: 0, active: 0, completed: 150, failed: 2 },
      { name: 'video-processing', pending: 1, active: 2, completed: 45, failed: 0 },
      { name: 'analytics', pending: 0, active: 1, completed: 200, failed: 1 },
      { name: 'notifications', pending: 3, active: 0, completed: 89, failed: 0 }
    ];
    
    const totalPending = queues.reduce((sum, q) => sum + q.pending, 0);
    const totalActive = queues.reduce((sum, q) => sum + q.active, 0);
    const totalFailed = queues.reduce((sum, q) => sum + q.failed, 0);
    
    const status = totalFailed > 10 ? 'warning' : 'healthy';
    
    return {
      status,
      queues,
      summary: {
        totalPending,
        totalActive,
        totalFailed,
        totalQueues: queues.length
      },
      warning: totalFailed > 10 ? `High failure rate: ${totalFailed} failed jobs` : null
    };
  } catch (error) {
    throw new Error(`Job queue check failed: ${error.message}`);
  }
}

/**
 * Check worker process status
 */
function checkWorkerProcess() {
  try {
    const pid = process.pid;
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      status: 'healthy',
      pid,
      uptime: Math.round(process.uptime()),
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        external: Math.round(memUsage.external / 1024 / 1024) + 'MB'
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      }
    };
  } catch (error) {
    throw new Error(`Worker process check failed: ${error.message}`);
  }
}

/**
 * Check external service dependencies
 */
async function checkExternalServices() {
  const services = [];
  
  // Check OpenAI API if configured
  if (process.env.OPENAI_API_KEY) {
    services.push({
      name: 'openai',
      status: 'healthy', // Mock status
      message: 'API key configured'
    });
  }
  
  // Check email service
  if (process.env.SMTP_HOST) {
    services.push({
      name: 'smtp',
      status: 'healthy', // Mock status
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT
    });
  }
  
  // Check file storage
  if (process.env.AWS_ACCESS_KEY_ID) {
    services.push({
      name: 'aws-s3',
      status: 'healthy', // Mock status
      region: process.env.AWS_REGION,
      bucket: process.env.AWS_S3_BUCKET
    });
  }
  
  return {
    status: 'healthy',
    services,
    count: services.length
  };
}

/**
 * Check disk space for temporary files
 */
async function checkDiskSpace() {
  try {
    const output = execSync('df -h /tmp', { encoding: 'utf8', timeout: 2000 });
    const lines = output.trim().split('\n');
    const diskInfo = lines[1].split(/\s+/);
    
    const usage = diskInfo[4];
    const usagePercent = parseInt(usage.replace('%', ''));
    
    return {
      status: usagePercent < 85 ? 'healthy' : 'warning',
      path: '/tmp',
      usage: usage,
      available: diskInfo[3],
      total: diskInfo[1],
      warning: usagePercent >= 85 ? 'Temporary disk space is low' : null
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Disk check failed: ${error.message}`
    };
  }
}

/**
 * Check environment configuration for worker
 */
function checkWorkerEnvironment() {
  const requiredEnvVars = [
    'NODE_ENV',
    'REDIS_URL',
    'DATABASE_URL'
  ];
  
  const optionalEnvVars = [
    'OPENAI_API_KEY',
    'SMTP_HOST',
    'AWS_ACCESS_KEY_ID',
    'WORKER_CONCURRENCY'
  ];
  
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  const configured = optionalEnvVars.filter(envVar => process.env[envVar]);
  
  return {
    status: missing.length === 0 ? 'healthy' : 'error',
    nodeEnv: process.env.NODE_ENV,
    missing: missing.length > 0 ? missing : undefined,
    required: requiredEnvVars.length,
    optional: configured.length,
    workerConcurrency: process.env.WORKER_CONCURRENCY || 'default'
  };
}

/**
 * Perform all worker health checks
 */
async function performWorkerHealthChecks() {
  const checks = {};
  
  try {
    // Redis connection check
    try {
      checks.redis = await checkRedisConnection();
    } catch (error) {
      checks.redis = { status: 'error', message: error.message };
    }
    
    // Job queue check
    try {
      checks.jobQueues = await checkJobQueues();
    } catch (error) {
      checks.jobQueues = { status: 'error', message: error.message };
    }
    
    // Worker process check
    try {
      checks.workerProcess = checkWorkerProcess();
    } catch (error) {
      checks.workerProcess = { status: 'error', message: error.message };
    }
    
    // External services check
    try {
      checks.externalServices = await checkExternalServices();
    } catch (error) {
      checks.externalServices = { status: 'error', message: error.message };
    }
    
    // System checks
    checks.disk = await checkDiskSpace();
    checks.environment = checkWorkerEnvironment();
    
    // Determine overall status
    const hasErrors = Object.values(checks).some(check => check.status === 'error');
    const hasWarnings = Object.values(checks).some(check => check.status === 'warning');
    
    healthStatus.status = hasErrors ? 'unhealthy' : hasWarnings ? 'degraded' : 'healthy';
    healthStatus.checks = checks;
    healthStatus.timestamp = new Date().toISOString();
    
    return healthStatus;
    
  } catch (error) {
    healthStatus.status = 'error';
    healthStatus.error = error.message;
    return healthStatus;
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    const result = await performWorkerHealthChecks();
    
    // Output result
    if (process.env.HEALTH_CHECK_FORMAT === 'json') {
      console.log(JSON.stringify(result, null, 2));
    } else {
      console.log(`Worker Health Status: ${result.status.toUpperCase()}`);
      console.log(`Timestamp: ${result.timestamp}`);
      console.log(`Uptime: ${Math.round(result.uptime)}s`);
      console.log(`Worker Type: ${result.worker.type}`);
      console.log(`Concurrency: ${result.worker.concurrency}`);
      
      Object.entries(result.checks).forEach(([name, check]) => {
        const status = check.status.toUpperCase();
        const message = check.message || check.warning || 'OK';
        console.log(`${name}: ${status} ${message ? '- ' + message : ''}`);
        
        // Show queue summary if available
        if (name === 'jobQueues' && check.summary) {
          console.log(`  Queues: ${check.summary.totalQueues}, Pending: ${check.summary.totalPending}, Active: ${check.summary.totalActive}, Failed: ${check.summary.totalFailed}`);
        }
      });
    }
    
    // Exit with appropriate code
    if (result.status === 'healthy') {
      process.exit(0);
    } else if (result.status === 'degraded') {
      process.exit(1); // Warning state
    } else {
      process.exit(2); // Error state
    }
    
  } catch (error) {
    console.error('Worker health check failed:', error.message);
    process.exit(3);
  }
}

// Handle signals
process.on('SIGTERM', () => {
  console.log('Worker health check interrupted');
  process.exit(130);
});

process.on('SIGINT', () => {
  console.log('Worker health check interrupted');
  process.exit(130);
});

// Run health check
if (require.main === module) {
  main();
}

module.exports = { performWorkerHealthChecks, checkRedisConnection, checkJobQueues };
