<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { VideoUpload, VideoContent } from '$lib/utils/types/batch';
  import { videoUploadService, videoContentService } from '$lib/utils/services/video';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { CloudUpload, Trash, CheckmarkFilled, Warning } from 'carbon-icons-svelte';

  export let lessonId: string | null = null;
  export let onVideoUploaded: ((video: VideoContent) => void) | null = null;
  export let className: string = '';
  export let maxFileSize: number = 500 * 1024 * 1024; // 500MB default
  export let acceptedFormats: string[] = ['mp4', 'webm', 'mov', 'avi'];

  const dispatch = createEventDispatcher<{
    uploadStarted: { uploadId: string };
    uploadProgress: { uploadId: string; progress: number };
    uploadCompleted: { uploadId: string; videoContent: VideoContent };
    uploadFailed: { uploadId: string; error: string };
  }>();

  let fileInput: HTMLInputElement;
  let dragActive = false;
  let uploads = writable<VideoUpload[]>([]);
  let error: string | null = null;

  $: userId = $globalStore.user?.id;

  function handleDragOver(e: DragEvent) {
    e.preventDefault();
    dragActive = true;
  }

  function handleDragLeave(e: DragEvent) {
    e.preventDefault();
    dragActive = false;
  }

  function handleDrop(e: DragEvent) {
    e.preventDefault();
    dragActive = false;
    
    const files = Array.from(e.dataTransfer?.files || []);
    handleFiles(files);
  }

  function handleFileSelect(e: Event) {
    const target = e.target as HTMLInputElement;
    const files = Array.from(target.files || []);
    handleFiles(files);
    
    // Reset input
    target.value = '';
  }

  function handleFiles(files: File[]) {
    error = null;
    
    for (const file of files) {
      if (!validateFile(file)) continue;
      startUpload(file);
    }
  }

  function validateFile(file: File): boolean {
    // Check file size
    if (file.size > maxFileSize) {
      error = `File size exceeds ${formatFileSize(maxFileSize)} limit`;
      return false;
    }

    // Check file format
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !acceptedFormats.includes(extension)) {
      error = `Unsupported format. Accepted formats: ${acceptedFormats.join(', ')}`;
      return false;
    }

    // Check if it's actually a video file
    if (!file.type.startsWith('video/')) {
      error = 'Please select a valid video file';
      return false;
    }

    return true;
  }

  async function startUpload(file: File) {
    if (!userId) {
      error = 'User not authenticated';
      return;
    }

    try {
      // Create upload record
      const upload = await videoUploadService.createUpload({
        original_filename: file.name,
        file_size: file.size,
        mime_type: file.type,
        upload_status: 'pending',
        upload_progress: 0,
        processing_progress: 0,
        uploaded_by: userId,
        metadata: {
          lesson_id: lessonId
        }
      });

      // Add to local uploads
      uploads.update(list => [...list, upload]);

      dispatch('uploadStarted', { uploadId: upload.id });

      // Start actual upload
      await performUpload(upload, file);

    } catch (err) {
      console.error('Error starting upload:', err);
      error = err.message || 'Failed to start upload';
    }
  }

  async function performUpload(upload: VideoUpload, file: File) {
    try {
      // Update status to uploading
      await videoUploadService.updateUploadProgress(upload.id, 0, 'uploading');
      
      uploads.update(list => 
        list.map(u => 
          u.id === upload.id 
            ? { ...u, upload_status: 'uploading', upload_progress: 0 }
            : u
        )
      );

      // Simulate upload progress (in real implementation, use FormData with XMLHttpRequest or fetch with progress)
      await simulateUploadProgress(upload.id, file);

    } catch (err) {
      console.error('Error during upload:', err);
      
      await videoUploadService.updateUploadProgress(upload.id, 0, 'failed');
      
      uploads.update(list => 
        list.map(u => 
          u.id === upload.id 
            ? { ...u, upload_status: 'failed', error_message: err.message }
            : u
        )
      );

      dispatch('uploadFailed', { uploadId: upload.id, error: err.message });
    }
  }

  async function simulateUploadProgress(uploadId: string, file: File) {
    let progress = 0;
    
    // Simulate upload
    while (progress < 100) {
      await new Promise(resolve => setTimeout(resolve, 500));
      progress += Math.random() * 15;
      progress = Math.min(100, progress);
      
      await videoUploadService.updateUploadProgress(uploadId, progress);
      
      uploads.update(list => 
        list.map(u => 
          u.id === uploadId 
            ? { ...u, upload_progress: progress }
            : u
        )
      );

      dispatch('uploadProgress', { uploadId, progress });
    }

    // Start processing
    await videoUploadService.updateUploadProgress(uploadId, 100, 'processing');
    
    uploads.update(list => 
      list.map(u => 
        u.id === uploadId 
          ? { ...u, upload_status: 'processing', processing_progress: 0 }
          : u
      )
    );

    // Simulate processing
    let processingProgress = 0;
    while (processingProgress < 100) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      processingProgress += Math.random() * 20;
      processingProgress = Math.min(100, processingProgress);
      
      await videoUploadService.updateProcessingProgress(uploadId, processingProgress);
      
      uploads.update(list => 
        list.map(u => 
          u.id === uploadId 
            ? { ...u, processing_progress: processingProgress }
            : u
        )
      );
    }

    // Create video content
    const videoContent = await videoContentService.createVideoContent({
      title: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
      description: '',
      video_url: `https://example.com/videos/${uploadId}.mp4`, // This would be the actual processed video URL
      thumbnail_url: `https://example.com/thumbnails/${uploadId}.jpg`,
      duration: 0, // Would be extracted during processing
      lesson_id: lessonId,
      quality_options: [
        { quality: 'auto', url: `https://example.com/videos/${uploadId}.mp4` },
        { quality: '720p', url: `https://example.com/videos/${uploadId}_720p.mp4` },
        { quality: '480p', url: `https://example.com/videos/${uploadId}_480p.mp4` }
      ],
      is_downloadable: true,
      watermark_settings: {
        enabled: false
      },
      security_settings: {
        drm_enabled: false,
        download_protection: false,
        screenshot_protection: false
      },
      metadata: {
        file_size: file.size,
        format: file.type,
        upload_status: 'ready'
      }
    });

    // Complete upload
    await videoUploadService.completeUpload(uploadId, videoContent.id);
    
    uploads.update(list => 
      list.map(u => 
        u.id === uploadId 
          ? { ...u, upload_status: 'completed', video_content_id: videoContent.id }
          : u
      )
    );

    dispatch('uploadCompleted', { uploadId, videoContent });
    
    if (onVideoUploaded) {
      onVideoUploaded(videoContent);
    }
  }

  async function removeUpload(uploadId: string) {
    uploads.update(list => list.filter(u => u.id !== uploadId));
  }

  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'completed':
        return CheckmarkFilled;
      case 'failed':
        return Warning;
      default:
        return CloudUpload;
    }
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'text-green-500';
      case 'failed':
        return 'text-red-500';
      case 'uploading':
      case 'processing':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  }
</script>

<div class="video-upload {className}">
  <Box>
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('upload.title', { default: 'Upload Video' })}
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        {$t('upload.description', { default: 'Upload video files to add to your lesson' })}
      </p>
    </div>

    <!-- Upload Area -->
    <div
      class="upload-area"
      class:drag-active={dragActive}
      on:dragover={handleDragOver}
      on:dragleave={handleDragLeave}
      on:drop={handleDrop}
      role="button"
      tabindex="0"
      on:click={() => fileInput.click()}
      on:keydown={(e) => e.key === 'Enter' && fileInput.click()}
    >
      <div class="upload-content">
        <CloudUpload size={48} class="text-gray-400 mb-4" />
        <p class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {$t('upload.drop_files', { default: 'Drop video files here' })}
        </p>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          {$t('upload.or_click', { default: 'or click to browse' })}
        </p>
        <PrimaryButton variant={VARIANTS.OUTLINED}>
          {$t('upload.select_files', { default: 'Select Files' })}
        </PrimaryButton>
      </div>
    </div>

    <input
      bind:this={fileInput}
      type="file"
      multiple
      accept={acceptedFormats.map(f => `.${f}`).join(',')}
      on:change={handleFileSelect}
      class="hidden"
    />

    <!-- Upload Info -->
    <div class="mt-4 text-xs text-gray-500 space-y-1">
      <p>
        {$t('upload.max_size', { default: 'Maximum file size' })}: {formatFileSize(maxFileSize)}
      </p>
      <p>
        {$t('upload.supported_formats', { default: 'Supported formats' })}: {acceptedFormats.join(', ').toUpperCase()}
      </p>
    </div>

    {#if error}
      <div class="mt-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-3">
        <p class="text-sm text-red-700 dark:text-red-300">{error}</p>
      </div>
    {/if}
  </Box>

  <!-- Upload Progress -->
  {#if $uploads.length > 0}
    <Box className="mt-4">
      <h4 class="font-semibold text-gray-900 dark:text-white mb-4">
        {$t('upload.progress', { default: 'Upload Progress' })}
      </h4>

      <div class="space-y-3">
        {#each $uploads as upload (upload.id)}
          {@const StatusIcon = getStatusIcon(upload.upload_status)}
          <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center space-x-3 flex-1">
              <div class="flex-shrink-0">
                <StatusIcon size={20} class={getStatusColor(upload.upload_status)} />
              </div>
              
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {upload.original_filename}
                </p>
                <div class="flex items-center space-x-4 mt-1">
                  <span class="text-xs text-gray-500 capitalize">
                    {$t(`upload.status.${upload.upload_status}`, { default: upload.upload_status })}
                  </span>
                  <span class="text-xs text-gray-500">
                    {formatFileSize(upload.file_size)}
                  </span>
                </div>
                
                {#if upload.upload_status === 'uploading'}
                  <div class="mt-2">
                    <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                      <span>Uploading: {Math.round(upload.upload_progress)}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style="width: {upload.upload_progress}%"
                      ></div>
                    </div>
                  </div>
                {:else if upload.upload_status === 'processing'}
                  <div class="mt-2">
                    <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                      <span>Processing: {Math.round(upload.processing_progress)}%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        class="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style="width: {upload.processing_progress}%"
                      ></div>
                    </div>
                  </div>
                {:else if upload.upload_status === 'failed' && upload.error_message}
                  <p class="text-xs text-red-500 mt-1">{upload.error_message}</p>
                {/if}
              </div>
            </div>
            
            <button
              on:click={() => removeUpload(upload.id)}
              class="p-2 text-gray-500 hover:text-red-500"
              title={$t('upload.remove', { default: 'Remove' })}
            >
              <Trash size={16} />
            </button>
          </div>
        {/each}
      </div>
    </Box>
  {/if}
</div>

<style>
  .upload-area {
    @apply border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer transition-colors;
  }

  .upload-area:hover,
  .upload-area.drag-active {
    @apply border-primary-500 bg-primary-50 dark:bg-primary-900;
  }

  .upload-content {
    @apply flex flex-col items-center;
  }

  .hidden {
    display: none;
  }
</style>
