export { matchers } from './matchers.js';

export const nodes = [
	() => import('./nodes/0'),
	() => import('./nodes/1'),
	() => import('./nodes/2'),
	() => import('./nodes/3'),
	() => import('./nodes/4'),
	() => import('./nodes/5'),
	() => import('./nodes/6'),
	() => import('./nodes/7'),
	() => import('./nodes/8'),
	() => import('./nodes/9'),
	() => import('./nodes/10'),
	() => import('./nodes/11'),
	() => import('./nodes/12'),
	() => import('./nodes/13'),
	() => import('./nodes/14'),
	() => import('./nodes/15'),
	() => import('./nodes/16'),
	() => import('./nodes/17'),
	() => import('./nodes/18'),
	() => import('./nodes/19'),
	() => import('./nodes/20'),
	() => import('./nodes/21'),
	() => import('./nodes/22'),
	() => import('./nodes/23'),
	() => import('./nodes/24'),
	() => import('./nodes/25'),
	() => import('./nodes/26'),
	() => import('./nodes/27'),
	() => import('./nodes/28'),
	() => import('./nodes/29'),
	() => import('./nodes/30'),
	() => import('./nodes/31'),
	() => import('./nodes/32'),
	() => import('./nodes/33'),
	() => import('./nodes/34'),
	() => import('./nodes/35'),
	() => import('./nodes/36'),
	() => import('./nodes/37'),
	() => import('./nodes/38'),
	() => import('./nodes/39'),
	() => import('./nodes/40'),
	() => import('./nodes/41'),
	() => import('./nodes/42'),
	() => import('./nodes/43'),
	() => import('./nodes/44'),
	() => import('./nodes/45'),
	() => import('./nodes/46'),
	() => import('./nodes/47'),
	() => import('./nodes/48'),
	() => import('./nodes/49'),
	() => import('./nodes/50'),
	() => import('./nodes/51'),
	() => import('./nodes/52'),
	() => import('./nodes/53'),
	() => import('./nodes/54'),
	() => import('./nodes/55'),
	() => import('./nodes/56'),
	() => import('./nodes/57'),
	() => import('./nodes/58'),
	() => import('./nodes/59'),
	() => import('./nodes/60'),
	() => import('./nodes/61'),
	() => import('./nodes/62'),
	() => import('./nodes/63'),
	() => import('./nodes/64'),
	() => import('./nodes/65')
];

export const server_loads = [0,4,5,7];

export const dictionary = {
		"/": [8],
		"/404": [9],
		"/batches": [10],
		"/batches/[id]": [11],
		"/batches/[id]/analytics": [12],
		"/batches/[id]/communication": [13],
		"/batches/[id]/live-sessions": [14],
		"/batches/[id]/live-sessions/[sessionId]": [15],
		"/batches/[id]/subjects/[subjectId]/chapters/[chapterId]/lessons/[lessonId]": [16],
		"/courses/[id]": [18,[2]],
		"/courses/[id]/attendance": [19,[2]],
		"/courses/[id]/certificates": [20,[2]],
		"/courses/[id]/landingpage": [21,[2]],
		"/courses/[id]/lessons": [22,[2]],
		"/courses/[id]/lessons/[...lessonParams]": [~23,[2]],
		"/courses/[id]/marks": [24,[2]],
		"/courses/[id]/people": [25,[2,3]],
		"/courses/[id]/people/[personId]": [26,[2,3]],
		"/courses/[id]/settings": [27,[2]],
		"/courses/[id]/submissions": [28,[2]],
		"/course/[slug]": [17],
		"/forgot": [29],
		"/home": [30],
		"/invite/s/[hash]": [31,[4]],
		"/invite/t/[hash]": [32,[5]],
		"/lms": [33,[6]],
		"/lms/community": [34,[6]],
		"/lms/community/ask": [35,[6]],
		"/lms/community/[slug]": [36,[6]],
		"/lms/exercises": [37,[6]],
		"/lms/explore": [38,[6]],
		"/lms/mylearning": [39,[6]],
		"/lms/settings": [40,[6]],
		"/login": [41],
		"/logout": [42],
		"/onboarding": [43],
		"/org/[slug]": [46,[7]],
		"/org/[orgId]/analytics": [44],
		"/org/[slug]/audience": [47,[7]],
		"/org/[slug]/audience/[...params]": [48,[7]],
		"/org/[slug]/community": [49,[7]],
		"/org/[slug]/community/ask": [50,[7]],
		"/org/[slug]/community/[slug]": [51,[7]],
		"/org/[slug]/courses": [52,[7]],
		"/org/[slug]/quiz": [53,[7]],
		"/org/[slug]/quiz/[slug]": [54,[7]],
		"/org/[slug]/settings": [55,[7]],
		"/org/[orgId]/settings/communication": [45],
		"/org/[slug]/settings/customize-lms": [56,[7]],
		"/org/[slug]/settings/domains": [57,[7]],
		"/org/[slug]/settings/teams": [58,[7]],
		"/org/[slug]/setup": [59,[7]],
		"/profile/[id]": [60],
		"/reset": [61],
		"/security": [62],
		"/security/policies": [63],
		"/signup": [64],
		"/upgrade": [65]
	};

export const hooks = {
	handleError: (({ error }) => { console.error(error) }),
};

export { default as root } from '../root.svelte';