export { matchers } from './matchers.js';

export const nodes = [
	() => import('./nodes/0'),
	() => import('./nodes/1'),
	() => import('./nodes/2'),
	() => import('./nodes/3'),
	() => import('./nodes/4'),
	() => import('./nodes/5'),
	() => import('./nodes/6'),
	() => import('./nodes/7'),
	() => import('./nodes/8'),
	() => import('./nodes/9'),
	() => import('./nodes/10'),
	() => import('./nodes/11'),
	() => import('./nodes/12'),
	() => import('./nodes/13'),
	() => import('./nodes/14'),
	() => import('./nodes/15'),
	() => import('./nodes/16'),
	() => import('./nodes/17'),
	() => import('./nodes/18'),
	() => import('./nodes/19'),
	() => import('./nodes/20'),
	() => import('./nodes/21'),
	() => import('./nodes/22'),
	() => import('./nodes/23'),
	() => import('./nodes/24'),
	() => import('./nodes/25'),
	() => import('./nodes/26'),
	() => import('./nodes/27'),
	() => import('./nodes/28'),
	() => import('./nodes/29'),
	() => import('./nodes/30'),
	() => import('./nodes/31'),
	() => import('./nodes/32'),
	() => import('./nodes/33'),
	() => import('./nodes/34'),
	() => import('./nodes/35'),
	() => import('./nodes/36'),
	() => import('./nodes/37'),
	() => import('./nodes/38'),
	() => import('./nodes/39'),
	() => import('./nodes/40'),
	() => import('./nodes/41'),
	() => import('./nodes/42'),
	() => import('./nodes/43'),
	() => import('./nodes/44'),
	() => import('./nodes/45'),
	() => import('./nodes/46'),
	() => import('./nodes/47'),
	() => import('./nodes/48'),
	() => import('./nodes/49'),
	() => import('./nodes/50'),
	() => import('./nodes/51'),
	() => import('./nodes/52'),
	() => import('./nodes/53'),
	() => import('./nodes/54'),
	() => import('./nodes/55'),
	() => import('./nodes/56'),
	() => import('./nodes/57'),
	() => import('./nodes/58'),
	() => import('./nodes/59'),
	() => import('./nodes/60'),
	() => import('./nodes/61'),
	() => import('./nodes/62'),
	() => import('./nodes/63'),
	() => import('./nodes/64'),
	() => import('./nodes/65'),
	() => import('./nodes/66'),
	() => import('./nodes/67'),
	() => import('./nodes/68'),
	() => import('./nodes/69')
];

export const server_loads = [0,4,5,8];

export const dictionary = {
		"/": [9],
		"/404": [10],
		"/batches": [11],
		"/batches/[id]": [12],
		"/batches/[id]/analytics": [13],
		"/batches/[id]/communication": [14],
		"/batches/[id]/live-sessions": [15],
		"/batches/[id]/live-sessions/[sessionId]": [16],
		"/batches/[id]/subjects/[subjectId]/chapters/[chapterId]/lessons/[lessonId]": [17],
		"/courses/[id]": [19,[2]],
		"/courses/[id]/attendance": [20,[2]],
		"/courses/[id]/certificates": [21,[2]],
		"/courses/[id]/landingpage": [22,[2]],
		"/courses/[id]/lessons": [23,[2]],
		"/courses/[id]/lessons/[...lessonParams]": [~24,[2]],
		"/courses/[id]/marks": [25,[2]],
		"/courses/[id]/people": [26,[2,3]],
		"/courses/[id]/people/[personId]": [27,[2,3]],
		"/courses/[id]/settings": [28,[2]],
		"/courses/[id]/submissions": [29,[2]],
		"/course/[slug]": [18],
		"/forgot": [30],
		"/home": [31],
		"/invite/s/[hash]": [32,[4]],
		"/invite/t/[hash]": [33,[5]],
		"/lms": [34,[6]],
		"/lms/community": [35,[6]],
		"/lms/community/ask": [36,[6]],
		"/lms/community/[slug]": [37,[6]],
		"/lms/exercises": [38,[6]],
		"/lms/explore": [39,[6]],
		"/lms/mylearning": [40,[6]],
		"/lms/settings": [41,[6]],
		"/login": [42],
		"/logout": [43],
		"/minimal": [44,[7]],
		"/onboarding": [45],
		"/org/[slug]": [48,[8]],
		"/org/[orgId]/analytics": [46],
		"/org/[slug]/audience": [49,[8]],
		"/org/[slug]/audience/[...params]": [50,[8]],
		"/org/[slug]/community": [51,[8]],
		"/org/[slug]/community/ask": [52,[8]],
		"/org/[slug]/community/[slug]": [53,[8]],
		"/org/[slug]/courses": [54,[8]],
		"/org/[slug]/quiz": [55,[8]],
		"/org/[slug]/quiz/[slug]": [56,[8]],
		"/org/[slug]/settings": [57,[8]],
		"/org/[orgId]/settings/communication": [47],
		"/org/[slug]/settings/customize-lms": [58,[8]],
		"/org/[slug]/settings/domains": [59,[8]],
		"/org/[slug]/settings/teams": [60,[8]],
		"/org/[slug]/setup": [61,[8]],
		"/profile/[id]": [62],
		"/reset": [63],
		"/security": [64],
		"/security/policies": [65],
		"/signup": [66],
		"/simple": [67],
		"/test": [68],
		"/upgrade": [69]
	};

export const hooks = {
	handleError: (({ error }) => { console.error(error) }),
};

export { default as root } from '../root.svelte';