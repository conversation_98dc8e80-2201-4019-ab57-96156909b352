<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { page } from '$app/stores';
  import type { VideoContent, VideoProgress } from '$lib/utils/types/batch';
  import { videoContentService } from '$lib/utils/services/video';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import AdvancedVideoPlayer from '$lib/components/VideoPlayer/AdvancedVideoPlayer.svelte';
  import DownloadManager from '$lib/components/VideoPlayer/DownloadManager.svelte';
  import VideoUpload from '$lib/components/VideoPlayer/VideoUpload.svelte';
  import Box from '$lib/components/Box/index.svelte';
  import { Tabs, Tab, TabContent } from 'carbon-components-svelte';
  import { Play, Download, Upload, Notes, Assignment } from 'carbon-icons-svelte';

  export let lessonId: string;
  export let lesson: any = null;
  export let canEdit: boolean = false;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    videoCompleted: { videoId: string; lessonId: string };
    progressUpdated: { videoId: string; progress: number };
    videoAdded: { videoContent: VideoContent };
  }>();

  let videos: (VideoContent & { progress?: VideoProgress })[] = [];
  let currentVideoIndex = 0;
  let selectedTab = 0;
  let loading = true;
  let error: string | null = null;

  const tabs = [
    { label: $t('lesson.tabs.videos', { default: 'Videos' }), icon: Play },
    { label: $t('lesson.tabs.downloads', { default: 'Downloads' }), icon: Download },
    { label: $t('lesson.tabs.materials', { default: 'Materials' }), icon: Notes },
    ...(canEdit ? [{ label: $t('lesson.tabs.upload', { default: 'Upload' }), icon: Upload }] : [])
  ];

  $: currentVideo = videos[currentVideoIndex];
  $: studentId = $globalStore.user?.id;
  $: isStudent = $globalStore.role === 'student';
  $: isInstructor = $globalStore.isOrgTeacher || $globalStore.isOrgAdmin;

  onMount(async () => {
    await loadLessonVideos();
  });

  async function loadLessonVideos() {
    try {
      loading = true;
      error = null;

      const lessonVideos = await videoContentService.getLessonVideos(lessonId, studentId);
      videos = lessonVideos;

      // If no videos and user can edit, show upload tab
      if (videos.length === 0 && canEdit) {
        selectedTab = tabs.findIndex(tab => tab.label.includes('Upload'));
      }

    } catch (err) {
      console.error('Error loading lesson videos:', err);
      error = err.message || 'Failed to load lesson videos';
    } finally {
      loading = false;
    }
  }

  function handleVideoProgress(event: CustomEvent) {
    const { currentTime, duration, percentage } = event.detail;
    dispatch('progressUpdated', { 
      videoId: currentVideo.id, 
      progress: percentage 
    });
  }

  function handleVideoCompleted(event: CustomEvent) {
    const { videoId } = event.detail;
    dispatch('videoCompleted', { videoId, lessonId });
    
    // Auto-advance to next video if available
    if (currentVideoIndex < videos.length - 1) {
      setTimeout(() => {
        currentVideoIndex++;
      }, 2000); // 2 second delay
    }
  }

  function handleVideoError(event: CustomEvent) {
    const { message } = event.detail;
    error = message;
  }

  function handleVideoUploaded(videoContent: VideoContent) {
    videos = [...videos, { ...videoContent, progress: undefined }];
    dispatch('videoAdded', { videoContent });
    
    // Switch to videos tab
    selectedTab = 0;
  }

  function selectVideo(index: number) {
    currentVideoIndex = index;
  }

  function formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  function getVideoProgress(video: VideoContent & { progress?: VideoProgress }): number {
    return video.progress?.completion_percentage || 0;
  }

  function isVideoCompleted(video: VideoContent & { progress?: VideoProgress }): boolean {
    return video.progress?.is_completed || false;
  }
</script>

<div class="enhanced-lesson-player {className}">
  {#if loading}
    <Box className="w-full">
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-400">
          {$t('lesson.loading', { default: 'Loading lesson content...' })}
        </span>
      </div>
    </Box>

  {:else if error}
    <Box className="w-full">
      <div class="text-center py-12">
        <div class="text-red-500 mb-4">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {$t('lesson.error.title', { default: 'Error Loading Lesson' })}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <button 
          on:click={loadLessonVideos}
          class="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
        >
          {$t('lesson.retry', { default: 'Try Again' })}
        </button>
      </div>
    </Box>

  {:else}
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- Main Video Player -->
      <div class="lg:col-span-3">
        {#if currentVideo}
          <Box className="mb-4">
            <div class="mb-4">
              <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {currentVideo.title}
              </h2>
              {#if currentVideo.description}
                <p class="text-gray-600 dark:text-gray-400">
                  {currentVideo.description}
                </p>
              {/if}
            </div>

            <AdvancedVideoPlayer
              videoContent={currentVideo}
              {lessonId}
              settings={{
                autoplay: false,
                controls: true,
                keyboard_shortcuts: true,
                quality_selector: true,
                theme: 'dark'
              }}
              on:progress={handleVideoProgress}
              on:completed={handleVideoCompleted}
              on:error={handleVideoError}
            />

            <!-- Video Info -->
            <div class="mt-4 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
              <div class="flex items-center space-x-4">
                {#if currentVideo.duration}
                  <span>Duration: {formatDuration(currentVideo.duration)}</span>
                {/if}
                {#if currentVideo.progress}
                  <span>Progress: {Math.round(getVideoProgress(currentVideo))}%</span>
                {/if}
              </div>
              
              {#if isVideoCompleted(currentVideo)}
                <div class="flex items-center text-green-600">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  {$t('lesson.completed', { default: 'Completed' })}
                </div>
              {/if}
            </div>
          </Box>

          <!-- Download Manager for Students -->
          {#if isStudent && currentVideo.is_downloadable}
            <DownloadManager 
              videoContent={currentVideo}
              className="mb-4"
            />
          {/if}
        {:else}
          <Box className="text-center py-12">
            <Play size={48} class="mx-auto text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {$t('lesson.no_videos', { default: 'No Videos Available' })}
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
              {canEdit 
                ? $t('lesson.no_videos_instructor', { default: 'Upload videos to get started' })
                : $t('lesson.no_videos_student', { default: 'Videos will appear here when available' })
              }
            </p>
          </Box>
        {/if}
      </div>

      <!-- Sidebar -->
      <div class="lg:col-span-1">
        <Tabs bind:selected={selectedTab} type="container">
          {#each tabs as tab, index}
            <Tab>
              <div class="flex items-center space-x-2">
                <svelte:component this={tab.icon} size={16} />
                <span class="hidden sm:inline">{tab.label}</span>
              </div>
            </Tab>
          {/each}
          
          <svelte:fragment slot="content">
            <!-- Videos Tab -->
            <TabContent>
              {#if selectedTab === 0}
                <div class="space-y-2">
                  <h4 class="font-semibold text-gray-900 dark:text-white mb-3">
                    {$t('lesson.video_list', { default: 'Video List' })}
                  </h4>
                  
                  {#if videos.length === 0}
                    <p class="text-sm text-gray-600 dark:text-gray-400 text-center py-4">
                      {$t('lesson.no_videos_sidebar', { default: 'No videos in this lesson' })}
                    </p>
                  {:else}
                    {#each videos as video, index (video.id)}
                      <button
                        on:click={() => selectVideo(index)}
                        class="w-full text-left p-3 rounded-lg border transition-colors"
                        class:border-primary-500={index === currentVideoIndex}
                        class:bg-primary-50={index === currentVideoIndex}
                        class:dark:bg-primary-900={index === currentVideoIndex}
                        class:border-gray-200={index !== currentVideoIndex}
                        class:dark:border-gray-700={index !== currentVideoIndex}
                        class:hover:bg-gray-50={index !== currentVideoIndex}
                        class:dark:hover:bg-gray-800={index !== currentVideoIndex}
                      >
                        <div class="flex items-start space-x-3">
                          <div class="flex-shrink-0 mt-1">
                            {#if isVideoCompleted(video)}
                              <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                              </div>
                            {:else}
                              <div class="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                                  {index + 1}
                                </span>
                              </div>
                            {/if}
                          </div>
                          
                          <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {video.title}
                            </p>
                            <div class="flex items-center justify-between mt-1">
                              {#if video.duration}
                                <span class="text-xs text-gray-500">
                                  {formatDuration(video.duration)}
                                </span>
                              {/if}
                              {#if video.progress}
                                <span class="text-xs text-gray-500">
                                  {Math.round(getVideoProgress(video))}%
                                </span>
                              {/if}
                            </div>
                            
                            {#if video.progress && getVideoProgress(video) > 0}
                              <div class="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                                <div 
                                  class="bg-primary-500 h-1 rounded-full"
                                  style="width: {getVideoProgress(video)}%"
                                ></div>
                              </div>
                            {/if}
                          </div>
                        </div>
                      </button>
                    {/each}
                  {/if}
                </div>
              {/if}
            </TabContent>

            <!-- Downloads Tab -->
            <TabContent>
              {#if selectedTab === 1}
                <DownloadManager 
                  videoContent={currentVideo}
                  showAddButton={false}
                />
              {/if}
            </TabContent>

            <!-- Materials Tab -->
            <TabContent>
              {#if selectedTab === 2}
                <div class="text-center py-8">
                  <Notes size={32} class="mx-auto text-gray-400 mb-2" />
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    {$t('lesson.materials_coming_soon', { default: 'Study materials coming soon' })}
                  </p>
                </div>
              {/if}
            </TabContent>

            <!-- Upload Tab (Instructors only) -->
            {#if canEdit}
              <TabContent>
                {#if selectedTab === tabs.length - 1}
                  <VideoUpload 
                    {lessonId}
                    onVideoUploaded={handleVideoUploaded}
                    maxFileSize={1024 * 1024 * 1024}
                    acceptedFormats={['mp4', 'webm', 'mov']}
                  />
                {/if}
              </TabContent>
            {/if}
          </svelte:fragment>
        </Tabs>
      </div>
    </div>
  {/if}
</div>

<style>
  .enhanced-lesson-player {
    @apply w-full;
  }
</style>
