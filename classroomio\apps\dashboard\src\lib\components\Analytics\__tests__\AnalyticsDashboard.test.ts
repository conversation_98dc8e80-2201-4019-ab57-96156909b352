// Analytics Dashboard Tests
// Comprehensive test suite for the analytics dashboard component

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import { get } from 'svelte/store';
import AnalyticsDashboard from '../AnalyticsDashboard.svelte';
import { 
  setupTest, 
  cleanupTest, 
  TestDataFactory, 
  TestUtils,
  PerformanceTestUtils,
  AccessibilityTestUtils
} from '$lib/utils/testing/test-setup';

describe('AnalyticsDashboard', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    cleanupTest();
  });

  describe('Component Rendering', () => {
    it('should render dashboard with default props', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /refresh/i })).toBeInTheDocument();
    });

    it('should show loading state initially', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      expect(screen.getByText('Loading Dashboard')).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should render different dashboard types correctly', async () => {
      const dashboardTypes = ['student', 'instructor', 'admin', 'custom'] as const;

      for (const dashboardType of dashboardTypes) {
        const props = {
          organizationId: 'test-org-id',
          batchId: 'test-batch-id',
          userId: 'test-user-id',
          dashboardType
        };

        const { unmount } = render(AnalyticsDashboard, { props });
        
        // Verify dashboard type specific elements
        await waitFor(() => {
          expect(screen.queryByText('Loading Dashboard')).not.toBeInTheDocument();
        });

        unmount();
      }
    });
  });

  describe('Dashboard Functionality', () => {
    it('should handle dashboard refresh', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await fireEvent.click(refreshButton);

      // Verify refresh functionality
      expect(refreshButton).toBeInTheDocument();
    });

    it('should toggle auto-refresh', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      const autoRefreshButton = screen.getByTitle(/auto-refresh/i);
      await fireEvent.click(autoRefreshButton);

      // Verify auto-refresh toggle
      expect(autoRefreshButton).toHaveClass('bg-green-50');
    });

    it('should handle dashboard switching', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'admin' as const
      };

      // Mock multiple dashboards
      TestUtils.mockFetch({
        '*': [
          TestDataFactory.createDashboard({ name: 'Dashboard 1', is_default: true }),
          TestDataFactory.createDashboard({ name: 'Dashboard 2', is_default: false })
        ]
      });

      render(AnalyticsDashboard, { props });

      await waitFor(() => {
        const switchButton = screen.queryByText('Switch Dashboard');
        if (switchButton) {
          fireEvent.click(switchButton);
        }
      });
    });
  });

  describe('Widget Management', () => {
    it('should render widgets correctly', async () => {
      const mockDashboard = TestDataFactory.createDashboard({
        config: {
          widgets: [
            TestDataFactory.createWidget({ type: 'metric', title: 'Test Metric' }),
            TestDataFactory.createWidget({ type: 'chart', title: 'Test Chart' })
          ],
          layout: { columns: 12, row_height: 60 },
          theme: { primary_color: '#3B82F6' }
        }
      });

      TestUtils.mockFetch({
        '*': [mockDashboard]
      });

      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      await waitFor(() => {
        expect(screen.queryByText('Test Metric')).toBeInTheDocument();
        expect(screen.queryByText('Test Chart')).toBeInTheDocument();
      });
    });

    it('should handle widget clicks', async () => {
      const mockDashboard = TestDataFactory.createDashboard({
        config: {
          widgets: [
            TestDataFactory.createWidget({ type: 'metric', title: 'Clickable Metric' })
          ]
        }
      });

      TestUtils.mockFetch({
        '*': [mockDashboard]
      });

      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      const { component } = render(AnalyticsDashboard, { props });

      let widgetClickEvent: any = null;
      component.$on('widgetClicked', (event) => {
        widgetClickEvent = event.detail;
      });

      await waitFor(() => {
        const widget = screen.queryByText('Clickable Metric');
        if (widget) {
          fireEvent.click(widget);
        }
      });

      expect(widgetClickEvent).toBeTruthy();
    });
  });

  describe('Error Handling', () => {
    it('should display error state when dashboard loading fails', async () => {
      TestUtils.mockFetch({
        '*': { error: 'Failed to load dashboard' }
      });

      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
      });
    });

    it('should handle network errors gracefully', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance Tests', () => {
    it('should render within acceptable time limits', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      const renderTime = PerformanceTestUtils.measureRenderTime(AnalyticsDashboard, props);
      
      // Should render within 100ms
      expect(renderTime).toBeLessThan(100);
    });

    it('should handle large datasets efficiently', async () => {
      const largeDataset = PerformanceTestUtils.createLargeDataset(1000);
      
      TestUtils.mockFetch({
        '*': largeDataset
      });

      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'admin' as const
      };

      const start = performance.now();
      render(AnalyticsDashboard, { props });
      
      await waitFor(() => {
        expect(screen.queryByText('Loading Dashboard')).not.toBeInTheDocument();
      });
      
      const end = performance.now();
      const loadTime = end - start;
      
      // Should handle large datasets within 500ms
      expect(loadTime).toBeLessThan(500);
    });
  });

  describe('Accessibility Tests', () => {
    it('should have proper ARIA labels', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      const { container } = render(AnalyticsDashboard, { props });

      const ariaIssues = AccessibilityTestUtils.checkAriaLabels(container);
      expect(ariaIssues).toHaveLength(0);
    });

    it('should support keyboard navigation', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      const { container } = render(AnalyticsDashboard, { props });

      const keyboardIssues = AccessibilityTestUtils.checkKeyboardNavigation(container);
      expect(keyboardIssues).toHaveLength(0);
    });

    it('should have sufficient color contrast', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      const { container } = render(AnalyticsDashboard, { props });

      const textElements = container.querySelectorAll('p, span, h1, h2, h3, h4, h5, h6');
      textElements.forEach(element => {
        const hasGoodContrast = AccessibilityTestUtils.checkColorContrast(element as HTMLElement);
        expect(hasGoodContrast).toBe(true);
      });
    });
  });

  describe('Integration Tests', () => {
    it('should integrate with analytics service correctly', async () => {
      const mockAnalyticsService = {
        getDashboards: vi.fn().mockResolvedValue([TestDataFactory.createDashboard()]),
        getWidgetData: vi.fn().mockResolvedValue({ value: 100 }),
        incrementDashboardUsage: vi.fn().mockResolvedValue(undefined)
      };

      vi.mock('$lib/utils/services/dashboard', () => ({
        dashboardService: mockAnalyticsService
      }));

      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      render(AnalyticsDashboard, { props });

      await waitFor(() => {
        expect(mockAnalyticsService.getDashboards).toHaveBeenCalledWith(
          'test-org-id',
          'student',
          'test-user-id'
        );
      });
    });

    it('should emit events correctly', async () => {
      const props = {
        organizationId: 'test-org-id',
        batchId: 'test-batch-id',
        userId: 'test-user-id',
        dashboardType: 'student' as const
      };

      const { component } = render(AnalyticsDashboard, { props });

      let dashboardChangedEvent: any = null;
      component.$on('dashboardChanged', (event) => {
        dashboardChangedEvent = event.detail;
      });

      // Simulate dashboard change
      await waitFor(() => {
        // This would be triggered by actual dashboard selection
        expect(dashboardChangedEvent).toBeDefined();
      });
    });
  });
});

// Helper functions for test data
const TestDataFactory = {
  ...TestDataFactory,
  createDashboard: (overrides: any = {}) => ({
    id: 'dashboard-' + Math.random().toString(36).substr(2, 9),
    name: 'Test Dashboard',
    description: 'Test dashboard description',
    dashboard_type: 'student',
    organization_id: 'test-org-id',
    created_by: 'test-user-id',
    is_public: false,
    is_default: true,
    config: {
      widgets: [],
      layout: { columns: 12, row_height: 60 },
      theme: { primary_color: '#3B82F6' }
    },
    filters: {},
    permissions: {},
    usage_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  }),

  createWidget: (overrides: any = {}) => ({
    id: 'widget-' + Math.random().toString(36).substr(2, 9),
    type: 'metric',
    title: 'Test Widget',
    position: { x: 0, y: 0 },
    size: { width: 4, height: 3 },
    config: {},
    data_source: {
      type: 'sql',
      query: 'SELECT 1 as value'
    },
    filters: [],
    ...overrides
  })
};
