# ClassroomIO Docker Development Setup

This guide provides instructions for running ClassroomIO using Docker, which is the most reliable way to get the development environment up and running quickly.

## 🚀 Quick Start with Docker

### **Prerequisites**

1. **Install Docker Desktop**
   - Download from: https://www.docker.com/products/docker-desktop
   - Install and start Docker Desktop
   - Wait for Dock<PERSON> to fully start (check system tray icon)

### **Automated Startup**

1. **Open Command Prompt or PowerShell**
2. **Navigate to ClassroomIO directory**:
   ```cmd
   cd path\to\classroomio
   ```
3. **Run the Docker startup script**:
   ```cmd
   start-docker.bat
   ```

The script will automatically:
- ✅ Check Docker installation and status
- ✅ Download required Docker images
- ✅ Start PostgreSQL database
- ✅ Start Redis cache
- ✅ Start Supabase services
- ✅ Build and start ClassroomIO application
- ✅ Open browser to http://localhost:5173

### **Stop Development Environment**

```cmd
stop-docker.bat
```

---

## 🐳 Docker Services

The development environment includes:

| Service | Port | Description |
|---------|------|-------------|
| **ClassroomIO App** | 5173 | Main SvelteKit application |
| **PostgreSQL** | 54322 | Database server |
| **Redis** | 6379 | Cache and session store |
| **Supabase API** | 54321 | Database API |
| **Supabase Studio** | 54323 | Database management UI |

---

## 🛠️ Manual Docker Commands

If you prefer manual control:

### **Start Development Environment**
```cmd
# Start all services
docker-compose -f docker-compose.dev.yml up --build

# Start in background (detached)
docker-compose -f docker-compose.dev.yml up --build -d
```

### **View Logs**
```cmd
# View all logs
docker-compose -f docker-compose.dev.yml logs -f

# View specific service logs
docker-compose -f docker-compose.dev.yml logs -f dashboard
docker-compose -f docker-compose.dev.yml logs -f postgres
```

### **Stop Services**
```cmd
# Stop all services
docker-compose -f docker-compose.dev.yml down

# Stop and remove volumes (reset data)
docker-compose -f docker-compose.dev.yml down -v
```

### **Restart Services**
```cmd
# Restart all services
docker-compose -f docker-compose.dev.yml restart

# Restart specific service
docker-compose -f docker-compose.dev.yml restart dashboard
```

---

## 🔧 Development Workflow

### **Making Code Changes**

1. **Edit files** in your local directory
2. **Changes are automatically synced** to the Docker container
3. **Hot reload** will update the application automatically
4. **No need to restart** Docker containers for code changes

### **Database Changes**

1. **Access Supabase Studio**: http://localhost:54323
2. **Make schema changes** through the UI
3. **Or connect directly** to PostgreSQL:
   ```cmd
   # Connection details:
   Host: localhost
   Port: 54322
   Database: classroomio
   Username: postgres
   Password: postgres
   ```

### **Installing New Dependencies**

```cmd
# Rebuild containers after adding dependencies
docker-compose -f docker-compose.dev.yml up --build
```

---

## 🐛 Troubleshooting

### **Docker Not Starting**

1. **Check Docker Desktop is running**:
   - Look for Docker icon in system tray
   - Should show "Docker Desktop is running"

2. **Restart Docker Desktop**:
   - Right-click Docker icon → Restart
   - Wait for full startup

3. **Check Docker version**:
   ```cmd
   docker --version
   docker-compose --version
   ```

### **Port Conflicts**

If ports are already in use:

1. **Check what's using the ports**:
   ```cmd
   netstat -ano | findstr :5173
   netstat -ano | findstr :54322
   ```

2. **Stop conflicting services** or modify ports in `docker-compose.dev.yml`

### **Container Build Failures**

1. **Clean Docker cache**:
   ```cmd
   docker system prune -a
   ```

2. **Rebuild from scratch**:
   ```cmd
   docker-compose -f docker-compose.dev.yml down -v
   docker-compose -f docker-compose.dev.yml up --build --force-recreate
   ```

### **Application Not Loading**

1. **Check container status**:
   ```cmd
   docker-compose -f docker-compose.dev.yml ps
   ```

2. **View application logs**:
   ```cmd
   docker-compose -f docker-compose.dev.yml logs dashboard
   ```

3. **Check health status**:
   ```cmd
   curl http://localhost:5173/health
   ```

### **Database Connection Issues**

1. **Check PostgreSQL container**:
   ```cmd
   docker-compose -f docker-compose.dev.yml logs postgres
   ```

2. **Test database connection**:
   ```cmd
   docker-compose -f docker-compose.dev.yml exec postgres psql -U postgres -d classroomio
   ```

---

## 🔄 Reset Development Environment

To completely reset and start fresh:

```cmd
# Stop all services and remove data
docker-compose -f docker-compose.dev.yml down -v

# Remove all ClassroomIO images
docker images | findstr classroomio | awk '{print $3}' | xargs docker rmi

# Clean Docker system
docker system prune -a

# Start fresh
start-docker.bat
```

---

## 📊 Monitoring and Debugging

### **Container Status**
```cmd
# View running containers
docker ps

# View all containers (including stopped)
docker ps -a

# View container resource usage
docker stats
```

### **Access Container Shell**
```cmd
# Access dashboard container
docker-compose -f docker-compose.dev.yml exec dashboard sh

# Access database container
docker-compose -f docker-compose.dev.yml exec postgres bash
```

### **Database Management**
```cmd
# Access PostgreSQL CLI
docker-compose -f docker-compose.dev.yml exec postgres psql -U postgres -d classroomio

# Backup database
docker-compose -f docker-compose.dev.yml exec postgres pg_dump -U postgres classroomio > backup.sql

# Restore database
docker-compose -f docker-compose.dev.yml exec -T postgres psql -U postgres -d classroomio < backup.sql
```

---

## 🎯 Production Deployment

For production deployment, use:

```cmd
# Production Docker Compose
docker-compose -f docker-compose.production.yml up -d

# Or use the production scripts
./scripts/deploy.sh production
```

---

## 📞 Support

If you encounter issues:

1. **Check the troubleshooting section above**
2. **View container logs** for specific error messages
3. **Check Docker Desktop status** and restart if needed
4. **Join Discord Community**: https://discord.gg/classroomio

---

## 🎉 Success!

If you see this after running `start-docker.bat`:

```
========================================
  ClassroomIO Development Ready!
========================================

Services:
  Main Application:  http://localhost:5173
  Health Check:      http://localhost:5173/health
  API Health:        http://localhost:5173/api/health
  Database:          localhost:54322
  Supabase Studio:   http://localhost:54323
```

**Congratulations! Your ClassroomIO development environment is running in Docker!** 🎓✨

The application should be accessible at http://localhost:5173 with all services properly configured and running.
