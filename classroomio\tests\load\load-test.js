// K6 Load Test for ClassroomIO
// Comprehensive performance testing scenarios

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');
const requestCount = new Counter('requests');

// Test configuration
export const options = {
  stages: [
    // Ramp up
    { duration: '2m', target: 10 },   // Ramp up to 10 users over 2 minutes
    { duration: '5m', target: 10 },   // Stay at 10 users for 5 minutes
    { duration: '2m', target: 50 },   // Ramp up to 50 users over 2 minutes
    { duration: '5m', target: 50 },   // Stay at 50 users for 5 minutes
    { duration: '2m', target: 100 },  // Ramp up to 100 users over 2 minutes
    { duration: '5m', target: 100 },  // Stay at 100 users for 5 minutes
    { duration: '5m', target: 0 },    // Ramp down to 0 users over 5 minutes
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
    http_req_failed: ['rate<0.01'],   // Error rate should be less than 1%
    errors: ['rate<0.01'],            // Custom error rate should be less than 1%
  },
};

// Base URL
const BASE_URL = __ENV.TEST_URL || 'http://localhost:5173';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'LoadTest123!' },
  { email: '<EMAIL>', password: 'LoadTest123!' },
  { email: '<EMAIL>', password: 'LoadTest123!' },
  { email: '<EMAIL>', password: 'LoadTest123!' },
];

// Authentication helper
function authenticate(user) {
  const loginPayload = JSON.stringify({
    email: user.email,
    password: user.password,
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const response = http.post(`${BASE_URL}/api/auth/login`, loginPayload, params);
  
  check(response, {
    'login successful': (r) => r.status === 200,
    'login response time OK': (r) => r.timings.duration < 1000,
  });

  if (response.status === 200) {
    const body = JSON.parse(response.body);
    return body.token;
  }
  
  return null;
}

// Main test function
export default function () {
  // Select random user
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  
  // Test scenarios with different weights
  const scenario = Math.random();
  
  if (scenario < 0.4) {
    // 40% - Browse courses scenario
    browseCourses();
  } else if (scenario < 0.7) {
    // 30% - Watch video scenario
    watchVideo(user);
  } else if (scenario < 0.9) {
    // 20% - Take quiz scenario
    takeQuiz(user);
  } else {
    // 10% - Admin operations scenario
    adminOperations(user);
  }
  
  // Random sleep between 1-5 seconds
  sleep(Math.random() * 4 + 1);
}

// Browse courses scenario (unauthenticated)
function browseCourses() {
  const responses = http.batch([
    ['GET', `${BASE_URL}/`],
    ['GET', `${BASE_URL}/courses`],
    ['GET', `${BASE_URL}/api/courses/public`],
  ]);

  responses.forEach((response, index) => {
    const endpoint = ['homepage', 'courses-page', 'courses-api'][index];
    
    check(response, {
      [`${endpoint} status is 200`]: (r) => r.status === 200,
      [`${endpoint} response time OK`]: (r) => r.timings.duration < 2000,
    });

    requestCount.add(1);
    responseTime.add(response.timings.duration);
    
    if (response.status !== 200) {
      errorRate.add(1);
    }
  });
}

// Watch video scenario (authenticated)
function watchVideo(user) {
  const token = authenticate(user);
  if (!token) {
    errorRate.add(1);
    return;
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // Get course list
  let response = http.get(`${BASE_URL}/api/courses`, { headers });
  check(response, {
    'get courses successful': (r) => r.status === 200,
  });

  if (response.status !== 200) {
    errorRate.add(1);
    return;
  }

  // Simulate video watching
  const videoId = 'test-video-123';
  
  // Start video
  response = http.get(`${BASE_URL}/api/videos/${videoId}`, { headers });
  check(response, {
    'get video successful': (r) => r.status === 200,
    'video response time OK': (r) => r.timings.duration < 1000,
  });

  requestCount.add(1);
  responseTime.add(response.timings.duration);

  // Track progress (simulate watching for 30 seconds)
  const progressPayload = JSON.stringify({
    current_time: 30,
    completion_percentage: 25,
    session_id: `session-${Date.now()}`,
  });

  response = http.post(`${BASE_URL}/api/videos/${videoId}/progress`, progressPayload, { headers });
  check(response, {
    'track progress successful': (r) => r.status === 200,
  });

  requestCount.add(1);
  responseTime.add(response.timings.duration);

  if (response.status !== 200) {
    errorRate.add(1);
  }
}

// Take quiz scenario (authenticated)
function takeQuiz(user) {
  const token = authenticate(user);
  if (!token) {
    errorRate.add(1);
    return;
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  const quizId = 'test-quiz-123';

  // Get quiz
  let response = http.get(`${BASE_URL}/api/quizzes/${quizId}`, { headers });
  check(response, {
    'get quiz successful': (r) => r.status === 200,
  });

  requestCount.add(1);
  responseTime.add(response.timings.duration);

  // Submit quiz answers
  const answersPayload = JSON.stringify({
    answers: [
      { question_id: 'q1', answer: 'A' },
      { question_id: 'q2', answer: 'B' },
      { question_id: 'q3', answer: 'C' },
    ],
    time_taken: 120, // 2 minutes
  });

  response = http.post(`${BASE_URL}/api/quizzes/${quizId}/submit`, answersPayload, { headers });
  check(response, {
    'submit quiz successful': (r) => r.status === 200,
    'quiz submission time OK': (r) => r.timings.duration < 2000,
  });

  requestCount.add(1);
  responseTime.add(response.timings.duration);

  if (response.status !== 200) {
    errorRate.add(1);
  }
}

// Admin operations scenario (authenticated)
function adminOperations(user) {
  const token = authenticate(user);
  if (!token) {
    errorRate.add(1);
    return;
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // Get analytics dashboard
  let response = http.get(`${BASE_URL}/api/analytics/dashboard?type=admin`, { headers });
  check(response, {
    'get analytics successful': (r) => r.status === 200,
    'analytics response time OK': (r) => r.timings.duration < 3000,
  });

  requestCount.add(1);
  responseTime.add(response.timings.duration);

  // Get user list
  response = http.get(`${BASE_URL}/api/users?page=1&limit=20`, { headers });
  check(response, {
    'get users successful': (r) => r.status === 200,
  });

  requestCount.add(1);
  responseTime.add(response.timings.duration);

  // Get system metrics
  response = http.get(`${BASE_URL}/api/system/metrics`, { headers });
  check(response, {
    'get metrics successful': (r) => r.status === 200,
  });

  requestCount.add(1);
  responseTime.add(response.timings.duration);

  if (response.status !== 200) {
    errorRate.add(1);
  }
}

// Setup function (runs once per VU)
export function setup() {
  console.log('Starting load test setup...');
  
  // Verify application is running
  const response = http.get(`${BASE_URL}/health`);
  if (response.status !== 200) {
    throw new Error(`Application not ready. Status: ${response.status}`);
  }
  
  console.log('Application is ready for load testing');
  return { baseUrl: BASE_URL };
}

// Teardown function (runs once after all VUs finish)
export function teardown(data) {
  console.log('Load test completed');
  console.log(`Base URL: ${data.baseUrl}`);
  console.log('Check the results for performance metrics');
}
