#!/usr/bin/env node

/**
 * Health Check Test Script for ClassroomIO
 * Validates application health and readiness for production
 */

const https = require('https');
const http = require('http');

// Configuration
const config = {
  baseUrl: process.env.TEST_URL || 'http://localhost:5173',
  timeout: 10000,
  retries: 3,
  interval: 2000
};

// Test results
const results = {
  timestamp: new Date().toISOString(),
  baseUrl: config.baseUrl,
  tests: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  }
};

// Logging functions
function log(level, message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
}

function logInfo(message) {
  log('info', message);
}

function logSuccess(message) {
  log('success', `✅ ${message}`);
}

function logWarning(message) {
  log('warning', `⚠️ ${message}`);
}

function logError(message) {
  log('error', `❌ ${message}`);
}

// HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const requestOptions = {
      timeout: config.timeout,
      headers: {
        'User-Agent': 'ClassroomIO-HealthCheck/1.0',
        ...options.headers
      },
      ...options
    };

    const req = protocol.get(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          responseTime: Date.now() - startTime
        });
      });
    });

    const startTime = Date.now();
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${config.timeout}ms`));
    });
  });
}

// Test functions
async function testApplicationHealth() {
  const testName = 'Application Health Check';
  logInfo(`Running ${testName}...`);
  
  try {
    const response = await makeRequest(`${config.baseUrl}/health`);
    
    if (response.statusCode === 200) {
      logSuccess(`${testName} passed - Status: ${response.statusCode}, Response time: ${response.responseTime}ms`);
      results.tests.push({
        name: testName,
        status: 'passed',
        responseTime: response.responseTime,
        statusCode: response.statusCode
      });
      results.summary.passed++;
    } else {
      logError(`${testName} failed - Status: ${response.statusCode}`);
      results.tests.push({
        name: testName,
        status: 'failed',
        error: `HTTP ${response.statusCode}`,
        responseTime: response.responseTime
      });
      results.summary.failed++;
    }
  } catch (error) {
    logError(`${testName} failed - ${error.message}`);
    results.tests.push({
      name: testName,
      status: 'failed',
      error: error.message
    });
    results.summary.failed++;
  }
  
  results.summary.total++;
}

async function testHomePage() {
  const testName = 'Home Page Accessibility';
  logInfo(`Running ${testName}...`);
  
  try {
    const response = await makeRequest(config.baseUrl);
    
    if (response.statusCode === 200) {
      // Check if response contains expected content
      const hasTitle = response.body.includes('<title>') || response.body.includes('ClassroomIO');
      const hasHtml = response.body.includes('<html') || response.body.includes('<!DOCTYPE html>');
      
      if (hasTitle && hasHtml) {
        logSuccess(`${testName} passed - Valid HTML content received`);
        results.tests.push({
          name: testName,
          status: 'passed',
          responseTime: response.responseTime,
          statusCode: response.statusCode
        });
        results.summary.passed++;
      } else {
        logWarning(`${testName} warning - Response received but content may be incomplete`);
        results.tests.push({
          name: testName,
          status: 'warning',
          responseTime: response.responseTime,
          statusCode: response.statusCode,
          message: 'Content validation failed'
        });
        results.summary.warnings++;
      }
    } else {
      logError(`${testName} failed - Status: ${response.statusCode}`);
      results.tests.push({
        name: testName,
        status: 'failed',
        error: `HTTP ${response.statusCode}`,
        responseTime: response.responseTime
      });
      results.summary.failed++;
    }
  } catch (error) {
    logError(`${testName} failed - ${error.message}`);
    results.tests.push({
      name: testName,
      status: 'failed',
      error: error.message
    });
    results.summary.failed++;
  }
  
  results.summary.total++;
}

async function testApiEndpoints() {
  const testName = 'API Endpoints Check';
  logInfo(`Running ${testName}...`);
  
  const endpoints = [
    { path: '/api/health', expectedStatus: 200 },
    { path: '/api/auth/login', expectedStatus: 405, method: 'GET' }, // Should reject GET
    { path: '/api/courses/public', expectedStatus: 200 },
  ];
  
  let passedEndpoints = 0;
  const endpointResults = [];
  
  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${config.baseUrl}${endpoint.path}`);
      
      if (response.statusCode === endpoint.expectedStatus) {
        logSuccess(`API ${endpoint.path} - Status: ${response.statusCode} (expected)`);
        endpointResults.push({
          path: endpoint.path,
          status: 'passed',
          statusCode: response.statusCode,
          responseTime: response.responseTime
        });
        passedEndpoints++;
      } else {
        logWarning(`API ${endpoint.path} - Status: ${response.statusCode} (expected: ${endpoint.expectedStatus})`);
        endpointResults.push({
          path: endpoint.path,
          status: 'warning',
          statusCode: response.statusCode,
          expectedStatus: endpoint.expectedStatus,
          responseTime: response.responseTime
        });
      }
    } catch (error) {
      logError(`API ${endpoint.path} - ${error.message}`);
      endpointResults.push({
        path: endpoint.path,
        status: 'failed',
        error: error.message
      });
    }
  }
  
  if (passedEndpoints === endpoints.length) {
    logSuccess(`${testName} passed - All ${endpoints.length} endpoints responded correctly`);
    results.tests.push({
      name: testName,
      status: 'passed',
      endpoints: endpointResults
    });
    results.summary.passed++;
  } else if (passedEndpoints > 0) {
    logWarning(`${testName} partial - ${passedEndpoints}/${endpoints.length} endpoints passed`);
    results.tests.push({
      name: testName,
      status: 'warning',
      endpoints: endpointResults,
      message: `${passedEndpoints}/${endpoints.length} endpoints passed`
    });
    results.summary.warnings++;
  } else {
    logError(`${testName} failed - No endpoints responded correctly`);
    results.tests.push({
      name: testName,
      status: 'failed',
      endpoints: endpointResults
    });
    results.summary.failed++;
  }
  
  results.summary.total++;
}

async function testResponseTimes() {
  const testName = 'Response Time Check';
  logInfo(`Running ${testName}...`);
  
  const maxResponseTime = 5000; // 5 seconds
  const endpoints = [
    config.baseUrl,
    `${config.baseUrl}/health`,
    `${config.baseUrl}/api/health`
  ];
  
  let slowEndpoints = 0;
  const timingResults = [];
  
  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(endpoint);
      
      timingResults.push({
        endpoint,
        responseTime: response.responseTime,
        statusCode: response.statusCode
      });
      
      if (response.responseTime > maxResponseTime) {
        logWarning(`Slow response from ${endpoint}: ${response.responseTime}ms`);
        slowEndpoints++;
      } else {
        logSuccess(`Good response time from ${endpoint}: ${response.responseTime}ms`);
      }
    } catch (error) {
      logError(`Failed to test ${endpoint}: ${error.message}`);
      timingResults.push({
        endpoint,
        error: error.message
      });
      slowEndpoints++;
    }
  }
  
  if (slowEndpoints === 0) {
    logSuccess(`${testName} passed - All endpoints respond within ${maxResponseTime}ms`);
    results.tests.push({
      name: testName,
      status: 'passed',
      timings: timingResults,
      threshold: maxResponseTime
    });
    results.summary.passed++;
  } else {
    logWarning(`${testName} warning - ${slowEndpoints} endpoints are slow`);
    results.tests.push({
      name: testName,
      status: 'warning',
      timings: timingResults,
      threshold: maxResponseTime,
      message: `${slowEndpoints} slow endpoints`
    });
    results.summary.warnings++;
  }
  
  results.summary.total++;
}

async function testSSLConfiguration() {
  const testName = 'SSL Configuration Check';
  
  if (!config.baseUrl.startsWith('https')) {
    logInfo(`Skipping ${testName} - Not using HTTPS`);
    results.tests.push({
      name: testName,
      status: 'skipped',
      message: 'Not using HTTPS'
    });
    results.summary.total++;
    return;
  }
  
  logInfo(`Running ${testName}...`);
  
  try {
    const response = await makeRequest(config.baseUrl);
    
    // Check security headers
    const securityHeaders = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection'
    ];
    
    const missingHeaders = securityHeaders.filter(header => !response.headers[header]);
    
    if (missingHeaders.length === 0) {
      logSuccess(`${testName} passed - All security headers present`);
      results.tests.push({
        name: testName,
        status: 'passed',
        securityHeaders: securityHeaders
      });
      results.summary.passed++;
    } else {
      logWarning(`${testName} warning - Missing security headers: ${missingHeaders.join(', ')}`);
      results.tests.push({
        name: testName,
        status: 'warning',
        missingHeaders,
        message: `Missing ${missingHeaders.length} security headers`
      });
      results.summary.warnings++;
    }
  } catch (error) {
    logError(`${testName} failed - ${error.message}`);
    results.tests.push({
      name: testName,
      status: 'failed',
      error: error.message
    });
    results.summary.failed++;
  }
  
  results.summary.total++;
}

// Main execution
async function runHealthChecks() {
  logInfo('Starting ClassroomIO health checks...');
  logInfo(`Target URL: ${config.baseUrl}`);
  
  // Run all health checks
  await testApplicationHealth();
  await testHomePage();
  await testApiEndpoints();
  await testResponseTimes();
  await testSSLConfiguration();
  
  // Generate summary
  logInfo('Health check summary:');
  logInfo(`Total tests: ${results.summary.total}`);
  logSuccess(`Passed: ${results.summary.passed}`);
  
  if (results.summary.warnings > 0) {
    logWarning(`Warnings: ${results.summary.warnings}`);
  }
  
  if (results.summary.failed > 0) {
    logError(`Failed: ${results.summary.failed}`);
  }
  
  // Output JSON results if requested
  if (process.env.OUTPUT_JSON === 'true') {
    console.log('\n' + JSON.stringify(results, null, 2));
  }
  
  // Exit with appropriate code
  if (results.summary.failed > 0) {
    process.exit(1); // Failure
  } else if (results.summary.warnings > 0) {
    process.exit(2); // Warnings
  } else {
    process.exit(0); // Success
  }
}

// Handle signals
process.on('SIGTERM', () => {
  logInfo('Health check interrupted');
  process.exit(130);
});

process.on('SIGINT', () => {
  logInfo('Health check interrupted');
  process.exit(130);
});

// Run health checks
if (require.main === module) {
  runHealthChecks().catch((error) => {
    logError(`Health check failed: ${error.message}`);
    process.exit(3);
  });
}

module.exports = { runHealthChecks, makeRequest };
