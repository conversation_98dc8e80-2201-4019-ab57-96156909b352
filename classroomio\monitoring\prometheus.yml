# Prometheus Configuration for ClassroomIO
# Comprehensive monitoring and alerting setup

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'classroomio-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # ClassroomIO Application
  - job_name: 'classroomio-app'
    static_configs:
      - targets: ['classroomio-app:3000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s
    metrics_path: /metrics

  # Redis Exporter
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    metrics_path: /metrics

  # Nginx Exporter
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s
    metrics_path: /metrics

  # Docker Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # Metabase Metrics
  - job_name: 'metabase'
    static_configs:
      - targets: ['classroomio-metabase:3000']
    scrape_interval: 30s
    metrics_path: /api/health
    scrape_timeout: 10s

  # Application-specific metrics
  - job_name: 'classroomio-analytics'
    static_configs:
      - targets: ['classroomio-app:3000']
    scrape_interval: 30s
    metrics_path: /api/metrics/analytics
    scrape_timeout: 10s

  # Video streaming metrics
  - job_name: 'classroomio-video'
    static_configs:
      - targets: ['classroomio-app:3000']
    scrape_interval: 30s
    metrics_path: /api/metrics/video
    scrape_timeout: 10s

  # Communication system metrics
  - job_name: 'classroomio-communication'
    static_configs:
      - targets: ['classroomio-app:3000']
    scrape_interval: 30s
    metrics_path: /api/metrics/communication
    scrape_timeout: 10s

  # Background worker metrics
  - job_name: 'classroomio-worker'
    static_configs:
      - targets: ['classroomio-worker:9464']
    scrape_interval: 15s
    metrics_path: /metrics

  # External service monitoring
  - job_name: 'external-apis'
    static_configs:
      - targets: 
        - 'api.openai.com'
        - 'api.daily.co'
        - 'api.cloudflare.com'
    scrape_interval: 60s
    metrics_path: /health
    scheme: https
    scrape_timeout: 30s

# Remote write configuration (for long-term storage)
remote_write:
  - url: "http://cortex:9009/api/prom/push"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500
