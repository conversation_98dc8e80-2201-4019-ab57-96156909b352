// Smoke Tests for ClassroomIO
// Quick validation tests to ensure basic functionality works after deployment

import { test, expect } from '@playwright/test';

test.describe('Smoke Tests - Critical Functionality', () => {
  test('Application loads successfully', async ({ page }) => {
    // Navigate to home page
    await page.goto('/');
    
    // Check if page loads without errors
    await expect(page).toHaveTitle(/ClassroomIO/);
    
    // Check for basic page structure
    await expect(page.locator('body')).toBeVisible();
    
    // Verify no JavaScript errors
    const errors: string[] = [];
    page.on('pageerror', (error) => {
      errors.push(error.message);
    });
    
    await page.waitForLoadState('networkidle');
    expect(errors).toHaveLength(0);
  });

  test('Health endpoint responds correctly', async ({ request }) => {
    const response = await request.get('/health');
    expect(response.status()).toBe(200);
    
    const body = await response.text();
    expect(body).toContain('healthy');
  });

  test('API health endpoint responds correctly', async ({ request }) => {
    const response = await request.get('/api/health');
    expect(response.status()).toBe(200);
    
    const body = await response.json();
    expect(body).toHaveProperty('status');
  });

  test('Login page loads and form is functional', async ({ page }) => {
    await page.goto('/login');
    
    // Check if login form elements are present
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Test form validation
    await page.click('button[type="submit"]');
    
    // Should show validation errors
    await expect(page.locator('text=required')).toBeVisible();
  });

  test('Public courses page loads', async ({ page }) => {
    await page.goto('/courses');
    
    // Check if courses page loads
    await expect(page).toHaveTitle(/Courses.*ClassroomIO/);
    
    // Check for courses container
    await expect(page.locator('[data-testid="courses-container"]')).toBeVisible();
  });

  test('Navigation menu works', async ({ page }) => {
    await page.goto('/');
    
    // Check if navigation is present
    await expect(page.locator('nav')).toBeVisible();
    
    // Test navigation links
    const navLinks = page.locator('nav a');
    const linkCount = await navLinks.count();
    expect(linkCount).toBeGreaterThan(0);
    
    // Click on a navigation link
    if (linkCount > 0) {
      await navLinks.first().click();
      await page.waitForLoadState('networkidle');
      
      // Should navigate successfully
      expect(page.url()).not.toBe('/');
    }
  });

  test('Static assets load correctly', async ({ page }) => {
    await page.goto('/');
    
    // Check for CSS
    const stylesheets = page.locator('link[rel="stylesheet"]');
    const cssCount = await stylesheets.count();
    expect(cssCount).toBeGreaterThan(0);
    
    // Check for JavaScript
    const scripts = page.locator('script[src]');
    const jsCount = await scripts.count();
    expect(jsCount).toBeGreaterThan(0);
    
    // Verify no 404 errors for assets
    const failedRequests: string[] = [];
    page.on('response', (response) => {
      if (response.status() === 404 && (
        response.url().includes('.css') ||
        response.url().includes('.js') ||
        response.url().includes('.png') ||
        response.url().includes('.jpg') ||
        response.url().includes('.svg')
      )) {
        failedRequests.push(response.url());
      }
    });
    
    await page.waitForLoadState('networkidle');
    expect(failedRequests).toHaveLength(0);
  });

  test('Responsive design works on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check if page is responsive
    await expect(page.locator('body')).toBeVisible();
    
    // Check if mobile navigation works
    const mobileMenu = page.locator('[data-testid="mobile-menu-button"]');
    if (await mobileMenu.isVisible()) {
      await mobileMenu.click();
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    }
  });

  test('Search functionality works', async ({ page }) => {
    await page.goto('/');
    
    // Look for search input
    const searchInput = page.locator('input[type="search"], input[placeholder*="search" i]');
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('test');
      await searchInput.press('Enter');
      
      // Should navigate to search results or show results
      await page.waitForLoadState('networkidle');
      
      // Verify search was performed
      expect(page.url()).toContain('search');
    }
  });

  test('Footer links are functional', async ({ page }) => {
    await page.goto('/');
    
    // Check if footer exists
    const footer = page.locator('footer');
    if (await footer.isVisible()) {
      // Check footer links
      const footerLinks = footer.locator('a');
      const linkCount = await footerLinks.count();
      
      if (linkCount > 0) {
        // Test first footer link
        const firstLink = footerLinks.first();
        const href = await firstLink.getAttribute('href');
        
        if (href && !href.startsWith('mailto:') && !href.startsWith('tel:')) {
          await firstLink.click();
          await page.waitForLoadState('networkidle');
          
          // Should navigate successfully
          expect(page.url()).toBeTruthy();
        }
      }
    }
  });

  test('Error pages work correctly', async ({ page }) => {
    // Test 404 page
    await page.goto('/non-existent-page');
    
    // Should show 404 page or redirect
    const title = await page.title();
    const content = await page.textContent('body');
    
    // Should either show 404 content or redirect to home
    expect(
      title.includes('404') || 
      title.includes('Not Found') || 
      page.url().endsWith('/') ||
      content?.includes('404') ||
      content?.includes('not found')
    ).toBeTruthy();
  });
});

test.describe('Smoke Tests - Performance', () => {
  test('Page load time is acceptable', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
  });

  test('API response time is acceptable', async ({ request }) => {
    const startTime = Date.now();
    
    const response = await request.get('/api/health');
    
    const responseTime = Date.now() - startTime;
    
    expect(response.status()).toBe(200);
    expect(responseTime).toBeLessThan(2000); // 2 seconds
  });
});

test.describe('Smoke Tests - Security', () => {
  test('Security headers are present (HTTPS only)', async ({ request }) => {
    const response = await request.get('/');
    
    // Only test security headers if using HTTPS
    if (response.url().startsWith('https://')) {
      const headers = response.headers();
      
      // Check for important security headers
      expect(headers['x-content-type-options']).toBeTruthy();
      expect(headers['x-frame-options']).toBeTruthy();
    }
  });

  test('No sensitive information in client-side code', async ({ page }) => {
    await page.goto('/');
    
    // Check page source for sensitive patterns
    const content = await page.content();
    
    // Should not contain sensitive information
    expect(content).not.toMatch(/password\s*[:=]\s*["'][^"']+["']/i);
    expect(content).not.toMatch(/api[_-]?key\s*[:=]\s*["'][^"']+["']/i);
    expect(content).not.toMatch(/secret\s*[:=]\s*["'][^"']+["']/i);
  });
});

test.describe('Smoke Tests - Accessibility', () => {
  test('Basic accessibility requirements', async ({ page }) => {
    await page.goto('/');
    
    // Check for basic accessibility features
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(0);
    
    // Check for main landmark
    const main = page.locator('main, [role="main"]');
    if (await main.count() > 0) {
      await expect(main.first()).toBeVisible();
    }
    
    // Check for heading structure
    const h1 = page.locator('h1');
    const h1Count = await h1.count();
    expect(h1Count).toBeGreaterThanOrEqual(1);
  });

  test('Images have alt text', async ({ page }) => {
    await page.goto('/');
    
    // Check all images have alt attributes
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      const ariaLabel = await img.getAttribute('aria-label');
      const role = await img.getAttribute('role');
      
      // Image should have alt text, aria-label, or be decorative
      expect(
        alt !== null || 
        ariaLabel !== null || 
        role === 'presentation' ||
        alt === '' // Empty alt for decorative images is acceptable
      ).toBeTruthy();
    }
  });
});
