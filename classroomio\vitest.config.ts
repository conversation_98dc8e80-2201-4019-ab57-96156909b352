// Vitest Configuration for ClassroomIO
// Comprehensive testing setup with coverage and performance monitoring

import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';
import { resolve } from 'path';

export default defineConfig({
  plugins: [sveltekit()],
  
  test: {
    // Test environment
    environment: 'jsdom',
    
    // Global setup and teardown
    globalSetup: ['./src/lib/utils/testing/global-setup.ts'],
    setupFiles: ['./src/lib/utils/testing/test-setup.ts'],
    
    // Test patterns
    include: [
      'src/**/*.{test,spec}.{js,ts}',
      'tests/**/*.{test,spec}.{js,ts}'
    ],
    exclude: [
      'node_modules/**',
      'dist/**',
      'build/**',
      '.svelte-kit/**'
    ],
    
    // Test execution
    globals: true,
    testTimeout: 10000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    
    // Parallel execution
    threads: true,
    maxThreads: 4,
    minThreads: 1,
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/**',
        'src/lib/utils/testing/**',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/dist/**',
        '**/build/**',
        '**/.svelte-kit/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        },
        // Component-specific thresholds
        'src/lib/components/Analytics/**': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        },
        'src/lib/components/VideoPlayer/**': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        },
        'src/lib/utils/services/**': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        }
      }
    },
    
    // Reporters
    reporter: [
      'default',
      'json',
      'html',
      'junit'
    ],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html',
      junit: './test-results/junit.xml'
    },
    
    // Watch mode
    watch: false,
    
    // Performance monitoring
    benchmark: {
      include: ['**/*.{bench,benchmark}.{js,ts}'],
      exclude: ['node_modules/**'],
      reporter: ['default', 'json'],
      outputFile: './test-results/benchmark.json'
    }
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '$lib': resolve('./src/lib'),
      '$app': resolve('./node_modules/@sveltejs/kit/src/runtime/app'),
      '$env': resolve('./src/env'),
      '$service-worker': resolve('./src/service-worker')
    }
  },
  
  // Define configuration
  define: {
    // Test environment variables
    'import.meta.env.VITE_SUPABASE_URL': JSON.stringify('http://localhost:54321'),
    'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify('test-anon-key'),
    'import.meta.env.VITE_APP_ENV': JSON.stringify('test')
  },
  
  // Server configuration for testing
  server: {
    deps: {
      inline: [
        '@testing-library/svelte',
        '@testing-library/jest-dom'
      ]
    }
  }
});
