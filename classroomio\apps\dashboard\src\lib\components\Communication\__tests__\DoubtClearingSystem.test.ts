// Doubt Clearing System Tests
// Comprehensive test suite for the AI-powered doubt clearing system

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import DoubtClearingSystem from '../DoubtClearingSystem.svelte';
import { 
  setupTest, 
  cleanupTest, 
  TestDataFactory, 
  TestUtils,
  PerformanceTestUtils,
  AccessibilityTestUtils
} from '$lib/utils/testing/test-setup';

describe('DoubtClearingSystem', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    cleanupTest();
  });

  describe('Component Rendering', () => {
    it('should render doubt submission form', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id'
      };

      render(DoubtClearingSystem, { props });

      expect(screen.getByRole('textbox', { name: /doubt title/i })).toBeInTheDocument();
      expect(screen.getByRole('textbox', { name: /doubt description/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /submit doubt/i })).toBeInTheDocument();
    });

    it('should render doubt list for instructors', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-instructor-id',
        userRole: 'instructor' as const
      };

      const mockDoubts = [
        TestDataFactory.createDoubt({ title: 'Test Doubt 1', status: 'pending' }),
        TestDataFactory.createDoubt({ title: 'Test Doubt 2', status: 'resolved' })
      ];

      TestUtils.mockFetch({
        '*': mockDoubts
      });

      render(DoubtClearingSystem, { props });

      await waitFor(() => {
        expect(screen.getByText('Test Doubt 1')).toBeInTheDocument();
        expect(screen.getByText('Test Doubt 2')).toBeInTheDocument();
      });
    });

    it('should show AI categorization suggestions', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id',
        enableAI: true
      };

      render(DoubtClearingSystem, { props });

      const descriptionField = screen.getByRole('textbox', { name: /doubt description/i });
      await fireEvent.input(descriptionField, { 
        target: { value: 'I am having trouble understanding calculus derivatives' } 
      });

      await waitFor(() => {
        expect(screen.getByText(/ai suggestions/i)).toBeInTheDocument();
      });
    });
  });

  describe('Doubt Submission', () => {
    it('should submit doubt successfully', async () => {
      const mockDoubtService = {
        submitDoubt: vi.fn().mockResolvedValue({ id: 'new-doubt-id' }),
        getDoubts: vi.fn().mockResolvedValue([])
      };

      vi.mock('$lib/utils/services/doubt', () => ({
        doubtService: mockDoubtService
      }));

      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id'
      };

      render(DoubtClearingSystem, { props });

      const titleField = screen.getByRole('textbox', { name: /doubt title/i });
      const descriptionField = screen.getByRole('textbox', { name: /doubt description/i });
      const submitButton = screen.getByRole('button', { name: /submit doubt/i });

      await fireEvent.input(titleField, { target: { value: 'Test Doubt Title' } });
      await fireEvent.input(descriptionField, { target: { value: 'Test doubt description' } });
      await fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockDoubtService.submitDoubt).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'Test Doubt Title',
            description: 'Test doubt description',
            batch_id: 'test-batch-id',
            subject_id: 'test-subject-id',
            user_id: 'test-user-id'
          })
        );
      });
    });

    it('should handle file attachments', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id',
        allowAttachments: true
      };

      render(DoubtClearingSystem, { props });

      const fileInput = screen.getByLabelText(/attach files/i);
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });

      await fireEvent.change(fileInput, { target: { files: [file] } });

      expect(screen.getByText('test.pdf')).toBeInTheDocument();
    });

    it('should validate form inputs', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id'
      };

      render(DoubtClearingSystem, { props });

      const submitButton = screen.getByRole('button', { name: /submit doubt/i });
      await fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/title is required/i)).toBeInTheDocument();
        expect(screen.getByText(/description is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('AI Integration', () => {
    it('should categorize doubts automatically', async () => {
      const mockAIService = {
        categorizeDoubt: vi.fn().mockResolvedValue({
          category: 'calculus',
          confidence: 0.95,
          suggestions: ['derivatives', 'limits']
        })
      };

      vi.mock('$lib/utils/services/ai', () => ({
        aiService: mockAIService
      }));

      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id',
        enableAI: true
      };

      render(DoubtClearingSystem, { props });

      const descriptionField = screen.getByRole('textbox', { name: /doubt description/i });
      await fireEvent.input(descriptionField, { 
        target: { value: 'I need help with derivatives in calculus' } 
      });

      await waitFor(() => {
        expect(mockAIService.categorizeDoubt).toHaveBeenCalledWith(
          'I need help with derivatives in calculus'
        );
      });

      await waitFor(() => {
        expect(screen.getByText('calculus')).toBeInTheDocument();
        expect(screen.getByText('derivatives')).toBeInTheDocument();
      });
    });

    it('should suggest similar resolved doubts', async () => {
      const mockAIService = {
        findSimilarDoubts: vi.fn().mockResolvedValue([
          { id: 'similar-1', title: 'Similar Doubt 1', similarity: 0.85 },
          { id: 'similar-2', title: 'Similar Doubt 2', similarity: 0.78 }
        ])
      };

      vi.mock('$lib/utils/services/ai', () => ({
        aiService: mockAIService
      }));

      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id',
        enableAI: true
      };

      render(DoubtClearingSystem, { props });

      const descriptionField = screen.getByRole('textbox', { name: /doubt description/i });
      await fireEvent.input(descriptionField, { 
        target: { value: 'How to solve quadratic equations?' } 
      });

      await waitFor(() => {
        expect(screen.getByText(/similar doubts/i)).toBeInTheDocument();
        expect(screen.getByText('Similar Doubt 1')).toBeInTheDocument();
      });
    });
  });

  describe('Instructor Features', () => {
    it('should allow instructors to respond to doubts', async () => {
      const mockDoubtService = {
        respondToDoubt: vi.fn().mockResolvedValue({ id: 'response-id' }),
        getDoubts: vi.fn().mockResolvedValue([
          TestDataFactory.createDoubt({ id: 'doubt-1', status: 'pending' })
        ])
      };

      vi.mock('$lib/utils/services/doubt', () => ({
        doubtService: mockDoubtService
      }));

      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-instructor-id',
        userRole: 'instructor' as const
      };

      render(DoubtClearingSystem, { props });

      await waitFor(() => {
        const respondButton = screen.getByRole('button', { name: /respond/i });
        fireEvent.click(respondButton);
      });

      await waitFor(() => {
        const responseField = screen.getByRole('textbox', { name: /response/i });
        fireEvent.input(responseField, { target: { value: 'Here is the solution...' } });
        
        const submitResponseButton = screen.getByRole('button', { name: /submit response/i });
        fireEvent.click(submitResponseButton);
      });

      await waitFor(() => {
        expect(mockDoubtService.respondToDoubt).toHaveBeenCalledWith(
          'doubt-1',
          expect.objectContaining({
            response: 'Here is the solution...',
            instructor_id: 'test-instructor-id'
          })
        );
      });
    });

    it('should show doubt analytics for instructors', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-instructor-id',
        userRole: 'instructor' as const,
        showAnalytics: true
      };

      const mockAnalytics = {
        total_doubts: 25,
        resolved_doubts: 20,
        pending_doubts: 5,
        average_response_time: 45
      };

      TestUtils.mockFetch({
        '*': mockAnalytics
      });

      render(DoubtClearingSystem, { props });

      await waitFor(() => {
        expect(screen.getByText('25')).toBeInTheDocument(); // total doubts
        expect(screen.getByText('20')).toBeInTheDocument(); // resolved doubts
        expect(screen.getByText('45')).toBeInTheDocument(); // avg response time
      });
    });
  });

  describe('Real-time Features', () => {
    it('should update doubt status in real-time', async () => {
      TestUtils.mockWebSocket();

      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id',
        enableRealTime: true
      };

      render(DoubtClearingSystem, { props });

      // Simulate WebSocket message
      const mockWebSocket = global.WebSocket as any;
      const wsInstance = mockWebSocket.mock.results[0].value;
      
      const statusUpdate = {
        type: 'doubt_status_update',
        doubt_id: 'doubt-1',
        status: 'resolved'
      };

      wsInstance.onmessage({ data: JSON.stringify(statusUpdate) });

      await waitFor(() => {
        expect(screen.getByText(/resolved/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance Tests', () => {
    it('should render within acceptable time limits', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id'
      };

      const renderTime = PerformanceTestUtils.measureRenderTime(DoubtClearingSystem, props);
      
      // Should render within 100ms
      expect(renderTime).toBeLessThan(100);
    });

    it('should handle large doubt lists efficiently', async () => {
      const largeDoubtList = PerformanceTestUtils.createLargeDataset(500).map(item => 
        TestDataFactory.createDoubt({ title: `Doubt ${item.id}` })
      );

      TestUtils.mockFetch({
        '*': largeDoubtList
      });

      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-instructor-id',
        userRole: 'instructor' as const
      };

      const start = performance.now();
      render(DoubtClearingSystem, { props });
      
      await waitFor(() => {
        expect(screen.getByText('Doubt 1')).toBeInTheDocument();
      });
      
      const end = performance.now();
      const loadTime = end - start;
      
      // Should handle large lists within 300ms
      expect(loadTime).toBeLessThan(300);
    });
  });

  describe('Accessibility Tests', () => {
    it('should have proper ARIA labels', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id'
      };

      const { container } = render(DoubtClearingSystem, { props });

      const ariaIssues = AccessibilityTestUtils.checkAriaLabels(container);
      expect(ariaIssues).toHaveLength(0);
    });

    it('should support keyboard navigation', async () => {
      const props = {
        batchId: 'test-batch-id',
        subjectId: 'test-subject-id',
        userId: 'test-user-id'
      };

      const { container } = render(DoubtClearingSystem, { props });

      const keyboardIssues = AccessibilityTestUtils.checkKeyboardNavigation(container);
      expect(keyboardIssues).toHaveLength(0);
    });
  });
});

// Helper functions for test data
const TestDataFactory = {
  ...TestDataFactory,
  createDoubt: (overrides: any = {}) => ({
    id: 'doubt-' + Math.random().toString(36).substr(2, 9),
    title: 'Test Doubt',
    description: 'Test doubt description',
    batch_id: 'test-batch-id',
    subject_id: 'test-subject-id',
    user_id: 'test-user-id',
    status: 'pending',
    priority: 'medium',
    category: 'general',
    tags: [],
    attachments: [],
    responses: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  })
};
