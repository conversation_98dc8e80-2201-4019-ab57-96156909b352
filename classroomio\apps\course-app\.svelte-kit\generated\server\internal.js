
import root from '../root.js';
import { set_building, set_prerendering } from '__sveltekit/environment';
import { set_assets } from '__sveltekit/paths';
import { set_manifest, set_read_implementation } from '__sveltekit/server';
import { set_private_env, set_public_env, set_safe_public_env } from '../../../../../node_modules/.pnpm/@sveltejs+kit@2.21.5_@sveltejs+vite-plugin-svelte@5.1.0_svelte@5.34.1_vite@6.3.5/node_modules/@sveltejs/kit/src/runtime/shared-server.js';

export const options = {
	app_template_contains_nonce: false,
	csp: {"mode":"auto","directives":{"upgrade-insecure-requests":false,"block-all-mixed-content":false},"reportOnly":{"upgrade-insecure-requests":false,"block-all-mixed-content":false}},
	csrf_check_origin: true,
	embedded: false,
	env_public_prefix: 'PUBLIC_',
	env_private_prefix: '',
	hash_routing: false,
	hooks: null, // added lazily, via `get_hooks`
	preload_strategy: "modulepreload",
	root,
	service_worker: false,
	templates: {
		app: ({ head, body, assets, nonce, env }) => "<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n\t<meta charset=\"utf-8\" />\r\n\t<link rel=\"icon\" href=\"" + assets + "/favicon.ico\" />\r\n\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n\r\n\t<title>Courseapp by ClassroomIO –&nbsp;Create your SAAS Academy in Minutes</title>\r\n\t<meta name=\"description\" content=\"Courseapp\" />\r\n\t<meta name=\"application-name\" content=\"Courseapp\" />\r\n\t<meta name=\"apple-mobile-web-app-title\" content=\"Courseapp\" />\r\n\t<meta name=\"theme-color\" content=\"#ffffff\" />\r\n\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n\r\n\t<!-- Facebook Meta Tags -->\r\n\t<meta property=\"og:url\" content=\"https://www.courseapp.oncws.com/\" />\r\n\t<meta property=\"og:image\" itemprop=\"image\" content=\"https://cdn.courseapp.oncws.com/courseapp-og.png\" />\r\n\t<meta property=\"og:title\" content=\"Courseapp by ClassroomIO –&nbsp;Create your SAAS Academy in Minutes\" />\r\n\t<meta property=\"og:description\"\r\n\t\tcontent=\"Series of templates available for you to clone, customise and launch your SAAS Academy. Scale your customer education effort today.\" />\r\n\r\n\t<meta property=\"og:type\" content=\"website\" />\r\n\t<meta property=\"og:image:type\" content=\"image/png\" />\r\n\t<meta property=\"og:image:width\" content=\"1920\" />\r\n\t<meta property=\"og:image:height\" content=\"1080\" />\r\n\t<meta property=\"og:image:secure_url\" itemprop=\"image\" content=\"https://cdn.courseapp.oncws.com/courseapp-og.png\" />\r\n\r\n\t<meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n\t<meta property=\"twitter:domain\" content=\"courseapp.oncws.com\" />\r\n\t<meta property=\"twitter:url\" content=\"https://www.courseapp.oncws.com/\" />\r\n\t<meta name=\"twitter:title\" content=\"Courseapp by ClassroomIO –&nbsp;Create your SAAS Academy in Minutes\" />\r\n\t<meta name=\"twitter:description\"\r\n\t\tcontent=\"Series of templates available for you to clone, customise and launch your SAAS Academy. Scale your customer education effort today.\" />\r\n\t<meta name=\"twitter:image\" content=\"https://cdn.courseapp.oncws.com/courseapp-og.png\" />\r\n\r\n\t" + head + "\r\n\r\n\t<script>(function (w, r) { w._rwq = r; w[r] = w[r] || function () { (w[r].q = w[r].q || []).push(arguments) } })(window, 'rewardful');</script>\r\n\t<script async src='https://r.wdfl.co/rw.js' data-rewardful='d216d5'></script>\r\n</head>\r\n\r\n<body data-sveltekit-preload-data=\"hover\">\r\n\t<div style=\"display: contents\">" + body + "</div>\r\n</body>\r\n\r\n</html>",
		error: ({ status, message }) => "<!doctype html>\n<html lang=\"en\">\n\t<head>\n\t\t<meta charset=\"utf-8\" />\n\t\t<title>" + message + "</title>\n\n\t\t<style>\n\t\t\tbody {\n\t\t\t\t--bg: white;\n\t\t\t\t--fg: #222;\n\t\t\t\t--divider: #ccc;\n\t\t\t\tbackground: var(--bg);\n\t\t\t\tcolor: var(--fg);\n\t\t\t\tfont-family:\n\t\t\t\t\tsystem-ui,\n\t\t\t\t\t-apple-system,\n\t\t\t\t\tBlinkMacSystemFont,\n\t\t\t\t\t'Segoe UI',\n\t\t\t\t\tRoboto,\n\t\t\t\t\tOxygen,\n\t\t\t\t\tUbuntu,\n\t\t\t\t\tCantarell,\n\t\t\t\t\t'Open Sans',\n\t\t\t\t\t'Helvetica Neue',\n\t\t\t\t\tsans-serif;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\theight: 100vh;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t.error {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmax-width: 32rem;\n\t\t\t\tmargin: 0 1rem;\n\t\t\t}\n\n\t\t\t.status {\n\t\t\t\tfont-weight: 200;\n\t\t\t\tfont-size: 3rem;\n\t\t\t\tline-height: 1;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -0.05rem;\n\t\t\t}\n\n\t\t\t.message {\n\t\t\t\tborder-left: 1px solid var(--divider);\n\t\t\t\tpadding: 0 0 0 1rem;\n\t\t\t\tmargin: 0 0 0 1rem;\n\t\t\t\tmin-height: 2.5rem;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.message h1 {\n\t\t\t\tfont-weight: 400;\n\t\t\t\tfont-size: 1em;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t@media (prefers-color-scheme: dark) {\n\t\t\t\tbody {\n\t\t\t\t\t--bg: #222;\n\t\t\t\t\t--fg: #ddd;\n\t\t\t\t\t--divider: #666;\n\t\t\t\t}\n\t\t\t}\n\t\t</style>\n\t</head>\n\t<body>\n\t\t<div class=\"error\">\n\t\t\t<span class=\"status\">" + status + "</span>\n\t\t\t<div class=\"message\">\n\t\t\t\t<h1>" + message + "</h1>\n\t\t\t</div>\n\t\t</div>\n\t</body>\n</html>\n"
	},
	version_hash: "6017jw"
};

export async function get_hooks() {
	let handle;
	let handleFetch;
	let handleError;
	let init;
	

	let reroute;
	let transport;
	

	return {
		handle,
		handleFetch,
		handleError,
		init,
		reroute,
		transport
	};
}

export { set_assets, set_building, set_manifest, set_prerendering, set_private_env, set_public_env, set_read_implementation, set_safe_public_env };
