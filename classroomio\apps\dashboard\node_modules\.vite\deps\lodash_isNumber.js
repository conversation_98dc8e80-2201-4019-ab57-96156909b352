import {
  require_baseGetTag,
  require_isObjectLike
} from "./chunk-BEY4UYWZ.js";
import {
  __commonJS
} from "./chunk-2GTGKKMZ.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isNumber.js
var require_isNumber = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isNumber.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var numberTag = "[object Number]";
    function isNumber(value) {
      return typeof value == "number" || isObjectLike(value) && baseGetTag(value) == numberTag;
    }
    module.exports = isNumber;
  }
});
export default require_isNumber();
//# sourceMappingURL=lodash_isNumber.js.map
