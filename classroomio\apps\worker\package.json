{"name": "@classroomio/worker", "version": "1.0.0", "description": "Background worker for ClassroomIO", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "dependencies": {"bull": "^4.11.4", "ioredis": "^5.3.2", "nodemailer": "^6.9.7", "sharp": "^0.32.6", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "@supabase/supabase-js": "^2.38.4", "openai": "^4.20.1", "aws-sdk": "^2.1490.0", "winston": "^3.11.0", "cron": "^3.1.6"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "eslint": "^8.53.0", "prettier": "^3.0.3"}, "keywords": ["classroomio", "worker", "background-jobs", "queue"], "author": "ClassroomIO Team", "license": "MIT"}