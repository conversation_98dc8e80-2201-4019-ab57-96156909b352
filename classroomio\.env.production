# ClassroomIO Production Environment Configuration
# IMPORTANT: Replace all placeholder values with actual production values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=production
APP_NAME=ClassroomIO
APP_URL=https://classroomio.com
API_URL=https://classroomio.com/api

# Server Configuration
PORT=3000
HOST=0.0.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Supabase Database URL - REPLACE WITH ACTUAL PRODUCTION URL
DATABASE_URL=**********************************************************************************************/postgres

# Supabase Configuration - REPLACE WITH ACTUAL PRODUCTION VALUES
SUPABASE_URL=https://REPLACE_WITH_PROJECT_ID.supabase.co
SUPABASE_ANON_KEY=REPLACE_WITH_ACTUAL_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=REPLACE_WITH_ACTUAL_SERVICE_ROLE_KEY

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=REPLACE_WITH_ACTUAL_REDIS_PASSWORD

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=REPLACE_WITH_ACTUAL_JWT_SECRET_MINIMUM_32_CHARACTERS
JWT_EXPIRES_IN=7d
SESSION_SECRET=REPLACE_WITH_ACTUAL_SESSION_SECRET_MINIMUM_32_CHARACTERS

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SMTP Settings - REPLACE WITH ACTUAL PRODUCTION SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=REPLACE_WITH_ACTUAL_EMAIL
SMTP_PASS=REPLACE_WITH_ACTUAL_APP_PASSWORD

# Email Templates
FROM_EMAIL=<EMAIL>
FROM_NAME=ClassroomIO

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# AWS S3 Configuration - REPLACE WITH ACTUAL PRODUCTION VALUES
AWS_ACCESS_KEY_ID=REPLACE_WITH_ACTUAL_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=REPLACE_WITH_ACTUAL_AWS_SECRET_KEY
AWS_REGION=us-east-1
AWS_S3_BUCKET=classroomio-production-uploads

# =============================================================================
# VIDEO STREAMING CONFIGURATION
# =============================================================================
# Cloudflare Stream - REPLACE WITH ACTUAL PRODUCTION VALUES
CLOUDFLARE_STREAM_API_TOKEN=REPLACE_WITH_ACTUAL_CLOUDFLARE_TOKEN
CLOUDFLARE_ACCOUNT_ID=REPLACE_WITH_ACTUAL_CLOUDFLARE_ACCOUNT_ID

# Video Processing
VIDEO_PROCESSING_ENABLED=true
VIDEO_TRANSCODING_ENABLED=true
VIDEO_THUMBNAIL_ENABLED=true

# Video Security
VIDEO_WATERMARK_ENABLED=true
VIDEO_DRM_ENABLED=true
VIDEO_DOWNLOAD_PROTECTION=true

# =============================================================================
# LIVE STREAMING CONFIGURATION
# =============================================================================
# Daily.co Configuration - REPLACE WITH ACTUAL PRODUCTION VALUES
DAILY_API_KEY=REPLACE_WITH_ACTUAL_DAILY_API_KEY
DAILY_DOMAIN=REPLACE_WITH_ACTUAL_DAILY_DOMAIN

# =============================================================================
# AI INTEGRATION
# =============================================================================
# OpenAI Configuration - REPLACE WITH ACTUAL PRODUCTION VALUES
OPENAI_API_KEY=REPLACE_WITH_ACTUAL_OPENAI_API_KEY
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# AI Features
AI_DOUBT_CATEGORIZATION=true
AI_CONTENT_GENERATION=true
AI_ANALYTICS_INSIGHTS=true

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================
# Metabase Configuration
METABASE_URL=http://classroomio-metabase:3000
METABASE_API_KEY=REPLACE_WITH_ACTUAL_METABASE_API_KEY
METABASE_USERNAME=<EMAIL>
METABASE_PASSWORD=REPLACE_WITH_ACTUAL_METABASE_PASSWORD

# Google Analytics - REPLACE WITH ACTUAL PRODUCTION VALUES
GA_TRACKING_ID=REPLACE_WITH_ACTUAL_GA_TRACKING_ID
GA_MEASUREMENT_ID=REPLACE_WITH_ACTUAL_GA_MEASUREMENT_ID

# Custom Analytics
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Prometheus Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Grafana Configuration
GRAFANA_URL=http://classroomio-grafana:3000
GRAFANA_USER=admin
GRAFANA_PASSWORD=REPLACE_WITH_ACTUAL_GRAFANA_PASSWORD

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log

# Error Tracking (Sentry) - REPLACE WITH ACTUAL PRODUCTION VALUES
SENTRY_DSN=REPLACE_WITH_ACTUAL_SENTRY_DSN
SENTRY_ENVIRONMENT=production

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================
# Slack Integration - REPLACE WITH ACTUAL PRODUCTION VALUES
SLACK_WEBHOOK_URL=REPLACE_WITH_ACTUAL_SLACK_WEBHOOK
SLACK_BOT_TOKEN=REPLACE_WITH_ACTUAL_SLACK_BOT_TOKEN

# Push Notifications - REPLACE WITH ACTUAL PRODUCTION VALUES
VAPID_PUBLIC_KEY=REPLACE_WITH_ACTUAL_VAPID_PUBLIC_KEY
VAPID_PRIVATE_KEY=REPLACE_WITH_ACTUAL_VAPID_PRIVATE_KEY
VAPID_SUBJECT=mailto:<EMAIL>

# =============================================================================
# PAYMENT INTEGRATION
# =============================================================================
# Stripe Configuration - REPLACE WITH ACTUAL PRODUCTION VALUES
STRIPE_PUBLISHABLE_KEY=pk_live_REPLACE_WITH_ACTUAL_STRIPE_PUBLISHABLE_KEY
STRIPE_SECRET_KEY=sk_live_REPLACE_WITH_ACTUAL_STRIPE_SECRET_KEY
STRIPE_WEBHOOK_SECRET=whsec_REPLACE_WITH_ACTUAL_STRIPE_WEBHOOK_SECRET

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS Settings
CORS_ORIGIN=https://classroomio.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Headers
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true

# Device Security
DEVICE_LOCKING_ENABLED=true
MAX_DEVICES_PER_USER=3

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Backup Settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=classroomio-production-backups

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
# Caching
CACHE_TTL=3600
CACHE_MAX_SIZE=100MB

# Database Connection Pool
DB_POOL_MIN=5
DB_POOL_MAX=50

# Worker Configuration
WORKER_CONCURRENCY=10
WORKER_TIMEOUT=300000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Feature Toggles
FEATURE_LIVE_STREAMING=true
FEATURE_AI_INTEGRATION=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_VIDEO_SECURITY=true
FEATURE_MOBILE_APP=true
FEATURE_BLOCKCHAIN_CERTIFICATES=false

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
TIMEZONE=UTC
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,hi

# =============================================================================
# SSL CONFIGURATION
# =============================================================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/nginx/ssl/classroomio.crt
SSL_KEY_PATH=/etc/nginx/ssl/classroomio.key

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================
# Organization Settings
DEFAULT_ORG_NAME=ClassroomIO Academy
DEFAULT_ORG_LOGO=https://classroomio.com/logo.png
DEFAULT_ORG_THEME=blue

# Branding
CUSTOM_BRANDING_ENABLED=true
CUSTOM_LOGO_URL=https://classroomio.com/logo.png
CUSTOM_FAVICON_URL=https://classroomio.com/favicon.ico
CUSTOM_PRIMARY_COLOR=#3B82F6
