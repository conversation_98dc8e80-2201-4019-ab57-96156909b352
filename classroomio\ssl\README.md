# SSL Certificates Directory

This directory contains SSL certificates for ClassroomIO production deployment.

## Required Files

For production deployment, you need to place the following files in this directory:

### Main Domain Certificate
- `classroomio.crt` - SSL certificate for main domain
- `classroomio.key` - Private key for main domain

### Analytics Subdomain Certificate (if using separate subdomain)
- `analytics.classroomio.crt` - SSL certificate for analytics subdomain
- `analytics.classroomio.key` - Private key for analytics subdomain

## Certificate Generation

### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Generate certificate for main domain
sudo certbot certonly --nginx -d classroomio.com -d www.classroomio.com

# Generate certificate for analytics subdomain
sudo certbot certonly --nginx -d analytics.classroomio.com

# Copy certificates to this directory
sudo cp /etc/letsencrypt/live/classroomio.com/fullchain.pem ./classroomio.crt
sudo cp /etc/letsencrypt/live/classroomio.com/privkey.pem ./classroomio.key
sudo cp /etc/letsencrypt/live/analytics.classroomio.com/fullchain.pem ./analytics.classroomio.crt
sudo cp /etc/letsencrypt/live/analytics.classroomio.com/privkey.pem ./analytics.classroomio.key

# Set proper permissions
sudo chown root:root *.crt *.key
sudo chmod 644 *.crt
sudo chmod 600 *.key
```

### Using Self-Signed Certificates (Development Only)

```bash
# Generate self-signed certificate for development
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout classroomio.key \
  -out classroomio.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Generate for analytics subdomain
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout analytics.classroomio.key \
  -out analytics.classroomio.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=analytics.localhost"
```

## Certificate Renewal

### Automatic Renewal with Let's Encrypt

```bash
# Add to crontab for automatic renewal
sudo crontab -e

# Add this line to renew certificates twice daily
0 12 * * * /usr/bin/certbot renew --quiet --post-hook "docker-compose restart nginx"
```

### Manual Renewal

```bash
# Renew certificates manually
sudo certbot renew

# Copy renewed certificates
sudo cp /etc/letsencrypt/live/classroomio.com/fullchain.pem ./classroomio.crt
sudo cp /etc/letsencrypt/live/classroomio.com/privkey.pem ./classroomio.key

# Restart nginx to load new certificates
docker-compose restart nginx
```

## Security Best Practices

1. **File Permissions**: Ensure certificate files have proper permissions
   - `.crt` files: 644 (readable by all)
   - `.key` files: 600 (readable by owner only)

2. **Regular Renewal**: Set up automatic certificate renewal

3. **Strong Ciphers**: Use modern TLS configurations (already configured in nginx.conf)

4. **HSTS**: HTTP Strict Transport Security is enabled in nginx configuration

5. **Certificate Monitoring**: Monitor certificate expiration dates

## Troubleshooting

### Certificate Verification

```bash
# Check certificate details
openssl x509 -in classroomio.crt -text -noout

# Check certificate expiration
openssl x509 -in classroomio.crt -noout -dates

# Test SSL configuration
curl -I https://classroomio.com
```

### Common Issues

1. **Permission Denied**: Check file permissions and ownership
2. **Certificate Expired**: Renew certificates using certbot
3. **Wrong Domain**: Ensure certificate matches your domain name
4. **Mixed Content**: Ensure all resources are loaded over HTTPS

## Development Setup

For development, you can use self-signed certificates or disable HTTPS entirely by:

1. Using HTTP only (not recommended for production-like testing)
2. Using self-signed certificates with browser security exceptions
3. Using a local CA like mkcert for trusted local certificates

```bash
# Using mkcert for local development
brew install mkcert  # or appropriate package manager
mkcert -install
mkcert localhost 127.0.0.1 ::1
mv localhost+2.pem classroomio.crt
mv localhost+2-key.pem classroomio.key
```

## Support

For SSL certificate issues:
- Check nginx error logs: `docker-compose logs nginx`
- Verify DNS configuration points to your server
- Ensure firewall allows ports 80 and 443
- Contact your domain registrar for DNS issues
