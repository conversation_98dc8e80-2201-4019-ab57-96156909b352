# Adding Translation To Your Code - Developer's Guide

In this guide, we'll walk through the process of adding translations to your code. This enables multi-translation support on the platform, enhancing the experience for every users.

## Obtain Your RapidAPI Key

Follow these steps:

1. Open your browser and navigate to https://rapidapi.com/hub.
2. Sign up using your GitHub account or email, whichever you prefer.
3. Search for 'Google Translate' then click on the 'Pricing' tab.
4. Choose a subscription plan and return to the 'Endpoints' tab.
5. Focus on the last tab (on your right).
6. Your key is located in the 'headers' object under 'X-RapidAPI-Key'.
7. Go to your `.env` file in the `/apps/dashboard` directory and assign your key to this variable `RAPID_API_KEY`

## Update Your English File

Once you have your API Key, follow these steps to add a translation:

1. Open the English file named `en` located in `apps\dashboard\src\lib\utils\translations\en.json`.
2. Create an object with a name corresponding to the translation you wish to add.
   Example: `"tabs": { "profile": "Profile", "landing_page": "Landing Page" }`.
3. Now, navigate to the file where you want to insert the translation.
4. Import the translation function:

````bash copy
import { t } from '$lib/utils/functions/translations';
````

5. Locate the line where you want to add the translation and use the following syntax: `$t('path to the value in the object')`.
   Example: `<p>{$t('tabs.profile')}</p>` (This will output "Profile").
   Note: `$t('')` returns a string.

## Run the translate script

Once you've updated the English file, you need to update the other files. To do this, run a command. Firstly ensure you're still in the `/apps/dashboard` directory, then execute:

````bash copy
pnpm run script:translate
````

This command runs a script that updates the other translation files.

