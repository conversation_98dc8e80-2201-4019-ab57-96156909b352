# ClassroomIO Windows Development Setup

This guide provides Windows-specific instructions for setting up and running ClassroomIO in development mode.

## 🚀 Quick Start

### **Automated Startup (Recommended)**

1. **Open Command Prompt or PowerShell** as Administrator (recommended)
2. **Navigate to the ClassroomIO directory**:
   ```cmd
   cd path\to\classroomio
   ```
3. **Run the automated startup script**:
   ```cmd
   start-dev.bat
   ```

The script will automatically:
- ✅ Check Node.js and package manager installation
- ✅ Set up environment configuration
- ✅ Install dependencies
- ✅ Start the development server
- ✅ Open your browser to http://localhost:5173

### **Stop Development Environment**

To stop all development services:
```cmd
stop-dev.bat
```

---

## 📋 Prerequisites

### **Required Software**

1. **Node.js 18+**
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **Package Manager**
   - **pnpm (Recommended)**: `npm install -g pnpm`
   - **npm (Alternative)**: Included with Node.js

3. **Git**
   - Download from: https://git-scm.com/
   - Required for version control

### **Optional Software**

1. **Supabase CLI** (for local database):
   ```cmd
   npm install -g supabase
   ```

2. **Visual Studio Code** (recommended editor):
   - Download from: https://code.visualstudio.com/

---

## 🛠️ Manual Setup (Alternative)

If you prefer manual setup or the automated script fails:

### **Step 1: Environment Configuration**

1. **Copy environment file**:
   ```cmd
   copy .env.example .env.local
   ```

2. **Edit .env.local** with your preferred settings (optional for development)

### **Step 2: Install Dependencies**

Using pnpm (recommended):
```cmd
pnpm install
```

Using npm (alternative):
```cmd
npm install
```

### **Step 3: Start Services**

**Option A: Start with Supabase (Full Stack)**
```cmd
# Start Supabase local instance
pnpm supabase start

# Start development server
pnpm dev
```

**Option B: Start without Supabase (Frontend Only)**
```cmd
# Start development server only
pnpm dev
```

---

## 🔧 Development Scripts

### **Available Commands**

| Command | Description |
|---------|-------------|
| `start-dev.bat` | **Automated startup** - Complete development environment |
| `stop-dev.bat` | **Automated stop** - Stop all development services |
| `pnpm dev` | Start development server only |
| `pnpm build` | Build for production |
| `pnpm preview` | Preview production build |
| `pnpm test` | Run all tests |
| `pnpm lint` | Check code quality |
| `pnpm format` | Format code |

### **Supabase Commands**

| Command | Description |
|---------|-------------|
| `pnpm supabase start` | Start local Supabase |
| `pnpm supabase stop` | Stop local Supabase |
| `pnpm supabase status` | Check Supabase status |
| `pnpm supabase reset` | Reset local database |

---

## 🌐 Development URLs

Once the development server is running:

| Service | URL | Description |
|---------|-----|-------------|
| **Main App** | http://localhost:5173 | ClassroomIO application |
| **Health Check** | http://localhost:5173/health | Application health status |
| **API Health** | http://localhost:5173/api/health | API health status |
| **Supabase Studio** | http://localhost:54323 | Database management (if running) |
| **Supabase API** | http://localhost:54321 | Local Supabase API (if running) |

---

## 🐛 Troubleshooting

### **Common Issues**

#### **Issue: "Node.js not found"**
**Solution:**
1. Install Node.js from https://nodejs.org/
2. Restart Command Prompt/PowerShell
3. Verify: `node --version`

#### **Issue: "Port 5173 already in use"**
**Solution:**
1. Run `stop-dev.bat` to stop existing servers
2. Or use a different port: `pnpm dev --port 3000`
3. Check running processes: `netstat -ano | findstr :5173`

#### **Issue: "pnpm not found"**
**Solution:**
1. Install pnpm: `npm install -g pnpm`
2. Or use npm instead: Edit scripts to use `npm` instead of `pnpm`

#### **Issue: "Permission denied"**
**Solution:**
1. Run Command Prompt as Administrator
2. Or check Windows Defender/Antivirus settings
3. Ensure project folder is not in a restricted location

#### **Issue: "Dependencies installation failed"**
**Solution:**
1. Clear cache: `pnpm store prune` or `npm cache clean --force`
2. Delete node_modules: `rmdir /s node_modules`
3. Reinstall: `pnpm install` or `npm install`

#### **Issue: "Supabase connection failed"**
**Solution:**
1. Check if Supabase is running: `pnpm supabase status`
2. Start Supabase: `pnpm supabase start`
3. Check firewall settings for ports 54321-54323

### **Advanced Troubleshooting**

#### **Reset Development Environment**
```cmd
# Stop all services
stop-dev.bat

# Clean dependencies
rmdir /s /q node_modules
del pnpm-lock.yaml

# Restart
start-dev.bat
```

#### **Check Running Processes**
```cmd
# Check Node.js processes
tasklist | findstr node.exe

# Check port usage
netstat -ano | findstr :5173

# Kill specific process
taskkill /PID <process-id> /F
```

#### **Environment Variables Debug**
```cmd
# Check environment variables
echo %NODE_ENV%
echo %PATH%

# Verify .env.local exists
dir .env.local
```

---

## 🔒 Windows Security Considerations

### **Windows Defender**
- Add ClassroomIO folder to Windows Defender exclusions
- Allow Node.js through Windows Firewall when prompted

### **Execution Policy (PowerShell)**
If you encounter execution policy errors:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### **Antivirus Software**
- Whitelist the ClassroomIO project directory
- Allow Node.js and npm/pnpm executables

---

## 📁 Project Structure

```
classroomio/
├── start-dev.bat           # 🚀 Automated startup script
├── stop-dev.bat            # 🛑 Automated stop script
├── .env.local              # 🔧 Local environment config
├── .env.example            # 📋 Environment template
├── package.json            # 📦 Project dependencies
├── apps/
│   └── dashboard/          # 🎯 Main SvelteKit application
├── supabase/               # 🗄️ Database configuration
├── scripts/                # 🛠️ Utility scripts
└── docs/                   # 📚 Documentation
```

---

## 🎯 Next Steps

After successful startup:

1. **Explore the Application**:
   - Visit http://localhost:5173
   - Test authentication features
   - Navigate through the dashboard

2. **Development Workflow**:
   - Make code changes (hot reload enabled)
   - Run tests: `pnpm test`
   - Check code quality: `pnpm lint`

3. **Database Setup** (if using Supabase):
   - Access Supabase Studio: http://localhost:54323
   - Run migrations if needed
   - Seed test data

4. **API Testing**:
   - Test health endpoints
   - Use browser dev tools
   - Test API endpoints with Postman/Insomnia

---

## 📞 Support

If you encounter issues:

1. **Check the troubleshooting section above**
2. **Review the startup logs** for specific error messages
3. **Check GitHub Issues**: https://github.com/classroomio/classroomio/issues
4. **Join Discord Community**: https://discord.gg/classroomio

---

## 🎉 Success!

If you see this message after running `start-dev.bat`:

```
🎉 ClassroomIO development environment is ready!
📱 Application URL: http://localhost:5173
🔍 Health Check: http://localhost:5173/health
🔧 API Health: http://localhost:5173/api/health
```

**Congratulations! Your ClassroomIO development environment is running successfully!** 🎓✨
