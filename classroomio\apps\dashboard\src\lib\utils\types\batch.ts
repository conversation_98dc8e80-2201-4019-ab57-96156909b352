// Batch-Centric Educational Platform Types
// Date: 2025-06-30

export interface Batch {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  start_date?: string;
  end_date?: string;
  organization_id: string;
  is_active: boolean;
  batch_code?: string;
  max_students: number;
  metadata: Record<string, any>;
  subjects?: Subject[];
  members?: BatchMember[];
  total_students?: number;
  total_subjects?: number;
}

export interface Subject {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  batch_id: string;
  order: number;
  is_active: boolean;
  subject_code?: string;
  instructor_id?: string;
  metadata: Record<string, any>;
  batch?: Batch;
  instructor?: Profile;
  chapters?: Chapter[];
  total_chapters?: number;
  total_lessons?: number;
}

export interface Chapter {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  description?: string;
  subject_id: string;
  order: number;
  is_active: boolean;
  estimated_duration?: number; // in minutes
  metadata: Record<string, any>;
  subject?: Subject;
  sections?: LessonSection[];
  total_sections?: number;
  total_lessons?: number;
}

export interface LessonSection {
  id: string;
  created_at: string;
  updated_at: string;
  title?: string;
  order: number;
  course_id?: string;
  chapter_id?: string;
  chapter?: Chapter;
  lessons?: Lesson[];
  total_lessons?: number;
}

export interface BatchMember {
  id: string;
  created_at: string;
  updated_at: string;
  batch_id: string;
  profile_id: string;
  role: 'student' | 'instructor' | 'admin';
  joined_at: string;
  is_active: boolean;
  student_id?: string;
  metadata: Record<string, any>;
  batch?: Batch;
  profile?: Profile;
}

export interface StudyMaterial {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  description?: string;
  type: 'note' | 'dpp' | 'assignment' | 'reference';
  content?: string;
  file_url?: string;
  lesson_id?: string;
  chapter_id?: string;
  subject_id?: string;
  batch_id?: string;
  created_by?: string;
  is_downloadable: boolean;
  metadata: Record<string, any>;
  lesson?: Lesson;
  chapter?: Chapter;
  subject?: Subject;
  batch?: Batch;
  creator?: Profile;
}

export interface VideoContent {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  description?: string;
  video_url: string;
  thumbnail_url?: string;
  duration?: number; // in seconds
  lesson_id?: string;
  quality_options: Array<{
    quality: string;
    url: string;
    size?: number;
    bitrate?: number;
    resolution?: string;
  }>;
  is_downloadable: boolean;
  watermark_settings: {
    enabled: boolean;
    text?: string;
    position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    opacity?: number;
  };
  security_settings: {
    drm_enabled: boolean;
    download_protection: boolean;
    screenshot_protection: boolean;
    device_limit?: number;
  };
  metadata: {
    file_size?: number;
    format?: string;
    codec?: string;
    resolution?: string;
    aspect_ratio?: string;
    frame_rate?: number;
    upload_status?: 'pending' | 'processing' | 'ready' | 'failed';
    processing_progress?: number;
    [key: string]: any;
  };
  lesson?: Lesson;
}

export interface DoubtSystem {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  description: string;
  student_id: string;
  batch_id?: string;
  subject_id?: string;
  lesson_id?: string;
  status: 'open' | 'answered' | 'closed';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  images: string[];
  answered_by?: string;
  answered_at?: string;
  answer?: string;
  metadata: Record<string, any>;
  student?: Profile;
  batch?: Batch;
  subject?: Subject;
  lesson?: Lesson;
  answerer?: Profile;
}

export interface DeviceSession {
  id: string;
  created_at: string;
  updated_at: string;
  profile_id: string;
  device_fingerprint: string;
  device_info: {
    browser?: string;
    os?: string;
    device_type?: string;
    screen_resolution?: string;
  };
  is_active: boolean;
  last_activity: string;
  ip_address?: string;
  user_agent?: string;
  session_token?: string;
  profile?: Profile;
}

export interface LiveSession {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  description?: string;
  batch_id?: string;
  subject_id?: string;
  lesson_id?: string;
  instructor_id: string;
  scheduled_at: string;
  duration?: number; // in minutes
  meeting_url?: string;
  meeting_id?: string;
  meeting_password?: string;
  status: 'scheduled' | 'live' | 'ended' | 'cancelled';
  recording_url?: string;
  attendance: Array<{
    profile_id: string;
    joined_at: string;
    left_at?: string;
    duration?: number;
  }>;
  metadata: Record<string, any>;
  batch?: Batch;
  subject?: Subject;
  lesson?: Lesson;
  instructor?: Profile;
}

export interface ProgressTracking {
  id: string;
  created_at: string;
  updated_at: string;
  student_id: string;
  batch_id?: string;
  subject_id?: string;
  chapter_id?: string;
  lesson_id?: string;
  video_id?: string;
  progress_type: 'video_watch' | 'lesson_complete' | 'assignment_submit' | 'test_attempt';
  progress_value: number; // percentage or score
  time_spent: number; // in seconds
  metadata: Record<string, any>;
  student?: Profile;
  batch?: Batch;
  subject?: Subject;
  chapter?: Chapter;
  lesson?: Lesson;
  video?: VideoContent;
}

// Hierarchy Response Type
export interface BatchHierarchy {
  batch_id: string;
  batch_name: string;
  subject_id?: string;
  subject_name?: string;
  subject_order?: number;
  chapter_id?: string;
  chapter_title?: string;
  chapter_order?: number;
  section_id?: string;
  section_title?: string;
  section_order?: number;
  lesson_id?: string;
  lesson_title?: string;
  lesson_order?: number;
}

// Video Player Types
export interface VideoPlayerSettings {
  autoplay: boolean;
  muted: boolean;
  controls: boolean;
  playback_rates: number[];
  keyboard_shortcuts: boolean;
  picture_in_picture: boolean;
  fullscreen: boolean;
  quality_selector: boolean;
  captions: boolean;
  theme: 'light' | 'dark' | 'auto';
  branding: {
    logo_url?: string;
    logo_position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    watermark_text?: string;
  };
}

export interface VideoProgress {
  id: string;
  video_id: string;
  student_id: string;
  lesson_id?: string;
  current_time: number; // in seconds
  duration: number; // in seconds
  completion_percentage: number;
  watch_time: number; // total watch time in seconds
  last_watched_at: string;
  is_completed: boolean;
  engagement_data: {
    play_count: number;
    pause_count: number;
    seek_count: number;
    replay_count: number;
    quality_changes: number;
    average_playback_rate: number;
    segments_watched: Array<{
      start: number;
      end: number;
      watch_count: number;
    }>;
  };
  created_at: string;
  updated_at: string;
}

export interface DownloadItem {
  id: string;
  video_id: string;
  student_id: string;
  quality: string;
  file_size: number;
  download_url: string;
  download_token: string;
  expires_at: string;
  status: 'pending' | 'downloading' | 'completed' | 'failed' | 'expired';
  progress: number; // 0-100
  downloaded_at?: string;
  local_path?: string;
  created_at: string;
  updated_at: string;
}

export interface VideoUpload {
  id: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  upload_url: string;
  upload_status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';
  upload_progress: number; // 0-100
  processing_progress: number; // 0-100
  error_message?: string;
  video_content_id?: string;
  uploaded_by: string;
  created_at: string;
  updated_at: string;
}

export interface VideoAnalytics {
  video_id: string;
  total_views: number;
  unique_viewers: number;
  total_watch_time: number; // in seconds
  average_watch_time: number;
  completion_rate: number; // percentage
  engagement_rate: number; // percentage
  popular_segments: Array<{
    start: number;
    end: number;
    view_count: number;
  }>;
  drop_off_points: Array<{
    time: number;
    drop_off_rate: number;
  }>;
  quality_distribution: Record<string, number>;
  device_distribution: Record<string, number>;
  last_updated: string;
}

// Import existing types
import type { Profile, Lesson } from './index';
