#!/bin/bash

# ClassroomIO Production Startup Script
# Handles graceful startup, health checks, and signal handling

set -euo pipefail

# Configuration
APP_NAME="ClassroomIO"
APP_PORT="${PORT:-3000}"
APP_HOST="${HOST:-0.0.0.0}"
NODE_ENV="${NODE_ENV:-production}"
HEALTH_CHECK_RETRIES="${HEALTH_CHECK_RETRIES:-5}"
HEALTH_CHECK_INTERVAL="${HEALTH_CHECK_INTERVAL:-10}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Signal handlers
cleanup() {
    local exit_code=$?
    log_info "Received shutdown signal, cleaning up..."
    
    if [ -n "${APP_PID:-}" ]; then
        log_info "Stopping application (PID: $APP_PID)..."
        kill -TERM "$APP_PID" 2>/dev/null || true
        
        # Wait for graceful shutdown
        local count=0
        while kill -0 "$APP_PID" 2>/dev/null && [ $count -lt 30 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # Force kill if still running
        if kill -0 "$APP_PID" 2>/dev/null; then
            log_warning "Force killing application..."
            kill -KILL "$APP_PID" 2>/dev/null || true
        fi
    fi
    
    log_info "Cleanup completed"
    exit $exit_code
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT SIGQUIT

# Pre-flight checks
preflight_checks() {
    log_info "Running pre-flight checks..."
    
    # Check Node.js version
    local node_version=$(node --version)
    log_info "Node.js version: $node_version"
    
    # Check if required files exist
    if [ ! -f "package.json" ]; then
        log_error "package.json not found"
        exit 1
    fi
    
    # Check environment variables
    local required_vars=("NODE_ENV" "DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "Missing required environment variables: ${missing_vars[*]}"
        exit 1
    fi
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        log_warning "Disk usage is high: ${disk_usage}%"
    fi
    
    # Check memory
    local mem_available=$(free -m | awk 'NR==2{printf "%.0f", $7*100/$2}')
    if [ "$mem_available" -lt 10 ]; then
        log_warning "Available memory is low: ${mem_available}%"
    fi
    
    log_success "Pre-flight checks completed"
}

# Database readiness check
wait_for_database() {
    log_info "Waiting for database to be ready..."
    
    local retries=0
    local max_retries=30
    
    while [ $retries -lt $max_retries ]; do
        if node -e "
            const { createClient } = require('@supabase/supabase-js');
            const client = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);
            client.from('profile').select('count').limit(1).then(() => {
                console.log('Database ready');
                process.exit(0);
            }).catch(() => {
                process.exit(1);
            });
        " 2>/dev/null; then
            log_success "Database is ready"
            return 0
        fi
        
        retries=$((retries + 1))
        log_info "Database not ready, retrying... ($retries/$max_retries)"
        sleep 2
    done
    
    log_error "Database failed to become ready after $max_retries attempts"
    exit 1
}

# Application health check
health_check() {
    local retries=0
    
    while [ $retries -lt $HEALTH_CHECK_RETRIES ]; do
        if node health-check.js >/dev/null 2>&1; then
            return 0
        fi
        
        retries=$((retries + 1))
        log_info "Health check failed, retrying... ($retries/$HEALTH_CHECK_RETRIES)"
        sleep $HEALTH_CHECK_INTERVAL
    done
    
    return 1
}

# Start the application
start_application() {
    log_info "Starting $APP_NAME..."
    log_info "Environment: $NODE_ENV"
    log_info "Host: $APP_HOST"
    log_info "Port: $APP_PORT"
    
    # Start the application in background
    if [ -f "build/index.js" ]; then
        # SvelteKit production build
        node build/index.js &
        APP_PID=$!
    elif [ -f "dist/index.js" ]; then
        # Alternative build location
        node dist/index.js &
        APP_PID=$!
    else
        # Fallback to npm start
        npm start &
        APP_PID=$!
    fi
    
    log_info "Application started with PID: $APP_PID"
    
    # Wait for application to be ready
    log_info "Waiting for application to be ready..."
    sleep 5
    
    # Perform health check
    if health_check; then
        log_success "$APP_NAME is healthy and ready to serve requests"
    else
        log_error "$APP_NAME failed health check"
        cleanup
        exit 1
    fi
}

# Monitor application
monitor_application() {
    log_info "Monitoring application health..."
    
    while true; do
        # Check if process is still running
        if ! kill -0 "$APP_PID" 2>/dev/null; then
            log_error "Application process died unexpectedly"
            exit 1
        fi
        
        # Periodic health check
        if ! health_check; then
            log_error "Application health check failed"
            exit 1
        fi
        
        sleep 30
    done
}

# Main execution
main() {
    log_info "Starting $APP_NAME production server..."
    
    # Run pre-flight checks
    preflight_checks
    
    # Wait for dependencies
    wait_for_database
    
    # Start application
    start_application
    
    # Monitor application
    monitor_application
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
