import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
// @ts-ignore
type MatcherParam<M> = M extends (param : string) => param is infer U ? U extends string ? U : string : string;
type RouteParams = {  };
type RouteId = '/';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];
type OutputDataShape<T> = MaybeWithVoid<Omit<App.PageData, RequiredKeys<T>> & Partial<Pick<App.PageData, keyof T & keyof App.PageData>> & Record<string, any>>
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
export type Snapshot<T = any> = Kit.Snapshot<T>;
type PageParentData = EnsureDefined<LayoutData>;
type LayoutRouteId = RouteId | "/" | "/404" | "/batches" | "/batches/[id]" | "/batches/[id]/analytics" | "/batches/[id]/communication" | "/batches/[id]/live-sessions" | "/batches/[id]/live-sessions/[sessionId]" | "/batches/[id]/subjects/[subjectId]/chapters/[chapterId]/lessons/[lessonId]" | "/course/[slug]" | "/courses/[id]" | "/courses/[id]/attendance" | "/courses/[id]/certificates" | "/courses/[id]/landingpage" | "/courses/[id]/lessons" | "/courses/[id]/lessons/[...lessonParams]" | "/courses/[id]/marks" | "/courses/[id]/people" | "/courses/[id]/people/[personId]" | "/courses/[id]/settings" | "/courses/[id]/submissions" | "/forgot" | "/home" | "/invite/s/[hash]" | "/invite/t/[hash]" | "/lms" | "/lms/community" | "/lms/community/ask" | "/lms/community/[slug]" | "/lms/exercises" | "/lms/explore" | "/lms/mylearning" | "/lms/settings" | "/login" | "/logout" | "/minimal" | "/onboarding" | "/org/[orgId]/analytics" | "/org/[orgId]/settings/communication" | "/org/[slug]" | "/org/[slug]/audience" | "/org/[slug]/audience/[...params]" | "/org/[slug]/community" | "/org/[slug]/community/ask" | "/org/[slug]/community/[slug]" | "/org/[slug]/courses" | "/org/[slug]/quiz" | "/org/[slug]/quiz/[slug]" | "/org/[slug]/settings" | "/org/[slug]/settings/customize-lms" | "/org/[slug]/settings/domains" | "/org/[slug]/settings/teams" | "/org/[slug]/setup" | "/profile/[id]" | "/reset" | "/security" | "/security/policies" | "/signup" | "/simple" | "/test" | "/upgrade" | null
type LayoutParams = RouteParams & { id?: string; sessionId?: string; subjectId?: string; chapterId?: string; lessonId?: string; slug?: string; lessonParams?: string; personId?: string; hash?: string; orgId?: string; params?: string }
type LayoutServerParentData = EnsureDefined<{}>;
type LayoutParentData = EnsureDefined<{}>;

export type PageServerData = null;
export type PageData = Expand<PageParentData>;
export type LayoutServerLoad<OutputData extends Partial<App.PageData> & Record<string, any> | void = Partial<App.PageData> & Record<string, any> | void> = Kit.ServerLoad<LayoutParams, LayoutServerParentData, OutputData, LayoutRouteId>;
export type LayoutServerLoadEvent = Parameters<LayoutServerLoad>[0];
export type LayoutServerData = Expand<OptionalUnion<EnsureDefined<Kit.AwaitedProperties<Awaited<ReturnType<typeof import('../../../../src/routes/+layout.server.js').load>>>>>>;
export type LayoutLoad<OutputData extends OutputDataShape<LayoutParentData> = OutputDataShape<LayoutParentData>> = Kit.Load<LayoutParams, LayoutServerData, LayoutParentData, OutputData, LayoutRouteId>;
export type LayoutLoadEvent = Parameters<LayoutLoad>[0];
export type LayoutData = Expand<Omit<LayoutParentData, keyof Kit.AwaitedProperties<Awaited<ReturnType<typeof import('../../../../src/routes/+layout.js').load>>>> & OptionalUnion<EnsureDefined<Kit.AwaitedProperties<Awaited<ReturnType<typeof import('../../../../src/routes/+layout.js').load>>>>>>;
export type RequestEvent = Kit.RequestEvent<RouteParams, RouteId>;