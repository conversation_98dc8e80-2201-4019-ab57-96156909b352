<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { DashboardWidget, ThemeConfig } from '$lib/utils/types/analytics';
  import { t } from '$lib/utils/functions/translations';
  import { 
    ChevronUp, 
    ChevronDown, 
    DataTable,
    Search,
    ChevronLeft,
    ChevronRight
  } from 'carbon-icons-svelte';

  export let widget: DashboardWidget;
  export let data: any;
  export let theme: ThemeConfig;

  const dispatch = createEventDispatcher<{
    click: { widget: DashboardWidget };
  }>();

  let searchQuery = '';
  let sortColumn = '';
  let sortDirection: 'asc' | 'desc' = 'asc';
  let currentPage = 1;
  let pageSize = widget.config.page_size || 10;

  $: tableData = prepareTableData(data);
  $: columns = getColumns(tableData);
  $: filteredData = filterData(tableData, searchQuery);
  $: sortedData = sortData(filteredData, sortColumn, sortDirection);
  $: paginatedData = paginateData(sortedData, currentPage, pageSize);
  $: totalPages = Math.ceil(sortedData.length / pageSize);
  $: showPagination = widget.config.show_pagination !== false && sortedData.length > pageSize;
  $: showSearch = widget.config.show_search !== false;

  function prepareTableData(rawData: any) {
    if (!rawData) return [];
    
    if (Array.isArray(rawData)) {
      return rawData;
    } else if (rawData.data && Array.isArray(rawData.data)) {
      return rawData.data;
    } else if (rawData.rows && Array.isArray(rawData.rows)) {
      return rawData.rows;
    }
    
    return [];
  }

  function getColumns(data: any[]) {
    if (data.length === 0) return [];
    
    // If widget has predefined columns, use those
    if (widget.config.columns && Array.isArray(widget.config.columns)) {
      return widget.config.columns;
    }
    
    // Auto-generate columns from data
    const firstRow = data[0];
    return Object.keys(firstRow).map(key => ({
      field: key,
      title: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      type: inferColumnType(firstRow[key]),
      sortable: true,
      width: 'auto'
    }));
  }

  function inferColumnType(value: any): string {
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
      return 'date';
    }
    return 'text';
  }

  function filterData(data: any[], query: string) {
    if (!query.trim()) return data;
    
    const lowerQuery = query.toLowerCase();
    return data.filter(row => 
      Object.values(row).some(value => 
        String(value).toLowerCase().includes(lowerQuery)
      )
    );
  }

  function sortData(data: any[], column: string, direction: 'asc' | 'desc') {
    if (!column) return data;
    
    return [...data].sort((a, b) => {
      const aVal = a[column];
      const bVal = b[column];
      
      // Handle null/undefined values
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return direction === 'asc' ? -1 : 1;
      if (bVal == null) return direction === 'asc' ? 1 : -1;
      
      // Compare values
      let comparison = 0;
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        comparison = aVal - bVal;
      } else {
        comparison = String(aVal).localeCompare(String(bVal));
      }
      
      return direction === 'asc' ? comparison : -comparison;
    });
  }

  function paginateData(data: any[], page: number, size: number) {
    const start = (page - 1) * size;
    const end = start + size;
    return data.slice(start, end);
  }

  function handleSort(column: any) {
    if (!column.sortable) return;
    
    if (sortColumn === column.field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortColumn = column.field;
      sortDirection = 'asc';
    }
  }

  function formatCellValue(value: any, column: any): string {
    if (value == null) return '';
    
    switch (column.type) {
      case 'number':
        if (column.format === 'currency') {
          return new Intl.NumberFormat('en-US', { 
            style: 'currency', 
            currency: 'USD' 
          }).format(value);
        } else if (column.format === 'percentage') {
          return `${(value * 100).toFixed(1)}%`;
        }
        return typeof value === 'number' ? value.toLocaleString() : String(value);
      
      case 'date':
        return new Date(value).toLocaleDateString();
      
      case 'boolean':
        return value ? '✓' : '✗';
      
      default:
        return String(value);
    }
  }

  function handleRowClick(row: any) {
    dispatch('click', { widget });
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
    }
  }
</script>

<div class="table-widget h-full flex flex-col">
  {#if tableData.length === 0}
    <!-- No Data State -->
    <div class="flex items-center justify-center h-full">
      <div class="text-center">
        <DataTable size={32} class="text-gray-400 mx-auto mb-2" />
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {$t('analytics.no_table_data', { default: 'No table data available' })}
        </p>
      </div>
    </div>

  {:else}
    <!-- Search Bar -->
    {#if showSearch}
      <div class="mb-4">
        <div class="relative">
          <Search size={16} class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            bind:value={searchQuery}
            placeholder={$t('analytics.search_table', { default: 'Search table...' })}
            class="w-full pl-9 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>
    {/if}

    <!-- Table -->
    <div class="flex-1 overflow-auto">
      <table class="w-full text-sm">
        <!-- Table Header -->
        <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
          <tr>
            {#each columns as column}
              <th 
                class="px-4 py-3 text-left font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 {column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700' : ''}"
                style="width: {column.width || 'auto'};"
                on:click={() => handleSort(column)}
                on:keydown={(e) => e.key === 'Enter' && handleSort(column)}
                role={column.sortable ? 'button' : 'columnheader'}
                tabindex={column.sortable ? 0 : -1}
              >
                <div class="flex items-center space-x-2">
                  <span>{column.title}</span>
                  {#if column.sortable && sortColumn === column.field}
                    <svelte:component 
                      this={sortDirection === 'asc' ? ChevronUp : ChevronDown} 
                      size={16} 
                      class="text-primary-600"
                    />
                  {/if}
                </div>
              </th>
            {/each}
          </tr>
        </thead>

        <!-- Table Body -->
        <tbody>
          {#each paginatedData as row, index}
            <tr 
              class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
              on:click={() => handleRowClick(row)}
              on:keydown={(e) => e.key === 'Enter' && handleRowClick(row)}
              role="button"
              tabindex="0"
            >
              {#each columns as column}
                <td 
                  class="px-4 py-3 text-gray-900 dark:text-white"
                  style="color: {theme?.text_color || '#111827'};"
                >
                  {formatCellValue(row[column.field], column)}
                </td>
              {/each}
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {#if showPagination}
      <div class="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          {$t('analytics.showing', { default: 'Showing' })} 
          {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, sortedData.length)} 
          {$t('analytics.of', { default: 'of' })} {sortedData.length} 
          {$t('analytics.results', { default: 'results' })}
        </div>

        <div class="flex items-center space-x-2">
          <!-- Previous Page -->
          <button
            on:click={() => goToPage(currentPage - 1)}
            disabled={currentPage === 1}
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft size={16} />
          </button>

          <!-- Page Numbers -->
          {#each Array.from({length: Math.min(5, totalPages)}, (_, i) => {
            const start = Math.max(1, currentPage - 2);
            return start + i;
          }).filter(page => page <= totalPages) as page}
            <button
              on:click={() => goToPage(page)}
              class="px-3 py-1 text-sm rounded {currentPage === page 
                ? 'bg-primary-600 text-white' 
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'}"
              style={currentPage === page ? `background-color: ${theme?.primary_color || '#3B82F6'};` : ''}
            >
              {page}
            </button>
          {/each}

          <!-- Next Page -->
          <button
            on:click={() => goToPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronRight size={16} />
          </button>
        </div>
      </div>
    {/if}

    <!-- Table Summary -->
    {#if data?.summary}
      <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div class="text-lg font-semibold text-gray-900 dark:text-white">
              {sortedData.length}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {$t('analytics.total_rows', { default: 'Total Rows' })}
            </div>
          </div>

          {#if data.summary.total_value !== undefined}
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {data.summary.total_value.toLocaleString()}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {$t('analytics.total_value', { default: 'Total Value' })}
              </div>
            </div>
          {/if}

          {#if data.summary.average_value !== undefined}
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {data.summary.average_value.toFixed(2)}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {$t('analytics.average_value', { default: 'Average' })}
              </div>
            </div>
          {/if}

          <div>
            <div class="text-lg font-semibold text-gray-900 dark:text-white">
              {currentPage}/{totalPages}
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {$t('analytics.page', { default: 'Page' })}
            </div>
          </div>
        </div>
      </div>
    {/if}
  {/if}
</div>

<style>
  .table-widget table {
    border-collapse: collapse;
  }

  .table-widget tbody tr:focus {
    outline: 2px solid var(--primary-color, #3B82F6);
    outline-offset: -2px;
  }

  .table-widget th:focus {
    outline: 2px solid var(--primary-color, #3B82F6);
    outline-offset: -2px;
  }
</style>
