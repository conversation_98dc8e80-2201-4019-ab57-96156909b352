{"version": 3, "sources": ["../../../../../node_modules/.pnpm/just-extend@6.2.0/node_modules/just-extend/index.mjs"], "sourcesContent": ["var objectExtend = extend;\n\n/*\n  var obj = {a: 3, b: 5};\n  extend(obj, {a: 4, c: 8}); // {a: 4, b: 5, c: 8}\n  obj; // {a: 4, b: 5, c: 8}\n\n  var obj = {a: 3, b: 5};\n  extend({}, obj, {a: 4, c: 8}); // {a: 4, b: 5, c: 8}\n  obj; // {a: 3, b: 5}\n\n  var arr = [1, 2, 3];\n  var obj = {a: 3, b: 5};\n  extend(obj, {c: arr}); // {a: 3, b: 5, c: [1, 2, 3]}\n  arr.push(4);\n  obj; // {a: 3, b: 5, c: [1, 2, 3, 4]}\n\n  var arr = [1, 2, 3];\n  var obj = {a: 3, b: 5};\n  extend(true, obj, {c: arr}); // {a: 3, b: 5, c: [1, 2, 3]}\n  arr.push(4);\n  obj; // {a: 3, b: 5, c: [1, 2, 3]}\n\n  extend({a: 4, b: 5}); // {a: 4, b: 5}\n  extend({a: 4, b: 5}, 3); {a: 4, b: 5}\n  extend({a: 4, b: 5}, true); {a: 4, b: 5}\n  extend('hello', {a: 4, b: 5}); // throws\n  extend(3, {a: 4, b: 5}); // throws\n*/\n\nfunction extend(/* [deep], obj1, obj2, [objn] */) {\n  var args = [].slice.call(arguments);\n  var deep = false;\n  if (typeof args[0] == 'boolean') {\n    deep = args.shift();\n  }\n  var result = args[0];\n  if (isUnextendable(result)) {\n    throw new Error('extendee must be an object');\n  }\n  var extenders = args.slice(1);\n  var len = extenders.length;\n  for (var i = 0; i < len; i++) {\n    var extender = extenders[i];\n    for (var key in extender) {\n      if (Object.prototype.hasOwnProperty.call(extender, key)) {\n        var value = extender[key];\n        if (deep && isCloneable(value)) {\n          var base = Array.isArray(value) ? [] : {};\n          result[key] = extend(\n            true,\n            Object.prototype.hasOwnProperty.call(result, key) && !isUnextendable(result[key])\n              ? result[key]\n              : base,\n            value\n          );\n        } else {\n          result[key] = value;\n        }\n      }\n    }\n  }\n  return result;\n}\n\nfunction isCloneable(obj) {\n  return Array.isArray(obj) || {}.toString.call(obj) == '[object Object]';\n}\n\nfunction isUnextendable(val) {\n  return !val || (typeof val != 'object' && typeof val != 'function');\n}\n\nexport {objectExtend as default};\n"], "mappings": ";;;AAAA,IAAI,eAAe;AA8BnB,SAAS,SAAyC;AAChD,MAAI,OAAO,CAAC,EAAE,MAAM,KAAK,SAAS;AAClC,MAAI,OAAO;AACX,MAAI,OAAO,KAAK,CAAC,KAAK,WAAW;AAC/B,WAAO,KAAK,MAAM;AAAA,EACpB;AACA,MAAI,SAAS,KAAK,CAAC;AACnB,MAAI,eAAe,MAAM,GAAG;AAC1B,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AACA,MAAI,YAAY,KAAK,MAAM,CAAC;AAC5B,MAAI,MAAM,UAAU;AACpB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,WAAW,UAAU,CAAC;AAC1B,aAAS,OAAO,UAAU;AACxB,UAAI,OAAO,UAAU,eAAe,KAAK,UAAU,GAAG,GAAG;AACvD,YAAI,QAAQ,SAAS,GAAG;AACxB,YAAI,QAAQ,YAAY,KAAK,GAAG;AAC9B,cAAI,OAAO,MAAM,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC;AACxC,iBAAO,GAAG,IAAI;AAAA,YACZ;AAAA,YACA,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,KAAK,CAAC,eAAe,OAAO,GAAG,CAAC,IAC5E,OAAO,GAAG,IACV;AAAA,YACJ;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO,GAAG,IAAI;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY,KAAK;AACxB,SAAO,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,SAAS,KAAK,GAAG,KAAK;AACxD;AAEA,SAAS,eAAe,KAAK;AAC3B,SAAO,CAAC,OAAQ,OAAO,OAAO,YAAY,OAAO,OAAO;AAC1D;", "names": []}