// TypeScript Type Definitions for ClassroomIO
// Comprehensive type definitions for the application

export interface User {
  id: string;
  email: string;
  fullname: string;
  avatar_url?: string;
  role: 'admin' | 'instructor' | 'student';
  organization_id?: string;
  created_at: string;
  updated_at: string;
  is_verified: boolean;
  last_login?: string;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: NotificationSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  in_app: boolean;
}

export interface Organization {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  settings: OrganizationSettings;
  created_at: string;
  updated_at: string;
  owner_id: string;
  subscription_plan?: string;
}

export interface OrganizationSettings {
  allow_public_signup: boolean;
  require_approval: boolean;
  custom_domain?: string;
  branding: BrandingSettings;
}

export interface BrandingSettings {
  primary_color: string;
  secondary_color: string;
  logo_url?: string;
  favicon_url?: string;
}

export interface Batch {
  id: string;
  name: string;
  description?: string;
  organization_id: string;
  start_date: string;
  end_date: string;
  status: 'draft' | 'active' | 'completed' | 'archived';
  max_students?: number;
  created_at: string;
  updated_at: string;
  instructor_id: string;
  settings: BatchSettings;
}

export interface BatchSettings {
  allow_late_enrollment: boolean;
  require_prerequisites: boolean;
  auto_approve_students: boolean;
  send_welcome_email: boolean;
}

export interface Course {
  id: string;
  title: string;
  description?: string;
  batch_id: string;
  instructor_id: string;
  duration_weeks?: number;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  status: 'draft' | 'published' | 'archived';
  created_at: string;
  updated_at: string;
  thumbnail?: string;
  tags: string[];
}

export interface Lesson {
  id: string;
  title: string;
  description?: string;
  course_id: string;
  content?: string;
  video_url?: string;
  duration_minutes?: number;
  order: number;
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
  resources: LessonResource[];
}

export interface LessonResource {
  id: string;
  name: string;
  type: 'file' | 'link' | 'video' | 'document';
  url: string;
  size?: number;
  mime_type?: string;
}

export interface Assignment {
  id: string;
  title: string;
  description: string;
  course_id: string;
  due_date?: string;
  max_points: number;
  status: 'draft' | 'published' | 'closed';
  created_at: string;
  updated_at: string;
  instructions?: string;
  attachments: AssignmentAttachment[];
}

export interface AssignmentAttachment {
  id: string;
  name: string;
  url: string;
  size: number;
  mime_type: string;
}

export interface Submission {
  id: string;
  assignment_id: string;
  student_id: string;
  content?: string;
  submitted_at: string;
  grade?: number;
  feedback?: string;
  status: 'submitted' | 'graded' | 'returned';
  attachments: SubmissionAttachment[];
}

export interface SubmissionAttachment {
  id: string;
  name: string;
  url: string;
  size: number;
  mime_type: string;
}

export interface Video {
  id: string;
  title: string;
  description?: string;
  url: string;
  thumbnail?: string;
  duration: number;
  quality_levels: string[];
  subtitles: VideoSubtitle[];
  security: VideoSecurity;
  created_at: string;
  updated_at: string;
}

export interface VideoSubtitle {
  language: string;
  url: string;
  label: string;
}

export interface VideoSecurity {
  watermark_enabled: boolean;
  download_protected: boolean;
  device_restricted: boolean;
  drm_enabled: boolean;
}

export interface VideoProgress {
  id: string;
  video_id: string;
  user_id: string;
  current_time: number;
  completion_percentage: number;
  last_watched: string;
  session_id: string;
}

export interface LiveSession {
  id: string;
  title: string;
  description?: string;
  batch_id: string;
  instructor_id: string;
  scheduled_at: string;
  duration_minutes: number;
  status: 'scheduled' | 'live' | 'ended' | 'cancelled';
  room_url?: string;
  recording_url?: string;
  settings: LiveSessionSettings;
  created_at: string;
  updated_at: string;
}

export interface LiveSessionSettings {
  allow_recording: boolean;
  require_approval: boolean;
  max_participants: number;
  enable_chat: boolean;
  enable_screen_share: boolean;
}

export interface Doubt {
  id: string;
  title: string;
  description: string;
  batch_id: string;
  subject_id?: string;
  user_id: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  category?: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  attachments: DoubtAttachment[];
}

export interface DoubtAttachment {
  id: string;
  filename: string;
  url: string;
  size: number;
  mime_type: string;
}

export interface DoubtResponse {
  id: string;
  doubt_id: string;
  responder_id: string;
  response: string;
  created_at: string;
  updated_at: string;
  attachments: DoubtResponseAttachment[];
}

export interface DoubtResponseAttachment {
  id: string;
  filename: string;
  url: string;
  size: number;
  mime_type: string;
}

export interface AnalyticsEvent {
  id: string;
  event_type: string;
  event_category: string;
  event_action: string;
  event_label?: string;
  event_value?: number;
  user_id?: string;
  session_id: string;
  properties: Record<string, any>;
  context: EventContext;
  timestamp: string;
}

export interface EventContext {
  user_agent: string;
  ip_address: string;
  referrer?: string;
  page_url: string;
  device_type: 'desktop' | 'mobile' | 'tablet';
  browser: string;
  os: string;
}

export interface LearningAnalytics {
  id: string;
  user_id: string;
  organization_id: string;
  batch_id: string;
  date: string;
  study_time_minutes: number;
  videos_completed: number;
  assignments_completed: number;
  forum_posts: number;
  doubts_resolved: number;
  engagement_score: number;
  progress_percentage: number;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  checks: Record<string, HealthCheck>;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  version: string;
}

export interface HealthCheck {
  status: 'healthy' | 'warning' | 'error';
  message?: string;
  responseTime?: number;
  statusCode?: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  pagination?: PaginationInfo;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any[];
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterForm {
  fullname: string;
  email: string;
  password: string;
  confirmPassword: string;
  organization_id?: string;
}

export interface CourseForm {
  title: string;
  description?: string;
  batch_id: string;
  duration_weeks?: number;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  thumbnail?: File;
  tags: string[];
}

// Store Types
export interface AppStore {
  user: User | null;
  organization: Organization | null;
  currentBatch: Batch | null;
  theme: 'light' | 'dark' | 'system';
  loading: boolean;
  error: string | null;
}

// Component Props Types
export interface VideoPlayerProps {
  src: string;
  title?: string;
  thumbnail?: string;
  autoplay?: boolean;
  controls?: boolean;
  watermark?: string;
  onProgress?: (progress: VideoProgress) => void;
  onComplete?: () => void;
}

export interface AnalyticsDashboardProps {
  type: 'student' | 'instructor' | 'admin';
  batch_id?: string;
  user_id?: string;
  date_range?: {
    start_date: string;
    end_date: string;
  };
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Database Table Types (for Supabase)
export interface Database {
  public: {
    Tables: {
      profile: {
        Row: User;
        Insert: Omit<User, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<User, 'id' | 'created_at'>>;
      };
      organization: {
        Row: Organization;
        Insert: Omit<Organization, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Organization, 'id' | 'created_at'>>;
      };
      batch: {
        Row: Batch;
        Insert: Omit<Batch, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Batch, 'id' | 'created_at'>>;
      };
      // Add more table types as needed
    };
  };
}
