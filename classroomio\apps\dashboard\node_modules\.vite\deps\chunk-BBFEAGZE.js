import {
  SvelteComponentDev,
  add_location,
  append_hydration_dev,
  assign,
  attr_dev,
  children,
  claim_svg_element,
  claim_text,
  compute_rest_props,
  detach_dev,
  dispatch_dev,
  exclude_internal_props,
  get_spread_update,
  init,
  insert_hydration_dev,
  noop,
  safe_not_equal,
  set_data_dev,
  set_svg_attributes,
  svg_element,
  text,
  validate_slots
} from "./chunk-E4ZC5ETH.js";

// ../../node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/MachineLearningModel.svelte
var file = "C:/Users/<USER>/Downloads/New folder (2)/classroomio/node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/MachineLearningModel.svelte";
function create_if_block(ctx) {
  let title_1;
  let t;
  const block = {
    c: function create() {
      title_1 = svg_element("title");
      t = text(
        /*title*/
        ctx[1]
      );
      this.h();
    },
    l: function claim(nodes) {
      title_1 = claim_svg_element(nodes, "title", {});
      var title_1_nodes = children(title_1);
      t = claim_text(
        title_1_nodes,
        /*title*/
        ctx[1]
      );
      title_1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      add_location(title_1, file, 22, 13, 543);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, title_1, anchor);
      append_hydration_dev(title_1, t);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*title*/
      2)
        set_data_dev(
          t,
          /*title*/
          ctx2[1]
        );
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(title_1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block.name,
    type: "if",
    source: "(23:2) {#if title}",
    ctx
  });
  return block;
}
function create_fragment(ctx) {
  let svg;
  let path;
  let if_block = (
    /*title*/
    ctx[1] && create_if_block(ctx)
  );
  let svg_levels = [
    { xmlns: "http://www.w3.org/2000/svg" },
    { viewBox: "0 0 32 32" },
    { fill: "currentColor" },
    { preserveAspectRatio: "xMidYMid meet" },
    { width: (
      /*size*/
      ctx[0]
    ) },
    { height: (
      /*size*/
      ctx[0]
    ) },
    /*attributes*/
    ctx[2],
    /*$$restProps*/
    ctx[3]
  ];
  let svg_data = {};
  for (let i = 0; i < svg_levels.length; i += 1) {
    svg_data = assign(svg_data, svg_levels[i]);
  }
  const block = {
    c: function create() {
      svg = svg_element("svg");
      if (if_block)
        if_block.c();
      path = svg_element("path");
      this.h();
    },
    l: function claim(nodes) {
      svg = claim_svg_element(nodes, "svg", {
        xmlns: true,
        viewBox: true,
        fill: true,
        preserveAspectRatio: true,
        width: true,
        height: true
      });
      var svg_nodes = children(svg);
      if (if_block)
        if_block.l(svg_nodes);
      path = claim_svg_element(svg_nodes, "path", { "stroke-width": true, d: true });
      children(path).forEach(detach_dev);
      svg_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(path, "stroke-width", "0");
      attr_dev(path, "d", "m27,19c1.6543,0,3-1.3457,3-3s-1.3457-3-3-3c-1.302,0-2.4016.8384-2.8157,2h-5.7703l7.3008-7.3008c.3911.1875.8235.3008,1.2852.3008,1.6543,0,3-1.3457,3-3s-1.3457-3-3-3-3,1.3457-3,3c0,.4619.1135.894.3005,1.2852l-8.3005,8.3008v-6.5859c0-1.1025.897-2,2-2h2v-2h-2c-1.2002,0-2.2661.5425-3,1.3823-.7339-.8398-1.7998-1.3823-3-1.3823h-1c-4.9624,0-9,4.0371-9,9v6c0,4.9629,4.0376,9,9,9h1c1.2002,0,2.2661-.5425,3-1.3823.7339.8398,1.7998,1.3823,3,1.3823h2v-2h-2c-1.103,0-2-.8975-2-2v-6.5859l8.3005,8.3008c-.187.3911-.3005.8232-.3005,1.2852,0,1.6543,1.3457,3,3,3s3-1.3457,3-3-1.3457-3-3-3c-.4617,0-.894.1133-1.2852.3008l-7.3008-7.3008h5.7703c.4141,1.1616,1.5137,2,2.8157,2Zm0-4c.5513,0,1,.4482,1,1s-.4487,1-1,1-1-.4482-1-1,.4487-1,1-1Zm0-11c.5515,0,1,.4487,1,1s-.4485,1-1,1-1-.4487-1-1,.4485-1,1-1Zm-13,8h-2v2h2v4h-2c-1.6543,0-3,1.3457-3,3v2h2v-2c0-.5518.4487-1,1-1h2v4c0,1.1025-.897,2-2,2h-1c-3.5195,0-6.4324-2.6133-6.9202-6h1.9202v-2h-2v-4h3c1.6543,0,3-1.3457,3-3v-2h-2v2c0,.5518-.4487,1-1,1h-2.9202c.4878-3.3867,3.4006-6,6.9202-6h1c1.103,0,2,.8975,2,2v4Zm14,15c0,.5513-.4485,1-1,1s-1-.4487-1-1,.4485-1,1-1,1,.4487,1,1Z");
      add_location(path, file, 23, 2, 573);
      set_svg_attributes(svg, svg_data);
      add_location(svg, file, 13, 0, 337);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, svg, anchor);
      if (if_block)
        if_block.m(svg, null);
      append_hydration_dev(svg, path);
    },
    p: function update(ctx2, [dirty]) {
      if (
        /*title*/
        ctx2[1]
      ) {
        if (if_block) {
          if_block.p(ctx2, dirty);
        } else {
          if_block = create_if_block(ctx2);
          if_block.c();
          if_block.m(svg, path);
        }
      } else if (if_block) {
        if_block.d(1);
        if_block = null;
      }
      set_svg_attributes(svg, svg_data = get_spread_update(svg_levels, [
        { xmlns: "http://www.w3.org/2000/svg" },
        { viewBox: "0 0 32 32" },
        { fill: "currentColor" },
        { preserveAspectRatio: "xMidYMid meet" },
        dirty & /*size*/
        1 && { width: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*size*/
        1 && { height: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*attributes*/
        4 && /*attributes*/
        ctx2[2],
        dirty & /*$$restProps*/
        8 && /*$$restProps*/
        ctx2[3]
      ]));
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(svg);
      }
      if (if_block)
        if_block.d();
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance($$self, $$props, $$invalidate) {
  let labelled;
  let attributes;
  const omit_props_names = ["size", "title"];
  let $$restProps = compute_rest_props($$props, omit_props_names);
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("MachineLearningModel", slots, []);
  let { size = 16 } = $$props;
  let { title = void 0 } = $$props;
  $$self.$$set = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    $$invalidate(3, $$restProps = compute_rest_props($$props, omit_props_names));
    if ("size" in $$new_props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$new_props)
      $$invalidate(1, title = $$new_props.title);
  };
  $$self.$capture_state = () => ({ size, title, labelled, attributes });
  $$self.$inject_state = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), $$new_props));
    if ("size" in $$props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$props)
      $$invalidate(1, title = $$new_props.title);
    if ("labelled" in $$props)
      $$invalidate(4, labelled = $$new_props.labelled);
    if ("attributes" in $$props)
      $$invalidate(2, attributes = $$new_props.attributes);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    $:
      $$invalidate(4, labelled = $$props["aria-label"] || $$props["aria-labelledby"] || title);
    $:
      $$invalidate(2, attributes = {
        "aria-hidden": labelled ? void 0 : true,
        role: labelled ? "img" : void 0,
        focusable: Number($$props["tabindex"]) === 0 ? true : void 0
      });
  };
  $$props = exclude_internal_props($$props);
  return [size, title, attributes, $$restProps, labelled];
}
var MachineLearningModel = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance, create_fragment, safe_not_equal, { size: 0, title: 1 });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "MachineLearningModel",
      options,
      id: create_fragment.name
    });
  }
  get size() {
    throw new Error("<MachineLearningModel>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<MachineLearningModel>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get title() {
    throw new Error("<MachineLearningModel>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set title(value) {
    throw new Error("<MachineLearningModel>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var MachineLearningModel_default = MachineLearningModel;

export {
  MachineLearningModel_default
};
//# sourceMappingURL=chunk-BBFEAGZE.js.map
