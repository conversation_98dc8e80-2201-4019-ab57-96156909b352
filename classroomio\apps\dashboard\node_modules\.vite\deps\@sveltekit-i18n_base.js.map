{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@sveltekit-i18n+base@1.3.7_svelte@4.2.20/node_modules/@sveltekit-i18n/base/dist/index.js"], "sourcesContent": ["var H=Object.defineProperty,q=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var K=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable;var N=(s,t,e)=>t in s?H(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,l=(s,t)=>{for(var e in t||(t={}))K.call(t,e)&&N(s,e,t[e]);if(x)for(var e of x(t))A.call(t,e)&&N(s,e,t[e]);return s},f=(s,t)=>q(s,B(t));var L=(s,t)=>{var e={};for(var a in s)K.call(s,a)&&t.indexOf(a)<0&&(e[a]=s[a]);if(s!=null&&x)for(var a of x(s))t.indexOf(a)<0&&A.call(s,a)&&(e[a]=s[a]);return e};import{derived as v,get as g,writable as m}from\"svelte/store\";var C=[\"error\",\"warn\",\"debug\"],$=({logger:s=console,level:t=C[1],prefix:e=\"[i18n]: \"})=>C.reduce((a,r,i)=>f(l({},a),{[r]:o=>C.indexOf(t)>=i&&s[r](`${e}${o}`)}),{}),c=$({}),V=s=>{c=s};var z=n=>{var d=n,{parser:s,key:t,params:e,translations:a,locale:r,fallbackLocale:i}=d,o=L(d,[\"parser\",\"key\",\"params\",\"translations\",\"locale\",\"fallbackLocale\"]);if(!t)return c.warn(`No translation key provided ('${r}' locale). Skipping translation...`),\"\";if(!r)return c.warn(`No locale provided for '${t}' key. Skipping translation...`),\"\";let u=(a[r]||{})[t];if(i&&u===void 0&&(c.debug(`No translation provided for '${t}' key in locale '${r}'. Trying fallback '${i}'`),u=(a[i]||{})[t]),u===void 0){if(c.debug(`No translation provided for '${t}' key in fallback '${i}'.`),o.hasOwnProperty(\"fallbackValue\"))return o.fallbackValue;c.warn(`No translation nor fallback found for '${t}' .`)}return s.parse(u,e,r,t)},h=(...s)=>s.length?s.filter(t=>!!t).map(t=>{let e=`${t}`.toLowerCase();try{let[a]=Intl.Collator.supportedLocalesOf(t);if(!a)throw new Error;e=a}catch(a){c.warn(`'${t}' locale is non-standard.`)}return e}):[],w=(s,t,e)=>{if(t&&Array.isArray(s))return s.map(a=>w(a,t));if(s&&typeof s==\"object\"){let a=Object.keys(s).reduce((r,i)=>{let o=s[i],n=e?`${e}.${i}`:`${i}`;return o&&typeof o==\"object\"&&!(t&&Array.isArray(o))?l(l({},r),w(o,t,n)):f(l({},r),{[n]:w(o,t)})},{});return Object.keys(a).length?a:null}return s},G=s=>s.reduce((t,{key:e,data:a,locale:r})=>{if(!a)return t;let[i]=h(r),o=f(l({},t[i]||{}),{[e]:a});return f(l({},t),{[i]:o})},{}),E=async s=>{try{let t=await Promise.all(s.map(r=>{var i=r,{loader:e}=i,a=L(i,[\"loader\"]);return new Promise(async o=>{let n;try{n=await e()}catch(d){c.error(`Failed to load translation. Verify your '${a.locale}' > '${a.key}' Loader.`),c.error(d)}o(f(l({loader:e},a),{data:n}))})}));return G(t)}catch(t){c.error(t)}return{}},W=s=>t=>{try{if(typeof t==\"string\")return t===s;if(typeof t==\"object\")return t.test(s)}catch(e){c.error(\"Invalid route config!\")}return!1},F=(s,t)=>{let e=!0;try{e=Object.keys(s).filter(a=>s[a]!==void 0).every(a=>s[a]===t[a])}catch(a){}return e};var D=1e3*60*60*24,O=class{constructor(t){this.cachedAt=0;this.loadedKeys={};this.currentRoute=m();this.config=m();this.isLoading=m(!1);this.promises=new Set;this.loading={subscribe:this.isLoading.subscribe,toPromise:(t,e)=>{let{fallbackLocale:a}=g(this.config),r=Array.from(this.promises).filter(i=>{let o=F({locale:h(t)[0],route:e},i);return a&&(o=o||F({locale:h(a)[0],route:e},i)),o}).map(({promise:i})=>i);return Promise.all(r)},get:()=>g(this.isLoading)};this.privateRawTranslations=m({});this.rawTranslations={subscribe:this.privateRawTranslations.subscribe,get:()=>g(this.rawTranslations)};this.privateTranslations=m({});this.translations={subscribe:this.privateTranslations.subscribe,get:()=>g(this.translations)};this.locales=f(l({},v([this.config,this.privateTranslations],([t,e])=>{if(!t)return[];let{loaders:a=[]}=t,r=a.map(({locale:o})=>o),i=Object.keys(e).map(o=>o);return Array.from(new Set([...h(...r),...h(...i)]))},[])),{get:()=>g(this.locales)});this.internalLocale=m();this.loaderTrigger=v([this.internalLocale,this.currentRoute],([t,e],a)=>{var r,i;t!==void 0&&e!==void 0&&!(t===((r=g(this.loaderTrigger))==null?void 0:r[0])&&e===((i=g(this.loaderTrigger))==null?void 0:i[1]))&&(c.debug(\"Triggering translation load...\"),a([t,e]))},[]);this.localeHelper=m();this.locale={subscribe:this.localeHelper.subscribe,forceSet:this.localeHelper.set,set:this.internalLocale.set,update:this.internalLocale.update,get:()=>g(this.locale)};this.initialized=v([this.locale,this.currentRoute,this.privateTranslations],([t,e,a],r)=>{g(this.initialized)||r(t!==void 0&&e!==void 0&&!!Object.keys(a).length)});this.translation=v([this.privateTranslations,this.locale,this.isLoading],([t,e,a],r)=>{let i=t[e];i&&Object.keys(i).length&&!a&&r(i)},{});this.t=f(l({},v([this.config,this.translation],r=>{var[i]=r,o=i,{parser:t,fallbackLocale:e}=o,a=L(o,[\"parser\",\"fallbackLocale\"]);return(n,...d)=>z(l({parser:t,key:n,params:d,translations:this.translations.get(),locale:this.locale.get(),fallbackLocale:e},a.hasOwnProperty(\"fallbackValue\")?{fallbackValue:a.fallbackValue}:{}))})),{get:(t,...e)=>g(this.t)(t,...e)});this.l=f(l({},v([this.config,this.translations],i=>{var[o,...n]=i,d=o,{parser:t,fallbackLocale:e}=d,a=L(d,[\"parser\",\"fallbackLocale\"]),[r]=n;return(u,b,...k)=>z(l({parser:t,key:b,params:k,translations:r,locale:u,fallbackLocale:e},a.hasOwnProperty(\"fallbackValue\")?{fallbackValue:a.fallbackValue}:{}))})),{get:(t,e,...a)=>g(this.l)(t,e,...a)});this.getLocale=t=>{let{fallbackLocale:e}=g(this.config)||{},a=t||e;if(!a)return;let r=this.locales.get();return r.find(o=>h(a).includes(o))||r.find(o=>h(e).includes(o))};this.setLocale=t=>{if(t&&t!==g(this.internalLocale))return c.debug(`Setting '${t}' locale.`),this.internalLocale.set(t),this.loading.toPromise(t,g(this.currentRoute))};this.setRoute=t=>{if(t!==g(this.currentRoute)){c.debug(`Setting '${t}' route.`),this.currentRoute.set(t);let e=g(this.internalLocale);return this.loading.toPromise(e,t)}};this.loadConfig=async t=>{await this.configLoader(t)};this.getTranslationProps=async(t=this.locale.get(),e=g(this.currentRoute))=>{let a=g(this.config);if(!a||!t)return[];let r=this.translations.get(),{loaders:i,fallbackLocale:o=\"\",cache:n=D}=a||{},d=Number.isNaN(+n)?D:+n;this.cachedAt?Date.now()>d+this.cachedAt&&(c.debug(\"Refreshing cache.\"),this.loadedKeys={},this.cachedAt=0):(c.debug(\"Setting cache timestamp.\"),this.cachedAt=Date.now());let[u,b]=h(t,o),k=r[u],I=r[b],R=(i||[]).map(j=>{var T=j,{locale:p}=T,y=L(T,[\"locale\"]);return f(l({},y),{locale:h(p)[0]})}).filter(({routes:p})=>!p||(p||[]).some(W(e))).filter(({key:p,locale:y})=>y===u&&(!k||!(this.loadedKeys[u]||[]).includes(p))||o&&y===b&&(!I||!(this.loadedKeys[b]||[]).includes(p)));if(R.length){this.isLoading.set(!0),c.debug(\"Fetching translations...\");let p=await E(R);this.isLoading.set(!1);let y=Object.keys(p).reduce((T,P)=>f(l({},T),{[P]:Object.keys(p[P])}),{}),j=R.filter(({key:T,locale:P})=>(y[P]||[]).some(S=>`${S}`.startsWith(T))).reduce((T,{key:P,locale:S})=>f(l({},T),{[S]:[...T[S]||[],P]}),{});return[p,j]}return[]};this.addTranslations=(t,e)=>{if(!t)return;let a=g(this.config),{preprocess:r}=a||{};c.debug(\"Adding translations...\");let i=Object.keys(t||{});this.privateRawTranslations.update(o=>i.reduce((n,d)=>f(l({},n),{[d]:l(l({},n[d]||{}),t[d])}),o)),this.privateTranslations.update(o=>i.reduce((n,d)=>{let u=!0,b=t[d];return typeof r==\"function\"&&(b=r(b)),(typeof r==\"function\"||r===\"none\")&&(u=!1),f(l({},n),{[d]:l(l({},n[d]||{}),u?w(b,r===\"preserveArrays\"):b)})},o)),i.forEach(o=>{let n=Object.keys(t[o]).map(d=>`${d}`.split(\".\")[0]);e&&(n=e[o]),this.loadedKeys[o]=Array.from(new Set([...this.loadedKeys[o]||[],...n||[]]))})};this.loader=async([t,e])=>{let a=this.getLocale(t)||void 0;c.debug(`Adding loader promise for '${a}' locale and '${e}' route.`);let r=(async()=>{let i=await this.getTranslationProps(a,e);i.length&&this.addTranslations(...i)})();this.promises.add({locale:a,route:e,promise:r}),r.then(()=>{a&&this.locale.get()!==a&&this.locale.forceSet(a)})};this.loadTranslations=(t,e=g(this.currentRoute)||\"\")=>{let a=this.getLocale(t);if(a)return this.setRoute(e),this.setLocale(a),this.loading.toPromise(a,e)};this.loaderTrigger.subscribe(this.loader),this.isLoading.subscribe(async e=>{e&&this.promises.size&&(await this.loading.toPromise(),this.promises.clear(),c.debug(\"Loader promises have been purged.\"))}),t&&this.loadConfig(t)}async configLoader(t){if(!t)return c.error(\"No config provided!\");let n=t,{initLocale:e,fallbackLocale:a,translations:r,log:i}=n,o=L(n,[\"initLocale\",\"fallbackLocale\",\"translations\",\"log\"]);i&&V($(i)),[e]=h(e),[a]=h(a),c.debug(\"Setting config.\"),this.config.set(l({initLocale:e,fallbackLocale:a,translations:r},o)),r&&this.addTranslations(r),e&&await this.loadTranslations(e)}};export{O as default};\n"], "mappings": ";;;;;;;;;;;AAAA,IAAI,IAAE,OAAO;AAAb,IAA4B,IAAE,OAAO;AAAiB,IAAI,IAAE,OAAO;AAA0B,IAAI,IAAE,OAAO;AAAsB,IAAI,IAAE,OAAO,UAAU;AAAvB,IAAsC,IAAE,OAAO,UAAU;AAAqB,IAAI,IAAE,CAAC,GAAE,GAAE,MAAI,KAAK,IAAE,EAAE,GAAE,GAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,IAAE,EAAE,CAAC,IAAE;AAAtF,IAAwF,IAAE,CAAC,GAAE,MAAI;AAAC,WAAQ,KAAK,MAAI,IAAE,CAAC;AAAG,MAAE,KAAK,GAAE,CAAC,KAAG,EAAE,GAAE,GAAE,EAAE,CAAC,CAAC;AAAE,MAAG;AAAE,aAAQ,KAAK,EAAE,CAAC;AAAE,QAAE,KAAK,GAAE,CAAC,KAAG,EAAE,GAAE,GAAE,EAAE,CAAC,CAAC;AAAE,SAAO;AAAC;AAA1M,IAA4M,IAAE,CAAC,GAAE,MAAI,EAAE,GAAE,EAAE,CAAC,CAAC;AAAE,IAAI,IAAE,CAAC,GAAE,MAAI;AAAC,MAAI,IAAE,CAAC;AAAE,WAAQ,KAAK;AAAE,MAAE,KAAK,GAAE,CAAC,KAAG,EAAE,QAAQ,CAAC,IAAE,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,MAAG,KAAG,QAAM;AAAE,aAAQ,KAAK,EAAE,CAAC;AAAE,QAAE,QAAQ,CAAC,IAAE,KAAG,EAAE,KAAK,GAAE,CAAC,MAAI,EAAE,CAAC,IAAE,EAAE,CAAC;AAAG,SAAO;AAAC;AAAgE,IAAI,IAAE,CAAC,SAAQ,QAAO,OAAO;AAA7B,IAA+B,IAAE,CAAC,EAAC,QAAO,IAAE,SAAQ,OAAM,IAAE,EAAE,CAAC,GAAE,QAAO,IAAE,WAAU,MAAI,EAAE,OAAO,CAAC,GAAE,GAAE,MAAI,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,OAAG,EAAE,QAAQ,CAAC,KAAG,KAAG,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAC,CAAC,GAAE,CAAC,CAAC;AAAlK,IAAoK,IAAE,EAAE,CAAC,CAAC;AAA1K,IAA4K,IAAE,OAAG;AAAC,MAAE;AAAC;AAAE,IAAI,IAAE,OAAG;AAAC,MAAI,IAAE,GAAE,EAAC,QAAO,GAAE,KAAI,GAAE,QAAO,GAAE,cAAa,GAAE,QAAO,GAAE,gBAAe,EAAC,IAAE,GAAE,IAAE,EAAE,GAAE,CAAC,UAAS,OAAM,UAAS,gBAAe,UAAS,gBAAgB,CAAC;AAAE,MAAG,CAAC;AAAE,WAAO,EAAE,KAAK,iCAAiC,CAAC,oCAAoC,GAAE;AAAG,MAAG,CAAC;AAAE,WAAO,EAAE,KAAK,2BAA2B,CAAC,gCAAgC,GAAE;AAAG,MAAI,KAAG,EAAE,CAAC,KAAG,CAAC,GAAG,CAAC;AAAE,MAAG,KAAG,MAAI,WAAS,EAAE,MAAM,gCAAgC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,GAAG,GAAE,KAAG,EAAE,CAAC,KAAG,CAAC,GAAG,CAAC,IAAG,MAAI,QAAO;AAAC,QAAG,EAAE,MAAM,gCAAgC,CAAC,sBAAsB,CAAC,IAAI,GAAE,EAAE,eAAe,eAAe;AAAE,aAAO,EAAE;AAAc,MAAE,KAAK,0CAA0C,CAAC,KAAK;AAAA,EAAC;AAAC,SAAO,EAAE,MAAM,GAAE,GAAE,GAAE,CAAC;AAAC;AAAtsB,IAAwsB,IAAE,IAAI,MAAI,EAAE,SAAO,EAAE,OAAO,OAAG,CAAC,CAAC,CAAC,EAAE,IAAI,OAAG;AAAC,MAAI,IAAE,GAAG,CAAC,GAAG,YAAY;AAAE,MAAG;AAAC,QAAG,CAAC,CAAC,IAAE,KAAK,SAAS,mBAAmB,CAAC;AAAE,QAAG,CAAC;AAAE,YAAM,IAAI;AAAM,QAAE;AAAA,EAAC,SAAO,GAAE;AAAC,MAAE,KAAK,IAAI,CAAC,2BAA2B;AAAA,EAAC;AAAC,SAAO;AAAC,CAAC,IAAE,CAAC;AAAt5B,IAAw5B,IAAE,CAAC,GAAE,GAAE,MAAI;AAAC,MAAG,KAAG,MAAM,QAAQ,CAAC;AAAE,WAAO,EAAE,IAAI,OAAG,EAAE,GAAE,CAAC,CAAC;AAAE,MAAG,KAAG,OAAO,KAAG,UAAS;AAAC,QAAI,IAAE,OAAO,KAAK,CAAC,EAAE,OAAO,CAAC,GAAE,MAAI;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,IAAE,GAAG,CAAC,IAAI,CAAC,KAAG,GAAG,CAAC;AAAG,aAAO,KAAG,OAAO,KAAG,YAAU,EAAE,KAAG,MAAM,QAAQ,CAAC,KAAG,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,CAAC,CAAC,IAAE,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,EAAE,GAAE,CAAC,EAAC,CAAC;AAAA,IAAC,GAAE,CAAC,CAAC;AAAE,WAAO,OAAO,KAAK,CAAC,EAAE,SAAO,IAAE;AAAA,EAAI;AAAC,SAAO;AAAC;AAArsC,IAAusC,IAAE,OAAG,EAAE,OAAO,CAAC,GAAE,EAAC,KAAI,GAAE,MAAK,GAAE,QAAO,EAAC,MAAI;AAAC,MAAG,CAAC;AAAE,WAAO;AAAE,MAAG,CAAC,CAAC,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,KAAG,CAAC,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,EAAC,CAAC;AAAE,SAAO,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,EAAC,CAAC;AAAC,GAAE,CAAC,CAAC;AAAv0C,IAAy0C,IAAE,OAAM,MAAG;AAAC,MAAG;AAAC,QAAI,IAAE,MAAM,QAAQ,IAAI,EAAE,IAAI,OAAG;AAAC,UAAI,IAAE,GAAE,EAAC,QAAO,EAAC,IAAE,GAAE,IAAE,EAAE,GAAE,CAAC,QAAQ,CAAC;AAAE,aAAO,IAAI,QAAQ,OAAM,MAAG;AAAC,YAAI;AAAE,YAAG;AAAC,cAAE,MAAM,EAAE;AAAA,QAAC,SAAO,GAAE;AAAC,YAAE,MAAM,4CAA4C,EAAE,MAAM,QAAQ,EAAE,GAAG,WAAW,GAAE,EAAE,MAAM,CAAC;AAAA,QAAC;AAAC,UAAE,EAAE,EAAE,EAAC,QAAO,EAAC,GAAE,CAAC,GAAE,EAAC,MAAK,EAAC,CAAC,CAAC;AAAA,MAAC,CAAC;AAAA,IAAC,CAAC,CAAC;AAAE,WAAO,EAAE,CAAC;AAAA,EAAC,SAAO,GAAE;AAAC,MAAE,MAAM,CAAC;AAAA,EAAC;AAAC,SAAM,CAAC;AAAC;AAA3oD,IAA6oD,IAAE,OAAG,OAAG;AAAC,MAAG;AAAC,QAAG,OAAO,KAAG;AAAS,aAAO,MAAI;AAAE,QAAG,OAAO,KAAG;AAAS,aAAO,EAAE,KAAK,CAAC;AAAA,EAAC,SAAO,GAAE;AAAC,MAAE,MAAM,uBAAuB;AAAA,EAAC;AAAC,SAAM;AAAE;AAAtxD,IAAwxD,IAAE,CAAC,GAAE,MAAI;AAAC,MAAI,IAAE;AAAG,MAAG;AAAC,QAAE,OAAO,KAAK,CAAC,EAAE,OAAO,OAAG,EAAE,CAAC,MAAI,MAAM,EAAE,MAAM,OAAG,EAAE,CAAC,MAAI,EAAE,CAAC,CAAC;AAAA,EAAC,SAAO,GAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAE,IAAI,IAAE,MAAI,KAAG,KAAG;AAAhB,IAAmB,IAAE,MAAK;AAAA,EAAC,YAAY,GAAE;AAAC,SAAK,WAAS;AAAE,SAAK,aAAW,CAAC;AAAE,SAAK,eAAa,SAAE;AAAE,SAAK,SAAO,SAAE;AAAE,SAAK,YAAU,SAAE,KAAE;AAAE,SAAK,WAAS,oBAAI;AAAI,SAAK,UAAQ,EAAC,WAAU,KAAK,UAAU,WAAU,WAAU,CAACA,IAAE,MAAI;AAAC,UAAG,EAAC,gBAAe,EAAC,IAAE,gBAAE,KAAK,MAAM,GAAE,IAAE,MAAM,KAAK,KAAK,QAAQ,EAAE,OAAO,OAAG;AAAC,YAAI,IAAE,EAAE,EAAC,QAAO,EAAEA,EAAC,EAAE,CAAC,GAAE,OAAM,EAAC,GAAE,CAAC;AAAE,eAAO,MAAI,IAAE,KAAG,EAAE,EAAC,QAAO,EAAE,CAAC,EAAE,CAAC,GAAE,OAAM,EAAC,GAAE,CAAC,IAAG;AAAA,MAAC,CAAC,EAAE,IAAI,CAAC,EAAC,SAAQ,EAAC,MAAI,CAAC;AAAE,aAAO,QAAQ,IAAI,CAAC;AAAA,IAAC,GAAE,KAAI,MAAI,gBAAE,KAAK,SAAS,EAAC;AAAE,SAAK,yBAAuB,SAAE,CAAC,CAAC;AAAE,SAAK,kBAAgB,EAAC,WAAU,KAAK,uBAAuB,WAAU,KAAI,MAAI,gBAAE,KAAK,eAAe,EAAC;AAAE,SAAK,sBAAoB,SAAE,CAAC,CAAC;AAAE,SAAK,eAAa,EAAC,WAAU,KAAK,oBAAoB,WAAU,KAAI,MAAI,gBAAE,KAAK,YAAY,EAAC;AAAE,SAAK,UAAQ,EAAE,EAAE,CAAC,GAAE,QAAE,CAAC,KAAK,QAAO,KAAK,mBAAmB,GAAE,CAAC,CAACA,IAAE,CAAC,MAAI;AAAC,UAAG,CAACA;AAAE,eAAM,CAAC;AAAE,UAAG,EAAC,SAAQ,IAAE,CAAC,EAAC,IAAEA,IAAE,IAAE,EAAE,IAAI,CAAC,EAAC,QAAO,EAAC,MAAI,CAAC,GAAE,IAAE,OAAO,KAAK,CAAC,EAAE,IAAI,OAAG,CAAC;AAAE,aAAO,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAAA,IAAC,GAAE,CAAC,CAAC,CAAC,GAAE,EAAC,KAAI,MAAI,gBAAE,KAAK,OAAO,EAAC,CAAC;AAAE,SAAK,iBAAe,SAAE;AAAE,SAAK,gBAAc,QAAE,CAAC,KAAK,gBAAe,KAAK,YAAY,GAAE,CAAC,CAACA,IAAE,CAAC,GAAE,MAAI;AAAC,UAAI,GAAE;AAAE,MAAAA,OAAI,UAAQ,MAAI,UAAQ,EAAEA,SAAM,IAAE,gBAAE,KAAK,aAAa,MAAI,OAAK,SAAO,EAAE,CAAC,MAAI,QAAM,IAAE,gBAAE,KAAK,aAAa,MAAI,OAAK,SAAO,EAAE,CAAC,QAAM,EAAE,MAAM,gCAAgC,GAAE,EAAE,CAACA,IAAE,CAAC,CAAC;AAAA,IAAE,GAAE,CAAC,CAAC;AAAE,SAAK,eAAa,SAAE;AAAE,SAAK,SAAO,EAAC,WAAU,KAAK,aAAa,WAAU,UAAS,KAAK,aAAa,KAAI,KAAI,KAAK,eAAe,KAAI,QAAO,KAAK,eAAe,QAAO,KAAI,MAAI,gBAAE,KAAK,MAAM,EAAC;AAAE,SAAK,cAAY,QAAE,CAAC,KAAK,QAAO,KAAK,cAAa,KAAK,mBAAmB,GAAE,CAAC,CAACA,IAAE,GAAE,CAAC,GAAE,MAAI;AAAC,sBAAE,KAAK,WAAW,KAAG,EAAEA,OAAI,UAAQ,MAAI,UAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,EAAE,MAAM;AAAA,IAAC,CAAC;AAAE,SAAK,cAAY,QAAE,CAAC,KAAK,qBAAoB,KAAK,QAAO,KAAK,SAAS,GAAE,CAAC,CAACA,IAAE,GAAE,CAAC,GAAE,MAAI;AAAC,UAAI,IAAEA,GAAE,CAAC;AAAE,WAAG,OAAO,KAAK,CAAC,EAAE,UAAQ,CAAC,KAAG,EAAE,CAAC;AAAA,IAAC,GAAE,CAAC,CAAC;AAAE,SAAK,IAAE,EAAE,EAAE,CAAC,GAAE,QAAE,CAAC,KAAK,QAAO,KAAK,WAAW,GAAE,OAAG;AAAC,UAAG,CAAC,CAAC,IAAE,GAAE,IAAE,GAAE,EAAC,QAAOA,IAAE,gBAAe,EAAC,IAAE,GAAE,IAAE,EAAE,GAAE,CAAC,UAAS,gBAAgB,CAAC;AAAE,aAAM,CAAC,MAAK,MAAI,EAAE,EAAE,EAAC,QAAOA,IAAE,KAAI,GAAE,QAAO,GAAE,cAAa,KAAK,aAAa,IAAI,GAAE,QAAO,KAAK,OAAO,IAAI,GAAE,gBAAe,EAAC,GAAE,EAAE,eAAe,eAAe,IAAE,EAAC,eAAc,EAAE,cAAa,IAAE,CAAC,CAAC,CAAC;AAAA,IAAC,CAAC,CAAC,GAAE,EAAC,KAAI,CAACA,OAAK,MAAI,gBAAE,KAAK,CAAC,EAAEA,IAAE,GAAG,CAAC,EAAC,CAAC;AAAE,SAAK,IAAE,EAAE,EAAE,CAAC,GAAE,QAAE,CAAC,KAAK,QAAO,KAAK,YAAY,GAAE,OAAG;AAAC,UAAG,CAAC,GAAE,GAAG,CAAC,IAAE,GAAE,IAAE,GAAE,EAAC,QAAOA,IAAE,gBAAe,EAAC,IAAE,GAAE,IAAE,EAAE,GAAE,CAAC,UAAS,gBAAgB,CAAC,GAAE,CAAC,CAAC,IAAE;AAAE,aAAM,CAAC,GAAE,MAAK,MAAI,EAAE,EAAE,EAAC,QAAOA,IAAE,KAAI,GAAE,QAAO,GAAE,cAAa,GAAE,QAAO,GAAE,gBAAe,EAAC,GAAE,EAAE,eAAe,eAAe,IAAE,EAAC,eAAc,EAAE,cAAa,IAAE,CAAC,CAAC,CAAC;AAAA,IAAC,CAAC,CAAC,GAAE,EAAC,KAAI,CAACA,IAAE,MAAK,MAAI,gBAAE,KAAK,CAAC,EAAEA,IAAE,GAAE,GAAG,CAAC,EAAC,CAAC;AAAE,SAAK,YAAU,CAAAA,OAAG;AAAC,UAAG,EAAC,gBAAe,EAAC,IAAE,gBAAE,KAAK,MAAM,KAAG,CAAC,GAAE,IAAEA,MAAG;AAAE,UAAG,CAAC;AAAE;AAAO,UAAI,IAAE,KAAK,QAAQ,IAAI;AAAE,aAAO,EAAE,KAAK,OAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,KAAG,EAAE,KAAK,OAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AAAA,IAAC;AAAE,SAAK,YAAU,CAAAA,OAAG;AAAC,UAAGA,MAAGA,OAAI,gBAAE,KAAK,cAAc;AAAE,eAAO,EAAE,MAAM,YAAYA,EAAC,WAAW,GAAE,KAAK,eAAe,IAAIA,EAAC,GAAE,KAAK,QAAQ,UAAUA,IAAE,gBAAE,KAAK,YAAY,CAAC;AAAA,IAAC;AAAE,SAAK,WAAS,CAAAA,OAAG;AAAC,UAAGA,OAAI,gBAAE,KAAK,YAAY,GAAE;AAAC,UAAE,MAAM,YAAYA,EAAC,UAAU,GAAE,KAAK,aAAa,IAAIA,EAAC;AAAE,YAAI,IAAE,gBAAE,KAAK,cAAc;AAAE,eAAO,KAAK,QAAQ,UAAU,GAAEA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAE,SAAK,aAAW,OAAMA,OAAG;AAAC,YAAM,KAAK,aAAaA,EAAC;AAAA,IAAC;AAAE,SAAK,sBAAoB,OAAMA,KAAE,KAAK,OAAO,IAAI,GAAE,IAAE,gBAAE,KAAK,YAAY,MAAI;AAAC,UAAI,IAAE,gBAAE,KAAK,MAAM;AAAE,UAAG,CAAC,KAAG,CAACA;AAAE,eAAM,CAAC;AAAE,UAAI,IAAE,KAAK,aAAa,IAAI,GAAE,EAAC,SAAQ,GAAE,gBAAe,IAAE,IAAG,OAAM,IAAE,EAAC,IAAE,KAAG,CAAC,GAAE,IAAE,OAAO,MAAM,CAAC,CAAC,IAAE,IAAE,CAAC;AAAE,WAAK,WAAS,KAAK,IAAI,IAAE,IAAE,KAAK,aAAW,EAAE,MAAM,mBAAmB,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,WAAS,MAAI,EAAE,MAAM,0BAA0B,GAAE,KAAK,WAAS,KAAK,IAAI;AAAG,UAAG,CAAC,GAAE,CAAC,IAAE,EAAEA,IAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,KAAG,KAAG,CAAC,GAAG,IAAI,OAAG;AAAC,YAAI,IAAE,GAAE,EAAC,QAAO,EAAC,IAAE,GAAE,IAAE,EAAE,GAAE,CAAC,QAAQ,CAAC;AAAE,eAAO,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,QAAO,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;AAAA,MAAC,CAAC,EAAE,OAAO,CAAC,EAAC,QAAO,EAAC,MAAI,CAAC,MAAI,KAAG,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAC,KAAI,GAAE,QAAO,EAAC,MAAI,MAAI,MAAI,CAAC,KAAG,EAAE,KAAK,WAAW,CAAC,KAAG,CAAC,GAAG,SAAS,CAAC,MAAI,KAAG,MAAI,MAAI,CAAC,KAAG,EAAE,KAAK,WAAW,CAAC,KAAG,CAAC,GAAG,SAAS,CAAC,EAAE;AAAE,UAAG,EAAE,QAAO;AAAC,aAAK,UAAU,IAAI,IAAE,GAAE,EAAE,MAAM,0BAA0B;AAAE,YAAI,IAAE,MAAM,EAAE,CAAC;AAAE,aAAK,UAAU,IAAI,KAAE;AAAE,YAAI,IAAE,OAAO,KAAK,CAAC,EAAE,OAAO,CAAC,GAAE,MAAI,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,OAAO,KAAK,EAAE,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,IAAE,EAAE,OAAO,CAAC,EAAC,KAAI,GAAE,QAAO,EAAC,OAAK,EAAE,CAAC,KAAG,CAAC,GAAG,KAAK,OAAG,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAE,EAAC,KAAI,GAAE,QAAO,EAAC,MAAI,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,CAAC,GAAG,EAAE,CAAC,KAAG,CAAC,GAAE,CAAC,EAAC,CAAC,GAAE,CAAC,CAAC;AAAE,eAAM,CAAC,GAAE,CAAC;AAAA,MAAC;AAAC,aAAM,CAAC;AAAA,IAAC;AAAE,SAAK,kBAAgB,CAACA,IAAE,MAAI;AAAC,UAAG,CAACA;AAAE;AAAO,UAAI,IAAE,gBAAE,KAAK,MAAM,GAAE,EAAC,YAAW,EAAC,IAAE,KAAG,CAAC;AAAE,QAAE,MAAM,wBAAwB;AAAE,UAAI,IAAE,OAAO,KAAKA,MAAG,CAAC,CAAC;AAAE,WAAK,uBAAuB,OAAO,OAAG,EAAE,OAAO,CAAC,GAAE,MAAI,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,KAAG,CAAC,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,KAAK,oBAAoB,OAAO,OAAG,EAAE,OAAO,CAAC,GAAE,MAAI;AAAC,YAAI,IAAE,MAAG,IAAEA,GAAE,CAAC;AAAE,eAAO,OAAO,KAAG,eAAa,IAAE,EAAE,CAAC,KAAI,OAAO,KAAG,cAAY,MAAI,YAAU,IAAE,QAAI,EAAE,EAAE,CAAC,GAAE,CAAC,GAAE,EAAC,CAAC,CAAC,GAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,KAAG,CAAC,CAAC,GAAE,IAAE,EAAE,GAAE,MAAI,gBAAgB,IAAE,CAAC,EAAC,CAAC;AAAA,MAAC,GAAE,CAAC,CAAC,GAAE,EAAE,QAAQ,OAAG;AAAC,YAAI,IAAE,OAAO,KAAKA,GAAE,CAAC,CAAC,EAAE,IAAI,OAAG,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC;AAAE,cAAI,IAAE,EAAE,CAAC,IAAG,KAAK,WAAW,CAAC,IAAE,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,KAAK,WAAW,CAAC,KAAG,CAAC,GAAE,GAAG,KAAG,CAAC,CAAC,CAAC,CAAC;AAAA,MAAC,CAAC;AAAA,IAAC;AAAE,SAAK,SAAO,OAAM,CAACA,IAAE,CAAC,MAAI;AAAC,UAAI,IAAE,KAAK,UAAUA,EAAC,KAAG;AAAO,QAAE,MAAM,8BAA8B,CAAC,iBAAiB,CAAC,UAAU;AAAE,UAAI,KAAG,YAAS;AAAC,YAAI,IAAE,MAAM,KAAK,oBAAoB,GAAE,CAAC;AAAE,UAAE,UAAQ,KAAK,gBAAgB,GAAG,CAAC;AAAA,MAAC,GAAG;AAAE,WAAK,SAAS,IAAI,EAAC,QAAO,GAAE,OAAM,GAAE,SAAQ,EAAC,CAAC,GAAE,EAAE,KAAK,MAAI;AAAC,aAAG,KAAK,OAAO,IAAI,MAAI,KAAG,KAAK,OAAO,SAAS,CAAC;AAAA,MAAC,CAAC;AAAA,IAAC;AAAE,SAAK,mBAAiB,CAACA,IAAE,IAAE,gBAAE,KAAK,YAAY,KAAG,OAAK;AAAC,UAAI,IAAE,KAAK,UAAUA,EAAC;AAAE,UAAG;AAAE,eAAO,KAAK,SAAS,CAAC,GAAE,KAAK,UAAU,CAAC,GAAE,KAAK,QAAQ,UAAU,GAAE,CAAC;AAAA,IAAC;AAAE,SAAK,cAAc,UAAU,KAAK,MAAM,GAAE,KAAK,UAAU,UAAU,OAAM,MAAG;AAAC,WAAG,KAAK,SAAS,SAAO,MAAM,KAAK,QAAQ,UAAU,GAAE,KAAK,SAAS,MAAM,GAAE,EAAE,MAAM,mCAAmC;AAAA,IAAE,CAAC,GAAE,KAAG,KAAK,WAAW,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,aAAa,GAAE;AAAC,QAAG,CAAC;AAAE,aAAO,EAAE,MAAM,qBAAqB;AAAE,QAAI,IAAE,GAAE,EAAC,YAAW,GAAE,gBAAe,GAAE,cAAa,GAAE,KAAI,EAAC,IAAE,GAAE,IAAE,EAAE,GAAE,CAAC,cAAa,kBAAiB,gBAAe,KAAK,CAAC;AAAE,SAAG,EAAE,EAAE,CAAC,CAAC,GAAE,CAAC,CAAC,IAAE,EAAE,CAAC,GAAE,CAAC,CAAC,IAAE,EAAE,CAAC,GAAE,EAAE,MAAM,iBAAiB,GAAE,KAAK,OAAO,IAAI,EAAE,EAAC,YAAW,GAAE,gBAAe,GAAE,cAAa,EAAC,GAAE,CAAC,CAAC,GAAE,KAAG,KAAK,gBAAgB,CAAC,GAAE,KAAG,MAAM,KAAK,iBAAiB,CAAC;AAAA,EAAC;AAAC;", "names": ["t"]}