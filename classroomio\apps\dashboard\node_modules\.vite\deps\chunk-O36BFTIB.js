import {
  __commonJS
} from "./chunk-2GTGKKMZ.js";

// ../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isObject.js
var require_isObject = __commonJS({
  "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isObject.js"(exports, module) {
    function isObject(value) {
      var type = typeof value;
      return value != null && (type == "object" || type == "function");
    }
    module.exports = isObject;
  }
});

export {
  require_isObject
};
//# sourceMappingURL=chunk-O36BFTIB.js.map
