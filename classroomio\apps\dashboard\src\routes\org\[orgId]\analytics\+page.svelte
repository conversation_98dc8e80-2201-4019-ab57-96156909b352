<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { writable } from 'svelte/store';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import { PageBody, PageNav } from '$lib/components/Page';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import AnalyticsDashboard from '$lib/components/Analytics/AnalyticsDashboard.svelte';
  import AdminAnalyticsDashboard from '$lib/components/Analytics/AdminAnalyticsDashboard.svelte';
  import ReportsManager from '$lib/components/Analytics/ReportsManager.svelte';
  import MetabaseIntegration from '$lib/components/Analytics/MetabaseIntegration.svelte';
  import { 
    Dashboard,
    ChartLine,
    Report,
    Connect,
    Settings,
    Warning,
    Analytics
  } from 'carbon-icons-svelte';

  let organizationId: string;
  let loading = true;
  let error: string | null = null;
  let activeTab = 'overview';

  $: currentUserId = $globalStore.user?.id;
  $: isOrgAdmin = $globalStore.isOrgAdmin;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Dashboard, description: 'Organization-wide analytics overview' },
    { id: 'detailed', label: 'Detailed Analytics', icon: ChartLine, description: 'Comprehensive analytics dashboards' },
    { id: 'reports', label: 'Reports', icon: Report, description: 'Custom reports and scheduled reporting' },
    { id: 'metabase', label: 'Advanced Analytics', icon: Connect, description: 'Metabase/Superset integration' },
    { id: 'settings', label: 'Settings', icon: Settings, description: 'Analytics configuration and settings' }
  ];

  onMount(async () => {
    organizationId = $page.params.orgId;
    await loadOrganizationData();
  });

  async function loadOrganizationData() {
    try {
      loading = true;
      error = null;

      // Verify organization access
      if (!isOrgAdmin) {
        throw new Error('Organization admin access required');
      }

      // Load organization data if needed
      // This would typically fetch organization details and verify permissions

    } catch (err) {
      console.error('Error loading organization data:', err);
      error = err.message || 'Failed to load organization analytics';
    } finally {
      loading = false;
    }
  }

  function handleDashboardChanged(event: CustomEvent) {
    console.log('Dashboard changed:', event.detail);
  }

  function handleWidgetClicked(event: CustomEvent) {
    console.log('Widget clicked:', event.detail);
  }

  function handleReportGenerated(event: CustomEvent) {
    console.log('Report generated:', event.detail);
  }

  function handleMetabaseConnected(event: CustomEvent) {
    console.log('Metabase connected:', event.detail);
  }
</script>

<svelte:head>
  <title>
    {$t('analytics.organization_analytics', { default: 'Organization Analytics' })} - ClassroomIO
  </title>
</svelte:head>

<PageNav title={$t('analytics.organization_analytics', { default: 'Organization Analytics' })}>
  <div class="flex items-center space-x-4">
    <!-- Tab Navigation -->
    <div class="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
      {#each tabs as tab}
        <button
          on:click={() => activeTab = tab.id}
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === tab.id 
            ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm' 
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}"
          title={tab.description}
        >
          <svelte:component this={tab.icon} size={16} class="mr-2" />
          {$t(`analytics.${tab.id}`, { default: tab.label })}
        </button>
      {/each}
    </div>

    <!-- Organization Info -->
    {#if $globalStore.org}
      <div class="text-sm text-gray-600 dark:text-gray-400">
        <span class="font-medium">{$globalStore.org.name}</span>
        {#if $globalStore.org.description}
          <span class="mx-2">•</span>
          <span>{$globalStore.org.description}</span>
        {/if}
      </div>
    {/if}
  </div>
</PageNav>

<PageBody>
  {#if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.loading_organization', { default: 'Loading Organization Analytics' })}
      </h3>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.error_loading', { default: 'Error Loading Analytics' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadOrganizationData}>
        {$t('analytics.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if !isOrgAdmin}
    <Box className="text-center py-12">
      <Warning size={48} class="text-yellow-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('analytics.access_denied', { default: 'Access Denied' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {$t('analytics.admin_required', { default: 'Organization admin access required to view analytics' })}
      </p>
    </Box>

  {:else}
    <!-- Tab Content -->
    <div class="min-h-[600px]">
      {#if activeTab === 'overview'}
        <!-- Organization Overview Dashboard -->
        <div class="space-y-6">
          <!-- Key Metrics Overview -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Box>
              <div class="p-6 text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">
                  {$globalStore.org?.total_batches || 0}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {$t('analytics.total_batches', { default: 'Total Batches' })}
                </div>
              </div>
            </Box>

            <Box>
              <div class="p-6 text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">
                  {$globalStore.org?.total_students || 0}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {$t('analytics.total_students', { default: 'Total Students' })}
                </div>
              </div>
            </Box>

            <Box>
              <div class="p-6 text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">
                  {$globalStore.org?.total_instructors || 0}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {$t('analytics.total_instructors', { default: 'Total Instructors' })}
                </div>
              </div>
            </Box>

            <Box>
              <div class="p-6 text-center">
                <div class="text-3xl font-bold text-orange-600 mb-2">
                  {$globalStore.org?.completion_rate || 0}%
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {$t('analytics.avg_completion', { default: 'Avg Completion Rate' })}
                </div>
              </div>
            </Box>
          </div>

          <!-- Overview Dashboard -->
          <AnalyticsDashboard
            {organizationId}
            batchId={null}
            userId={currentUserId}
            dashboardType="admin"
            on:dashboardChanged={handleDashboardChanged}
            on:widgetClicked={handleWidgetClicked}
          />
        </div>

      {:else if activeTab === 'detailed'}
        <!-- Detailed Analytics Dashboard -->
        <AdminAnalyticsDashboard
          {organizationId}
          batchId={null}
          on:organizationAnalytics={(e) => console.log('Organization analytics:', e.detail)}
        />

      {:else if activeTab === 'reports'}
        <!-- Reports Manager -->
        <ReportsManager
          {organizationId}
          batchId={null}
          userId={currentUserId}
          on:reportGenerated={handleReportGenerated}
        />

      {:else if activeTab === 'metabase'}
        <!-- Metabase/Superset Integration -->
        <MetabaseIntegration
          {organizationId}
          on:connected={handleMetabaseConnected}
          on:dashboardCreated={(e) => console.log('Dashboard created:', e.detail)}
        />

      {:else if activeTab === 'settings'}
        <!-- Analytics Settings -->
        <div class="space-y-6">
          <Box>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {$t('analytics.data_retention', { default: 'Data Retention Settings' })}
              </h3>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {$t('analytics.analytics_retention', { default: 'Analytics Data Retention (days)' })}
                  </label>
                  <input
                    type="number"
                    value="365"
                    min="30"
                    max="2555"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                  />
                  <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {$t('analytics.retention_desc', { default: 'How long to keep detailed analytics data' })}
                  </p>
                </div>

                <div>
                  <label class="flex items-center">
                    <input type="checkbox" checked class="mr-3" />
                    <span class="text-gray-900 dark:text-white">
                      {$t('analytics.enable_real_time', { default: 'Enable real-time analytics' })}
                    </span>
                  </label>
                </div>

                <div>
                  <label class="flex items-center">
                    <input type="checkbox" checked class="mr-3" />
                    <span class="text-gray-900 dark:text-white">
                      {$t('analytics.enable_caching', { default: 'Enable analytics caching' })}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </Box>

          <Box>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {$t('analytics.privacy_settings', { default: 'Privacy Settings' })}
              </h3>
              
              <div class="space-y-4">
                <div>
                  <label class="flex items-center">
                    <input type="checkbox" checked class="mr-3" />
                    <span class="text-gray-900 dark:text-white">
                      {$t('analytics.anonymize_data', { default: 'Anonymize student data in exports' })}
                    </span>
                  </label>
                </div>

                <div>
                  <label class="flex items-center">
                    <input type="checkbox" class="mr-3" />
                    <span class="text-gray-900 dark:text-white">
                      {$t('analytics.allow_external_sharing', { default: 'Allow external analytics sharing' })}
                    </span>
                  </label>
                </div>

                <div>
                  <label class="flex items-center">
                    <input type="checkbox" checked class="mr-3" />
                    <span class="text-gray-900 dark:text-white">
                      {$t('analytics.gdpr_compliance', { default: 'GDPR compliance mode' })}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </Box>

          <Box>
            <div class="p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {$t('analytics.integration_settings', { default: 'Integration Settings' })}
              </h3>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {$t('analytics.metabase_url', { default: 'Metabase URL' })}
                  </label>
                  <input
                    type="url"
                    placeholder="https://metabase.yourorg.com"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {$t('analytics.api_key', { default: 'API Key' })}
                  </label>
                  <input
                    type="password"
                    placeholder="Enter API key"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                  />
                </div>

                <div class="flex space-x-3">
                  <PrimaryButton variant={VARIANTS.OUTLINED} size="sm">
                    {$t('analytics.test_connection', { default: 'Test Connection' })}
                  </PrimaryButton>
                  
                  <PrimaryButton variant={VARIANTS.CONTAINED} size="sm">
                    {$t('analytics.save_settings', { default: 'Save Settings' })}
                  </PrimaryButton>
                </div>
              </div>
            </div>
          </Box>
        </div>

      {:else}
        <!-- Fallback for unknown tabs -->
        <Box className="text-center py-12">
          <Analytics size={48} class="text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {$t('analytics.tab_not_found', { default: 'Tab Not Found' })}
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            {$t('analytics.tab_not_found_desc', { default: 'The requested analytics tab could not be found' })}
          </p>
        </Box>
      {/if}
    </div>

    <!-- Quick Actions -->
    <div class="fixed bottom-6 right-6 flex flex-col space-y-3">
      <button
        on:click={() => activeTab = 'reports'}
        class="w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
        title={$t('analytics.quick_report', { default: 'Generate report' })}
      >
        <Report size={20} />
      </button>

      <button
        on:click={() => activeTab = 'metabase'}
        class="w-12 h-12 bg-purple-600 hover:bg-purple-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors"
        title={$t('analytics.advanced_analytics', { default: 'Advanced analytics' })}
      >
        <Connect size={20} />
      </button>
    </div>
  {/if}
</PageBody>

<style>
  /* Custom styles for organization analytics */
  :global(.analytics-dashboard) {
    min-height: 600px;
  }

  /* Floating action buttons */
  .fixed {
    z-index: 40;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    :global(.analytics-dashboard .dashboard-grid) {
      grid-template-columns: 1fr !important;
    }
  }
</style>
