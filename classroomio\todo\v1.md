# V1

## Quick Win

- [x] Create open source friends page
- [x] Create guide for linking supabase cloud to local environment
- [] Create roadmap

## Complex

Features that would implemented internally with possible collaboration with the community

### Backlog

- [] rrefactor email sending
  - [] don't send email address from FE
  - [] don't await sending via nodemailer
- [] implement multi language
- [] implement supabase RLS
- [] integrate lemonsqueezy

### In progress

- [] Course announcements
- [] Create basic API for ClassroomIO
- [] Link shorterner API
- [] Fix video upload functionality and make it open source

### Done
