{"version": 3, "sources": ["../../../../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs", "../../../../../node_modules/.pnpm/@formatjs+fast-memoize@2.2.7/node_modules/@formatjs/fast-memoize/index.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/error.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/types.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/regex.generated.js", "../../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/date-time.js", "../../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/regex.generated.js", "../../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/number.js", "../../../../../node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.14/node_modules/@formatjs/icu-skeleton-parser/index.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/time-data.generated.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/date-time-pattern-generator.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/parser.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/manipulator.js", "../../../../../node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.2/node_modules/@formatjs/icu-messageformat-parser/index.js", "../../../../../node_modules/.pnpm/intl-messageformat@10.7.16/node_modules/intl-messageformat/src/error.js", "../../../../../node_modules/.pnpm/intl-messageformat@10.7.16/node_modules/intl-messageformat/src/formatters.js", "../../../../../node_modules/.pnpm/intl-messageformat@10.7.16/node_modules/intl-messageformat/src/core.js", "../../../../../node_modules/.pnpm/intl-messageformat@10.7.16/node_modules/intl-messageformat/index.js", "../../../../../node_modules/.pnpm/@sveltekit-i18n+parser-icu@1.0.8/node_modules/@sveltekit-i18n/parser-icu/dist/index.js"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "\"use strict\";\n//\n// Main\n//\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.strategies = void 0;\nexports.memoize = memoize;\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nexports.strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ErrorKind = void 0;\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (exports.ErrorKind = ErrorKind = {}));\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SKELETON_TYPE = exports.TYPE = void 0;\nexports.isLiteralElement = isLiteralElement;\nexports.isArgumentElement = isArgumentElement;\nexports.isNumberElement = isNumberElement;\nexports.isDateElement = isDateElement;\nexports.isTimeElement = isTimeElement;\nexports.isSelectElement = isSelectElement;\nexports.isPluralElement = isPluralElement;\nexports.isPoundElement = isPoundElement;\nexports.isTagElement = isTagElement;\nexports.isNumberSkeleton = isNumberSkeleton;\nexports.isDateTimeSkeleton = isDateTimeSkeleton;\nexports.createLiteralElement = createLiteralElement;\nexports.createNumberElement = createNumberElement;\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (exports.TYPE = TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (exports.SKELETON_TYPE = SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.WHITE_SPACE_REGEX = exports.SPACE_SEPARATOR_REGEX = void 0;\n// @generated from regex-gen.ts\nexports.SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexports.WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseDateTimeSkeleton = parseDateTimeSkeleton;\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.WHITE_SPACE_REGEX = void 0;\n// @generated from regex-gen.ts\nexports.WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseNumberSkeletonFromString = parseNumberSkeletonFromString;\nexports.parseNumberSkeleton = parseNumberSkeleton;\nvar tslib_1 = require(\"tslib\");\nvar regex_generated_1 = require(\"./regex.generated\");\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(regex_generated_1.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = tslib_1.__assign(tslib_1.__assign(tslib_1.__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (tslib_1.__assign(tslib_1.__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = tslib_1.__assign(tslib_1.__assign(tslib_1.__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (tslib_1.__assign(tslib_1.__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = tslib_1.__assign(tslib_1.__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = tslib_1.__assign(tslib_1.__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = tslib_1.__assign(tslib_1.__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = tslib_1.__assign(tslib_1.__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = tslib_1.__assign(tslib_1.__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"./date-time\"), exports);\ntslib_1.__exportStar(require(\"./number\"), exports);\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timeData = void 0;\n// @generated from time-data-gen.ts\n// prettier-ignore  \nexports.timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getBestPattern = getBestPattern;\nvar time_data_generated_1 = require(\"./time-data.generated\");\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = time_data_generated_1.timeData[regionTag || ''] ||\n        time_data_generated_1.timeData[languageTag || ''] ||\n        time_data_generated_1.timeData[\"\".concat(languageTag, \"-001\")] ||\n        time_data_generated_1.timeData['001'];\n    return hourCycles[0];\n}\n", "\"use strict\";\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Parser = void 0;\nvar tslib_1 = require(\"tslib\");\nvar error_1 = require(\"./error\");\nvar types_1 = require(\"./types\");\nvar regex_generated_1 = require(\"./regex.generated\");\nvar icu_skeleton_parser_1 = require(\"@formatjs/icu-skeleton-parser\");\nvar date_time_pattern_generator_1 = require(\"./date-time-pattern-generator\");\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(regex_generated_1.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(regex_generated_1.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: types_1.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(error_1.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: types_1.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(error_1.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(error_1.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(error_1.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: types_1.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(error_1.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(error_1.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: types_1.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(error_1.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(error_1.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: types_1.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(error_1.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: types_1.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(error_1.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0, date_time_pattern_generator_1.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: types_1.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0, icu_skeleton_parser_1.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? types_1.TYPE.date : types_1.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? types_1.TYPE.number\n                            : argType === 'date'\n                                ? types_1.TYPE.date\n                                : types_1.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(error_1.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, tslib_1.__assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, error_1.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: types_1.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: types_1.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(error_1.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(error_1.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(error_1.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0, icu_skeleton_parser_1.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(error_1.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: types_1.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0, icu_skeleton_parser_1.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, error_1.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? error_1.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : error_1.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? error_1.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? error_1.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : error_1.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(error_1.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexports.Parser = Parser;\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hoistSelectors = hoistSelectors;\nexports.isStructurallySame = isStructurallySame;\nvar tslib_1 = require(\"tslib\");\nvar types_1 = require(\"./types\");\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return tslib_1.__spreadArray([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors(tslib_1.__spreadArray(tslib_1.__spreadArray(tslib_1.__spreadArray([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return (0, types_1.isPluralElement)(el) || (0, types_1.isSelectElement)(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if ((0, types_1.isTagElement)(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nfunction hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if ((0, types_1.isTagElement)(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if ((0, types_1.isArgumentElement)(el) ||\n            (0, types_1.isDateElement)(el) ||\n            (0, types_1.isTimeElement)(el) ||\n            (0, types_1.isNumberElement)(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if ((0, types_1.isPluralElement)(el) || (0, types_1.isSelectElement)(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if ((0, types_1.isTagElement)(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nfunction isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(types_1.TYPE[type], \" vs \").concat(types_1.TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isStructurallySame = exports._Parser = void 0;\nexports.parse = parse;\nvar tslib_1 = require(\"tslib\");\nvar error_1 = require(\"./error\");\nvar parser_1 = require(\"./parser\");\nvar types_1 = require(\"./types\");\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0, types_1.isSelectElement)(el) || (0, types_1.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0, types_1.isNumberElement)(el) && (0, types_1.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0, types_1.isDateElement)(el) || (0, types_1.isTimeElement)(el)) &&\n            (0, types_1.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0, types_1.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = tslib_1.__assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new parser_1.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(error_1.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\ntslib_1.__exportStar(require(\"./types\"), exports);\n// only for testing\nexports._Parser = parser_1.Parser;\nvar manipulator_1 = require(\"./manipulator\");\nObject.defineProperty(exports, \"isStructurallySame\", { enumerable: true, get: function () { return manipulator_1.isStructurallySame; } });\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MissingValueError = exports.InvalidValueTypeError = exports.InvalidValueError = exports.FormatError = exports.ErrorCode = void 0;\nvar tslib_1 = require(\"tslib\");\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    tslib_1.__extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexports.FormatError = FormatError;\nvar InvalidValueError = /** @class */ (function (_super) {\n    tslib_1.__extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexports.InvalidValueError = InvalidValueError;\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    tslib_1.__extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexports.InvalidValueTypeError = InvalidValueTypeError;\nvar MissingValueError = /** @class */ (function (_super) {\n    tslib_1.__extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexports.MissingValueError = MissingValueError;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PART_TYPE = void 0;\nexports.isFormatXMLElementFn = isFormatXMLElementFn;\nexports.formatToParts = formatToParts;\nvar icu_messageformat_parser_1 = require(\"@formatjs/icu-messageformat-parser\");\nvar error_1 = require(\"./error\");\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (exports.PART_TYPE = PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0, icu_messageformat_parser_1.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0, icu_messageformat_parser_1.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0, icu_messageformat_parser_1.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new error_1.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0, icu_messageformat_parser_1.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0, icu_messageformat_parser_1.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0, icu_messageformat_parser_1.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0, icu_messageformat_parser_1.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0, icu_messageformat_parser_1.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new error_1.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0, icu_messageformat_parser_1.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new error_1.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0, icu_messageformat_parser_1.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new error_1.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", error_1.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new error_1.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n", "\"use strict\";\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.IntlMessageFormat = void 0;\nvar tslib_1 = require(\"tslib\");\nvar fast_memoize_1 = require(\"@formatjs/fast-memoize\");\nvar icu_messageformat_parser_1 = require(\"@formatjs/icu-messageformat-parser\");\nvar formatters_1 = require(\"./formatters\");\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return tslib_1.__assign(tslib_1.__assign(tslib_1.__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = tslib_1.__assign(tslib_1.__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, tslib_1.__assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0, fast_memoize_1.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, tslib_1.__spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: fast_memoize_1.strategies.variadic,\n        }),\n        getDateTimeFormat: (0, fast_memoize_1.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, tslib_1.__spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: fast_memoize_1.strategies.variadic,\n        }),\n        getPluralRules: (0, fast_memoize_1.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, tslib_1.__spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: fast_memoize_1.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== formatters_1.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0, formatters_1.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = tslib_1.__rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, tslib_1.__assign(tslib_1.__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = icu_messageformat_parser_1.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexports.IntlMessageFormat = IntlMessageFormat;\n", "\"use strict\";\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.IntlMessageFormat = void 0;\nvar tslib_1 = require(\"tslib\");\nvar core_1 = require(\"./src/core\");\nObject.defineProperty(exports, \"IntlMessageFormat\", { enumerable: true, get: function () { return core_1.IntlMessageFormat; } });\ntslib_1.__exportStar(require(\"./src/core\"), exports);\ntslib_1.__exportStar(require(\"./src/error\"), exports);\ntslib_1.__exportStar(require(\"./src/formatters\"), exports);\nexports.default = core_1.IntlMessageFormat;\n", "import{IntlMessageFormat as p}from\"intl-messageformat\";var f=e=>({parse:(r,[t,o],a,n)=>r===void 0?`${n}`:new p(r,a,o,e).format(t)}),i=f;export{i as default};\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBO,SAAS,UAAU,GAAG,GAAG;AAC9B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACpF;AAaO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAASA,MAAK;AAAG,QAAI,OAAO,UAAU,eAAe,KAAK,GAAGA,EAAC,KAAK,EAAE,QAAQA,EAAC,IAAI;AAC9E,QAAEA,EAAC,IAAI,EAAEA,EAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAASC,KAAI,GAAGD,KAAI,OAAO,sBAAsB,CAAC,GAAGC,KAAID,GAAE,QAAQC,MAAK;AACpE,UAAI,EAAE,QAAQD,GAAEC,EAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAGD,GAAEC,EAAC,CAAC;AACzE,UAAED,GAAEC,EAAC,CAAC,IAAI,EAAED,GAAEC,EAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAEO,SAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AACxD,MAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,QAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA;AACxH,aAASA,KAAI,WAAW,SAAS,GAAGA,MAAK,GAAGA;AAAK,UAAI,IAAI,WAAWA,EAAC;AAAG,aAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,SAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAC9D;AAEO,SAAS,QAAQ,YAAY,WAAW;AAC7C,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACtE;AAEO,SAAS,aAAa,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACvG,WAAS,OAAOC,IAAG;AAAE,QAAIA,OAAM,UAAU,OAAOA,OAAM;AAAY,YAAM,IAAI,UAAU,mBAAmB;AAAG,WAAOA;AAAA,EAAG;AACtH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI,CAAC;AACtG,MAAI,GAAG,OAAO;AACd,WAASD,KAAI,WAAW,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAC7C,QAAI,UAAU,CAAC;AACf,aAASD,MAAK;AAAW,cAAQA,EAAC,IAAIA,OAAM,WAAW,CAAC,IAAI,UAAUA,EAAC;AACvE,aAASA,MAAK,UAAU;AAAQ,cAAQ,OAAOA,EAAC,IAAI,UAAU,OAAOA,EAAC;AACtE,YAAQ,iBAAiB,SAAUE,IAAG;AAAE,UAAI;AAAM,cAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAOA,MAAK,IAAI,CAAC;AAAA,IAAG;AAC5K,QAAI,UAAU,GAAG,WAAWD,EAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAI,IAAI,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW;AAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW;AAAU,cAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG;AAAG,mBAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG;AAAG,mBAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI;AAAG,qBAAa,QAAQ,CAAC;AAAA,IACvD,WACS,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS;AAAS,qBAAa,QAAQ,CAAC;AAAA;AACvC,mBAAW,GAAG,IAAI;AAAA,IAC3B;AAAA,EACJ;AACA,MAAI;AAAQ,WAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACT;AAEO,SAAS,kBAAkB,SAAS,cAAc,OAAO;AAC9D,MAAI,WAAW,UAAU,SAAS;AAClC,WAASA,KAAI,GAAGA,KAAI,aAAa,QAAQA,MAAK;AAC1C,YAAQ,WAAW,aAAaA,EAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAaA,EAAC,EAAE,KAAK,OAAO;AAAA,EAC1F;AACA,SAAO,WAAW,QAAQ;AAC5B;AAEO,SAAS,UAAU,GAAG;AAC3B,SAAO,OAAO,MAAM,WAAW,IAAI,GAAG,OAAO,CAAC;AAChD;AAEO,SAAS,kBAAkBC,IAAG,MAAM,QAAQ;AACjD,MAAI,OAAO,SAAS;AAAU,WAAO,KAAK,cAAc,IAAI,OAAO,KAAK,aAAa,GAAG,IAAI;AAC5F,SAAO,OAAO,eAAeA,IAAG,QAAQ,EAAE,cAAc,MAAM,OAAO,SAAS,GAAG,OAAO,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC;AACrH;AAEO,SAAS,WAAW,aAAa,eAAe;AACrD,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa;AAAY,WAAO,QAAQ,SAAS,aAAa,aAAa;AAC/H;AAEO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAC3D,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACH;AAEO,SAAS,YAAY,SAAS,MAAM;AACzC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI;AAAG,YAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGA,IAAG,GAAG,GAAG,IAAI,OAAO,QAAQ,OAAO,aAAa,aAAa,WAAW,QAAQ,SAAS;AAC/L,SAAO,EAAE,OAAO,KAAK,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AAC1J,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIA;AAAG,YAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK;AAAG,UAAI;AAC1C,YAAIA,KAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG;AAAM,iBAAO;AAC3J,YAAI,IAAI,GAAG;AAAG,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,gBAAQ,GAAG,CAAC,GAAG;AAAA,UACX,KAAK;AAAA,UAAG,KAAK;AAAG,gBAAI;AAAI;AAAA,UACxB,KAAK;AAAG,cAAE;AAAS,mBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,UACtD,KAAK;AAAG,cAAE;AAAS,gBAAI,GAAG,CAAC;AAAG,iBAAK,CAAC,CAAC;AAAG;AAAA,UACxC,KAAK;AAAG,iBAAK,EAAE,IAAI,IAAI;AAAG,cAAE,KAAK,IAAI;AAAG;AAAA,UACxC;AACI,gBAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,kBAAI;AAAG;AAAA,YAAU;AAC3G,gBAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,gBAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,YAAO;AACrF,gBAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,kBAAI;AAAI;AAAA,YAAO;AACpE,gBAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,gBAAE,QAAQ,EAAE,CAAC;AAAG,gBAAE,IAAI,KAAK,EAAE;AAAG;AAAA,YAAO;AAClE,gBAAI,EAAE,CAAC;AAAG,gBAAE,IAAI,IAAI;AACpB,cAAE,KAAK,IAAI;AAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK,SAAS,CAAC;AAAA,MAC7B,SAAS,GAAG;AAAE,aAAK,CAAC,GAAG,CAAC;AAAG,YAAI;AAAA,MAAG,UAAE;AAAU,QAAAA,KAAI,IAAI;AAAA,MAAG;AACzD,QAAI,GAAG,CAAC,IAAI;AAAG,YAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACF;AAcO,SAAS,aAAa,GAAG,GAAG;AACjC,WAASF,MAAK;AAAG,QAAIA,OAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAK,GAAGA,EAAC;AAAG,sBAAgB,GAAG,GAAGA,EAAC;AAC9G;AAEO,SAAS,SAAS,GAAG;AAC1B,MAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,GAAGC,KAAI;AAC5E,MAAI;AAAG,WAAO,EAAE,KAAK,CAAC;AACtB,MAAI,KAAK,OAAO,EAAE,WAAW;AAAU,WAAO;AAAA,MAC1C,MAAM,WAAY;AACd,YAAI,KAAKA,MAAK,EAAE;AAAQ,cAAI;AAC5B,eAAO,EAAE,OAAO,KAAK,EAAEA,IAAG,GAAG,MAAM,CAAC,EAAE;AAAA,MAC1C;AAAA,IACJ;AACA,QAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AACvF;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC;AAAG,WAAO;AACf,MAAIA,KAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAIA,GAAE,KAAK,GAAG;AAAM,SAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAIA,GAAE,QAAQ;AAAI,UAAE,KAAKA,EAAC;AAAA,IACnD,UACA;AAAU,UAAI;AAAG,cAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACT;AAGO,SAAS,WAAW;AACzB,WAAS,KAAK,CAAC,GAAGA,KAAI,GAAGA,KAAI,UAAU,QAAQA;AAC3C,SAAK,GAAG,OAAO,OAAO,UAAUA,EAAC,CAAC,CAAC;AACvC,SAAO;AACT;AAGO,SAAS,iBAAiB;AAC/B,WAAS,IAAI,GAAGA,KAAI,GAAG,KAAK,UAAU,QAAQA,KAAI,IAAIA;AAAK,SAAK,UAAUA,EAAC,EAAE;AAC7E,WAAS,IAAI,MAAM,CAAC,GAAG,IAAI,GAAGA,KAAI,GAAGA,KAAI,IAAIA;AACzC,aAAS,IAAI,UAAUA,EAAC,GAAG,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAK;AAC1D,QAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO;AACT;AAEO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW;AAAG,aAASA,KAAI,GAAG,IAAI,KAAK,QAAQ,IAAIA,KAAI,GAAGA,MAAK;AACjF,UAAI,MAAM,EAAEA,MAAK,OAAO;AACpB,YAAI,CAAC;AAAI,eAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAGA,EAAC;AACnD,WAAGA,EAAC,IAAI,KAAKA,EAAC;AAAA,MAClB;AAAA,IACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;AAEO,SAAS,QAAQ,GAAG;AACzB,SAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AACrE;AAEO,SAAS,iBAAiB,SAAS,YAAY,WAAW;AAC/D,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAGA,IAAG,IAAI,CAAC;AAC5D,SAAOA,KAAI,OAAO,QAAQ,OAAO,kBAAkB,aAAa,gBAAgB,QAAQ,SAAS,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU,WAAW,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AACtN,WAAS,YAAYC,IAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,QAAQ,QAAQ,CAAC,EAAE,KAAKA,IAAG,MAAM;AAAA,IAAG;AAAA,EAAG;AAC9F,WAAS,KAAK,GAAGA,IAAG;AAAE,QAAI,EAAE,CAAC,GAAG;AAAE,MAAAD,GAAE,CAAC,IAAI,SAAU,GAAG;AAAE,eAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,YAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAG,UAAIC;AAAG,QAAAD,GAAE,CAAC,IAAIC,GAAED,GAAE,CAAC,CAAC;AAAA,IAAG;AAAA,EAAE;AACvK,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI;AAAE,WAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IAAG,SAAS,GAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAK,GAAG;AAAE,MAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAOC,IAAG,GAAG;AAAE,QAAIA,GAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE;AAAQ,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACnF;AAEO,SAAS,iBAAiB,GAAG;AAClC,MAAID,IAAGD;AACP,SAAOC,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS,SAAU,GAAG;AAAE,UAAM;AAAA,EAAG,CAAC,GAAG,KAAK,QAAQ,GAAGA,GAAE,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AAC1I,WAAS,KAAK,GAAGC,IAAG;AAAE,IAAAD,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAU,GAAG;AAAE,cAAQD,KAAI,CAACA,MAAK,EAAE,OAAO,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,MAAM,IAAIE,KAAIA,GAAE,CAAC,IAAI;AAAA,IAAG,IAAIA;AAAA,EAAG;AACvI;AAEO,SAAS,cAAc,GAAG;AAC/B,MAAI,CAAC,OAAO;AAAe,UAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,EAAE,OAAO,aAAa,GAAGD;AACjC,SAAO,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAAGA,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AAC9M,WAAS,KAAK,GAAG;AAAE,IAAAA,GAAE,CAAC,IAAI,EAAE,CAAC,KAAK,SAAU,GAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,YAAI,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,MAAM,EAAE,KAAK;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AAC/J,WAAS,OAAO,SAAS,QAAQ,GAAG,GAAG;AAAE,YAAQ,QAAQ,CAAC,EAAE,KAAK,SAASE,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAM,EAAE,CAAC;AAAA,IAAG,GAAG,MAAM;AAAA,EAAG;AAC7H;AAEO,SAAS,qBAAqB,QAAQ,KAAK;AAChD,MAAI,OAAO,gBAAgB;AAAE,WAAO,eAAe,QAAQ,OAAO,EAAE,OAAO,IAAI,CAAC;AAAA,EAAG,OAAO;AAAE,WAAO,MAAM;AAAA,EAAK;AAC9G,SAAO;AACT;AAiBO,SAAS,aAAa,KAAK;AAChC,MAAI,OAAO,IAAI;AAAY,WAAO;AAClC,MAAI,SAAS,CAAC;AACd,MAAI,OAAO;AAAM,aAAS,IAAI,QAAQ,GAAG,GAAGF,KAAI,GAAGA,KAAI,EAAE,QAAQA;AAAK,UAAI,EAAEA,EAAC,MAAM;AAAW,wBAAgB,QAAQ,KAAK,EAAEA,EAAC,CAAC;AAAA;AAC/H,qBAAmB,QAAQ,GAAG;AAC9B,SAAO;AACT;AAEO,SAAS,gBAAgB,KAAK;AACnC,SAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,SAAS,IAAI;AACxD;AAEO,SAAS,uBAAuB,UAAU,OAAO,MAAMC,IAAG;AAC/D,MAAI,SAAS,OAAO,CAACA;AAAG,UAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAACA,KAAI,CAAC,MAAM,IAAI,QAAQ;AAAG,UAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAMA,KAAI,SAAS,MAAMA,GAAE,KAAK,QAAQ,IAAIA,KAAIA,GAAE,QAAQ,MAAM,IAAI,QAAQ;AAC9F;AAEO,SAAS,uBAAuB,UAAU,OAAO,OAAO,MAAMA,IAAG;AACtE,MAAI,SAAS;AAAK,UAAM,IAAI,UAAU,gCAAgC;AACtE,MAAI,SAAS,OAAO,CAACA;AAAG,UAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAACA,KAAI,CAAC,MAAM,IAAI,QAAQ;AAAG,UAAM,IAAI,UAAU,yEAAyE;AAChL,SAAQ,SAAS,MAAMA,GAAE,KAAK,UAAU,KAAK,IAAIA,KAAIA,GAAE,QAAQ,QAAQ,MAAM,IAAI,UAAU,KAAK,GAAI;AACtG;AAEO,SAAS,sBAAsB,OAAO,UAAU;AACrD,MAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa;AAAa,UAAM,IAAI,UAAU,wCAAwC;AACvJ,SAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,IAAI,QAAQ;AAC9E;AAEO,SAAS,wBAAwB,KAAK,OAAO,OAAO;AACzD,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACtC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAAY,YAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI,SAAS;AACb,QAAI,OAAO;AACT,UAAI,CAAC,OAAO;AAAc,cAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IACrC;AACA,QAAI,YAAY,QAAQ;AACtB,UAAI,CAAC,OAAO;AAAS,cAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAC9B,UAAI;AAAO,gBAAQ;AAAA,IACrB;AACA,QAAI,OAAO,YAAY;AAAY,YAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI;AAAO,gBAAU,WAAW;AAAE,YAAI;AAAE,gBAAM,KAAK,IAAI;AAAA,QAAG,SAAS,GAAG;AAAE,iBAAO,QAAQ,OAAO,CAAC;AAAA,QAAG;AAAA,MAAE;AACpG,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,MAAa,CAAC;AAAA,EACjE,WACS,OAAO;AACd,QAAI,MAAM,KAAK,EAAE,OAAO,KAAK,CAAC;AAAA,EAChC;AACA,SAAO;AACT;AAOO,SAAS,mBAAmB,KAAK;AACtC,WAAS,KAAK,GAAG;AACf,QAAI,QAAQ,IAAI,WAAW,IAAI,iBAAiB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC5G,QAAI,WAAW;AAAA,EACjB;AACA,MAAI,GAAG,IAAI;AACX,WAAS,OAAO;AACd,WAAO,IAAI,IAAI,MAAM,IAAI,GAAG;AAC1B,UAAI;AACF,YAAI,CAAC,EAAE,SAAS,MAAM;AAAG,iBAAO,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,IAAI;AACrF,YAAI,EAAE,SAAS;AACb,cAAI,SAAS,EAAE,QAAQ,KAAK,EAAE,KAAK;AACnC,cAAI,EAAE;AAAO,mBAAO,KAAK,GAAG,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,mBAAK,CAAC;AAAG,qBAAO,KAAK;AAAA,YAAG,CAAC;AAAA,QACxG;AACK,eAAK;AAAA,MACZ,SACO,GAAG;AACR,aAAK,CAAC;AAAA,MACR;AAAA,IACF;AACA,QAAI,MAAM;AAAG,aAAO,IAAI,WAAW,QAAQ,OAAO,IAAI,KAAK,IAAI,QAAQ,QAAQ;AAC/E,QAAI,IAAI;AAAU,YAAM,IAAI;AAAA,EAC9B;AACA,SAAO,KAAK;AACd;AAEO,SAAS,iCAAiC,MAAM,aAAa;AAClE,MAAI,OAAO,SAAS,YAAY,WAAW,KAAK,IAAI,GAAG;AACnD,WAAO,KAAK,QAAQ,oDAAoD,SAAU,GAAG,KAAK,GAAG,KAAK,IAAI;AAClG,aAAO,MAAM,cAAc,SAAS,QAAQ,MAAM,CAAC,OAAO,CAAC,MAAM,IAAK,IAAI,MAAM,MAAM,GAAG,YAAY,IAAI;AAAA,IAC7G,CAAC;AAAA,EACL;AACA,SAAO;AACT;AA7WA,IAgBI,eAeO,UAyHA,iBA2GP,oBAMA,SA8DA,kBAwCG;AA/WP;AAAA;AAgBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AACjC,sBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUE,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAA,MAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,iBAASL,MAAKK;AAAG,cAAI,OAAO,UAAU,eAAe,KAAKA,IAAGL,EAAC;AAAG,YAAAI,GAAEJ,EAAC,IAAIK,GAAEL,EAAC;AAAA,MAAG;AACpG,aAAO,cAAc,GAAG,CAAC;AAAA,IAC3B;AAUO,IAAI,WAAW,WAAW;AAC/B,iBAAW,OAAO,UAAU,SAASM,UAAS,GAAG;AAC7C,iBAAS,GAAGL,KAAI,GAAG,IAAI,UAAU,QAAQA,KAAI,GAAGA,MAAK;AACjD,cAAI,UAAUA,EAAC;AACf,mBAASD,MAAK;AAAG,gBAAI,OAAO,UAAU,eAAe,KAAK,GAAGA,EAAC;AAAG,gBAAEA,EAAC,IAAI,EAAEA,EAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACvC;AAgHO,IAAI,kBAAkB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAClE,UAAI,OAAO;AAAW,aAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AAC/E,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAChE;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACnC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AAC1B,UAAI,OAAO;AAAW,aAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACb;AAiGA,IAAI,qBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG;AACvD,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACpE,IAAK,SAAS,GAAG,GAAG;AAClB,QAAE,SAAS,IAAI;AAAA,IACjB;AAEA,IAAI,UAAU,SAAS,GAAG;AACxB,gBAAU,OAAO,uBAAuB,SAAUO,IAAG;AACnD,YAAI,KAAK,CAAC;AACV,iBAAS,KAAKA;AAAG,cAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC;AAAG,eAAG,GAAG,MAAM,IAAI;AACjF,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,CAAC;AAAA,IAClB;AAuDA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AACrH,UAAI,IAAI,IAAI,MAAM,OAAO;AACzB,aAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AAAA,IACjF;AAqCA,IAAO,oBAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AChZA;AAAA;AAAA;AAIA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAClB,aAAS,QAAQ,IAAI,SAAS;AAC1B,UAAI,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ;AACvD,UAAI,aAAa,WAAW,QAAQ,aAAa,QAAQ,aAAa;AACtE,UAAI,WAAW,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAChE,aAAO,SAAS,IAAI;AAAA,QAChB;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAIA,aAAS,YAAY,OAAO;AACxB,aAAQ,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU;AAAA,IAC3E;AACA,aAAS,QAAQ,IAAI,OAAO,YAAY,KAAK;AACzC,UAAI,WAAW,YAAY,GAAG,IAAI,MAAM,WAAW,GAAG;AACtD,UAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,UAAI,OAAO,kBAAkB,aAAa;AACtC,wBAAgB,GAAG,KAAK,MAAM,GAAG;AACjC,cAAM,IAAI,UAAU,aAAa;AAAA,MACrC;AACA,aAAO;AAAA,IACX;AACA,aAAS,SAAS,IAAI,OAAO,YAAY;AACrC,UAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAClD,UAAI,WAAW,WAAW,IAAI;AAC9B,UAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,UAAI,OAAO,kBAAkB,aAAa;AACtC,wBAAgB,GAAG,MAAM,MAAM,IAAI;AACnC,cAAM,IAAI,UAAU,aAAa;AAAA,MACrC;AACA,aAAO;AAAA,IACX;AACA,aAAS,SAAS,IAAI,SAAS,UAAU,OAAO,WAAW;AACvD,aAAO,SAAS,KAAK,SAAS,IAAI,OAAO,SAAS;AAAA,IACtD;AACA,aAAS,gBAAgB,IAAI,SAAS;AAClC,UAAI,WAAW,GAAG,WAAW,IAAI,UAAU;AAC3C,aAAO,SAAS,IAAI,MAAM,UAAU,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AAAA,IAClF;AACA,aAAS,iBAAiB,IAAI,SAAS;AACnC,aAAO,SAAS,IAAI,MAAM,UAAU,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AAAA,IAClF;AACA,aAAS,gBAAgB,IAAI,SAAS;AAClC,aAAO,SAAS,IAAI,MAAM,SAAS,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AAAA,IACjF;AAIA,QAAI,oBAAoB,WAAY;AAChC,aAAO,KAAK,UAAU,SAAS;AAAA,IACnC;AAIA,QAAI;AAAA;AAAA,MAA6C,WAAY;AACzD,iBAASC,+BAA8B;AACnC,eAAK,QAAQ,uBAAO,OAAO,IAAI;AAAA,QACnC;AACA,QAAAA,6BAA4B,UAAU,MAAM,SAAU,KAAK;AACvD,iBAAO,KAAK,MAAM,GAAG;AAAA,QACzB;AACA,QAAAA,6BAA4B,UAAU,MAAM,SAAU,KAAK,OAAO;AAC9D,eAAK,MAAM,GAAG,IAAI;AAAA,QACtB;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,QAAI,eAAe;AAAA,MACf,QAAQ,SAAS,SAAS;AACtB,eAAO,IAAI,4BAA4B;AAAA,MAC3C;AAAA,IACJ;AACA,YAAQ,aAAa;AAAA,MACjB,UAAU;AAAA,MACV,SAAS;AAAA,IACb;AAAA;AAAA;;;ACnFA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,YAAY;AACpB,QAAI;AACJ,KAAC,SAAUC,YAAW;AAElB,MAAAA,WAAUA,WAAU,+BAA+B,IAAI,CAAC,IAAI;AAE5D,MAAAA,WAAUA,WAAU,gBAAgB,IAAI,CAAC,IAAI;AAE7C,MAAAA,WAAUA,WAAU,oBAAoB,IAAI,CAAC,IAAI;AAEjD,MAAAA,WAAUA,WAAU,sBAAsB,IAAI,CAAC,IAAI;AAEnD,MAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,MAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,MAAAA,WAAUA,WAAU,yBAAyB,IAAI,CAAC,IAAI;AAEtD,MAAAA,WAAUA,WAAU,4BAA4B,IAAI,CAAC,IAAI;AAEzD,MAAAA,WAAUA,WAAU,wBAAwB,IAAI,CAAC,IAAI;AAErD,MAAAA,WAAUA,WAAU,2BAA2B,IAAI,EAAE,IAAI;AAEzD,MAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAEhE,MAAAA,WAAUA,WAAU,gCAAgC,IAAI,EAAE,IAAI;AAE9D,MAAAA,WAAUA,WAAU,qCAAqC,IAAI,EAAE,IAAI;AAEnE,MAAAA,WAAUA,WAAU,sCAAsC,IAAI,EAAE,IAAI;AAEpE,MAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,MAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,MAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAKxE,MAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAExE,MAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAKhE,MAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAIlE,MAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAElE,MAAAA,WAAUA,WAAU,sBAAsB,IAAI,EAAE,IAAI;AAEpD,MAAAA,WAAUA,WAAU,aAAa,IAAI,EAAE,IAAI;AAE3C,MAAAA,WAAUA,WAAU,kBAAkB,IAAI,EAAE,IAAI;AAEhD,MAAAA,WAAUA,WAAU,uBAAuB,IAAI,EAAE,IAAI;AAErD,MAAAA,WAAUA,WAAU,cAAc,IAAI,EAAE,IAAI;AAAA,IAChD,GAAG,cAAc,QAAQ,YAAY,YAAY,CAAC,EAAE;AAAA;AAAA;;;ACjEpD;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,gBAAgB,QAAQ,OAAO;AACvC,YAAQ,mBAAmB;AAC3B,YAAQ,oBAAoB;AAC5B,YAAQ,kBAAkB;AAC1B,YAAQ,gBAAgB;AACxB,YAAQ,gBAAgB;AACxB,YAAQ,kBAAkB;AAC1B,YAAQ,kBAAkB;AAC1B,YAAQ,iBAAiB;AACzB,YAAQ,eAAe;AACvB,YAAQ,mBAAmB;AAC3B,YAAQ,qBAAqB;AAC7B,YAAQ,uBAAuB;AAC/B,YAAQ,sBAAsB;AAC9B,QAAI;AACJ,KAAC,SAAUC,OAAM;AAIb,MAAAA,MAAKA,MAAK,SAAS,IAAI,CAAC,IAAI;AAI5B,MAAAA,MAAKA,MAAK,UAAU,IAAI,CAAC,IAAI;AAI7B,MAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,MAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,MAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,MAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,MAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAK3B,MAAAA,MAAKA,MAAK,OAAO,IAAI,CAAC,IAAI;AAI1B,MAAAA,MAAKA,MAAK,KAAK,IAAI,CAAC,IAAI;AAAA,IAC5B,GAAG,SAAS,QAAQ,OAAO,OAAO,CAAC,EAAE;AACrC,QAAI;AACJ,KAAC,SAAUC,gBAAe;AACtB,MAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,MAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AAAA,IACnD,GAAG,kBAAkB,QAAQ,gBAAgB,gBAAgB,CAAC,EAAE;AAIhE,aAAS,iBAAiB,IAAI;AAC1B,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,kBAAkB,IAAI;AAC3B,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,gBAAgB,IAAI;AACzB,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,cAAc,IAAI;AACvB,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,cAAc,IAAI;AACvB,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,gBAAgB,IAAI;AACzB,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,gBAAgB,IAAI;AACzB,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,eAAe,IAAI;AACxB,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,aAAa,IAAI;AACtB,aAAO,GAAG,SAAS,KAAK;AAAA,IAC5B;AACA,aAAS,iBAAiB,IAAI;AAC1B,aAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AAAA,IACxE;AACA,aAAS,mBAAmB,IAAI;AAC5B,aAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AAAA,IACxE;AACA,aAAS,qBAAqB,OAAO;AACjC,aAAO;AAAA,QACH,MAAM,KAAK;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,oBAAoB,OAAO,OAAO;AACvC,aAAO;AAAA,QACH,MAAM,KAAK;AAAA,QACX;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AC7GA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,oBAAoB,QAAQ,wBAAwB;AAE5D,YAAQ,wBAAwB;AAChC,YAAQ,oBAAoB;AAAA;AAAA;;;ACL5B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,wBAAwB;AAMhC,QAAI,kBAAkB;AAOtB,aAAS,sBAAsB,UAAU;AACrC,UAAI,SAAS,CAAC;AACd,eAAS,QAAQ,iBAAiB,SAAU,OAAO;AAC/C,YAAI,MAAM,MAAM;AAChB,gBAAQ,MAAM,CAAC,GAAG;AAAA,UAEd,KAAK;AACD,mBAAO,MAAM,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AACzD;AAAA,UAEJ,KAAK;AACD,mBAAO,OAAO,QAAQ,IAAI,YAAY;AACtC;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,8DAA8D;AAAA,UAEvF,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,4CAA4C;AAAA,UAErE,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,QAAQ,CAAC,WAAW,WAAW,SAAS,QAAQ,QAAQ,EAAE,MAAM,CAAC;AACxE;AAAA,UAEJ,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,yCAAyC;AAAA,UAClE,KAAK;AACD,mBAAO,MAAM,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC3C;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,2DAA2D;AAAA,UAEpF,KAAK;AACD,mBAAO,UAAU,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AAC7D;AAAA,UACJ,KAAK;AACD,gBAAI,MAAM,GAAG;AACT,oBAAM,IAAI,WAAW,+CAA+C;AAAA,YACxE;AACA,mBAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA,UACJ,KAAK;AACD,gBAAI,MAAM,GAAG;AACT,oBAAM,IAAI,WAAW,+CAA+C;AAAA,YACxE;AACA,mBAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA,UAEJ,KAAK;AACD,mBAAO,SAAS;AAChB;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,4DAA4D;AAAA,UAErF,KAAK;AACD,mBAAO,YAAY;AACnB,mBAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,UACJ,KAAK;AACD,mBAAO,YAAY;AACnB,mBAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,UACJ,KAAK;AACD,mBAAO,YAAY;AACnB,mBAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,UACJ,KAAK;AACD,mBAAO,YAAY;AACnB,mBAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,kEAAkE;AAAA,UAE3F,KAAK;AACD,mBAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA,UAEJ,KAAK;AACD,mBAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,4DAA4D;AAAA,UAErF,KAAK;AACD,mBAAO,eAAe,MAAM,IAAI,UAAU;AAC1C;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,kBAAM,IAAI,WAAW,sEAAsE;AAAA,QACnG;AACA,eAAO;AAAA,MACX,CAAC;AACD,aAAO;AAAA,IACX;AAAA;AAAA;;;AC3HA,IAAAC,2BAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,oBAAoB;AAE5B,YAAQ,oBAAoB;AAAA;AAAA;;;ACJ5B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,gCAAgC;AACxC,YAAQ,sBAAsB;AAC9B,QAAI,UAAU;AACd,QAAI,oBAAoB;AACxB,aAAS,8BAA8B,UAAU;AAC7C,UAAI,SAAS,WAAW,GAAG;AACvB,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACrD;AAEA,UAAI,eAAe,SACd,MAAM,kBAAkB,iBAAiB,EACzC,OAAO,SAAU,GAAG;AAAE,eAAO,EAAE,SAAS;AAAA,MAAG,CAAC;AACjD,UAAI,SAAS,CAAC;AACd,eAAS,KAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,QAAQ,MAAM;AAC9E,YAAI,cAAc,eAAe,EAAE;AACnC,YAAI,iBAAiB,YAAY,MAAM,GAAG;AAC1C,YAAI,eAAe,WAAW,GAAG;AAC7B,gBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC7C;AACA,YAAI,OAAO,eAAe,CAAC,GAAG,UAAU,eAAe,MAAM,CAAC;AAC9D,iBAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,cAAI,SAAS,UAAU,EAAE;AACzB,cAAI,OAAO,WAAW,GAAG;AACrB,kBAAM,IAAI,MAAM,yBAAyB;AAAA,UAC7C;AAAA,QACJ;AACA,eAAO,KAAK,EAAE,MAAY,QAAiB,CAAC;AAAA,MAChD;AACA,aAAO;AAAA,IACX;AACA,aAAS,cAAc,MAAM;AACzB,aAAO,KAAK,QAAQ,WAAW,EAAE;AAAA,IACrC;AACA,QAAI,2BAA2B;AAC/B,QAAI,8BAA8B;AAClC,QAAI,sBAAsB;AAC1B,QAAI,8BAA8B;AAClC,aAAS,0BAA0B,KAAK;AACpC,UAAI,SAAS,CAAC;AACd,UAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAC7B,eAAO,mBAAmB;AAAA,MAC9B,WACS,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAClC,eAAO,mBAAmB;AAAA,MAC9B;AACA,UAAI,QAAQ,6BAA6B,SAAU,GAAG,IAAI,IAAI;AAE1D,YAAI,OAAO,OAAO,UAAU;AACxB,iBAAO,2BAA2B,GAAG;AACrC,iBAAO,2BAA2B,GAAG;AAAA,QACzC,WAES,OAAO,KAAK;AACjB,iBAAO,2BAA2B,GAAG;AAAA,QACzC,WAES,GAAG,CAAC,MAAM,KAAK;AACpB,iBAAO,2BAA2B,GAAG;AAAA,QACzC,OAEK;AACD,iBAAO,2BAA2B,GAAG;AACrC,iBAAO,2BACH,GAAG,UAAU,OAAO,OAAO,WAAW,GAAG,SAAS;AAAA,QAC1D;AACA,eAAO;AAAA,MACX,CAAC;AACD,aAAO;AAAA,IACX;AACA,aAAS,UAAU,KAAK;AACpB,cAAQ,KAAK;AAAA,QACT,KAAK;AACD,iBAAO;AAAA,YACH,aAAa;AAAA,UACjB;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,YACH,cAAc;AAAA,UAClB;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,YACH,aAAa;AAAA,UACjB;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,YACH,aAAa;AAAA,YACb,cAAc;AAAA,UAClB;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,YACH,aAAa;AAAA,UACjB;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,YACH,aAAa;AAAA,YACb,cAAc;AAAA,UAClB;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AACD,iBAAO;AAAA,YACH,aAAa;AAAA,UACjB;AAAA,MACR;AAAA,IACJ;AACA,aAAS,yCAAyC,MAAM;AAEpD,UAAI;AACJ,UAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,KAAK;AACpC,iBAAS;AAAA,UACL,UAAU;AAAA,QACd;AACA,eAAO,KAAK,MAAM,CAAC;AAAA,MACvB,WACS,KAAK,CAAC,MAAM,KAAK;AACtB,iBAAS;AAAA,UACL,UAAU;AAAA,QACd;AACA,eAAO,KAAK,MAAM,CAAC;AAAA,MACvB;AACA,UAAI,QAAQ;AACR,YAAI,cAAc,KAAK,MAAM,GAAG,CAAC;AACjC,YAAI,gBAAgB,MAAM;AACtB,iBAAO,cAAc;AACrB,iBAAO,KAAK,MAAM,CAAC;AAAA,QACvB,WACS,gBAAgB,MAAM;AAC3B,iBAAO,cAAc;AACrB,iBAAO,KAAK,MAAM,CAAC;AAAA,QACvB;AACA,YAAI,CAAC,4BAA4B,KAAK,IAAI,GAAG;AACzC,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC/D;AACA,eAAO,uBAAuB,KAAK;AAAA,MACvC;AACA,aAAO;AAAA,IACX;AACA,aAAS,qBAAqB,KAAK;AAC/B,UAAI,SAAS,CAAC;AACd,UAAI,WAAW,UAAU,GAAG;AAC5B,UAAI,UAAU;AACV,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAIA,aAAS,oBAAoB,QAAQ;AACjC,UAAI,SAAS,CAAC;AACd,eAAS,KAAK,GAAG,WAAW,QAAQ,KAAK,SAAS,QAAQ,MAAM;AAC5D,YAAI,QAAQ,SAAS,EAAE;AACvB,gBAAQ,MAAM,MAAM;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,QAAQ;AACf;AAAA,UACJ,KAAK;AACD,mBAAO,QAAQ;AACf,mBAAO,QAAQ;AACf;AAAA,UACJ,KAAK;AACD,mBAAO,QAAQ;AACf,mBAAO,WAAW,MAAM,QAAQ,CAAC;AACjC;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,cAAc;AACrB;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,wBAAwB;AAC/B;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,QAAQ;AACf,mBAAO,OAAO,cAAc,MAAM,QAAQ,CAAC,CAAC;AAC5C;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,WAAW;AAClB,mBAAO,iBAAiB;AACxB;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,WAAW;AAClB,mBAAO,iBAAiB;AACxB;AAAA,UACJ,KAAK;AACD,qBAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,aAAa,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKC,MAAK;AAAE,qBAAQ,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,YAAI,GAAG,CAAC,CAAC,CAAC;AACxO;AAAA,UACJ,KAAK;AACD,qBAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,cAAc,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKA,MAAK;AAAE,qBAAQ,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,YAAI,GAAG,CAAC,CAAC,CAAC;AACzO;AAAA,UACJ,KAAK;AACD,mBAAO,WAAW;AAClB;AAAA,UAEJ,KAAK;AACD,mBAAO,kBAAkB;AACzB,mBAAO,cAAc;AACrB;AAAA,UACJ,KAAK;AACD,mBAAO,kBAAkB;AACzB,mBAAO,cAAc;AACrB;AAAA,UACJ,KAAK;AACD,mBAAO,kBAAkB;AACzB,mBAAO,cAAc;AACrB;AAAA,UACJ,KAAK;AACD,mBAAO,kBAAkB;AACzB;AAAA,UACJ,KAAK;AACD,mBAAO,QAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAC1C;AAAA,UACJ,KAAK;AACD,mBAAO,eAAe;AACtB;AAAA,UACJ,KAAK;AACD,mBAAO,eAAe;AACtB;AAAA,UACJ,KAAK;AACD,mBAAO,eAAe;AACtB;AAAA,UACJ,KAAK;AACD,mBAAO,eAAe;AACtB;AAAA,UACJ,KAAK;AACD,mBAAO,eAAe;AACtB;AAAA,UACJ,KAAK;AACD,mBAAO,eAAe;AACtB;AAAA,UACJ,KAAK;AACD,mBAAO,eAAe;AACtB;AAAA,UAEJ,KAAK;AACD,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,oBAAM,IAAI,WAAW,0DAA0D;AAAA,YACnF;AACA,kBAAM,QAAQ,CAAC,EAAE,QAAQ,qBAAqB,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3E,kBAAI,IAAI;AACJ,uBAAO,uBAAuB,GAAG;AAAA,cACrC,WACS,MAAM,IAAI;AACf,sBAAM,IAAI,MAAM,oDAAoD;AAAA,cACxE,WACS,IAAI;AACT,sBAAM,IAAI,MAAM,kDAAkD;AAAA,cACtE;AACA,qBAAO;AAAA,YACX,CAAC;AACD;AAAA,QACR;AAEA,YAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,iBAAO,uBAAuB,MAAM,KAAK;AACzC;AAAA,QACJ;AACA,YAAI,yBAAyB,KAAK,MAAM,IAAI,GAAG;AAI3C,cAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,kBAAM,IAAI,WAAW,+DAA+D;AAAA,UACxF;AACA,gBAAM,KAAK,QAAQ,0BAA0B,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAE1E,gBAAI,OAAO,KAAK;AACZ,qBAAO,wBAAwB,GAAG;AAAA,YACtC,WAES,MAAM,GAAG,CAAC,MAAM,KAAK;AAC1B,qBAAO,wBAAwB,GAAG;AAAA,YACtC,WAES,MAAM,IAAI;AACf,qBAAO,wBAAwB,GAAG;AAClC,qBAAO,wBAAwB,GAAG,SAAS,GAAG;AAAA,YAClD,OACK;AACD,qBAAO,wBAAwB,GAAG;AAClC,qBAAO,wBAAwB,GAAG;AAAA,YACtC;AACA,mBAAO;AAAA,UACX,CAAC;AACD,cAAI,MAAM,MAAM,QAAQ,CAAC;AAEzB,cAAI,QAAQ,KAAK;AACb,qBAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,qBAAqB,iBAAiB,CAAC;AAAA,UACrG,WACS,KAAK;AACV,qBAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,GAAG,CAAC;AAAA,UAC1F;AACA;AAAA,QACJ;AAEA,YAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,mBAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,MAAM,IAAI,CAAC;AAC7F;AAAA,QACJ;AACA,YAAI,WAAW,UAAU,MAAM,IAAI;AACnC,YAAI,UAAU;AACV,mBAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,GAAG,QAAQ;AAAA,QACpE;AACA,YAAI,sCAAsC,yCAAyC,MAAM,IAAI;AAC7F,YAAI,qCAAqC;AACrC,mBAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM,GAAG,mCAAmC;AAAA,QAC/F;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AC/TA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,UAAU;AACd,YAAQ,aAAa,qBAAwB,OAAO;AACpD,YAAQ,aAAa,kBAAqB,OAAO;AAAA;AAAA;;;ACJjD;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,WAAW;AAGnB,YAAQ,WAAW;AAAA,MACf,OAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,MACA,SAAS;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AC34CA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,QAAI,wBAAwB;AAQ5B,aAAS,eAAe,UAAU,QAAQ;AACtC,UAAI,eAAe;AACnB,eAAS,aAAa,GAAG,aAAa,SAAS,QAAQ,cAAc;AACjE,YAAI,cAAc,SAAS,OAAO,UAAU;AAC5C,YAAI,gBAAgB,KAAK;AACrB,cAAI,cAAc;AAClB,iBAAO,aAAa,IAAI,SAAS,UAC7B,SAAS,OAAO,aAAa,CAAC,MAAM,aAAa;AACjD;AACA;AAAA,UACJ;AACA,cAAI,UAAU,KAAK,cAAc;AACjC,cAAI,eAAe,cAAc,IAAI,IAAI,KAAK,eAAe;AAC7D,cAAI,gBAAgB;AACpB,cAAI,WAAW,+BAA+B,MAAM;AACpD,cAAI,YAAY,OAAO,YAAY,KAAK;AACpC,2BAAe;AAAA,UACnB;AACA,iBAAO,iBAAiB,GAAG;AACvB,4BAAgB;AAAA,UACpB;AACA,iBAAO,YAAY,GAAG;AAClB,2BAAe,WAAW;AAAA,UAC9B;AAAA,QACJ,WACS,gBAAgB,KAAK;AAC1B,0BAAgB;AAAA,QACpB,OACK;AACD,0BAAgB;AAAA,QACpB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAMA,aAAS,+BAA+B,QAAQ;AAC5C,UAAI,YAAY,OAAO;AACvB,UAAI,cAAc;AAAA,MAEd,OAAO;AAAA,MAEP,OAAO,WAAW,QAAQ;AAE1B,oBAAY,OAAO,WAAW,CAAC;AAAA,MACnC;AACA,UAAI,WAAW;AACX,gBAAQ,WAAW;AAAA,UACf,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX;AACI,kBAAM,IAAI,MAAM,mBAAmB;AAAA,QAC3C;AAAA,MACJ;AAEA,UAAI,cAAc,OAAO;AACzB,UAAI;AACJ,UAAI,gBAAgB,QAAQ;AACxB,oBAAY,OAAO,SAAS,EAAE;AAAA,MAClC;AACA,UAAI,aAAa,sBAAsB,SAAS,aAAa,EAAE,KAC3D,sBAAsB,SAAS,eAAe,EAAE,KAChD,sBAAsB,SAAS,GAAG,OAAO,aAAa,MAAM,CAAC,KAC7D,sBAAsB,SAAS,KAAK;AACxC,aAAO,WAAW,CAAC;AAAA,IACvB;AAAA;AAAA;;;ACrFA;AAAA;AAAA;AACA,QAAI;AACJ,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,SAAS;AACjB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,oBAAoB;AACxB,QAAI,wBAAwB;AAC5B,QAAI,gCAAgC;AACpC,QAAI,8BAA8B,IAAI,OAAO,IAAI,OAAO,kBAAkB,sBAAsB,QAAQ,GAAG,CAAC;AAC5G,QAAI,4BAA4B,IAAI,OAAO,GAAG,OAAO,kBAAkB,sBAAsB,QAAQ,IAAI,CAAC;AAC1G,aAAS,eAAe,OAAO,KAAK;AAChC,aAAO,EAAE,OAAc,IAAS;AAAA,IACpC;AAGA,QAAI,sBAAsB,CAAC,CAAC,OAAO,UAAU,cAAc,KAAK,WAAW,KAAK,CAAC;AACjF,QAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,QAAI,uBAAuB,CAAC,CAAC,OAAO;AACpC,QAAI,uBAAuB,CAAC,CAAC,OAAO,UAAU;AAC9C,QAAI,eAAe,CAAC,CAAC,OAAO,UAAU;AACtC,QAAI,aAAa,CAAC,CAAC,OAAO,UAAU;AACpC,QAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,QAAI,gBAAgB,yBACd,OAAO,gBACP,SAAU,GAAG;AACX,aAAQ,OAAO,MAAM,YACjB,SAAS,CAAC,KACV,KAAK,MAAM,CAAC,MAAM,KAClB,KAAK,IAAI,CAAC,KAAK;AAAA,IACvB;AAEJ,QAAI,yBAAyB;AAC7B,QAAI;AACI,WAAK,GAAG,6CAA6C,IAAI;AAO7D,iCAA2B,KAAK,GAAG,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO;AAAA,IAClG,SACO,GAAG;AACN,+BAAyB;AAAA,IAC7B;AAXQ;AAYR,QAAI,aAAa;AAAA;AAAA,MAET,SAASC,YAAW,GAAG,QAAQ,UAAU;AACrC,eAAO,EAAE,WAAW,QAAQ,QAAQ;AAAA,MACxC;AAAA;AAAA;AAAA,MAEA,SAASA,YAAW,GAAG,QAAQ,UAAU;AACrC,eAAO,EAAE,MAAM,UAAU,WAAW,OAAO,MAAM,MAAM;AAAA,MAC3D;AAAA;AACR,QAAI,gBAAgB,yBACd,OAAO;AAAA;AAAA,MAEL,SAASC,iBAAgB;AACrB,YAAI,aAAa,CAAC;AAClB,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,qBAAW,EAAE,IAAI,UAAU,EAAE;AAAA,QACjC;AACA,YAAI,WAAW;AACf,YAAI,SAAS,WAAW;AACxB,YAAIC,KAAI;AACR,YAAI;AACJ,eAAO,SAASA,IAAG;AACf,iBAAO,WAAWA,IAAG;AACrB,cAAI,OAAO;AACP,kBAAM,WAAW,OAAO,4BAA4B;AACxD,sBACI,OAAO,QACD,OAAO,aAAa,IAAI,IACxB,OAAO,eAAe,QAAQ,UAAY,MAAM,OAAS,OAAO,OAAS,KAAM;AAAA,QAC7F;AACA,eAAO;AAAA,MACX;AAAA;AACR,QAAI;AAAA;AAAA,MAEJ,uBACM,OAAO;AAAA;AAAA,QAEL,SAASC,aAAY,SAAS;AAC1B,cAAI,MAAM,CAAC;AACX,mBAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,gBAAIC,MAAK,UAAU,EAAE,GAAG,IAAIA,IAAG,CAAC,GAAG,IAAIA,IAAG,CAAC;AAC3C,gBAAI,CAAC,IAAI;AAAA,UACb;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AACR,QAAI,cAAc;AAAA;AAAA,MAEV,SAASC,aAAY,GAAG,OAAO;AAC3B,eAAO,EAAE,YAAY,KAAK;AAAA,MAC9B;AAAA;AAAA;AAAA,MAEA,SAASA,aAAY,GAAG,OAAO;AAC3B,YAAI,OAAO,EAAE;AACb,YAAI,QAAQ,KAAK,SAAS,MAAM;AAC5B,iBAAO;AAAA,QACX;AACA,YAAI,QAAQ,EAAE,WAAW,KAAK;AAC9B,YAAI;AACJ,eAAO,QAAQ,SACX,QAAQ,SACR,QAAQ,MAAM,SACb,SAAS,EAAE,WAAW,QAAQ,CAAC,KAAK,SACrC,SAAS,QACP,SACE,QAAQ,SAAW,OAAO,SAAS,SAAU;AAAA,MACzD;AAAA;AACR,QAAI,YAAY;AAAA;AAAA,MAER,SAASC,WAAU,GAAG;AAClB,eAAO,EAAE,UAAU;AAAA,MACvB;AAAA;AAAA;AAAA,MAEA,SAASA,WAAU,GAAG;AAClB,eAAO,EAAE,QAAQ,6BAA6B,EAAE;AAAA,MACpD;AAAA;AACR,QAAI,UAAU;AAAA;AAAA,MAEN,SAASC,SAAQ,GAAG;AAChB,eAAO,EAAE,QAAQ;AAAA,MACrB;AAAA;AAAA;AAAA,MAEA,SAASA,SAAQ,GAAG;AAChB,eAAO,EAAE,QAAQ,2BAA2B,EAAE;AAAA,MAClD;AAAA;AAER,aAAS,GAAG,GAAG,MAAM;AACjB,aAAO,IAAI,OAAO,GAAG,IAAI;AAAA,IAC7B;AAEA,QAAI;AACJ,QAAI,wBAAwB;AAEpB,+BAAyB,GAAG,6CAA6C,IAAI;AACjF,+BAAyB,SAASC,wBAAuB,GAAG,OAAO;AAC/D,YAAIJ;AACJ,+BAAuB,YAAY;AACnC,YAAI,QAAQ,uBAAuB,KAAK,CAAC;AACzC,gBAAQA,MAAK,MAAM,CAAC,OAAO,QAAQA,QAAO,SAASA,MAAK;AAAA,MAC5D;AAAA,IACJ,OACK;AAED,+BAAyB,SAASI,wBAAuB,GAAG,OAAO;AAC/D,YAAI,QAAQ,CAAC;AACb,eAAO,MAAM;AACT,cAAI,IAAI,YAAY,GAAG,KAAK;AAC5B,cAAI,MAAM,UAAa,cAAc,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC5D;AAAA,UACJ;AACA,gBAAM,KAAK,CAAC;AACZ,mBAAS,KAAK,QAAU,IAAI;AAAA,QAChC;AACA,eAAO,cAAc,MAAM,QAAQ,KAAK;AAAA,MAC5C;AAAA,IACJ;AAtBQ;AAuBR,QAAI;AAAA;AAAA,MAAwB,WAAY;AACpC,iBAASC,QAAO,SAAS,SAAS;AAC9B,cAAI,YAAY,QAAQ;AAAE,sBAAU,CAAC;AAAA,UAAG;AACxC,eAAK,UAAU;AACf,eAAK,WAAW,EAAE,QAAQ,GAAG,MAAM,GAAG,QAAQ,EAAE;AAChD,eAAK,YAAY,CAAC,CAAC,QAAQ;AAC3B,eAAK,SAAS,QAAQ;AACtB,eAAK,sBAAsB,CAAC,CAAC,QAAQ;AACrC,eAAK,uBAAuB,CAAC,CAAC,QAAQ;AAAA,QAC1C;AACA,QAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,cAAI,KAAK,OAAO,MAAM,GAAG;AACrB,kBAAM,MAAM,8BAA8B;AAAA,UAC9C;AACA,iBAAO,KAAK,aAAa,GAAG,IAAI,KAAK;AAAA,QACzC;AACA,QAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe,mBAAmB;AACtF,cAAI,WAAW,CAAC;AAChB,iBAAO,CAAC,KAAK,MAAM,GAAG;AAClB,gBAAI,OAAO,KAAK,KAAK;AACrB,gBAAI,SAAS,KAAe;AACxB,kBAAI,SAAS,KAAK,cAAc,cAAc,iBAAiB;AAC/D,kBAAI,OAAO,KAAK;AACZ,uBAAO;AAAA,cACX;AACA,uBAAS,KAAK,OAAO,GAAG;AAAA,YAC5B,WACS,SAAS,OAAiB,eAAe,GAAG;AACjD;AAAA,YACJ,WACS,SAAS,OACb,kBAAkB,YAAY,kBAAkB,kBAAkB;AACnE,kBAAI,WAAW,KAAK,cAAc;AAClC,mBAAK,KAAK;AACV,uBAAS,KAAK;AAAA,gBACV,MAAM,QAAQ,KAAK;AAAA,gBACnB,UAAU,eAAe,UAAU,KAAK,cAAc,CAAC;AAAA,cAC3D,CAAC;AAAA,YACL,WACS,SAAS,MACd,CAAC,KAAK,aACN,KAAK,KAAK,MAAM,IAClB;AACE,kBAAI,mBAAmB;AACnB;AAAA,cACJ,OACK;AACD,uBAAO,KAAK,MAAM,QAAQ,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,cACzH;AAAA,YACJ,WACS,SAAS,MACd,CAAC,KAAK,aACN,SAAS,KAAK,KAAK,KAAK,CAAC,GAAG;AAC5B,kBAAI,SAAS,KAAK,SAAS,cAAc,aAAa;AACtD,kBAAI,OAAO,KAAK;AACZ,uBAAO;AAAA,cACX;AACA,uBAAS,KAAK,OAAO,GAAG;AAAA,YAC5B,OACK;AACD,kBAAI,SAAS,KAAK,aAAa,cAAc,aAAa;AAC1D,kBAAI,OAAO,KAAK;AACZ,uBAAO;AAAA,cACX;AACA,uBAAS,KAAK,OAAO,GAAG;AAAA,YAC5B;AAAA,UACJ;AACA,iBAAO,EAAE,KAAK,UAAU,KAAK,KAAK;AAAA,QACtC;AAmBA,QAAAA,QAAO,UAAU,WAAW,SAAU,cAAc,eAAe;AAC/D,cAAI,gBAAgB,KAAK,cAAc;AACvC,eAAK,KAAK;AACV,cAAI,UAAU,KAAK,aAAa;AAChC,eAAK,UAAU;AACf,cAAI,KAAK,OAAO,IAAI,GAAG;AAEnB,mBAAO;AAAA,cACH,KAAK;AAAA,gBACD,MAAM,QAAQ,KAAK;AAAA,gBACnB,OAAO,IAAI,OAAO,SAAS,IAAI;AAAA,gBAC/B,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,cAChE;AAAA,cACA,KAAK;AAAA,YACT;AAAA,UACJ,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,gBAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,IAAI;AAC5E,gBAAI,eAAe,KAAK;AACpB,qBAAO;AAAA,YACX;AACA,gBAAI,WAAW,eAAe;AAE9B,gBAAI,sBAAsB,KAAK,cAAc;AAC7C,gBAAI,KAAK,OAAO,IAAI,GAAG;AACnB,kBAAI,KAAK,MAAM,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG;AACxC,uBAAO,KAAK,MAAM,QAAQ,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,cAC9G;AACA,kBAAI,8BAA8B,KAAK,cAAc;AACrD,kBAAI,iBAAiB,KAAK,aAAa;AACvC,kBAAI,YAAY,gBAAgB;AAC5B,uBAAO,KAAK,MAAM,QAAQ,UAAU,uBAAuB,eAAe,6BAA6B,KAAK,cAAc,CAAC,CAAC;AAAA,cAChI;AACA,mBAAK,UAAU;AACf,kBAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,uBAAO,KAAK,MAAM,QAAQ,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,cAC9G;AACA,qBAAO;AAAA,gBACH,KAAK;AAAA,kBACD,MAAM,QAAQ,KAAK;AAAA,kBACnB,OAAO;AAAA,kBACP;AAAA,kBACA,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,gBAChE;AAAA,gBACA,KAAK;AAAA,cACT;AAAA,YACJ,OACK;AACD,qBAAO,KAAK,MAAM,QAAQ,UAAU,cAAc,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,YACzG;AAAA,UACJ,OACK;AACD,mBAAO,KAAK,MAAM,QAAQ,UAAU,aAAa,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,UACxG;AAAA,QACJ;AAIA,QAAAA,QAAO,UAAU,eAAe,WAAY;AACxC,cAAI,cAAc,KAAK,OAAO;AAC9B,eAAK,KAAK;AACV,iBAAO,CAAC,KAAK,MAAM,KAAK,4BAA4B,KAAK,KAAK,CAAC,GAAG;AAC9D,iBAAK,KAAK;AAAA,UACd;AACA,iBAAO,KAAK,QAAQ,MAAM,aAAa,KAAK,OAAO,CAAC;AAAA,QACxD;AACA,QAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe;AACnE,cAAI,QAAQ,KAAK,cAAc;AAC/B,cAAI,QAAQ;AACZ,iBAAO,MAAM;AACT,gBAAI,mBAAmB,KAAK,cAAc,aAAa;AACvD,gBAAI,kBAAkB;AAClB,uBAAS;AACT;AAAA,YACJ;AACA,gBAAI,sBAAsB,KAAK,iBAAiB,cAAc,aAAa;AAC3E,gBAAI,qBAAqB;AACrB,uBAAS;AACT;AAAA,YACJ;AACA,gBAAI,uBAAuB,KAAK,yBAAyB;AACzD,gBAAI,sBAAsB;AACtB,uBAAS;AACT;AAAA,YACJ;AACA;AAAA,UACJ;AACA,cAAI,WAAW,eAAe,OAAO,KAAK,cAAc,CAAC;AACzD,iBAAO;AAAA,YACH,KAAK,EAAE,MAAM,QAAQ,KAAK,SAAS,OAAc,SAAmB;AAAA,YACpE,KAAK;AAAA,UACT;AAAA,QACJ;AACA,QAAAA,QAAO,UAAU,2BAA2B,WAAY;AACpD,cAAI,CAAC,KAAK,MAAM,KACZ,KAAK,KAAK,MAAM,OACf,KAAK;AAAA,UAEF,CAAC,gBAAgB,KAAK,KAAK,KAAK,CAAC,IAAI;AACzC,iBAAK,KAAK;AACV,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AAMA,QAAAA,QAAO,UAAU,gBAAgB,SAAU,eAAe;AACtD,cAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,IAAc;AAC9C,mBAAO;AAAA,UACX;AAGA,kBAAQ,KAAK,KAAK,GAAG;AAAA,YACjB,KAAK;AAED,mBAAK,KAAK;AACV,mBAAK,KAAK;AACV,qBAAO;AAAA,YAEX,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACD;AAAA,YACJ,KAAK;AACD,kBAAI,kBAAkB,YAAY,kBAAkB,iBAAiB;AACjE;AAAA,cACJ;AACA,qBAAO;AAAA,YACX;AACI,qBAAO;AAAA,UACf;AACA,eAAK,KAAK;AACV,cAAI,aAAa,CAAC,KAAK,KAAK,CAAC;AAC7B,eAAK,KAAK;AAEV,iBAAO,CAAC,KAAK,MAAM,GAAG;AAClB,gBAAI,KAAK,KAAK,KAAK;AACnB,gBAAI,OAAO,IAAc;AACrB,kBAAI,KAAK,KAAK,MAAM,IAAc;AAC9B,2BAAW,KAAK,EAAE;AAElB,qBAAK,KAAK;AAAA,cACd,OACK;AAED,qBAAK,KAAK;AACV;AAAA,cACJ;AAAA,YACJ,OACK;AACD,yBAAW,KAAK,EAAE;AAAA,YACtB;AACA,iBAAK,KAAK;AAAA,UACd;AACA,iBAAO,cAAc,MAAM,QAAQ,UAAU;AAAA,QACjD;AACA,QAAAA,QAAO,UAAU,mBAAmB,SAAU,cAAc,eAAe;AACvE,cAAI,KAAK,MAAM,GAAG;AACd,mBAAO;AAAA,UACX;AACA,cAAI,KAAK,KAAK,KAAK;AACnB,cAAI,OAAO,MACP,OAAO,OACN,OAAO,OACH,kBAAkB,YAAY,kBAAkB,oBACpD,OAAO,OAAiB,eAAe,GAAI;AAC5C,mBAAO;AAAA,UACX,OACK;AACD,iBAAK,KAAK;AACV,mBAAO,cAAc,EAAE;AAAA,UAC3B;AAAA,QACJ;AACA,QAAAA,QAAO,UAAU,gBAAgB,SAAU,cAAc,mBAAmB;AACxE,cAAI,uBAAuB,KAAK,cAAc;AAC9C,eAAK,KAAK;AACV,eAAK,UAAU;AACf,cAAI,KAAK,MAAM,GAAG;AACd,mBAAO,KAAK,MAAM,QAAQ,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UACjI;AACA,cAAI,KAAK,KAAK,MAAM,KAAe;AAC/B,iBAAK,KAAK;AACV,mBAAO,KAAK,MAAM,QAAQ,UAAU,gBAAgB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UAClH;AAEA,cAAI,QAAQ,KAAK,0BAA0B,EAAE;AAC7C,cAAI,CAAC,OAAO;AACR,mBAAO,KAAK,MAAM,QAAQ,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UACtH;AACA,eAAK,UAAU;AACf,cAAI,KAAK,MAAM,GAAG;AACd,mBAAO,KAAK,MAAM,QAAQ,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UACjI;AACA,kBAAQ,KAAK,KAAK,GAAG;AAAA,YAEjB,KAAK,KAAe;AAChB,mBAAK,KAAK;AACV,qBAAO;AAAA,gBACH,KAAK;AAAA,kBACD,MAAM,QAAQ,KAAK;AAAA;AAAA,kBAEnB;AAAA,kBACA,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,gBACvE;AAAA,gBACA,KAAK;AAAA,cACT;AAAA,YACJ;AAAA,YAEA,KAAK,IAAc;AACf,mBAAK,KAAK;AACV,mBAAK,UAAU;AACf,kBAAI,KAAK,MAAM,GAAG;AACd,uBAAO,KAAK,MAAM,QAAQ,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,cACjI;AACA,qBAAO,KAAK,qBAAqB,cAAc,mBAAmB,OAAO,oBAAoB;AAAA,YACjG;AAAA,YACA;AACI,qBAAO,KAAK,MAAM,QAAQ,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UAC1H;AAAA,QACJ;AAKA,QAAAA,QAAO,UAAU,4BAA4B,WAAY;AACrD,cAAI,mBAAmB,KAAK,cAAc;AAC1C,cAAI,cAAc,KAAK,OAAO;AAC9B,cAAI,QAAQ,uBAAuB,KAAK,SAAS,WAAW;AAC5D,cAAI,YAAY,cAAc,MAAM;AACpC,eAAK,OAAO,SAAS;AACrB,cAAI,cAAc,KAAK,cAAc;AACrC,cAAI,WAAW,eAAe,kBAAkB,WAAW;AAC3D,iBAAO,EAAE,OAAc,SAAmB;AAAA,QAC9C;AACA,QAAAA,QAAO,UAAU,uBAAuB,SAAU,cAAc,mBAAmB,OAAO,sBAAsB;AAC5G,cAAIL;AAIJ,cAAI,oBAAoB,KAAK,cAAc;AAC3C,cAAI,UAAU,KAAK,0BAA0B,EAAE;AAC/C,cAAI,kBAAkB,KAAK,cAAc;AACzC,kBAAQ,SAAS;AAAA,YACb,KAAK;AAED,qBAAO,KAAK,MAAM,QAAQ,UAAU,sBAAsB,eAAe,mBAAmB,eAAe,CAAC;AAAA,YAChH,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK,QAAQ;AAIT,mBAAK,UAAU;AACf,kBAAI,mBAAmB;AACvB,kBAAI,KAAK,OAAO,GAAG,GAAG;AAClB,qBAAK,UAAU;AACf,oBAAI,qBAAqB,KAAK,cAAc;AAC5C,oBAAI,SAAS,KAAK,8BAA8B;AAChD,oBAAI,OAAO,KAAK;AACZ,yBAAO;AAAA,gBACX;AACA,oBAAI,QAAQ,QAAQ,OAAO,GAAG;AAC9B,oBAAI,MAAM,WAAW,GAAG;AACpB,yBAAO,KAAK,MAAM,QAAQ,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,gBACzH;AACA,oBAAI,gBAAgB,eAAe,oBAAoB,KAAK,cAAc,CAAC;AAC3E,mCAAmB,EAAE,OAAc,cAA6B;AAAA,cACpE;AACA,kBAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,kBAAI,eAAe,KAAK;AACpB,uBAAO;AAAA,cACX;AACA,kBAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAE1E,kBAAI,oBAAoB,WAAW,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,MAAM,CAAC,GAAG;AAErI,oBAAI,WAAW,UAAU,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACxD,oBAAI,YAAY,UAAU;AACtB,sBAAI,SAAS,KAAK,8BAA8B,UAAU,iBAAiB,aAAa;AACxF,sBAAI,OAAO,KAAK;AACZ,2BAAO;AAAA,kBACX;AACA,yBAAO;AAAA,oBACH,KAAK,EAAE,MAAM,QAAQ,KAAK,QAAQ,OAAc,UAAU,YAAY,OAAO,OAAO,IAAI;AAAA,oBACxF,KAAK;AAAA,kBACT;AAAA,gBACJ,OACK;AACD,sBAAI,SAAS,WAAW,GAAG;AACvB,2BAAO,KAAK,MAAM,QAAQ,UAAU,2BAA2B,UAAU;AAAA,kBAC7E;AACA,sBAAI,kBAAkB;AAItB,sBAAI,KAAK,QAAQ;AACb,uCAAmB,GAAG,8BAA8B,gBAAgB,UAAU,KAAK,MAAM;AAAA,kBAC7F;AACA,sBAAI,QAAQ;AAAA,oBACR,MAAM,QAAQ,cAAc;AAAA,oBAC5B,SAAS;AAAA,oBACT,UAAU,iBAAiB;AAAA,oBAC3B,eAAe,KAAK,wBACb,GAAG,sBAAsB,uBAAuB,eAAe,IAChE,CAAC;AAAA,kBACX;AACA,sBAAI,OAAO,YAAY,SAAS,QAAQ,KAAK,OAAO,QAAQ,KAAK;AACjE,yBAAO;AAAA,oBACH,KAAK,EAAE,MAAY,OAAc,UAAU,YAAY,MAAa;AAAA,oBACpE,KAAK;AAAA,kBACT;AAAA,gBACJ;AAAA,cACJ;AAEA,qBAAO;AAAA,gBACH,KAAK;AAAA,kBACD,MAAM,YAAY,WACZ,QAAQ,KAAK,SACb,YAAY,SACR,QAAQ,KAAK,OACb,QAAQ,KAAK;AAAA,kBACvB;AAAA,kBACA,UAAU;AAAA,kBACV,QAAQA,MAAK,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,QAAQA,QAAO,SAASA,MAAK;AAAA,gBAC9I;AAAA,gBACA,KAAK;AAAA,cACT;AAAA,YACJ;AAAA,YACA,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK,UAAU;AAIX,kBAAI,oBAAoB,KAAK,cAAc;AAC3C,mBAAK,UAAU;AACf,kBAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,uBAAO,KAAK,MAAM,QAAQ,UAAU,gCAAgC,eAAe,mBAAmB,QAAQ,SAAS,CAAC,GAAG,iBAAiB,CAAC,CAAC;AAAA,cAClJ;AACA,mBAAK,UAAU;AASf,kBAAI,wBAAwB,KAAK,0BAA0B;AAC3D,kBAAI,eAAe;AACnB,kBAAI,YAAY,YAAY,sBAAsB,UAAU,UAAU;AAClE,oBAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,yBAAO,KAAK,MAAM,QAAQ,UAAU,qCAAqC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,gBACvI;AACA,qBAAK,UAAU;AACf,oBAAI,SAAS,KAAK,uBAAuB,QAAQ,UAAU,qCAAqC,QAAQ,UAAU,oCAAoC;AACtJ,oBAAI,OAAO,KAAK;AACZ,yBAAO;AAAA,gBACX;AAEA,qBAAK,UAAU;AACf,wCAAwB,KAAK,0BAA0B;AACvD,+BAAe,OAAO;AAAA,cAC1B;AACA,kBAAI,gBAAgB,KAAK,8BAA8B,cAAc,SAAS,mBAAmB,qBAAqB;AACtH,kBAAI,cAAc,KAAK;AACnB,uBAAO;AAAA,cACX;AACA,kBAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,kBAAI,eAAe,KAAK;AACpB,uBAAO;AAAA,cACX;AACA,kBAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAC1E,kBAAI,YAAY,UAAU;AACtB,uBAAO;AAAA,kBACH,KAAK;AAAA,oBACD,MAAM,QAAQ,KAAK;AAAA,oBACnB;AAAA,oBACA,SAAS,YAAY,cAAc,GAAG;AAAA,oBACtC,UAAU;AAAA,kBACd;AAAA,kBACA,KAAK;AAAA,gBACT;AAAA,cACJ,OACK;AACD,uBAAO;AAAA,kBACH,KAAK;AAAA,oBACD,MAAM,QAAQ,KAAK;AAAA,oBACnB;AAAA,oBACA,SAAS,YAAY,cAAc,GAAG;AAAA,oBACtC,QAAQ;AAAA,oBACR,YAAY,YAAY,WAAW,aAAa;AAAA,oBAChD,UAAU;AAAA,kBACd;AAAA,kBACA,KAAK;AAAA,gBACT;AAAA,cACJ;AAAA,YACJ;AAAA,YACA;AACI,qBAAO,KAAK,MAAM,QAAQ,UAAU,uBAAuB,eAAe,mBAAmB,eAAe,CAAC;AAAA,UACrH;AAAA,QACJ;AACA,QAAAK,QAAO,UAAU,wBAAwB,SAAU,sBAAsB;AAGrE,cAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAe;AAC/C,mBAAO,KAAK,MAAM,QAAQ,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UACjI;AACA,eAAK,KAAK;AACV,iBAAO,EAAE,KAAK,MAAM,KAAK,KAAK;AAAA,QAClC;AAIA,QAAAA,QAAO,UAAU,gCAAgC,WAAY;AACzD,cAAI,eAAe;AACnB,cAAI,gBAAgB,KAAK,cAAc;AACvC,iBAAO,CAAC,KAAK,MAAM,GAAG;AAClB,gBAAI,KAAK,KAAK,KAAK;AACnB,oBAAQ,IAAI;AAAA,cACR,KAAK,IAAc;AAGf,qBAAK,KAAK;AACV,oBAAI,qBAAqB,KAAK,cAAc;AAC5C,oBAAI,CAAC,KAAK,UAAU,GAAG,GAAG;AACtB,yBAAO,KAAK,MAAM,QAAQ,UAAU,kCAAkC,eAAe,oBAAoB,KAAK,cAAc,CAAC,CAAC;AAAA,gBAClI;AACA,qBAAK,KAAK;AACV;AAAA,cACJ;AAAA,cACA,KAAK,KAAe;AAChB,gCAAgB;AAChB,qBAAK,KAAK;AACV;AAAA,cACJ;AAAA,cACA,KAAK,KAAe;AAChB,oBAAI,eAAe,GAAG;AAClB,kCAAgB;AAAA,gBACpB,OACK;AACD,yBAAO;AAAA,oBACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,oBAC3D,KAAK;AAAA,kBACT;AAAA,gBACJ;AACA;AAAA,cACJ;AAAA,cACA;AACI,qBAAK,KAAK;AACV;AAAA,YACR;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,YAC3D,KAAK;AAAA,UACT;AAAA,QACJ;AACA,QAAAA,QAAO,UAAU,gCAAgC,SAAU,UAAU,UAAU;AAC3E,cAAI,SAAS,CAAC;AACd,cAAI;AACA,sBAAU,GAAG,sBAAsB,+BAA+B,QAAQ;AAAA,UAC9E,SACO,GAAG;AACN,mBAAO,KAAK,MAAM,QAAQ,UAAU,yBAAyB,QAAQ;AAAA,UACzE;AACA,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,QAAQ,cAAc;AAAA,cAC5B;AAAA,cACA;AAAA,cACA,eAAe,KAAK,wBACb,GAAG,sBAAsB,qBAAqB,MAAM,IACrD,CAAC;AAAA,YACX;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ;AAWA,QAAAA,QAAO,UAAU,gCAAgC,SAAU,cAAc,eAAe,gBAAgB,uBAAuB;AAC3H,cAAIL;AACJ,cAAI,iBAAiB;AACrB,cAAI,UAAU,CAAC;AACf,cAAI,kBAAkB,oBAAI,IAAI;AAC9B,cAAI,WAAW,sBAAsB,OAAO,mBAAmB,sBAAsB;AAIrF,iBAAO,MAAM;AACT,gBAAI,SAAS,WAAW,GAAG;AACvB,kBAAI,gBAAgB,KAAK,cAAc;AACvC,kBAAI,kBAAkB,YAAY,KAAK,OAAO,GAAG,GAAG;AAEhD,oBAAI,SAAS,KAAK,uBAAuB,QAAQ,UAAU,iCAAiC,QAAQ,UAAU,gCAAgC;AAC9I,oBAAI,OAAO,KAAK;AACZ,yBAAO;AAAA,gBACX;AACA,mCAAmB,eAAe,eAAe,KAAK,cAAc,CAAC;AACrE,2BAAW,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,cACrE,OACK;AACD;AAAA,cACJ;AAAA,YACJ;AAEA,gBAAI,gBAAgB,IAAI,QAAQ,GAAG;AAC/B,qBAAO,KAAK,MAAM,kBAAkB,WAC9B,QAAQ,UAAU,qCAClB,QAAQ,UAAU,oCAAoC,gBAAgB;AAAA,YAChF;AACA,gBAAI,aAAa,SAAS;AACtB,+BAAiB;AAAA,YACrB;AAIA,iBAAK,UAAU;AACf,gBAAI,uBAAuB,KAAK,cAAc;AAC9C,gBAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,qBAAO,KAAK,MAAM,kBAAkB,WAC9B,QAAQ,UAAU,2CAClB,QAAQ,UAAU,0CAA0C,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,YAChI;AACA,gBAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,cAAc;AACtF,gBAAI,eAAe,KAAK;AACpB,qBAAO;AAAA,YACX;AACA,gBAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,gBAAI,eAAe,KAAK;AACpB,qBAAO;AAAA,YACX;AACA,oBAAQ,KAAK;AAAA,cACT;AAAA,cACA;AAAA,gBACI,OAAO,eAAe;AAAA,gBACtB,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,cACvE;AAAA,YACJ,CAAC;AAED,4BAAgB,IAAI,QAAQ;AAE5B,iBAAK,UAAU;AACf,YAACA,MAAK,KAAK,0BAA0B,GAAG,WAAWA,IAAG,OAAO,mBAAmBA,IAAG;AAAA,UACvF;AACA,cAAI,QAAQ,WAAW,GAAG;AACtB,mBAAO,KAAK,MAAM,kBAAkB,WAC9B,QAAQ,UAAU,kCAClB,QAAQ,UAAU,iCAAiC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,UACvH;AACA,cAAI,KAAK,uBAAuB,CAAC,gBAAgB;AAC7C,mBAAO,KAAK,MAAM,QAAQ,UAAU,sBAAsB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,UACxH;AACA,iBAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,QACrC;AACA,QAAAK,QAAO,UAAU,yBAAyB,SAAU,mBAAmB,oBAAoB;AACvF,cAAI,OAAO;AACX,cAAI,mBAAmB,KAAK,cAAc;AAC1C,cAAI,KAAK,OAAO,GAAG,GAAG;AAAA,UACtB,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,mBAAO;AAAA,UACX;AACA,cAAI,YAAY;AAChB,cAAI,UAAU;AACd,iBAAO,CAAC,KAAK,MAAM,GAAG;AAClB,gBAAI,KAAK,KAAK,KAAK;AACnB,gBAAI,MAAM,MAAgB,MAAM,IAAc;AAC1C,0BAAY;AACZ,wBAAU,UAAU,MAAM,KAAK;AAC/B,mBAAK,KAAK;AAAA,YACd,OACK;AACD;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,WAAW,eAAe,kBAAkB,KAAK,cAAc,CAAC;AACpE,cAAI,CAAC,WAAW;AACZ,mBAAO,KAAK,MAAM,mBAAmB,QAAQ;AAAA,UACjD;AACA,qBAAW;AACX,cAAI,CAAC,cAAc,OAAO,GAAG;AACzB,mBAAO,KAAK,MAAM,oBAAoB,QAAQ;AAAA,UAClD;AACA,iBAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,QACrC;AACA,QAAAA,QAAO,UAAU,SAAS,WAAY;AAClC,iBAAO,KAAK,SAAS;AAAA,QACzB;AACA,QAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,iBAAO,KAAK,OAAO,MAAM,KAAK,QAAQ;AAAA,QAC1C;AACA,QAAAA,QAAO,UAAU,gBAAgB,WAAY;AAEzC,iBAAO;AAAA,YACH,QAAQ,KAAK,SAAS;AAAA,YACtB,MAAM,KAAK,SAAS;AAAA,YACpB,QAAQ,KAAK,SAAS;AAAA,UAC1B;AAAA,QACJ;AAKA,QAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,cAAI,SAAS,KAAK,SAAS;AAC3B,cAAI,UAAU,KAAK,QAAQ,QAAQ;AAC/B,kBAAM,MAAM,cAAc;AAAA,UAC9B;AACA,cAAI,OAAO,YAAY,KAAK,SAAS,MAAM;AAC3C,cAAI,SAAS,QAAW;AACpB,kBAAM,MAAM,UAAU,OAAO,QAAQ,0CAA0C,CAAC;AAAA,UACpF;AACA,iBAAO;AAAA,QACX;AACA,QAAAA,QAAO,UAAU,QAAQ,SAAU,MAAM,UAAU;AAC/C,iBAAO;AAAA,YACH,KAAK;AAAA,YACL,KAAK;AAAA,cACD;AAAA,cACA,SAAS,KAAK;AAAA,cACd;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,QAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,cAAI,KAAK,MAAM,GAAG;AACd;AAAA,UACJ;AACA,cAAI,OAAO,KAAK,KAAK;AACrB,cAAI,SAAS,IAAe;AACxB,iBAAK,SAAS,QAAQ;AACtB,iBAAK,SAAS,SAAS;AACvB,iBAAK,SAAS,UAAU;AAAA,UAC5B,OACK;AACD,iBAAK,SAAS,UAAU;AAExB,iBAAK,SAAS,UAAU,OAAO,QAAU,IAAI;AAAA,UACjD;AAAA,QACJ;AAOA,QAAAA,QAAO,UAAU,SAAS,SAAU,QAAQ;AACxC,cAAI,WAAW,KAAK,SAAS,QAAQ,KAAK,OAAO,CAAC,GAAG;AACjD,qBAASP,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACpC,mBAAK,KAAK;AAAA,YACd;AACA,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AAKA,QAAAO,QAAO,UAAU,YAAY,SAAU,SAAS;AAC5C,cAAI,gBAAgB,KAAK,OAAO;AAChC,cAAI,QAAQ,KAAK,QAAQ,QAAQ,SAAS,aAAa;AACvD,cAAI,SAAS,GAAG;AACZ,iBAAK,OAAO,KAAK;AACjB,mBAAO;AAAA,UACX,OACK;AACD,iBAAK,OAAO,KAAK,QAAQ,MAAM;AAC/B,mBAAO;AAAA,UACX;AAAA,QACJ;AAKA,QAAAA,QAAO,UAAU,SAAS,SAAU,cAAc;AAC9C,cAAI,KAAK,OAAO,IAAI,cAAc;AAC9B,kBAAM,MAAM,gBAAgB,OAAO,cAAc,uDAAuD,EAAE,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,UACnI;AACA,yBAAe,KAAK,IAAI,cAAc,KAAK,QAAQ,MAAM;AACzD,iBAAO,MAAM;AACT,gBAAI,SAAS,KAAK,OAAO;AACzB,gBAAI,WAAW,cAAc;AACzB;AAAA,YACJ;AACA,gBAAI,SAAS,cAAc;AACvB,oBAAM,MAAM,gBAAgB,OAAO,cAAc,0CAA0C,CAAC;AAAA,YAChG;AACA,iBAAK,KAAK;AACV,gBAAI,KAAK,MAAM,GAAG;AACd;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAEA,QAAAA,QAAO,UAAU,YAAY,WAAY;AACrC,iBAAO,CAAC,KAAK,MAAM,KAAK,cAAc,KAAK,KAAK,CAAC,GAAG;AAChD,iBAAK,KAAK;AAAA,UACd;AAAA,QACJ;AAKA,QAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,cAAI,KAAK,MAAM,GAAG;AACd,mBAAO;AAAA,UACX;AACA,cAAI,OAAO,KAAK,KAAK;AACrB,cAAI,SAAS,KAAK,OAAO;AACzB,cAAI,WAAW,KAAK,QAAQ,WAAW,UAAU,QAAQ,QAAU,IAAI,EAAE;AACzE,iBAAO,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,QACjE;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,SAAS;AAMjB,aAAS,SAAS,WAAW;AACzB,aAAS,aAAa,MAAM,aAAa,OACpC,aAAa,MAAM,aAAa;AAAA,IACzC;AACA,aAAS,gBAAgB,WAAW;AAChC,aAAO,SAAS,SAAS,KAAK,cAAc;AAAA,IAChD;AAEA,aAAS,4BAA4B,GAAG;AACpC,aAAQ,MAAM,MACV,MAAM,MACL,KAAK,MAAM,KAAK,MACjB,MAAM,MACL,KAAK,MAAM,KAAK,OAChB,KAAK,MAAM,KAAK,MACjB,KAAK,OACJ,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAS,KAAK,QACnB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAW,KAAK;AAAA,IAC9B;AAKA,aAAS,cAAc,GAAG;AACtB,aAAS,KAAK,KAAU,KAAK,MACzB,MAAM,MACN,MAAM,OACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM;AAAA,IACd;AAKA,aAAS,iBAAiB,GAAG;AACzB,aAAS,KAAK,MAAU,KAAK,MACzB,MAAM,MACL,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACL,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,OAAU,KAAK,OACrB,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,OACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK;AAAA,IAC7B;AAAA;AAAA;;;AC9vCA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB;AACzB,YAAQ,qBAAqB;AAC7B,QAAI,UAAU;AACd,QAAI,UAAU;AACd,aAAS,UAAU,KAAK;AACpB,UAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,eAAO,QAAQ,cAAc,CAAC,GAAG,IAAI,IAAI,SAAS,GAAG,IAAI;AAAA,MAC7D;AACA,UAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;AAEzC,eAAO,OAAO,KAAK,GAAG,EAAE,OAAO,SAAU,QAAQ,GAAG;AAEhD,iBAAO,CAAC,IAAI,UAAU,IAAI,CAAC,CAAC;AAC5B,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AAAA,MACT;AACA,aAAO;AAAA,IACX;AACA,aAAS,2BAA2B,KAAK,IAAI,kBAAkB;AAE3D,UAAI,SAAS,UAAU,EAAE;AACzB,UAAI,UAAU,OAAO;AACrB,aAAO,UAAU,OAAO,KAAK,OAAO,EAAE,OAAO,SAAU,KAAK,GAAG;AAC3D,YAAI,WAAW,eAAe,QAAQ,cAAc,QAAQ,cAAc,QAAQ,cAAc,CAAC,GAAG,IAAI,MAAM,GAAG,gBAAgB,GAAG,IAAI,GAAG,QAAQ,CAAC,EAAE,OAAO,IAAI,GAAG,IAAI,MAAM,mBAAmB,CAAC,GAAG,IAAI,CAAC;AAC1M,YAAI,CAAC,IAAI;AAAA,UACL,OAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,IACX;AACA,aAAS,wBAAwB,IAAI;AACjC,cAAQ,GAAG,QAAQ,iBAAiB,EAAE,MAAM,GAAG,QAAQ,iBAAiB,EAAE;AAAA,IAC9E;AACA,aAAS,0BAA0B,KAAK;AACpC,aAAO,CAAC,CAAC,IAAI,KAAK,SAAU,IAAI;AAC5B,YAAI,wBAAwB,EAAE,GAAG;AAC7B,iBAAO;AAAA,QACX;AACA,aAAK,GAAG,QAAQ,cAAc,EAAE,GAAG;AAC/B,iBAAO,0BAA0B,GAAG,QAAQ;AAAA,QAChD;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AAYA,aAAS,eAAe,KAAK;AACzB,eAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACjC,YAAI,KAAK,IAAIA,EAAC;AACd,YAAI,wBAAwB,EAAE,GAAG;AAC7B,iBAAO,CAAC,2BAA2B,KAAK,IAAIA,EAAC,CAAC;AAAA,QAClD;AACA,aAAK,GAAG,QAAQ,cAAc,EAAE,KAAK,0BAA0B,CAAC,EAAE,CAAC,GAAG;AAClE,gBAAM,IAAI,MAAM,8GAA8G;AAAA,QAClI;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAMA,aAAS,iBAAiB,KAAK,MAAM;AACjC,UAAI,SAAS,QAAQ;AAAE,eAAO,oBAAI,IAAI;AAAA,MAAG;AACzC,UAAI,QAAQ,SAAU,IAAI;AACtB,aAAK,GAAG,QAAQ,mBAAmB,EAAE,MAChC,GAAG,QAAQ,eAAe,EAAE,MAC5B,GAAG,QAAQ,eAAe,EAAE,MAC5B,GAAG,QAAQ,iBAAiB,EAAE,GAAG;AAClC,cAAI,GAAG,SAAS,QAAQ,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,MAAM;AACpD,kBAAM,IAAI,MAAM,YAAY,OAAO,GAAG,OAAO,wBAAwB,CAAC;AAAA,UAC1E;AACA,eAAK,IAAI,GAAG,OAAO,GAAG,IAAI;AAAA,QAC9B;AACA,aAAK,GAAG,QAAQ,iBAAiB,EAAE,MAAM,GAAG,QAAQ,iBAAiB,EAAE,GAAG;AACtE,eAAK,IAAI,GAAG,OAAO,GAAG,IAAI;AAC1B,iBAAO,KAAK,GAAG,OAAO,EAAE,QAAQ,SAAU,GAAG;AACzC,6BAAiB,GAAG,QAAQ,CAAC,EAAE,OAAO,IAAI;AAAA,UAC9C,CAAC;AAAA,QACL;AACA,aAAK,GAAG,QAAQ,cAAc,EAAE,GAAG;AAC/B,eAAK,IAAI,GAAG,OAAO,GAAG,IAAI;AAC1B,2BAAiB,GAAG,UAAU,IAAI;AAAA,QACtC;AAAA,MACJ,CAAC;AAAA,IACL;AAQA,aAAS,mBAAmB,GAAG,GAAG;AAC9B,UAAI,QAAQ,oBAAI,IAAI;AACpB,UAAI,QAAQ,oBAAI,IAAI;AACpB,uBAAiB,GAAG,KAAK;AACzB,uBAAiB,GAAG,KAAK;AACzB,UAAI,MAAM,SAAS,MAAM,MAAM;AAC3B,eAAO;AAAA,UACH,SAAS;AAAA,UACT,OAAO,IAAI,MAAM,mCAAmC,OAAO,MAAM,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,QAAQ,EAAE,OAAO,MAAM,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,QAC9J;AAAA,MACJ;AACA,aAAO,MAAM,KAAK,MAAM,QAAQ,CAAC,EAAE,OAAO,SAAU,QAAQ,IAAI;AAC5D,YAAI,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC;AAC5B,YAAI,CAAC,OAAO,SAAS;AACjB,iBAAO;AAAA,QACX;AACA,YAAI,QAAQ,MAAM,IAAI,GAAG;AACzB,YAAI,SAAS,MAAM;AACf,iBAAO;AAAA,YACH,SAAS;AAAA,YACT,OAAO,IAAI,MAAM,oBAAoB,OAAO,KAAK,aAAa,CAAC;AAAA,UACnE;AAAA,QACJ;AACA,YAAI,UAAU,MAAM;AAChB,iBAAO;AAAA,YACH,SAAS;AAAA,YACT,OAAO,IAAI,MAAM,YAAY,OAAO,KAAK,0BAA0B,EAAE,OAAO,QAAQ,KAAK,IAAI,GAAG,MAAM,EAAE,OAAO,QAAQ,KAAK,KAAK,CAAC,CAAC;AAAA,UACvI;AAAA,QACJ;AACA,eAAO;AAAA,MACX,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IACxB;AAAA;AAAA;;;AC1IA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,qBAAqB,QAAQ,UAAU;AAC/C,YAAQ,QAAQ;AAChB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,UAAU;AACd,aAAS,cAAc,KAAK;AACxB,UAAI,QAAQ,SAAU,IAAI;AACtB,eAAO,GAAG;AACV,aAAK,GAAG,QAAQ,iBAAiB,EAAE,MAAM,GAAG,QAAQ,iBAAiB,EAAE,GAAG;AACtE,mBAAS,KAAK,GAAG,SAAS;AACtB,mBAAO,GAAG,QAAQ,CAAC,EAAE;AACrB,0BAAc,GAAG,QAAQ,CAAC,EAAE,KAAK;AAAA,UACrC;AAAA,QACJ,YACU,GAAG,QAAQ,iBAAiB,EAAE,MAAM,GAAG,QAAQ,kBAAkB,GAAG,KAAK,GAAG;AAClF,iBAAO,GAAG,MAAM;AAAA,QACpB,aACW,GAAG,QAAQ,eAAe,EAAE,MAAM,GAAG,QAAQ,eAAe,EAAE,OACpE,GAAG,QAAQ,oBAAoB,GAAG,KAAK,GAAG;AAC3C,iBAAO,GAAG,MAAM;AAAA,QACpB,YACU,GAAG,QAAQ,cAAc,EAAE,GAAG;AACpC,wBAAc,GAAG,QAAQ;AAAA,QAC7B;AAAA,MACJ,CAAC;AAAA,IACL;AACA,aAAS,MAAM,SAAS,MAAM;AAC1B,UAAI,SAAS,QAAQ;AAAE,eAAO,CAAC;AAAA,MAAG;AAClC,aAAO,QAAQ,SAAS,EAAE,sBAAsB,MAAM,qBAAqB,KAAK,GAAG,IAAI;AACvF,UAAI,SAAS,IAAI,SAAS,OAAO,SAAS,IAAI,EAAE,MAAM;AACtD,UAAI,OAAO,KAAK;AACZ,YAAI,QAAQ,YAAY,QAAQ,UAAU,OAAO,IAAI,IAAI,CAAC;AAE1D,cAAM,WAAW,OAAO,IAAI;AAE5B,cAAM,kBAAkB,OAAO,IAAI;AACnC,cAAM;AAAA,MACV;AACA,UAAI,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,kBAAkB;AACrE,sBAAc,OAAO,GAAG;AAAA,MAC5B;AACA,aAAO,OAAO;AAAA,IAClB;AACA,YAAQ,aAAa,iBAAoB,OAAO;AAEhD,YAAQ,UAAU,SAAS;AAC3B,QAAI,gBAAgB;AACpB,WAAO,eAAe,SAAS,sBAAsB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,cAAc;AAAA,IAAoB,EAAE,CAAC;AAAA;AAAA;;;AClDxI,IAAAC,iBAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,oBAAoB,QAAQ,wBAAwB,QAAQ,oBAAoB,QAAQ,cAAc,QAAQ,YAAY;AAClI,QAAI,UAAU;AACd,QAAI;AACJ,KAAC,SAAUC,YAAW;AAElB,MAAAA,WAAU,eAAe,IAAI;AAE7B,MAAAA,WAAU,eAAe,IAAI;AAE7B,MAAAA,WAAU,kBAAkB,IAAI;AAAA,IACpC,GAAG,cAAc,QAAQ,YAAY,YAAY,CAAC,EAAE;AACpD,QAAI;AAAA;AAAA,MAA6B,SAAU,QAAQ;AAC/C,gBAAQ,UAAUC,cAAa,MAAM;AACrC,iBAASA,aAAY,KAAK,MAAM,iBAAiB;AAC7C,cAAI,QAAQ,OAAO,KAAK,MAAM,GAAG,KAAK;AACtC,gBAAM,OAAO;AACb,gBAAM,kBAAkB;AACxB,iBAAO;AAAA,QACX;AACA,QAAAA,aAAY,UAAU,WAAW,WAAY;AACzC,iBAAO,oBAAoB,OAAO,KAAK,MAAM,IAAI,EAAE,OAAO,KAAK,OAAO;AAAA,QAC1E;AACA,eAAOA;AAAA,MACX,EAAE,KAAK;AAAA;AACP,YAAQ,cAAc;AACtB,QAAI;AAAA;AAAA,MAAmC,SAAU,QAAQ;AACrD,gBAAQ,UAAUC,oBAAmB,MAAM;AAC3C,iBAASA,mBAAkB,YAAY,OAAO,SAAS,iBAAiB;AACpE,iBAAO,OAAO,KAAK,MAAM,uBAAwB,OAAO,YAAY,MAAQ,EAAE,OAAO,OAAO,kBAAoB,EAAE,OAAO,OAAO,KAAK,OAAO,EAAE,KAAK,MAAM,GAAG,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,QACpN;AACA,eAAOA;AAAA,MACX,EAAE,WAAW;AAAA;AACb,YAAQ,oBAAoB;AAC5B,QAAI;AAAA;AAAA,MAAuC,SAAU,QAAQ;AACzD,gBAAQ,UAAUC,wBAAuB,MAAM;AAC/C,iBAASA,uBAAsB,OAAO,MAAM,iBAAiB;AACzD,iBAAO,OAAO,KAAK,MAAM,cAAe,OAAO,OAAO,oBAAqB,EAAE,OAAO,IAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,QAC5I;AACA,eAAOA;AAAA,MACX,EAAE,WAAW;AAAA;AACb,YAAQ,wBAAwB;AAChC,QAAI;AAAA;AAAA,MAAmC,SAAU,QAAQ;AACrD,gBAAQ,UAAUC,oBAAmB,MAAM;AAC3C,iBAASA,mBAAkB,YAAY,iBAAiB;AACpD,iBAAO,OAAO,KAAK,MAAM,qCAAsC,OAAO,YAAY,oCAAsC,EAAE,OAAO,iBAAiB,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,QAC1M;AACA,eAAOA;AAAA,MACX,EAAE,WAAW;AAAA;AACb,YAAQ,oBAAoB;AAAA;AAAA;;;AClD5B;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,YAAY;AACpB,YAAQ,uBAAuB;AAC/B,YAAQ,gBAAgB;AACxB,QAAI,6BAA6B;AACjC,QAAI,UAAU;AACd,QAAI;AACJ,KAAC,SAAUC,YAAW;AAClB,MAAAA,WAAUA,WAAU,SAAS,IAAI,CAAC,IAAI;AACtC,MAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AAAA,IACzC,GAAG,cAAc,QAAQ,YAAY,YAAY,CAAC,EAAE;AACpD,aAAS,aAAa,OAAO;AACzB,UAAI,MAAM,SAAS,GAAG;AAClB,eAAO;AAAA,MACX;AACA,aAAO,MAAM,OAAO,SAAU,KAAK,MAAM;AACrC,YAAI,WAAW,IAAI,IAAI,SAAS,CAAC;AACjC,YAAI,CAAC,YACD,SAAS,SAAS,UAAU,WAC5B,KAAK,SAAS,UAAU,SAAS;AACjC,cAAI,KAAK,IAAI;AAAA,QACjB,OACK;AACD,mBAAS,SAAS,KAAK;AAAA,QAC3B;AACA,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AAAA,IACT;AACA,aAAS,qBAAqB,IAAI;AAC9B,aAAO,OAAO,OAAO;AAAA,IACzB;AAEA,aAAS,cAAc,KAAK,SAAS,YAAY,SAAS,QAAQ,oBAElE,iBAAiB;AAEb,UAAI,IAAI,WAAW,MAAM,GAAG,2BAA2B,kBAAkB,IAAI,CAAC,CAAC,GAAG;AAC9E,eAAO;AAAA,UACH;AAAA,YACI,MAAM,UAAU;AAAA,YAChB,OAAO,IAAI,CAAC,EAAE;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,SAAS,CAAC;AACd,eAAS,KAAK,GAAG,QAAQ,KAAK,KAAK,MAAM,QAAQ,MAAM;AACnD,YAAI,KAAK,MAAM,EAAE;AAEjB,aAAK,GAAG,2BAA2B,kBAAkB,EAAE,GAAG;AACtD,iBAAO,KAAK;AAAA,YACR,MAAM,UAAU;AAAA,YAChB,OAAO,GAAG;AAAA,UACd,CAAC;AACD;AAAA,QACJ;AAGA,aAAK,GAAG,2BAA2B,gBAAgB,EAAE,GAAG;AACpD,cAAI,OAAO,uBAAuB,UAAU;AACxC,mBAAO,KAAK;AAAA,cACR,MAAM,UAAU;AAAA,cAChB,OAAO,WAAW,gBAAgB,OAAO,EAAE,OAAO,kBAAkB;AAAA,YACxE,CAAC;AAAA,UACL;AACA;AAAA,QACJ;AACA,YAAI,UAAU,GAAG;AAEjB,YAAI,EAAE,UAAU,WAAW,SAAS;AAChC,gBAAM,IAAI,QAAQ,kBAAkB,SAAS,eAAe;AAAA,QAChE;AACA,YAAI,QAAQ,OAAO,OAAO;AAC1B,aAAK,GAAG,2BAA2B,mBAAmB,EAAE,GAAG;AACvD,cAAI,CAAC,SAAS,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAClE,oBACI,OAAO,UAAU,YAAY,OAAO,UAAU,WACxC,OAAO,KAAK,IACZ;AAAA,UACd;AACA,iBAAO,KAAK;AAAA,YACR,MAAM,OAAO,UAAU,WAAW,UAAU,UAAU,UAAU;AAAA,YAChE;AAAA,UACJ,CAAC;AACD;AAAA,QACJ;AAIA,aAAK,GAAG,2BAA2B,eAAe,EAAE,GAAG;AACnD,cAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,KACpB,GAAG,2BAA2B,oBAAoB,GAAG,KAAK,IACvD,GAAG,MAAM,gBACT;AACV,iBAAO,KAAK;AAAA,YACR,MAAM,UAAU;AAAA,YAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,UACrB,CAAC;AACD;AAAA,QACJ;AACA,aAAK,GAAG,2BAA2B,eAAe,EAAE,GAAG;AACnD,cAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,KACpB,GAAG,2BAA2B,oBAAoB,GAAG,KAAK,IACvD,GAAG,MAAM,gBACT,QAAQ,KAAK;AACvB,iBAAO,KAAK;AAAA,YACR,MAAM,UAAU;AAAA,YAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,UACrB,CAAC;AACD;AAAA,QACJ;AACA,aAAK,GAAG,2BAA2B,iBAAiB,EAAE,GAAG;AACrD,cAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,OAAO,GAAG,KAAK,KACtB,GAAG,2BAA2B,kBAAkB,GAAG,KAAK,IACrD,GAAG,MAAM,gBACT;AACV,cAAI,SAAS,MAAM,OAAO;AACtB,oBACI,SACK,MAAM,SAAS;AAAA,UAC5B;AACA,iBAAO,KAAK;AAAA,YACR,MAAM,UAAU;AAAA,YAChB,OAAO,WACF,gBAAgB,SAAS,KAAK,EAC9B,OAAO,KAAK;AAAA,UACrB,CAAC;AACD;AAAA,QACJ;AACA,aAAK,GAAG,2BAA2B,cAAc,EAAE,GAAG;AAClD,cAAI,WAAW,GAAG,UAAU,UAAU,GAAG;AACzC,cAAI,WAAW,OAAO,OAAO;AAC7B,cAAI,CAAC,qBAAqB,QAAQ,GAAG;AACjC,kBAAM,IAAI,QAAQ,sBAAsB,SAAS,YAAY,eAAe;AAAA,UAChF;AACA,cAAI,QAAQ,cAAc,UAAU,SAAS,YAAY,SAAS,QAAQ,kBAAkB;AAC5F,cAAI,SAAS,SAAS,MAAM,IAAI,SAAUC,IAAG;AAAE,mBAAOA,GAAE;AAAA,UAAO,CAAC,CAAC;AACjE,cAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,qBAAS,CAAC,MAAM;AAAA,UACpB;AACA,iBAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,SAAU,GAAG;AAC9C,mBAAO;AAAA,cACH,MAAM,OAAO,MAAM,WAAW,UAAU,UAAU,UAAU;AAAA,cAC5D,OAAO;AAAA,YACX;AAAA,UACJ,CAAC,CAAC;AAAA,QACN;AACA,aAAK,GAAG,2BAA2B,iBAAiB,EAAE,GAAG;AACrD,cAAI,MAAM,GAAG,QAAQ,KAAK,KAAK,GAAG,QAAQ;AAC1C,cAAI,CAAC,KAAK;AACN,kBAAM,IAAI,QAAQ,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,UACjG;AACA,iBAAO,KAAK,MAAM,QAAQ,cAAc,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM,CAAC;AACxF;AAAA,QACJ;AACA,aAAK,GAAG,2BAA2B,iBAAiB,EAAE,GAAG;AACrD,cAAI,MAAM,GAAG,QAAQ,IAAI,OAAO,KAAK,CAAC;AACtC,cAAI,CAAC,KAAK;AACN,gBAAI,CAAC,KAAK,aAAa;AACnB,oBAAM,IAAI,QAAQ,YAAY,mHAAqH,QAAQ,UAAU,kBAAkB,eAAe;AAAA,YAC1M;AACA,gBAAI,OAAO,WACN,eAAe,SAAS,EAAE,MAAM,GAAG,WAAW,CAAC,EAC/C,OAAO,SAAS,GAAG,UAAU,EAAE;AACpC,kBAAM,GAAG,QAAQ,IAAI,KAAK,GAAG,QAAQ;AAAA,UACzC;AACA,cAAI,CAAC,KAAK;AACN,kBAAM,IAAI,QAAQ,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,UACjG;AACA,iBAAO,KAAK,MAAM,QAAQ,cAAc,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,SAAS,GAAG,UAAU,EAAE,CAAC;AAClH;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,aAAa,MAAM;AAAA,IAC9B;AAAA;AAAA;;;ACrLA;AAAA;AAAA;AAMA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,oBAAoB;AAC5B,QAAI,UAAU;AACd,QAAI,iBAAiB;AACrB,QAAI,6BAA6B;AACjC,QAAI,eAAe;AAEnB,aAAS,YAAY,IAAI,IAAI;AACzB,UAAI,CAAC,IAAI;AACL,eAAO;AAAA,MACX;AACA,aAAO,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAI,MAAM,CAAC,CAAE,GAAI,MAAM,CAAC,CAAE,GAAG,OAAO,KAAK,EAAE,EAAE,OAAO,SAAU,KAAK,GAAG;AAC7H,YAAI,CAAC,IAAI,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAI,GAAG,CAAC,KAAK,CAAC,CAAE;AACpE,eAAO;AAAA,MACX,GAAG,CAAC,CAAC,CAAC;AAAA,IACV;AACA,aAAS,aAAa,eAAe,SAAS;AAC1C,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,aAAO,OAAO,KAAK,aAAa,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,YAAI,CAAC,IAAI,YAAY,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC;AACjD,eAAO;AAAA,MACX,GAAG,QAAQ,SAAS,CAAC,GAAG,aAAa,CAAC;AAAA,IAC1C;AACA,aAAS,uBAAuB,OAAO;AACnC,aAAO;AAAA,QACH,QAAQ,WAAY;AAChB,iBAAO;AAAA,YACH,KAAK,SAAU,KAAK;AAChB,qBAAO,MAAM,GAAG;AAAA,YACpB;AAAA,YACA,KAAK,SAAU,KAAK,OAAO;AACvB,oBAAM,GAAG,IAAI;AAAA,YACjB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,wBAAwB,OAAO;AACpC,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,UAC5B,QAAQ,CAAC;AAAA,UACT,UAAU,CAAC;AAAA,UACX,aAAa,CAAC;AAAA,QAClB;AAAA,MAAG;AACH,aAAO;AAAA,QACH,kBAAkB,GAAG,eAAe,SAAS,WAAY;AACrD,cAAI;AACJ,cAAI,OAAO,CAAC;AACZ,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAK,EAAE,IAAI,UAAU,EAAE;AAAA,UAC3B;AACA,iBAAO,MAAM,KAAK,KAAK,cAAc,KAAK,MAAM,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,QACvG,GAAG;AAAA,UACC,OAAO,uBAAuB,MAAM,MAAM;AAAA,UAC1C,UAAU,eAAe,WAAW;AAAA,QACxC,CAAC;AAAA,QACD,oBAAoB,GAAG,eAAe,SAAS,WAAY;AACvD,cAAI;AACJ,cAAI,OAAO,CAAC;AACZ,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAK,EAAE,IAAI,UAAU,EAAE;AAAA,UAC3B;AACA,iBAAO,MAAM,KAAK,KAAK,gBAAgB,KAAK,MAAM,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,QACzG,GAAG;AAAA,UACC,OAAO,uBAAuB,MAAM,QAAQ;AAAA,UAC5C,UAAU,eAAe,WAAW;AAAA,QACxC,CAAC;AAAA,QACD,iBAAiB,GAAG,eAAe,SAAS,WAAY;AACpD,cAAI;AACJ,cAAI,OAAO,CAAC;AACZ,mBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAK,EAAE,IAAI,UAAU,EAAE;AAAA,UAC3B;AACA,iBAAO,MAAM,KAAK,KAAK,aAAa,KAAK,MAAM,IAAI,QAAQ,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,QACtG,GAAG;AAAA,UACC,OAAO,uBAAuB,MAAM,WAAW;AAAA,UAC/C,UAAU,eAAe,WAAW;AAAA,QACxC,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAI;AAAA;AAAA,MAAmC,WAAY;AAC/C,iBAASC,mBAAkB,SAAS,SAAS,iBAAiB,MAAM;AAChE,cAAI,YAAY,QAAQ;AAAE,sBAAUA,mBAAkB;AAAA,UAAe;AACrE,cAAI,QAAQ;AACZ,eAAK,iBAAiB;AAAA,YAClB,QAAQ,CAAC;AAAA,YACT,UAAU,CAAC;AAAA,YACX,aAAa,CAAC;AAAA,UAClB;AACA,eAAK,SAAS,SAAU,QAAQ;AAC5B,gBAAI,QAAQ,MAAM,cAAc,MAAM;AAEtC,gBAAI,MAAM,WAAW,GAAG;AACpB,qBAAO,MAAM,CAAC,EAAE;AAAA,YACpB;AACA,gBAAI,SAAS,MAAM,OAAO,SAAU,KAAK,MAAM;AAC3C,kBAAI,CAAC,IAAI,UACL,KAAK,SAAS,aAAa,UAAU,WACrC,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,UAAU;AACzC,oBAAI,KAAK,KAAK,KAAK;AAAA,cACvB,OACK;AACD,oBAAI,IAAI,SAAS,CAAC,KAAK,KAAK;AAAA,cAChC;AACA,qBAAO;AAAA,YACX,GAAG,CAAC,CAAC;AACL,gBAAI,OAAO,UAAU,GAAG;AACpB,qBAAO,OAAO,CAAC,KAAK;AAAA,YACxB;AACA,mBAAO;AAAA,UACX;AACA,eAAK,gBAAgB,SAAU,QAAQ;AACnC,oBAAQ,GAAG,aAAa,eAAe,MAAM,KAAK,MAAM,SAAS,MAAM,YAAY,MAAM,SAAS,QAAQ,QAAW,MAAM,OAAO;AAAA,UACtI;AACA,eAAK,kBAAkB,WAAY;AAC/B,gBAAIC;AACJ,mBAAQ;AAAA,cACJ,UAAUA,MAAK,MAAM,oBAAoB,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,MAClF,KAAK,aAAa,mBAAmB,MAAM,OAAO,EAAE,CAAC;AAAA,YAC7D;AAAA,UACJ;AACA,eAAK,SAAS,WAAY;AAAE,mBAAO,MAAM;AAAA,UAAK;AAE9C,eAAK,UAAU;AACf,eAAK,iBAAiBD,mBAAkB,cAAc,OAAO;AAC7D,cAAI,OAAO,YAAY,UAAU;AAC7B,iBAAK,UAAU;AACf,gBAAI,CAACA,mBAAkB,SAAS;AAC5B,oBAAM,IAAI,UAAU,6EAA6E;AAAA,YACrG;AACA,gBAAI,KAAK,QAAQ,CAAC,GAAG,aAAa,GAAG,YAAY,YAAY,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC;AAE9F,iBAAK,MAAMA,mBAAkB,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,QAAQ,KAAK,eAAe,CAAC,CAAC;AAAA,UACpI,OACK;AACD,iBAAK,MAAM;AAAA,UACf;AACA,cAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,GAAG;AAC1B,kBAAM,IAAI,UAAU,gDAAgD;AAAA,UACxE;AAGA,eAAK,UAAU,aAAaA,mBAAkB,SAAS,eAAe;AACtE,eAAK,aACA,QAAQ,KAAK,cAAe,wBAAwB,KAAK,cAAc;AAAA,QAChF;AACA,eAAO,eAAeA,oBAAmB,iBAAiB;AAAA,UACtD,KAAK,WAAY;AACb,gBAAI,CAACA,mBAAkB,uBAAuB;AAC1C,cAAAA,mBAAkB,wBACd,IAAI,KAAK,aAAa,EAAE,gBAAgB,EAAE;AAAA,YAClD;AACA,mBAAOA,mBAAkB;AAAA,UAC7B;AAAA,UACA,YAAY;AAAA,UACZ,cAAc;AAAA,QAClB,CAAC;AACD,QAAAA,mBAAkB,wBAAwB;AAC1C,QAAAA,mBAAkB,gBAAgB,SAAU,SAAS;AACjD,cAAI,OAAO,KAAK,WAAW,aAAa;AACpC;AAAA,UACJ;AACA,cAAI,mBAAmB,KAAK,aAAa,mBAAmB,OAAO;AACnE,cAAI,iBAAiB,SAAS,GAAG;AAC7B,mBAAO,IAAI,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,UAC9C;AACA,iBAAO,IAAI,KAAK,OAAO,OAAO,YAAY,WAAW,UAAU,QAAQ,CAAC,CAAC;AAAA,QAC7E;AACA,QAAAA,mBAAkB,UAAU,2BAA2B;AAIvD,QAAAA,mBAAkB,UAAU;AAAA,UACxB,QAAQ;AAAA,YACJ,SAAS;AAAA,cACL,uBAAuB;AAAA,YAC3B;AAAA,YACA,UAAU;AAAA,cACN,OAAO;AAAA,YACX;AAAA,YACA,SAAS;AAAA,cACL,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,MAAM;AAAA,YACF,OAAO;AAAA,cACH,OAAO;AAAA,cACP,KAAK;AAAA,cACL,MAAM;AAAA,YACV;AAAA,YACA,QAAQ;AAAA,cACJ,OAAO;AAAA,cACP,KAAK;AAAA,cACL,MAAM;AAAA,YACV;AAAA,YACA,MAAM;AAAA,cACF,OAAO;AAAA,cACP,KAAK;AAAA,cACL,MAAM;AAAA,YACV;AAAA,YACA,MAAM;AAAA,cACF,SAAS;AAAA,cACT,OAAO;AAAA,cACP,KAAK;AAAA,cACL,MAAM;AAAA,YACV;AAAA,UACJ;AAAA,UACA,MAAM;AAAA,YACF,OAAO;AAAA,cACH,MAAM;AAAA,cACN,QAAQ;AAAA,YACZ;AAAA,YACA,QAAQ;AAAA,cACJ,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,QAAQ;AAAA,YACZ;AAAA,YACA,MAAM;AAAA,cACF,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,cAAc;AAAA,YAClB;AAAA,YACA,MAAM;AAAA,cACF,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,cAAc;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,oBAAoB;AAAA;AAAA;;;AChP5B;AAAA;AAAA;AAMA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,oBAAoB;AAC5B,QAAI,UAAU;AACd,QAAI,SAAS;AACb,WAAO,eAAe,SAAS,qBAAqB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,OAAO;AAAA,IAAmB,EAAE,CAAC;AAC/H,YAAQ,aAAa,gBAAuB,OAAO;AACnD,YAAQ,aAAa,kBAAwB,OAAO;AACpD,YAAQ,aAAa,sBAA6B,OAAO;AACzD,YAAQ,UAAU,OAAO;AAAA;AAAA;;;ACdzB,gCAAkC;AAAqB,IAAI,IAAE,QAAI,EAAC,OAAM,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAE,MAAI,MAAI,SAAO,GAAG,CAAC,KAAG,IAAI,0BAAAE,kBAAE,GAAE,GAAE,GAAE,CAAC,EAAE,OAAO,CAAC,EAAC;AAA1E,IAA6E,IAAE;", "names": ["p", "i", "f", "v", "d", "b", "__assign", "o", "ObjectWithoutPrototypeCache", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TYPE", "SKELETON_TYPE", "require_regex_generated", "opt", "startsWith", "fromCodePoint", "i", "fromEntries", "_a", "codePointAt", "trimStart", "trimEnd", "matchIdentifierAtIndex", "<PERSON><PERSON><PERSON>", "i", "require_error", "ErrorCode", "FormatError", "InvalidValueError", "InvalidValueTypeError", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PART_TYPE", "p", "IntlMessageFormat", "_a", "p"]}