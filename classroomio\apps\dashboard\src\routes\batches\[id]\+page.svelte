<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { 
    currentBatch, 
    subjects, 
    batchMembers,
    batchActions,
    subjectActions,
    memberActions,
    currentBatchStudents,
    currentBatchInstructors,
    activeSubjects
  } from '$lib/components/Batch/store';
  import { batchService, subjectService, memberService } from '$lib/utils/services/batch';
  import { PageBody, PageNav } from '$lib/components/Page';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { Add, People, BookOpen, Calendar, Settings } from 'carbon-icons-svelte';
  import { Tabs, Tab, TabContent } from 'carbon-components-svelte';

  export let data;

  let batchId: string;
  let selectedTab = 0;
  let loading = true;
  let error: string | null = null;

  const tabs = [
    { label: $t('batch.tabs.overview', { default: 'Overview' }), value: 'overview' },
    { label: $t('batch.tabs.subjects', { default: 'Subjects' }), value: 'subjects' },
    { label: $t('batch.tabs.students', { default: 'Students' }), value: 'students' },
    { label: $t('batch.tabs.schedule', { default: 'Schedule' }), value: 'schedule' }
  ];

  onMount(async () => {
    batchId = $page.params.id;
    await loadBatchData();
  });

  async function loadBatchData() {
    try {
      loading = true;
      error = null;

      // Load batch details
      const batch = await batchService.getBatch(batchId);
      if (!batch) {
        throw new Error('Batch not found');
      }
      batchActions.setBatch(batch);

      // Load subjects
      const batchSubjects = await subjectService.getSubjects(batchId);
      subjectActions.setSubjects(batchSubjects);

      // Load members
      const members = await memberService.getBatchMembers(batchId);
      memberActions.setMembers(members);

    } catch (err) {
      console.error('Error loading batch data:', err);
      error = err.message || 'Failed to load batch data';
    } finally {
      loading = false;
    }
  }

  function handleCreateSubject() {
    goto(`/batches/${batchId}/subjects/create`);
  }

  function handleAddStudents() {
    goto(`/batches/${batchId}/students/add`);
  }

  function handleBatchSettings() {
    goto(`/batches/${batchId}/settings`);
  }

  function handleSubjectClick(subject: any) {
    goto(`/batches/${batchId}/subjects/${subject.id}`);
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString();
  }

  function getBatchProgress() {
    if (!$currentBatch || !$activeSubjects.length) return 0;
    // Calculate overall batch progress based on subjects completion
    // This is a simplified calculation - you might want to make it more sophisticated
    return Math.round(($activeSubjects.length / ($currentBatch.total_subjects || 1)) * 100);
  }
</script>

<svelte:head>
  <title>
    {$currentBatch?.name || 'Batch'} - ClassroomIO
  </title>
</svelte:head>

{#if loading}
  <PageBody>
    <Box className="w-full">
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-400">
          {$t('batch.loading', { default: 'Loading batch...' })}
        </span>
      </div>
    </Box>
  </PageBody>

{:else if error}
  <PageBody>
    <Box className="w-full">
      <div class="text-center py-12">
        <div class="text-red-500 mb-4">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {$t('batch.error.title', { default: 'Error Loading Batch' })}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <PrimaryButton onClick={loadBatchData}>
          {$t('batch.retry', { default: 'Try Again' })}
        </PrimaryButton>
      </div>
    </Box>
  </PageBody>

{:else if $currentBatch}
  <PageNav title={$currentBatch.name}>
    <div class="flex items-center space-x-4">
      <PrimaryButton
        variant={VARIANTS.OUTLINED}
        onClick={handleBatchSettings}
      >
        <Settings size={20} class="mr-2" />
        {$t('batch.settings', { default: 'Settings' })}
      </PrimaryButton>
    </div>
  </PageNav>

  <PageBody>
    <!-- Batch Header Info -->
    <Box className="w-full mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-primary-600">{$currentBatchStudents.length}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">
            {$t('batch.stats.students', { default: 'Students' })}
          </div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-primary-600">{$activeSubjects.length}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">
            {$t('batch.stats.subjects', { default: 'Subjects' })}
          </div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-primary-600">{$currentBatchInstructors.length}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">
            {$t('batch.stats.instructors', { default: 'Instructors' })}
          </div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-primary-600">{getBatchProgress()}%</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">
            {$t('batch.stats.progress', { default: 'Progress' })}
          </div>
        </div>
      </div>
    </Box>

    <!-- Tabs -->
    <Tabs bind:selected={selectedTab}>
      {#each tabs as tab, index}
        <Tab label={tab.label} />
      {/each}
      
      <svelte:fragment slot="content">
        <!-- Overview Tab -->
        <TabContent>
          {#if selectedTab === 0}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Batch Information -->
              <Box>
                <h3 class="text-lg font-semibold mb-4">
                  {$t('batch.overview.info', { default: 'Batch Information' })}
                </h3>
                <div class="space-y-3">
                  <div>
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {$t('batch.fields.description', { default: 'Description' })}
                    </label>
                    <p class="text-gray-900 dark:text-white">
                      {$currentBatch.description || $t('batch.no_description', { default: 'No description provided' })}
                    </p>
                  </div>
                  
                  <div>
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {$t('batch.fields.code', { default: 'Batch Code' })}
                    </label>
                    <p class="text-gray-900 dark:text-white font-mono">
                      {$currentBatch.batch_code || 'N/A'}
                    </p>
                  </div>
                  
                  {#if $currentBatch.start_date}
                    <div>
                      <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {$t('batch.fields.duration', { default: 'Duration' })}
                      </label>
                      <p class="text-gray-900 dark:text-white">
                        {formatDate($currentBatch.start_date)}
                        {#if $currentBatch.end_date}
                          - {formatDate($currentBatch.end_date)}
                        {/if}
                      </p>
                    </div>
                  {/if}
                </div>
              </Box>

              <!-- Quick Actions -->
              <Box>
                <h3 class="text-lg font-semibold mb-4">
                  {$t('batch.overview.actions', { default: 'Quick Actions' })}
                </h3>
                <div class="space-y-3">
                  <PrimaryButton
                    variant={VARIANTS.CONTAINED}
                    onClick={handleCreateSubject}
                    className="w-full"
                  >
                    <BookOpen size={20} class="mr-2" />
                    {$t('batch.actions.create_subject', { default: 'Create Subject' })}
                  </PrimaryButton>
                  
                  <PrimaryButton
                    variant={VARIANTS.OUTLINED}
                    onClick={handleAddStudents}
                    className="w-full"
                  >
                    <People size={20} class="mr-2" />
                    {$t('batch.actions.add_students', { default: 'Add Students' })}
                  </PrimaryButton>
                </div>
              </Box>
            </div>
          {/if}
        </TabContent>

        <!-- Subjects Tab -->
        <TabContent>
          {#if selectedTab === 1}
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">
                {$t('batch.subjects.title', { default: 'Subjects' })}
              </h3>
              <PrimaryButton
                variant={VARIANTS.CONTAINED}
                onClick={handleCreateSubject}
              >
                <Add size={20} class="mr-2" />
                {$t('batch.subjects.create', { default: 'Create Subject' })}
              </PrimaryButton>
            </div>

            {#if $activeSubjects.length === 0}
              <Box className="w-full">
                <div class="text-center py-12">
                  <BookOpen size={48} class="mx-auto text-gray-400 mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {$t('batch.subjects.empty.title', { default: 'No Subjects Yet' })}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400 mb-6">
                    {$t('batch.subjects.empty.description', { default: 'Create subjects to organize your curriculum.' })}
                  </p>
                  <PrimaryButton
                    variant={VARIANTS.CONTAINED}
                    onClick={handleCreateSubject}
                  >
                    <Add size={20} class="mr-2" />
                    {$t('batch.subjects.create_first', { default: 'Create First Subject' })}
                  </PrimaryButton>
                </div>
              </Box>
            {:else}
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {#each $activeSubjects as subject (subject.id)}
                  <Box className="cursor-pointer hover:shadow-lg transition-shadow">
                    <div on:click={() => handleSubjectClick(subject)} on:keydown role="button" tabindex="0">
                      <h4 class="font-semibold text-lg mb-2">{subject.name}</h4>
                      {#if subject.description}
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                          {subject.description}
                        </p>
                      {/if}
                      <div class="flex justify-between items-center text-sm text-gray-500">
                        <span>{subject.total_chapters || 0} chapters</span>
                        <span>{subject.total_lessons || 0} lessons</span>
                      </div>
                    </div>
                  </Box>
                {/each}
              </div>
            {/if}
          {/if}
        </TabContent>

        <!-- Students Tab -->
        <TabContent>
          {#if selectedTab === 2}
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold">
                {$t('batch.students.title', { default: 'Students' })}
              </h3>
              <PrimaryButton
                variant={VARIANTS.CONTAINED}
                onClick={handleAddStudents}
              >
                <Add size={20} class="mr-2" />
                {$t('batch.students.add', { default: 'Add Students' })}
              </PrimaryButton>
            </div>

            {#if $currentBatchStudents.length === 0}
              <Box className="w-full">
                <div class="text-center py-12">
                  <People size={48} class="mx-auto text-gray-400 mb-4" />
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {$t('batch.students.empty.title', { default: 'No Students Yet' })}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400 mb-6">
                    {$t('batch.students.empty.description', { default: 'Add students to start teaching.' })}
                  </p>
                  <PrimaryButton
                    variant={VARIANTS.CONTAINED}
                    onClick={handleAddStudents}
                  >
                    <Add size={20} class="mr-2" />
                    {$t('batch.students.add_first', { default: 'Add First Student' })}
                  </PrimaryButton>
                </div>
              </Box>
            {:else}
              <!-- Student list implementation -->
              <Box className="w-full">
                <div class="space-y-4">
                  {#each $currentBatchStudents as member (member.id)}
                    <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div class="flex items-center">
                        <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                          <span class="text-primary-600 font-semibold">
                            {member.profile?.fullname?.charAt(0) || 'S'}
                          </span>
                        </div>
                        <div>
                          <p class="font-medium text-gray-900 dark:text-white">
                            {member.profile?.fullname || 'Unknown Student'}
                          </p>
                          <p class="text-sm text-gray-600 dark:text-gray-400">
                            {member.student_id || member.profile?.email}
                          </p>
                        </div>
                      </div>
                      <div class="text-sm text-gray-500">
                        Joined {formatDate(member.joined_at)}
                      </div>
                    </div>
                  {/each}
                </div>
              </Box>
            {/if}
          {/if}
        </TabContent>

        <!-- Schedule Tab -->
        <TabContent>
          {#if selectedTab === 3}
            <Box className="w-full">
              <div class="text-center py-12">
                <Calendar size={48} class="mx-auto text-gray-400 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {$t('batch.schedule.coming_soon', { default: 'Schedule Coming Soon' })}
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                  {$t('batch.schedule.description', { default: 'Live class scheduling will be available soon.' })}
                </p>
              </div>
            </Box>
          {/if}
        </TabContent>
      </svelte:fragment>
    </Tabs>
  </PageBody>
{/if}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
