<script lang="ts">
  import { onMount, onDestroy, createEventDispatcher } from 'svelte';
  import { browser } from '$app/environment';
  import type { VideoContent, VideoProgress, VideoPlayerSettings } from '$lib/utils/types/batch';
  import { videoProgressService } from '$lib/utils/services/video';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';

  export let videoContent: VideoContent;
  export let lessonId: string | null = null;
  export let settings: Partial<VideoPlayerSettings> = {};
  export let className: string = '';
  export let autoSaveProgress: boolean = true;
  export let showDownloadButton: boolean = true;

  const dispatch = createEventDispatcher<{
    progress: { currentTime: number; duration: number; percentage: number };
    completed: { videoId: string; completionTime: number };
    error: { message: string; code?: string };
    qualityChange: { quality: string };
    fullscreenChange: { isFullscreen: boolean };
  }>();

  let videoElement: HTMLVideoElement;
  let playerContainer: HTMLDivElement;
  let isLoading = true;
  let isPlaying = false;
  let currentTime = 0;
  let duration = 0;
  let volume = 1;
  let playbackRate = 1;
  let selectedQuality = 'auto';
  let isFullscreen = false;
  let showControls = true;
  let buffered = 0;
  let error: string | null = null;

  // Progress tracking
  let progressSaveInterval: NodeJS.Timeout;
  let lastSavedTime = 0;
  let watchStartTime = 0;
  let totalWatchTime = 0;
  let engagementData = {
    play_count: 0,
    pause_count: 0,
    seek_count: 0,
    replay_count: 0,
    quality_changes: 0,
    average_playback_rate: 1.0,
    segments_watched: []
  };

  // Default settings
  const defaultSettings: VideoPlayerSettings = {
    autoplay: false,
    muted: false,
    controls: true,
    playback_rates: [0.5, 0.75, 1, 1.25, 1.5, 2],
    keyboard_shortcuts: true,
    picture_in_picture: true,
    fullscreen: true,
    quality_selector: true,
    captions: true,
    theme: 'dark',
    branding: {}
  };

  $: playerSettings = { ...defaultSettings, ...settings };
  $: studentId = $globalStore.user?.id;
  $: completionPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
  $: isCompleted = completionPercentage >= 80; // 80% completion threshold

  onMount(async () => {
    if (!browser) return;
    
    try {
      await initializePlayer();
      await loadProgress();
      setupProgressTracking();
    } catch (err) {
      console.error('Error initializing video player:', err);
      error = 'Failed to initialize video player';
      dispatch('error', { message: error });
    }
  });

  onDestroy(() => {
    if (progressSaveInterval) {
      clearInterval(progressSaveInterval);
    }
    saveProgress(); // Save final progress
  });

  async function initializePlayer() {
    if (!videoElement) return;

    // Set up video element
    videoElement.preload = 'metadata';
    videoElement.crossOrigin = 'anonymous';
    
    // Load video source
    if (videoContent.quality_options?.length > 0) {
      // Use highest quality as default, or find 'auto' quality
      const autoQuality = videoContent.quality_options.find(q => q.quality === 'auto');
      const defaultQuality = autoQuality || videoContent.quality_options[0];
      videoElement.src = defaultQuality.url;
      selectedQuality = defaultQuality.quality;
    } else {
      videoElement.src = videoContent.video_url;
    }

    // Set up event listeners
    setupVideoEventListeners();
    
    // Apply settings
    videoElement.muted = playerSettings.muted;
    videoElement.volume = volume;
    videoElement.playbackRate = playbackRate;

    isLoading = false;
  }

  function setupVideoEventListeners() {
    if (!videoElement) return;

    videoElement.addEventListener('loadedmetadata', () => {
      duration = videoElement.duration;
      dispatch('progress', { currentTime, duration, percentage: completionPercentage });
    });

    videoElement.addEventListener('timeupdate', () => {
      currentTime = videoElement.currentTime;
      updateBuffered();
      dispatch('progress', { currentTime, duration, percentage: completionPercentage });
    });

    videoElement.addEventListener('play', () => {
      isPlaying = true;
      watchStartTime = Date.now();
      engagementData.play_count++;
    });

    videoElement.addEventListener('pause', () => {
      isPlaying = false;
      if (watchStartTime > 0) {
        totalWatchTime += Date.now() - watchStartTime;
        watchStartTime = 0;
      }
      engagementData.pause_count++;
    });

    videoElement.addEventListener('seeking', () => {
      engagementData.seek_count++;
    });

    videoElement.addEventListener('ended', () => {
      isPlaying = false;
      if (watchStartTime > 0) {
        totalWatchTime += Date.now() - watchStartTime;
        watchStartTime = 0;
      }
      handleVideoCompleted();
    });

    videoElement.addEventListener('error', (e) => {
      error = 'Video playback error';
      dispatch('error', { message: error });
    });

    videoElement.addEventListener('ratechange', () => {
      playbackRate = videoElement.playbackRate;
      engagementData.average_playback_rate = 
        (engagementData.average_playback_rate + playbackRate) / 2;
    });

    // Keyboard shortcuts
    if (playerSettings.keyboard_shortcuts) {
      setupKeyboardShortcuts();
    }
  }

  function setupKeyboardShortcuts() {
    document.addEventListener('keydown', handleKeyboardShortcut);
  }

  function handleKeyboardShortcut(e: KeyboardEvent) {
    if (!videoElement || document.activeElement !== videoElement) return;

    switch (e.code) {
      case 'Space':
        e.preventDefault();
        togglePlay();
        break;
      case 'ArrowLeft':
        e.preventDefault();
        seek(currentTime - 10);
        break;
      case 'ArrowRight':
        e.preventDefault();
        seek(currentTime + 10);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setVolume(Math.min(1, volume + 0.1));
        break;
      case 'ArrowDown':
        e.preventDefault();
        setVolume(Math.max(0, volume - 0.1));
        break;
      case 'KeyF':
        e.preventDefault();
        toggleFullscreen();
        break;
      case 'KeyM':
        e.preventDefault();
        toggleMute();
        break;
    }
  }

  async function loadProgress() {
    if (!studentId || !autoSaveProgress) return;

    try {
      const progress = await videoProgressService.getVideoProgress(videoContent.id, studentId);
      if (progress && progress.current_time > 0) {
        currentTime = progress.current_time;
        totalWatchTime = progress.watch_time || 0;
        engagementData = { ...engagementData, ...progress.engagement_data };
        
        // Resume from last position if not completed
        if (!progress.is_completed && videoElement) {
          videoElement.currentTime = progress.current_time;
        }
      }
    } catch (err) {
      console.error('Error loading video progress:', err);
    }
  }

  function setupProgressTracking() {
    if (!autoSaveProgress || !studentId) return;

    // Save progress every 10 seconds
    progressSaveInterval = setInterval(() => {
      if (Math.abs(currentTime - lastSavedTime) >= 10) {
        saveProgress();
      }
    }, 10000);
  }

  async function saveProgress() {
    if (!studentId || !autoSaveProgress || currentTime === lastSavedTime) return;

    try {
      await videoProgressService.updateVideoProgress({
        video_id: videoContent.id,
        student_id: studentId,
        lesson_id: lessonId,
        current_time: currentTime,
        duration: duration,
        watch_time: totalWatchTime / 1000, // Convert to seconds
        engagement_data: engagementData
      });
      
      lastSavedTime = currentTime;
    } catch (err) {
      console.error('Error saving video progress:', err);
    }
  }

  function updateBuffered() {
    if (!videoElement || !videoElement.buffered.length) return;
    
    const bufferedEnd = videoElement.buffered.end(videoElement.buffered.length - 1);
    buffered = duration > 0 ? (bufferedEnd / duration) * 100 : 0;
  }

  function togglePlay() {
    if (!videoElement) return;
    
    if (isPlaying) {
      videoElement.pause();
    } else {
      videoElement.play().catch(err => {
        console.error('Error playing video:', err);
        error = 'Failed to play video';
        dispatch('error', { message: error });
      });
    }
  }

  function seek(time: number) {
    if (!videoElement) return;
    
    const clampedTime = Math.max(0, Math.min(duration, time));
    videoElement.currentTime = clampedTime;
    currentTime = clampedTime;
  }

  function setVolume(newVolume: number) {
    if (!videoElement) return;
    
    volume = Math.max(0, Math.min(1, newVolume));
    videoElement.volume = volume;
  }

  function toggleMute() {
    if (!videoElement) return;
    
    videoElement.muted = !videoElement.muted;
  }

  function setPlaybackRate(rate: number) {
    if (!videoElement) return;
    
    playbackRate = rate;
    videoElement.playbackRate = rate;
  }

  function changeQuality(quality: string) {
    if (!videoElement || !videoContent.quality_options) return;
    
    const qualityOption = videoContent.quality_options.find(q => q.quality === quality);
    if (!qualityOption) return;
    
    const wasPlaying = isPlaying;
    const currentTimeBackup = currentTime;
    
    videoElement.src = qualityOption.url;
    selectedQuality = quality;
    engagementData.quality_changes++;
    
    videoElement.addEventListener('loadedmetadata', () => {
      videoElement.currentTime = currentTimeBackup;
      if (wasPlaying) {
        videoElement.play();
      }
    }, { once: true });
    
    dispatch('qualityChange', { quality });
  }

  function toggleFullscreen() {
    if (!playerContainer) return;
    
    if (!document.fullscreenElement) {
      playerContainer.requestFullscreen().then(() => {
        isFullscreen = true;
        dispatch('fullscreenChange', { isFullscreen: true });
      }).catch(err => {
        console.error('Error entering fullscreen:', err);
      });
    } else {
      document.exitFullscreen().then(() => {
        isFullscreen = false;
        dispatch('fullscreenChange', { isFullscreen: false });
      }).catch(err => {
        console.error('Error exiting fullscreen:', err);
      });
    }
  }

  function handleVideoCompleted() {
    if (isCompleted) {
      dispatch('completed', { 
        videoId: videoContent.id, 
        completionTime: totalWatchTime / 1000 
      });
      saveProgress(); // Save final progress
    }
  }

  function formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
</script>

<div 
  bind:this={playerContainer}
  class="advanced-video-player {className}"
  class:fullscreen={isFullscreen}
  class:loading={isLoading}
>
  {#if isLoading}
    <div class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{$t('video.loading', { default: 'Loading video...' })}</p>
    </div>
  {/if}

  {#if error}
    <div class="error-overlay">
      <div class="error-icon">⚠️</div>
      <p>{error}</p>
      <button on:click={() => window.location.reload()}>
        {$t('video.retry', { default: 'Retry' })}
      </button>
    </div>
  {:else}
    <video
      bind:this={videoElement}
      class="video-element"
      poster={videoContent.thumbnail_url}
      tabindex="0"
      on:click={togglePlay}
    >
      <track kind="captions" src="" label="English" default />
      {$t('video.not_supported', { default: 'Your browser does not support the video tag.' })}
    </video>

    <!-- Custom Controls -->
    {#if playerSettings.controls && showControls}
      <div class="video-controls" class:visible={showControls}>
        <!-- Progress Bar -->
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-buffered" style="width: {buffered}%"></div>
            <div class="progress-played" style="width: {completionPercentage}%"></div>
            <input
              type="range"
              min="0"
              max={duration}
              bind:value={currentTime}
              on:input={(e) => seek(Number(e.target.value))}
              class="progress-slider"
            />
          </div>
        </div>

        <!-- Control Buttons -->
        <div class="controls-row">
          <div class="controls-left">
            <button class="control-btn" on:click={togglePlay}>
              {#if isPlaying}
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
              {:else}
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              {/if}
            </button>

            <div class="volume-control">
              <button class="control-btn" on:click={toggleMute}>
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
                </svg>
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                bind:value={volume}
                on:input={(e) => setVolume(Number(e.target.value))}
                class="volume-slider"
              />
            </div>

            <div class="time-display">
              <span>{formatTime(currentTime)} / {formatTime(duration)}</span>
            </div>
          </div>

          <div class="controls-right">
            <!-- Playback Rate -->
            <select 
              bind:value={playbackRate} 
              on:change={(e) => setPlaybackRate(Number(e.target.value))}
              class="playback-rate-select"
            >
              {#each playerSettings.playback_rates as rate}
                <option value={rate}>{rate}x</option>
              {/each}
            </select>

            <!-- Quality Selector -->
            {#if playerSettings.quality_selector && videoContent.quality_options?.length > 1}
              <select 
                bind:value={selectedQuality}
                on:change={(e) => changeQuality(e.target.value)}
                class="quality-select"
              >
                {#each videoContent.quality_options as option}
                  <option value={option.quality}>
                    {option.quality === 'auto' ? 'Auto' : option.quality}
                  </option>
                {/each}
              </select>
            {/if}

            <!-- Fullscreen -->
            {#if playerSettings.fullscreen}
              <button class="control-btn" on:click={toggleFullscreen}>
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                </svg>
              </button>
            {/if}
          </div>
        </div>
      </div>
    {/if}

    <!-- Watermark -->
    {#if videoContent.watermark_settings?.enabled}
      <div class="watermark" class:watermark-{videoContent.watermark_settings.position || 'bottom-right'}>
        {videoContent.watermark_settings.text || $globalStore.user?.fullname || 'Student'}
      </div>
    {/if}
  {/if}
</div>

<style>
  .advanced-video-player {
    @apply relative w-full bg-black rounded-lg overflow-hidden;
    aspect-ratio: 16/9;
  }

  .advanced-video-player.fullscreen {
    @apply fixed inset-0 z-50;
    aspect-ratio: unset;
  }

  .video-element {
    @apply w-full h-full object-contain cursor-pointer;
  }

  .loading-overlay,
  .error-overlay {
    @apply absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-75 text-white;
  }

  .loading-spinner {
    @apply w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin mb-4;
  }

  .error-icon {
    @apply text-4xl mb-4;
  }

  .error-overlay button {
    @apply mt-4 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors;
  }

  .video-controls {
    @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .advanced-video-player:hover .video-controls.visible {
    opacity: 1;
  }

  .progress-container {
    @apply mb-3;
  }

  .progress-bar {
    @apply relative h-2 bg-gray-600 rounded-full cursor-pointer;
  }

  .progress-buffered {
    @apply absolute top-0 left-0 h-full bg-gray-400 rounded-full;
  }

  .progress-played {
    @apply absolute top-0 left-0 h-full bg-primary-500 rounded-full;
  }

  .progress-slider {
    @apply absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer;
  }

  .controls-row {
    @apply flex items-center justify-between;
  }

  .controls-left,
  .controls-right {
    @apply flex items-center space-x-3;
  }

  .control-btn {
    @apply w-8 h-8 text-white hover:text-primary-400 transition-colors;
  }

  .control-btn svg {
    @apply w-full h-full;
  }

  .volume-control {
    @apply flex items-center space-x-2;
  }

  .volume-slider {
    @apply w-20 h-1 bg-gray-600 rounded-full appearance-none cursor-pointer;
  }

  .volume-slider::-webkit-slider-thumb {
    @apply w-3 h-3 bg-white rounded-full appearance-none cursor-pointer;
  }

  .time-display {
    @apply text-sm text-white font-mono;
  }

  .playback-rate-select,
  .quality-select {
    @apply bg-transparent text-white text-sm border border-gray-600 rounded px-2 py-1;
  }

  .watermark {
    @apply absolute text-white text-sm font-semibold pointer-events-none;
    opacity: 0.7;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }

  .watermark-top-left {
    @apply top-4 left-4;
  }

  .watermark-top-right {
    @apply top-4 right-4;
  }

  .watermark-bottom-left {
    @apply bottom-16 left-4;
  }

  .watermark-bottom-right {
    @apply bottom-16 right-4;
  }

  .watermark-center {
    @apply top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    .controls-row {
      @apply flex-col space-y-2;
    }

    .controls-left,
    .controls-right {
      @apply w-full justify-center;
    }

    .volume-control {
      @apply hidden;
    }

    .time-display {
      @apply text-xs;
    }
  }

  /* Focus styles for accessibility */
  .video-element:focus,
  .control-btn:focus,
  .progress-slider:focus,
  .volume-slider:focus {
    @apply outline-none ring-2 ring-primary-500 ring-opacity-50;
  }
</style>
