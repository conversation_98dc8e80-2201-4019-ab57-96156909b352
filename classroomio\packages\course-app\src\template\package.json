{"name": "courseapp", "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "test": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@playwright/test": "^1.48.2", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.5.27", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.9.0", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "jsdom": "^25.0.1", "mdsvex": "^0.11.2", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^2.5.4", "tailwind-variants": "^0.2.1", "tailwindcss": "^3.4.9", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.0", "vite": "^5.4.4", "vitest": "^2.1.4"}, "dependencies": {"carbon-icons-svelte": "^12.13.0", "dotenv": "^16.5.0", "rehype-slug": "^6.0.0", "rehype-unwrap-images": "^1.0.0", "remark-toc": "^9.0.0", "shiki": "^1.23.1", "svelte-meta-tags": "^4.0.4", "svelte-motion": "^0.12.2"}}