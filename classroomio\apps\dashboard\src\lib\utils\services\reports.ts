// Reports Service
// Service for managing custom reports and scheduled reporting

import { supabase } from '$lib/utils/functions/supabase';
import type {
  ReportTemplate,
  ScheduledReport,
  ExportFormat,
  QueryConfig,
  VisualizationConfig
} from '$lib/utils/types/analytics';

export class ReportsService {
  // Report Template Management
  async getReportTemplates(
    organizationId: string,
    reportType?: string,
    userId?: string
  ): Promise<ReportTemplate[]> {
    let query = supabase
      .from('report_templates')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (reportType) {
      query = query.eq('report_type', reportType);
    }

    if (userId) {
      query = query.or(`is_public.eq.true,created_by.eq.${userId}`);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async getReportTemplate(templateId: string): Promise<ReportTemplate | null> {
    const { data, error } = await supabase
      .from('report_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) throw error;
    return data;
  }

  async createReportTemplate(templateData: Partial<ReportTemplate>): Promise<ReportTemplate> {
    const { data, error } = await supabase
      .from('report_templates')
      .insert({
        name: templateData.name,
        description: templateData.description,
        report_type: templateData.report_type,
        organization_id: templateData.organization_id,
        created_by: templateData.created_by,
        is_public: templateData.is_public || false,
        query_config: templateData.query_config || { data_source: '', parameters: [] },
        visualization_config: templateData.visualization_config || { charts: [], tables: [], layout: 'single' },
        filters: templateData.filters || [],
        schedule_config: templateData.schedule_config || { enabled: false, frequency: 'daily', timezone: 'UTC', recipients: [] },
        export_formats: templateData.export_formats || ['pdf'],
        permissions: templateData.permissions || {}
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateReportTemplate(
    templateId: string,
    updates: Partial<ReportTemplate>
  ): Promise<ReportTemplate> {
    const { data, error } = await supabase
      .from('report_templates')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteReportTemplate(templateId: string): Promise<void> {
    const { error } = await supabase
      .from('report_templates')
      .delete()
      .eq('id', templateId);

    if (error) throw error;
  }

  async incrementTemplateUsage(templateId: string): Promise<void> {
    const { error } = await supabase
      .from('report_templates')
      .update({
        usage_count: supabase.raw('usage_count + 1'),
        last_generated_at: new Date().toISOString()
      })
      .eq('id', templateId);

    if (error) throw error;
  }

  // Report Generation
  async generateReport(
    templateId: string,
    filters: Record<string, any> = {},
    format: ExportFormat = 'pdf'
  ): Promise<{ data: any; metadata: any }> {
    const template = await this.getReportTemplate(templateId);
    if (!template) {
      throw new Error('Report template not found');
    }

    // Execute query with filters
    const reportData = await this.executeReportQuery(template.query_config, filters);
    
    // Apply visualization configuration
    const visualizedData = this.applyVisualizationConfig(reportData, template.visualization_config);
    
    // Update usage statistics
    await this.incrementTemplateUsage(templateId);

    return {
      data: visualizedData,
      metadata: {
        template_name: template.name,
        generated_at: new Date().toISOString(),
        filters_applied: filters,
        format: format,
        total_records: Array.isArray(reportData) ? reportData.length : 1
      }
    };
  }

  private async executeReportQuery(
    queryConfig: QueryConfig,
    filters: Record<string, any>
  ): Promise<any> {
    let query = queryConfig.sql || '';
    const parameters = { ...filters };

    // Replace parameters in query
    queryConfig.parameters.forEach(param => {
      const value = parameters[param.name] || param.default_value;
      if (value !== undefined) {
        query = query.replace(new RegExp(`\\$${param.name}`, 'g'), this.formatParameterValue(value, param.type));
      }
    });

    const { data, error } = await supabase.rpc('execute_analytics_query', {
      query: query
    });

    if (error) throw error;
    return data;
  }

  private formatParameterValue(value: any, type: string): string {
    switch (type) {
      case 'string':
        return `'${value.toString().replace(/'/g, "''")}'`;
      case 'date':
        return `'${value}'`;
      case 'boolean':
        return value ? 'true' : 'false';
      case 'number':
        return value.toString();
      default:
        return `'${value}'`;
    }
  }

  private applyVisualizationConfig(data: any, config: VisualizationConfig): any {
    return {
      raw_data: data,
      charts: config.charts.map(chart => ({
        ...chart,
        data: this.prepareChartData(data, chart)
      })),
      tables: config.tables.map(table => ({
        ...table,
        data: this.prepareTableData(data, table)
      })),
      layout: config.layout
    };
  }

  private prepareChartData(data: any[], chartConfig: any): any {
    if (!Array.isArray(data)) return [];

    return data.map(row => ({
      x: row[chartConfig.x_axis],
      y: row[chartConfig.y_axis],
      series: chartConfig.series.map(s => row[s])
    }));
  }

  private prepareTableData(data: any[], tableConfig: any): any {
    if (!Array.isArray(data)) return [];

    return data.map(row => {
      const tableRow: any = {};
      tableConfig.columns.forEach(col => {
        tableRow[col.field] = this.formatTableValue(row[col.field], col.type, col.format);
      });
      return tableRow;
    });
  }

  private formatTableValue(value: any, type: string, format?: string): any {
    if (value === null || value === undefined) return '';

    switch (type) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'number':
        if (format === 'currency') {
          return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
        } else if (format === 'percentage') {
          return `${(value * 100).toFixed(2)}%`;
        }
        return value.toLocaleString();
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return value.toString();
    }
  }

  // Scheduled Reports Management
  async getScheduledReports(userId: string): Promise<ScheduledReport[]> {
    const { data, error } = await supabase
      .from('scheduled_reports')
      .select(`
        *,
        report_templates(name, report_type)
      `)
      .eq('created_by', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async createScheduledReport(reportData: Partial<ScheduledReport>): Promise<ScheduledReport> {
    // Calculate next run time based on schedule
    const nextRunAt = this.calculateNextRunTime(
      reportData.schedule_expression!,
      reportData.timezone || 'UTC'
    );

    const { data, error } = await supabase
      .from('scheduled_reports')
      .insert({
        template_id: reportData.template_id,
        name: reportData.name,
        schedule_expression: reportData.schedule_expression,
        timezone: reportData.timezone || 'UTC',
        recipients: reportData.recipients || [],
        filters: reportData.filters || {},
        format: reportData.format || 'pdf',
        is_active: reportData.is_active !== false,
        next_run_at: nextRunAt,
        created_by: reportData.created_by
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateScheduledReport(
    reportId: string,
    updates: Partial<ScheduledReport>
  ): Promise<ScheduledReport> {
    const updateData: any = { ...updates };

    // Recalculate next run time if schedule changed
    if (updates.schedule_expression || updates.timezone) {
      const currentReport = await this.getScheduledReport(reportId);
      if (currentReport) {
        updateData.next_run_at = this.calculateNextRunTime(
          updates.schedule_expression || currentReport.schedule_expression,
          updates.timezone || currentReport.timezone
        );
      }
    }

    const { data, error } = await supabase
      .from('scheduled_reports')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', reportId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getScheduledReport(reportId: string): Promise<ScheduledReport | null> {
    const { data, error } = await supabase
      .from('scheduled_reports')
      .select('*')
      .eq('id', reportId)
      .single();

    if (error) throw error;
    return data;
  }

  async deleteScheduledReport(reportId: string): Promise<void> {
    const { error } = await supabase
      .from('scheduled_reports')
      .delete()
      .eq('id', reportId);

    if (error) throw error;
  }

  private calculateNextRunTime(cronExpression: string, timezone: string): string {
    // This is a simplified implementation
    // In production, you'd use a proper cron parser library
    const now = new Date();
    
    // For demo purposes, just add 24 hours
    const nextRun = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    
    return nextRun.toISOString();
  }

  // Export Functions
  async exportReport(
    reportData: any,
    format: ExportFormat,
    filename: string
  ): Promise<Blob> {
    switch (format) {
      case 'pdf':
        return this.exportToPDF(reportData, filename);
      case 'excel':
        return this.exportToExcel(reportData, filename);
      case 'csv':
        return this.exportToCSV(reportData, filename);
      case 'json':
        return this.exportToJSON(reportData, filename);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  private async exportToPDF(reportData: any, filename: string): Promise<Blob> {
    // This would integrate with a PDF generation library like jsPDF or Puppeteer
    // For now, return a mock blob
    return new Blob(['PDF content'], { type: 'application/pdf' });
  }

  private async exportToExcel(reportData: any, filename: string): Promise<Blob> {
    // This would integrate with a library like SheetJS
    // For now, return a mock blob
    return new Blob(['Excel content'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }

  private async exportToCSV(reportData: any, filename: string): Promise<Blob> {
    if (!reportData.tables || reportData.tables.length === 0) {
      throw new Error('No table data available for CSV export');
    }

    const table = reportData.tables[0];
    const headers = table.columns.map(col => col.title).join(',');
    const rows = table.data.map(row => 
      table.columns.map(col => `"${row[col.field] || ''}"`).join(',')
    ).join('\n');

    const csvContent = `${headers}\n${rows}`;
    return new Blob([csvContent], { type: 'text/csv' });
  }

  private async exportToJSON(reportData: any, filename: string): Promise<Blob> {
    const jsonContent = JSON.stringify(reportData, null, 2);
    return new Blob([jsonContent], { type: 'application/json' });
  }

  // Report Templates
  async getDefaultReportTemplates(): Promise<Partial<ReportTemplate>[]> {
    return [
      {
        name: 'Student Progress Report',
        report_type: 'student_progress',
        query_config: {
          sql: `
            SELECT 
              p.fullname as student_name,
              la.total_study_time_minutes,
              la.videos_completed,
              la.assignments_completed,
              la.engagement_score,
              la.date
            FROM learning_analytics la
            JOIN profile p ON la.user_id = p.id
            WHERE la.batch_id = $batch_id
              AND la.date BETWEEN $date_from AND $date_to
            ORDER BY la.engagement_score DESC
          `,
          data_source: 'learning_analytics',
          parameters: [
            { name: 'batch_id', type: 'string', required: true },
            { name: 'date_from', type: 'date', required: true },
            { name: 'date_to', type: 'date', required: true }
          ]
        },
        visualization_config: {
          charts: [
            {
              type: 'bar',
              title: 'Student Engagement Scores',
              x_axis: 'student_name',
              y_axis: 'engagement_score',
              series: ['engagement_score'],
              colors: ['#3B82F6']
            }
          ],
          tables: [
            {
              title: 'Detailed Progress',
              columns: [
                { field: 'student_name', title: 'Student', type: 'text', sortable: true },
                { field: 'total_study_time_minutes', title: 'Study Time (min)', type: 'number', sortable: true },
                { field: 'videos_completed', title: 'Videos', type: 'number', sortable: true },
                { field: 'assignments_completed', title: 'Assignments', type: 'number', sortable: true },
                { field: 'engagement_score', title: 'Engagement', type: 'number', sortable: true }
              ],
              sorting: true,
              pagination: true
            }
          ],
          layout: 'grid'
        }
      },
      {
        name: 'Batch Performance Report',
        report_type: 'batch_performance',
        query_config: {
          sql: `
            SELECT 
              b.name as batch_name,
              COUNT(DISTINCT la.user_id) as total_students,
              AVG(la.engagement_score) as avg_engagement,
              SUM(la.total_study_time_minutes) as total_study_time,
              SUM(la.videos_completed) as total_videos_completed
            FROM learning_analytics la
            JOIN batch b ON la.batch_id = b.id
            WHERE la.date BETWEEN $date_from AND $date_to
            GROUP BY b.id, b.name
            ORDER BY avg_engagement DESC
          `,
          data_source: 'learning_analytics',
          parameters: [
            { name: 'date_from', type: 'date', required: true },
            { name: 'date_to', type: 'date', required: true }
          ]
        }
      }
    ];
  }
}

export const reportsService = new ReportsService();
