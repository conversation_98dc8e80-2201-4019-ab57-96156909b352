<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { writable } from 'svelte/store';
  import type { SecurityEvent, SecurityMetrics, EnhancedDeviceSession } from '$lib/utils/types/security';
  import { securityEventService, deviceManagementService } from '$lib/utils/services/security';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Security, 
    Warning, 
    CheckmarkFilled, 
    View, 
    Block,
    Refresh,
    Download,
    Filter
  } from 'carbon-icons-svelte';
  import { Tabs, Tab, TabContent } from 'carbon-components-svelte';

  export let organizationId: string;
  export let className: string = '';

  let selectedTab = 0;
  let loading = true;
  let error: string | null = null;
  let refreshInterval: NodeJS.Timeout | null = null;

  // Data stores
  let securityMetrics = writable<SecurityMetrics | null>(null);
  let recentEvents = writable<SecurityEvent[]>([]);
  let deviceSessions = writable<EnhancedDeviceSession[]>([]);
  let selectedTimeRange = '24h';
  let selectedEventType = 'all';
  let selectedSeverity = 'all';

  const tabs = [
    { label: $t('security.tabs.overview', { default: 'Overview' }), value: 'overview' },
    { label: $t('security.tabs.events', { default: 'Security Events' }), value: 'events' },
    { label: $t('security.tabs.devices', { default: 'Device Management' }), value: 'devices' },
    { label: $t('security.tabs.alerts', { default: 'Alerts' }), value: 'alerts' }
  ];

  $: isAdmin = $globalStore.isOrgAdmin;

  onMount(async () => {
    if (isAdmin) {
      await loadSecurityData();
      startAutoRefresh();
    }
  });

  onDestroy(() => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }
  });

  async function loadSecurityData() {
    try {
      loading = true;
      error = null;

      // Load security metrics
      const metrics = await securityEventService.getMetrics(organizationId, selectedTimeRange as any);
      securityMetrics.set(metrics);

      // Load recent events
      const events = await securityEventService.getEvents({
        start_date: getStartDate(selectedTimeRange),
        limit: 50
      });
      recentEvents.set(events);

      // Load device sessions (mock for now - would need proper API)
      // const devices = await deviceManagementService.getOrganizationDevices(organizationId);
      // deviceSessions.set(devices);

    } catch (err) {
      console.error('Error loading security data:', err);
      error = err.message || 'Failed to load security data';
    } finally {
      loading = false;
    }
  }

  function startAutoRefresh() {
    refreshInterval = setInterval(() => {
      loadSecurityData();
    }, 30000); // Refresh every 30 seconds
  }

  function getStartDate(timeRange: string): string {
    const now = new Date();
    switch (timeRange) {
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
    }
  }

  function getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  }

  function getEventTypeIcon(eventType: string) {
    switch (eventType) {
      case 'screenshot_attempt':
      case 'recording_attempt':
        return Warning;
      case 'device_blocked':
      case 'unauthorized_access':
        return Block;
      case 'exam_violation':
        return Security;
      default:
        return View;
    }
  }

  function formatTimestamp(timestamp: string): string {
    return new Date(timestamp).toLocaleString();
  }

  function exportSecurityReport() {
    // Implementation for exporting security report
    console.log('Exporting security report...');
  }

  async function blockDevice(deviceId: string) {
    try {
      await deviceManagementService.blockDevice(deviceId, 'Blocked from security dashboard');
      await loadSecurityData(); // Refresh data
    } catch (err) {
      console.error('Error blocking device:', err);
    }
  }

  async function approveDevice(deviceId: string) {
    try {
      const userId = $globalStore.user?.id;
      if (userId) {
        await deviceManagementService.approveDevice(deviceId, userId);
        await loadSecurityData(); // Refresh data
      }
    } catch (err) {
      console.error('Error approving device:', err);
    }
  }

  $: filteredEvents = $recentEvents.filter(event => {
    if (selectedEventType !== 'all' && event.event_type !== selectedEventType) return false;
    if (selectedSeverity !== 'all' && event.severity !== selectedSeverity) return false;
    return true;
  });
</script>

<div class="security-dashboard {className}">
  {#if !isAdmin}
    <Box className="text-center py-12">
      <Security size={48} class="text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {$t('security.access_denied', { default: 'Access Denied' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {$t('security.admin_required', { default: 'Administrator privileges required to access security dashboard' })}
      </p>
    </Box>

  {:else if loading}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('security.loading', { default: 'Loading Security Data' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {$t('security.loading_desc', { default: 'Gathering security metrics and events...' })}
      </p>
    </Box>

  {:else if error}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('security.error', { default: 'Error Loading Security Data' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{error}</p>
      <PrimaryButton onClick={loadSecurityData}>
        {$t('security.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else}
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          {$t('security.dashboard', { default: 'Security Dashboard' })}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          {$t('security.dashboard_desc', { default: 'Monitor and manage security events and policies' })}
        </p>
      </div>
      
      <div class="flex items-center space-x-3">
        <select 
          bind:value={selectedTimeRange}
          on:change={loadSecurityData}
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
        >
          <option value="24h">{$t('security.last_24h', { default: 'Last 24 Hours' })}</option>
          <option value="7d">{$t('security.last_7d', { default: 'Last 7 Days' })}</option>
          <option value="30d">{$t('security.last_30d', { default: 'Last 30 Days' })}</option>
        </select>
        
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={loadSecurityData}
        >
          <Refresh size={20} class="mr-2" />
          {$t('security.refresh', { default: 'Refresh' })}
        </PrimaryButton>
        
        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={exportSecurityReport}
        >
          <Download size={20} class="mr-2" />
          {$t('security.export', { default: 'Export Report' })}
        </PrimaryButton>
      </div>
    </div>

    <!-- Security Metrics Overview -->
    {#if $securityMetrics}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Box>
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600 mb-2">
              {$securityMetrics.total_events}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('security.total_events', { default: 'Total Events' })}
            </div>
          </div>
        </Box>
        
        <Box>
          <div class="text-center">
            <div class="text-3xl font-bold text-red-600 mb-2">
              {$securityMetrics.blocked_attempts}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('security.blocked_attempts', { default: 'Blocked Attempts' })}
            </div>
          </div>
        </Box>
        
        <Box>
          <div class="text-center">
            <div class="text-3xl font-bold text-yellow-600 mb-2">
              {$securityMetrics.suspicious_activities}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('security.suspicious_activities', { default: 'Suspicious Activities' })}
            </div>
          </div>
        </Box>
        
        <Box>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">
              {$securityMetrics.security_score}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {$t('security.security_score', { default: 'Security Score' })}
            </div>
          </div>
        </Box>
      </div>
    {/if}

    <!-- Tabs -->
    <Tabs bind:selected={selectedTab}>
      {#each tabs as tab, index}
        <Tab label={tab.label} />
      {/each}
      
      <svelte:fragment slot="content">
        <!-- Overview Tab -->
        <TabContent>
          {#if selectedTab === 0}
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Top Threats -->
              <Box>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {$t('security.top_threats', { default: 'Top Threats' })}
                </h3>
                {#if $securityMetrics?.top_threats.length > 0}
                  <div class="space-y-3">
                    {#each $securityMetrics.top_threats as threat}
                      <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center">
                          <svelte:component this={getEventTypeIcon(threat.type)} size={20} class="mr-3 text-red-500" />
                          <div>
                            <p class="font-medium text-gray-900 dark:text-white">
                              {threat.type.replace('_', ' ').toUpperCase()}
                            </p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                              Last: {formatTimestamp(threat.last_occurrence)}
                            </p>
                          </div>
                        </div>
                        <div class="text-right">
                          <div class="text-lg font-bold text-red-600">{threat.count}</div>
                          <div class="text-xs text-gray-500">occurrences</div>
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <p class="text-gray-600 dark:text-gray-400 text-center py-8">
                    {$t('security.no_threats', { default: 'No security threats detected' })}
                  </p>
                {/if}
              </Box>

              <!-- Events by Severity -->
              <Box>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {$t('security.events_by_severity', { default: 'Events by Severity' })}
                </h3>
                {#if $securityMetrics}
                  <div class="space-y-3">
                    {#each Object.entries($securityMetrics.events_by_severity) as [severity, count]}
                      <div class="flex items-center justify-between">
                        <div class="flex items-center">
                          <div class="w-3 h-3 rounded-full mr-3 {getSeverityColor(severity).split(' ')[1]}"></div>
                          <span class="capitalize text-gray-900 dark:text-white">{severity}</span>
                        </div>
                        <span class="font-semibold text-gray-900 dark:text-white">{count}</span>
                      </div>
                    {/each}
                  </div>
                {/if}
              </Box>
            </div>
          {/if}
        </TabContent>

        <!-- Events Tab -->
        <TabContent>
          {#if selectedTab === 1}
            <!-- Filters -->
            <div class="flex items-center space-x-4 mb-6">
              <div class="flex items-center">
                <Filter size={20} class="mr-2 text-gray-500" />
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">Filters:</span>
              </div>
              
              <select 
                bind:value={selectedEventType}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
              >
                <option value="all">{$t('security.all_types', { default: 'All Types' })}</option>
                <option value="screenshot_attempt">Screenshot Attempts</option>
                <option value="recording_attempt">Recording Attempts</option>
                <option value="device_change">Device Changes</option>
                <option value="exam_violation">Exam Violations</option>
              </select>
              
              <select 
                bind:value={selectedSeverity}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
              >
                <option value="all">{$t('security.all_severities', { default: 'All Severities' })}</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>

            <!-- Events List -->
            <Box>
              <div class="space-y-3">
                {#each filteredEvents as event (event.id)}
                  <div class="flex items-start justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="flex items-start space-x-3">
                      <svelte:component this={getEventTypeIcon(event.event_type)} size={20} class="mt-1 text-gray-500" />
                      <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                          <h4 class="font-medium text-gray-900 dark:text-white">
                            {event.event_type.replace('_', ' ').toUpperCase()}
                          </h4>
                          <span class="px-2 py-1 text-xs rounded-full {getSeverityColor(event.severity)}">
                            {event.severity}
                          </span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {formatTimestamp(event.created_at)}
                        </p>
                        {#if event.event_data && Object.keys(event.event_data).length > 0}
                          <details class="text-sm">
                            <summary class="cursor-pointer text-primary-600 hover:text-primary-700">
                              View Details
                            </summary>
                            <pre class="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-x-auto">
                              {JSON.stringify(event.event_data, null, 2)}
                            </pre>
                          </details>
                        {/if}
                      </div>
                    </div>
                    
                    {#if event.is_blocked}
                      <div class="flex items-center text-red-600">
                        <Block size={16} class="mr-1" />
                        <span class="text-xs">Blocked</span>
                      </div>
                    {/if}
                  </div>
                {:else}
                  <p class="text-gray-600 dark:text-gray-400 text-center py-8">
                    {$t('security.no_events', { default: 'No security events found' })}
                  </p>
                {/each}
              </div>
            </Box>
          {/if}
        </TabContent>

        <!-- Devices Tab -->
        <TabContent>
          {#if selectedTab === 2}
            <Box>
              <div class="text-center py-8">
                <Security size={48} class="text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {$t('security.device_management', { default: 'Device Management' })}
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                  {$t('security.device_management_desc', { default: 'Device management features coming soon' })}
                </p>
              </div>
            </Box>
          {/if}
        </TabContent>

        <!-- Alerts Tab -->
        <TabContent>
          {#if selectedTab === 3}
            <Box>
              <div class="text-center py-8">
                <Warning size={48} class="text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {$t('security.alerts', { default: 'Security Alerts' })}
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                  {$t('security.alerts_desc', { default: 'Real-time security alerts and notifications' })}
                </p>
              </div>
            </Box>
          {/if}
        </TabContent>
      </svelte:fragment>
    </Tabs>
  {/if}
</div>

<style>
  .security-dashboard {
    @apply w-full;
  }
</style>
