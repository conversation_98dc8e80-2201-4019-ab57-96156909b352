{"version": 3, "sources": ["../../../../../node_modules/.pnpm/canvas-confetti@1.9.3/node_modules/canvas-confetti/src/confetti.js"], "sourcesContent": ["/* globals Map */\n\n(function main(global, module, isWorker, workerSize) {\n  var canUseWorker = !!(\n    global.Worker &&\n    global.Blob &&\n    global.Promise &&\n    global.OffscreenCanvas &&\n    global.OffscreenCanvasRenderingContext2D &&\n    global.HTMLCanvasElement &&\n    global.HTMLCanvasElement.prototype.transferControlToOffscreen &&\n    global.URL &&\n    global.URL.createObjectURL);\n\n  var canUsePaths = typeof Path2D === 'function' && typeof DOMMatrix === 'function';\n  var canDrawBitmap = (function () {\n    // this mostly supports ssr\n    if (!global.OffscreenCanvas) {\n      return false;\n    }\n\n    var canvas = new OffscreenCanvas(1, 1);\n    var ctx = canvas.getContext('2d');\n    ctx.fillRect(0, 0, 1, 1);\n    var bitmap = canvas.transferToImageBitmap();\n\n    try {\n      ctx.createPattern(bitmap, 'no-repeat');\n    } catch (e) {\n      return false;\n    }\n\n    return true;\n  })();\n\n  function noop() {}\n\n  // create a promise if it exists, otherwise, just\n  // call the function directly\n  function promise(func) {\n    var ModulePromise = module.exports.Promise;\n    var Prom = ModulePromise !== void 0 ? ModulePromise : global.Promise;\n\n    if (typeof Prom === 'function') {\n      return new Prom(func);\n    }\n\n    func(noop, noop);\n\n    return null;\n  }\n\n  var bitmapMapper = (function (skipTransform, map) {\n    // see https://github.com/catdad/canvas-confetti/issues/209\n    // creating canvases is actually pretty expensive, so we should create a\n    // 1:1 map for bitmap:canvas, so that we can animate the confetti in\n    // a performant manner, but also not store them forever so that we don't\n    // have a memory leak\n    return {\n      transform: function(bitmap) {\n        if (skipTransform) {\n          return bitmap;\n        }\n\n        if (map.has(bitmap)) {\n          return map.get(bitmap);\n        }\n\n        var canvas = new OffscreenCanvas(bitmap.width, bitmap.height);\n        var ctx = canvas.getContext('2d');\n        ctx.drawImage(bitmap, 0, 0);\n\n        map.set(bitmap, canvas);\n\n        return canvas;\n      },\n      clear: function () {\n        map.clear();\n      }\n    };\n  })(canDrawBitmap, new Map());\n\n  var raf = (function () {\n    var TIME = Math.floor(1000 / 60);\n    var frame, cancel;\n    var frames = {};\n    var lastFrameTime = 0;\n\n    if (typeof requestAnimationFrame === 'function' && typeof cancelAnimationFrame === 'function') {\n      frame = function (cb) {\n        var id = Math.random();\n\n        frames[id] = requestAnimationFrame(function onFrame(time) {\n          if (lastFrameTime === time || lastFrameTime + TIME - 1 < time) {\n            lastFrameTime = time;\n            delete frames[id];\n\n            cb();\n          } else {\n            frames[id] = requestAnimationFrame(onFrame);\n          }\n        });\n\n        return id;\n      };\n      cancel = function (id) {\n        if (frames[id]) {\n          cancelAnimationFrame(frames[id]);\n        }\n      };\n    } else {\n      frame = function (cb) {\n        return setTimeout(cb, TIME);\n      };\n      cancel = function (timer) {\n        return clearTimeout(timer);\n      };\n    }\n\n    return { frame: frame, cancel: cancel };\n  }());\n\n  var getWorker = (function () {\n    var worker;\n    var prom;\n    var resolves = {};\n\n    function decorate(worker) {\n      function execute(options, callback) {\n        worker.postMessage({ options: options || {}, callback: callback });\n      }\n      worker.init = function initWorker(canvas) {\n        var offscreen = canvas.transferControlToOffscreen();\n        worker.postMessage({ canvas: offscreen }, [offscreen]);\n      };\n\n      worker.fire = function fireWorker(options, size, done) {\n        if (prom) {\n          execute(options, null);\n          return prom;\n        }\n\n        var id = Math.random().toString(36).slice(2);\n\n        prom = promise(function (resolve) {\n          function workerDone(msg) {\n            if (msg.data.callback !== id) {\n              return;\n            }\n\n            delete resolves[id];\n            worker.removeEventListener('message', workerDone);\n\n            prom = null;\n\n            bitmapMapper.clear();\n\n            done();\n            resolve();\n          }\n\n          worker.addEventListener('message', workerDone);\n          execute(options, id);\n\n          resolves[id] = workerDone.bind(null, { data: { callback: id }});\n        });\n\n        return prom;\n      };\n\n      worker.reset = function resetWorker() {\n        worker.postMessage({ reset: true });\n\n        for (var id in resolves) {\n          resolves[id]();\n          delete resolves[id];\n        }\n      };\n    }\n\n    return function () {\n      if (worker) {\n        return worker;\n      }\n\n      if (!isWorker && canUseWorker) {\n        var code = [\n          'var CONFETTI, SIZE = {}, module = {};',\n          '(' + main.toString() + ')(this, module, true, SIZE);',\n          'onmessage = function(msg) {',\n          '  if (msg.data.options) {',\n          '    CONFETTI(msg.data.options).then(function () {',\n          '      if (msg.data.callback) {',\n          '        postMessage({ callback: msg.data.callback });',\n          '      }',\n          '    });',\n          '  } else if (msg.data.reset) {',\n          '    CONFETTI && CONFETTI.reset();',\n          '  } else if (msg.data.resize) {',\n          '    SIZE.width = msg.data.resize.width;',\n          '    SIZE.height = msg.data.resize.height;',\n          '  } else if (msg.data.canvas) {',\n          '    SIZE.width = msg.data.canvas.width;',\n          '    SIZE.height = msg.data.canvas.height;',\n          '    CONFETTI = module.exports.create(msg.data.canvas);',\n          '  }',\n          '}',\n        ].join('\\n');\n        try {\n          worker = new Worker(URL.createObjectURL(new Blob([code])));\n        } catch (e) {\n          // eslint-disable-next-line no-console\n          typeof console !== undefined && typeof console.warn === 'function' ? console.warn('🎊 Could not load worker', e) : null;\n\n          return null;\n        }\n\n        decorate(worker);\n      }\n\n      return worker;\n    };\n  })();\n\n  var defaults = {\n    particleCount: 50,\n    angle: 90,\n    spread: 45,\n    startVelocity: 45,\n    decay: 0.9,\n    gravity: 1,\n    drift: 0,\n    ticks: 200,\n    x: 0.5,\n    y: 0.5,\n    shapes: ['square', 'circle'],\n    zIndex: 100,\n    colors: [\n      '#26ccff',\n      '#a25afd',\n      '#ff5e7e',\n      '#88ff5a',\n      '#fcff42',\n      '#ffa62d',\n      '#ff36ff'\n    ],\n    // probably should be true, but back-compat\n    disableForReducedMotion: false,\n    scalar: 1\n  };\n\n  function convert(val, transform) {\n    return transform ? transform(val) : val;\n  }\n\n  function isOk(val) {\n    return !(val === null || val === undefined);\n  }\n\n  function prop(options, name, transform) {\n    return convert(\n      options && isOk(options[name]) ? options[name] : defaults[name],\n      transform\n    );\n  }\n\n  function onlyPositiveInt(number){\n    return number < 0 ? 0 : Math.floor(number);\n  }\n\n  function randomInt(min, max) {\n    // [min, max)\n    return Math.floor(Math.random() * (max - min)) + min;\n  }\n\n  function toDecimal(str) {\n    return parseInt(str, 16);\n  }\n\n  function colorsToRgb(colors) {\n    return colors.map(hexToRgb);\n  }\n\n  function hexToRgb(str) {\n    var val = String(str).replace(/[^0-9a-f]/gi, '');\n\n    if (val.length < 6) {\n        val = val[0]+val[0]+val[1]+val[1]+val[2]+val[2];\n    }\n\n    return {\n      r: toDecimal(val.substring(0,2)),\n      g: toDecimal(val.substring(2,4)),\n      b: toDecimal(val.substring(4,6))\n    };\n  }\n\n  function getOrigin(options) {\n    var origin = prop(options, 'origin', Object);\n    origin.x = prop(origin, 'x', Number);\n    origin.y = prop(origin, 'y', Number);\n\n    return origin;\n  }\n\n  function setCanvasWindowSize(canvas) {\n    canvas.width = document.documentElement.clientWidth;\n    canvas.height = document.documentElement.clientHeight;\n  }\n\n  function setCanvasRectSize(canvas) {\n    var rect = canvas.getBoundingClientRect();\n    canvas.width = rect.width;\n    canvas.height = rect.height;\n  }\n\n  function getCanvas(zIndex) {\n    var canvas = document.createElement('canvas');\n\n    canvas.style.position = 'fixed';\n    canvas.style.top = '0px';\n    canvas.style.left = '0px';\n    canvas.style.pointerEvents = 'none';\n    canvas.style.zIndex = zIndex;\n\n    return canvas;\n  }\n\n  function ellipse(context, x, y, radiusX, radiusY, rotation, startAngle, endAngle, antiClockwise) {\n    context.save();\n    context.translate(x, y);\n    context.rotate(rotation);\n    context.scale(radiusX, radiusY);\n    context.arc(0, 0, 1, startAngle, endAngle, antiClockwise);\n    context.restore();\n  }\n\n  function randomPhysics(opts) {\n    var radAngle = opts.angle * (Math.PI / 180);\n    var radSpread = opts.spread * (Math.PI / 180);\n\n    return {\n      x: opts.x,\n      y: opts.y,\n      wobble: Math.random() * 10,\n      wobbleSpeed: Math.min(0.11, Math.random() * 0.1 + 0.05),\n      velocity: (opts.startVelocity * 0.5) + (Math.random() * opts.startVelocity),\n      angle2D: -radAngle + ((0.5 * radSpread) - (Math.random() * radSpread)),\n      tiltAngle: (Math.random() * (0.75 - 0.25) + 0.25) * Math.PI,\n      color: opts.color,\n      shape: opts.shape,\n      tick: 0,\n      totalTicks: opts.ticks,\n      decay: opts.decay,\n      drift: opts.drift,\n      random: Math.random() + 2,\n      tiltSin: 0,\n      tiltCos: 0,\n      wobbleX: 0,\n      wobbleY: 0,\n      gravity: opts.gravity * 3,\n      ovalScalar: 0.6,\n      scalar: opts.scalar,\n      flat: opts.flat\n    };\n  }\n\n  function updateFetti(context, fetti) {\n    fetti.x += Math.cos(fetti.angle2D) * fetti.velocity + fetti.drift;\n    fetti.y += Math.sin(fetti.angle2D) * fetti.velocity + fetti.gravity;\n    fetti.velocity *= fetti.decay;\n\n    if (fetti.flat) {\n      fetti.wobble = 0;\n      fetti.wobbleX = fetti.x + (10 * fetti.scalar);\n      fetti.wobbleY = fetti.y + (10 * fetti.scalar);\n\n      fetti.tiltSin = 0;\n      fetti.tiltCos = 0;\n      fetti.random = 1;\n    } else {\n      fetti.wobble += fetti.wobbleSpeed;\n      fetti.wobbleX = fetti.x + ((10 * fetti.scalar) * Math.cos(fetti.wobble));\n      fetti.wobbleY = fetti.y + ((10 * fetti.scalar) * Math.sin(fetti.wobble));\n\n      fetti.tiltAngle += 0.1;\n      fetti.tiltSin = Math.sin(fetti.tiltAngle);\n      fetti.tiltCos = Math.cos(fetti.tiltAngle);\n      fetti.random = Math.random() + 2;\n    }\n\n    var progress = (fetti.tick++) / fetti.totalTicks;\n\n    var x1 = fetti.x + (fetti.random * fetti.tiltCos);\n    var y1 = fetti.y + (fetti.random * fetti.tiltSin);\n    var x2 = fetti.wobbleX + (fetti.random * fetti.tiltCos);\n    var y2 = fetti.wobbleY + (fetti.random * fetti.tiltSin);\n\n    context.fillStyle = 'rgba(' + fetti.color.r + ', ' + fetti.color.g + ', ' + fetti.color.b + ', ' + (1 - progress) + ')';\n\n    context.beginPath();\n\n    if (canUsePaths && fetti.shape.type === 'path' && typeof fetti.shape.path === 'string' && Array.isArray(fetti.shape.matrix)) {\n      context.fill(transformPath2D(\n        fetti.shape.path,\n        fetti.shape.matrix,\n        fetti.x,\n        fetti.y,\n        Math.abs(x2 - x1) * 0.1,\n        Math.abs(y2 - y1) * 0.1,\n        Math.PI / 10 * fetti.wobble\n      ));\n    } else if (fetti.shape.type === 'bitmap') {\n      var rotation = Math.PI / 10 * fetti.wobble;\n      var scaleX = Math.abs(x2 - x1) * 0.1;\n      var scaleY = Math.abs(y2 - y1) * 0.1;\n      var width = fetti.shape.bitmap.width * fetti.scalar;\n      var height = fetti.shape.bitmap.height * fetti.scalar;\n\n      var matrix = new DOMMatrix([\n        Math.cos(rotation) * scaleX,\n        Math.sin(rotation) * scaleX,\n        -Math.sin(rotation) * scaleY,\n        Math.cos(rotation) * scaleY,\n        fetti.x,\n        fetti.y\n      ]);\n\n      // apply the transform matrix from the confetti shape\n      matrix.multiplySelf(new DOMMatrix(fetti.shape.matrix));\n\n      var pattern = context.createPattern(bitmapMapper.transform(fetti.shape.bitmap), 'no-repeat');\n      pattern.setTransform(matrix);\n\n      context.globalAlpha = (1 - progress);\n      context.fillStyle = pattern;\n      context.fillRect(\n        fetti.x - (width / 2),\n        fetti.y - (height / 2),\n        width,\n        height\n      );\n      context.globalAlpha = 1;\n    } else if (fetti.shape === 'circle') {\n      context.ellipse ?\n        context.ellipse(fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI) :\n        ellipse(context, fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI);\n    } else if (fetti.shape === 'star') {\n      var rot = Math.PI / 2 * 3;\n      var innerRadius = 4 * fetti.scalar;\n      var outerRadius = 8 * fetti.scalar;\n      var x = fetti.x;\n      var y = fetti.y;\n      var spikes = 5;\n      var step = Math.PI / spikes;\n\n      while (spikes--) {\n        x = fetti.x + Math.cos(rot) * outerRadius;\n        y = fetti.y + Math.sin(rot) * outerRadius;\n        context.lineTo(x, y);\n        rot += step;\n\n        x = fetti.x + Math.cos(rot) * innerRadius;\n        y = fetti.y + Math.sin(rot) * innerRadius;\n        context.lineTo(x, y);\n        rot += step;\n      }\n    } else {\n      context.moveTo(Math.floor(fetti.x), Math.floor(fetti.y));\n      context.lineTo(Math.floor(fetti.wobbleX), Math.floor(y1));\n      context.lineTo(Math.floor(x2), Math.floor(y2));\n      context.lineTo(Math.floor(x1), Math.floor(fetti.wobbleY));\n    }\n\n    context.closePath();\n    context.fill();\n\n    return fetti.tick < fetti.totalTicks;\n  }\n\n  function animate(canvas, fettis, resizer, size, done) {\n    var animatingFettis = fettis.slice();\n    var context = canvas.getContext('2d');\n    var animationFrame;\n    var destroy;\n\n    var prom = promise(function (resolve) {\n      function onDone() {\n        animationFrame = destroy = null;\n\n        context.clearRect(0, 0, size.width, size.height);\n        bitmapMapper.clear();\n\n        done();\n        resolve();\n      }\n\n      function update() {\n        if (isWorker && !(size.width === workerSize.width && size.height === workerSize.height)) {\n          size.width = canvas.width = workerSize.width;\n          size.height = canvas.height = workerSize.height;\n        }\n\n        if (!size.width && !size.height) {\n          resizer(canvas);\n          size.width = canvas.width;\n          size.height = canvas.height;\n        }\n\n        context.clearRect(0, 0, size.width, size.height);\n\n        animatingFettis = animatingFettis.filter(function (fetti) {\n          return updateFetti(context, fetti);\n        });\n\n        if (animatingFettis.length) {\n          animationFrame = raf.frame(update);\n        } else {\n          onDone();\n        }\n      }\n\n      animationFrame = raf.frame(update);\n      destroy = onDone;\n    });\n\n    return {\n      addFettis: function (fettis) {\n        animatingFettis = animatingFettis.concat(fettis);\n\n        return prom;\n      },\n      canvas: canvas,\n      promise: prom,\n      reset: function () {\n        if (animationFrame) {\n          raf.cancel(animationFrame);\n        }\n\n        if (destroy) {\n          destroy();\n        }\n      }\n    };\n  }\n\n  function confettiCannon(canvas, globalOpts) {\n    var isLibCanvas = !canvas;\n    var allowResize = !!prop(globalOpts || {}, 'resize');\n    var hasResizeEventRegistered = false;\n    var globalDisableForReducedMotion = prop(globalOpts, 'disableForReducedMotion', Boolean);\n    var shouldUseWorker = canUseWorker && !!prop(globalOpts || {}, 'useWorker');\n    var worker = shouldUseWorker ? getWorker() : null;\n    var resizer = isLibCanvas ? setCanvasWindowSize : setCanvasRectSize;\n    var initialized = (canvas && worker) ? !!canvas.__confetti_initialized : false;\n    var preferLessMotion = typeof matchMedia === 'function' && matchMedia('(prefers-reduced-motion)').matches;\n    var animationObj;\n\n    function fireLocal(options, size, done) {\n      var particleCount = prop(options, 'particleCount', onlyPositiveInt);\n      var angle = prop(options, 'angle', Number);\n      var spread = prop(options, 'spread', Number);\n      var startVelocity = prop(options, 'startVelocity', Number);\n      var decay = prop(options, 'decay', Number);\n      var gravity = prop(options, 'gravity', Number);\n      var drift = prop(options, 'drift', Number);\n      var colors = prop(options, 'colors', colorsToRgb);\n      var ticks = prop(options, 'ticks', Number);\n      var shapes = prop(options, 'shapes');\n      var scalar = prop(options, 'scalar');\n      var flat = !!prop(options, 'flat');\n      var origin = getOrigin(options);\n\n      var temp = particleCount;\n      var fettis = [];\n\n      var startX = canvas.width * origin.x;\n      var startY = canvas.height * origin.y;\n\n      while (temp--) {\n        fettis.push(\n          randomPhysics({\n            x: startX,\n            y: startY,\n            angle: angle,\n            spread: spread,\n            startVelocity: startVelocity,\n            color: colors[temp % colors.length],\n            shape: shapes[randomInt(0, shapes.length)],\n            ticks: ticks,\n            decay: decay,\n            gravity: gravity,\n            drift: drift,\n            scalar: scalar,\n            flat: flat\n          })\n        );\n      }\n\n      // if we have a previous canvas already animating,\n      // add to it\n      if (animationObj) {\n        return animationObj.addFettis(fettis);\n      }\n\n      animationObj = animate(canvas, fettis, resizer, size , done);\n\n      return animationObj.promise;\n    }\n\n    function fire(options) {\n      var disableForReducedMotion = globalDisableForReducedMotion || prop(options, 'disableForReducedMotion', Boolean);\n      var zIndex = prop(options, 'zIndex', Number);\n\n      if (disableForReducedMotion && preferLessMotion) {\n        return promise(function (resolve) {\n          resolve();\n        });\n      }\n\n      if (isLibCanvas && animationObj) {\n        // use existing canvas from in-progress animation\n        canvas = animationObj.canvas;\n      } else if (isLibCanvas && !canvas) {\n        // create and initialize a new canvas\n        canvas = getCanvas(zIndex);\n        document.body.appendChild(canvas);\n      }\n\n      if (allowResize && !initialized) {\n        // initialize the size of a user-supplied canvas\n        resizer(canvas);\n      }\n\n      var size = {\n        width: canvas.width,\n        height: canvas.height\n      };\n\n      if (worker && !initialized) {\n        worker.init(canvas);\n      }\n\n      initialized = true;\n\n      if (worker) {\n        canvas.__confetti_initialized = true;\n      }\n\n      function onResize() {\n        if (worker) {\n          // TODO this really shouldn't be immediate, because it is expensive\n          var obj = {\n            getBoundingClientRect: function () {\n              if (!isLibCanvas) {\n                return canvas.getBoundingClientRect();\n              }\n            }\n          };\n\n          resizer(obj);\n\n          worker.postMessage({\n            resize: {\n              width: obj.width,\n              height: obj.height\n            }\n          });\n          return;\n        }\n\n        // don't actually query the size here, since this\n        // can execute frequently and rapidly\n        size.width = size.height = null;\n      }\n\n      function done() {\n        animationObj = null;\n\n        if (allowResize) {\n          hasResizeEventRegistered = false;\n          global.removeEventListener('resize', onResize);\n        }\n\n        if (isLibCanvas && canvas) {\n          if (document.body.contains(canvas)) {\n            document.body.removeChild(canvas); \n          }\n          canvas = null;\n          initialized = false;\n        }\n      }\n\n      if (allowResize && !hasResizeEventRegistered) {\n        hasResizeEventRegistered = true;\n        global.addEventListener('resize', onResize, false);\n      }\n\n      if (worker) {\n        return worker.fire(options, size, done);\n      }\n\n      return fireLocal(options, size, done);\n    }\n\n    fire.reset = function () {\n      if (worker) {\n        worker.reset();\n      }\n\n      if (animationObj) {\n        animationObj.reset();\n      }\n    };\n\n    return fire;\n  }\n\n  // Make default export lazy to defer worker creation until called.\n  var defaultFire;\n  function getDefaultFire() {\n    if (!defaultFire) {\n      defaultFire = confettiCannon(null, { useWorker: true, resize: true });\n    }\n    return defaultFire;\n  }\n\n  function transformPath2D(pathString, pathMatrix, x, y, scaleX, scaleY, rotation) {\n    var path2d = new Path2D(pathString);\n\n    var t1 = new Path2D();\n    t1.addPath(path2d, new DOMMatrix(pathMatrix));\n\n    var t2 = new Path2D();\n    // see https://developer.mozilla.org/en-US/docs/Web/API/DOMMatrix/DOMMatrix\n    t2.addPath(t1, new DOMMatrix([\n      Math.cos(rotation) * scaleX,\n      Math.sin(rotation) * scaleX,\n      -Math.sin(rotation) * scaleY,\n      Math.cos(rotation) * scaleY,\n      x,\n      y\n    ]));\n\n    return t2;\n  }\n\n  function shapeFromPath(pathData) {\n    if (!canUsePaths) {\n      throw new Error('path confetti are not supported in this browser');\n    }\n\n    var path, matrix;\n\n    if (typeof pathData === 'string') {\n      path = pathData;\n    } else {\n      path = pathData.path;\n      matrix = pathData.matrix;\n    }\n\n    var path2d = new Path2D(path);\n    var tempCanvas = document.createElement('canvas');\n    var tempCtx = tempCanvas.getContext('2d');\n\n    if (!matrix) {\n      // attempt to figure out the width of the path, up to 1000x1000\n      var maxSize = 1000;\n      var minX = maxSize;\n      var minY = maxSize;\n      var maxX = 0;\n      var maxY = 0;\n      var width, height;\n\n      // do some line skipping... this is faster than checking\n      // every pixel and will be mostly still correct\n      for (var x = 0; x < maxSize; x += 2) {\n        for (var y = 0; y < maxSize; y += 2) {\n          if (tempCtx.isPointInPath(path2d, x, y, 'nonzero')) {\n            minX = Math.min(minX, x);\n            minY = Math.min(minY, y);\n            maxX = Math.max(maxX, x);\n            maxY = Math.max(maxY, y);\n          }\n        }\n      }\n\n      width = maxX - minX;\n      height = maxY - minY;\n\n      var maxDesiredSize = 10;\n      var scale = Math.min(maxDesiredSize/width, maxDesiredSize/height);\n\n      matrix = [\n        scale, 0, 0, scale,\n        -Math.round((width/2) + minX) * scale,\n        -Math.round((height/2) + minY) * scale\n      ];\n    }\n\n    return {\n      type: 'path',\n      path: path,\n      matrix: matrix\n    };\n  }\n\n  function shapeFromText(textData) {\n    var text,\n        scalar = 1,\n        color = '#000000',\n        // see https://nolanlawson.com/2022/04/08/the-struggle-of-using-native-emoji-on-the-web/\n        fontFamily = '\"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\", \"Twemoji Mozilla\", \"system emoji\", sans-serif';\n\n    if (typeof textData === 'string') {\n      text = textData;\n    } else {\n      text = textData.text;\n      scalar = 'scalar' in textData ? textData.scalar : scalar;\n      fontFamily = 'fontFamily' in textData ? textData.fontFamily : fontFamily;\n      color = 'color' in textData ? textData.color : color;\n    }\n\n    // all other confetti are 10 pixels,\n    // so this pixel size is the de-facto 100% scale confetti\n    var fontSize = 10 * scalar;\n    var font = '' + fontSize + 'px ' + fontFamily;\n\n    var canvas = new OffscreenCanvas(fontSize, fontSize);\n    var ctx = canvas.getContext('2d');\n\n    ctx.font = font;\n    var size = ctx.measureText(text);\n    var width = Math.ceil(size.actualBoundingBoxRight + size.actualBoundingBoxLeft);\n    var height = Math.ceil(size.actualBoundingBoxAscent + size.actualBoundingBoxDescent);\n\n    var padding = 2;\n    var x = size.actualBoundingBoxLeft + padding;\n    var y = size.actualBoundingBoxAscent + padding;\n    width += padding + padding;\n    height += padding + padding;\n\n    canvas = new OffscreenCanvas(width, height);\n    ctx = canvas.getContext('2d');\n    ctx.font = font;\n    ctx.fillStyle = color;\n\n    ctx.fillText(text, x, y);\n\n    var scale = 1 / scalar;\n\n    return {\n      type: 'bitmap',\n      // TODO these probably need to be transfered for workers\n      bitmap: canvas.transferToImageBitmap(),\n      matrix: [scale, 0, 0, scale, -width * scale / 2, -height * scale / 2]\n    };\n  }\n\n  module.exports = function() {\n    return getDefaultFire().apply(this, arguments);\n  };\n  module.exports.reset = function() {\n    getDefaultFire().reset();\n  };\n  module.exports.create = confettiCannon;\n  module.exports.shapeFromPath = shapeFromPath;\n  module.exports.shapeFromText = shapeFromText;\n}((function () {\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n\n  return this || {};\n})(), module, false));\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,KAAC,SAAS,KAAK,QAAQA,SAAQ,UAAU,YAAY;AACnD,UAAI,eAAe,CAAC,EAClB,OAAO,UACP,OAAO,QACP,OAAO,WACP,OAAO,mBACP,OAAO,qCACP,OAAO,qBACP,OAAO,kBAAkB,UAAU,8BACnC,OAAO,OACP,OAAO,IAAI;AAEb,UAAI,cAAc,OAAO,WAAW,cAAc,OAAO,cAAc;AACvE,UAAI,gBAAiB,WAAY;AAE/B,YAAI,CAAC,OAAO,iBAAiB;AAC3B,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,IAAI,gBAAgB,GAAG,CAAC;AACrC,YAAI,MAAM,OAAO,WAAW,IAAI;AAChC,YAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AACvB,YAAI,SAAS,OAAO,sBAAsB;AAE1C,YAAI;AACF,cAAI,cAAc,QAAQ,WAAW;AAAA,QACvC,SAAS,GAAG;AACV,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,EAAG;AAEH,eAAS,OAAO;AAAA,MAAC;AAIjB,eAAS,QAAQ,MAAM;AACrB,YAAI,gBAAgBA,QAAO,QAAQ;AACnC,YAAI,OAAO,kBAAkB,SAAS,gBAAgB,OAAO;AAE7D,YAAI,OAAO,SAAS,YAAY;AAC9B,iBAAO,IAAI,KAAK,IAAI;AAAA,QACtB;AAEA,aAAK,MAAM,IAAI;AAEf,eAAO;AAAA,MACT;AAEA,UAAI,eAAgB,SAAU,eAAe,KAAK;AAMhD,eAAO;AAAA,UACL,WAAW,SAAS,QAAQ;AAC1B,gBAAI,eAAe;AACjB,qBAAO;AAAA,YACT;AAEA,gBAAI,IAAI,IAAI,MAAM,GAAG;AACnB,qBAAO,IAAI,IAAI,MAAM;AAAA,YACvB;AAEA,gBAAI,SAAS,IAAI,gBAAgB,OAAO,OAAO,OAAO,MAAM;AAC5D,gBAAI,MAAM,OAAO,WAAW,IAAI;AAChC,gBAAI,UAAU,QAAQ,GAAG,CAAC;AAE1B,gBAAI,IAAI,QAAQ,MAAM;AAEtB,mBAAO;AAAA,UACT;AAAA,UACA,OAAO,WAAY;AACjB,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF;AAAA,MACF,EAAG,eAAe,oBAAI,IAAI,CAAC;AAE3B,UAAI,MAAO,WAAY;AACrB,YAAI,OAAO,KAAK,MAAM,MAAO,EAAE;AAC/B,YAAI,OAAO;AACX,YAAI,SAAS,CAAC;AACd,YAAI,gBAAgB;AAEpB,YAAI,OAAO,0BAA0B,cAAc,OAAO,yBAAyB,YAAY;AAC7F,kBAAQ,SAAU,IAAI;AACpB,gBAAI,KAAK,KAAK,OAAO;AAErB,mBAAO,EAAE,IAAI,sBAAsB,SAAS,QAAQ,MAAM;AACxD,kBAAI,kBAAkB,QAAQ,gBAAgB,OAAO,IAAI,MAAM;AAC7D,gCAAgB;AAChB,uBAAO,OAAO,EAAE;AAEhB,mBAAG;AAAA,cACL,OAAO;AACL,uBAAO,EAAE,IAAI,sBAAsB,OAAO;AAAA,cAC5C;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT;AACA,mBAAS,SAAU,IAAI;AACrB,gBAAI,OAAO,EAAE,GAAG;AACd,mCAAqB,OAAO,EAAE,CAAC;AAAA,YACjC;AAAA,UACF;AAAA,QACF,OAAO;AACL,kBAAQ,SAAU,IAAI;AACpB,mBAAO,WAAW,IAAI,IAAI;AAAA,UAC5B;AACA,mBAAS,SAAU,OAAO;AACxB,mBAAO,aAAa,KAAK;AAAA,UAC3B;AAAA,QACF;AAEA,eAAO,EAAE,OAAc,OAAe;AAAA,MACxC,EAAE;AAEF,UAAI,YAAa,WAAY;AAC3B,YAAI;AACJ,YAAI;AACJ,YAAI,WAAW,CAAC;AAEhB,iBAAS,SAASC,SAAQ;AACxB,mBAAS,QAAQ,SAAS,UAAU;AAClC,YAAAA,QAAO,YAAY,EAAE,SAAS,WAAW,CAAC,GAAG,SAAmB,CAAC;AAAA,UACnE;AACA,UAAAA,QAAO,OAAO,SAAS,WAAW,QAAQ;AACxC,gBAAI,YAAY,OAAO,2BAA2B;AAClD,YAAAA,QAAO,YAAY,EAAE,QAAQ,UAAU,GAAG,CAAC,SAAS,CAAC;AAAA,UACvD;AAEA,UAAAA,QAAO,OAAO,SAAS,WAAW,SAAS,MAAM,MAAM;AACrD,gBAAI,MAAM;AACR,sBAAQ,SAAS,IAAI;AACrB,qBAAO;AAAA,YACT;AAEA,gBAAI,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AAE3C,mBAAO,QAAQ,SAAU,SAAS;AAChC,uBAAS,WAAW,KAAK;AACvB,oBAAI,IAAI,KAAK,aAAa,IAAI;AAC5B;AAAA,gBACF;AAEA,uBAAO,SAAS,EAAE;AAClB,gBAAAA,QAAO,oBAAoB,WAAW,UAAU;AAEhD,uBAAO;AAEP,6BAAa,MAAM;AAEnB,qBAAK;AACL,wBAAQ;AAAA,cACV;AAEA,cAAAA,QAAO,iBAAiB,WAAW,UAAU;AAC7C,sBAAQ,SAAS,EAAE;AAEnB,uBAAS,EAAE,IAAI,WAAW,KAAK,MAAM,EAAE,MAAM,EAAE,UAAU,GAAG,EAAC,CAAC;AAAA,YAChE,CAAC;AAED,mBAAO;AAAA,UACT;AAEA,UAAAA,QAAO,QAAQ,SAAS,cAAc;AACpC,YAAAA,QAAO,YAAY,EAAE,OAAO,KAAK,CAAC;AAElC,qBAAS,MAAM,UAAU;AACvB,uBAAS,EAAE,EAAE;AACb,qBAAO,SAAS,EAAE;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAEA,eAAO,WAAY;AACjB,cAAI,QAAQ;AACV,mBAAO;AAAA,UACT;AAEA,cAAI,CAAC,YAAY,cAAc;AAC7B,gBAAI,OAAO;AAAA,cACT;AAAA,cACA,MAAM,KAAK,SAAS,IAAI;AAAA,cACxB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,EAAE,KAAK,IAAI;AACX,gBAAI;AACF,uBAAS,IAAI,OAAO,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAAA,YAC3D,SAAS,GAAG;AAEV,qBAAO,YAAY,UAAa,OAAO,QAAQ,SAAS,aAAa,QAAQ,KAAK,4BAA4B,CAAC,IAAI;AAEnH,qBAAO;AAAA,YACT;AAEA,qBAAS,MAAM;AAAA,UACjB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF,EAAG;AAEH,UAAI,WAAW;AAAA,QACb,eAAe;AAAA,QACf,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,QACP,GAAG;AAAA,QACH,GAAG;AAAA,QACH,QAAQ,CAAC,UAAU,QAAQ;AAAA,QAC3B,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA;AAAA,QAEA,yBAAyB;AAAA,QACzB,QAAQ;AAAA,MACV;AAEA,eAAS,QAAQ,KAAK,WAAW;AAC/B,eAAO,YAAY,UAAU,GAAG,IAAI;AAAA,MACtC;AAEA,eAAS,KAAK,KAAK;AACjB,eAAO,EAAE,QAAQ,QAAQ,QAAQ;AAAA,MACnC;AAEA,eAAS,KAAK,SAAS,MAAM,WAAW;AACtC,eAAO;AAAA,UACL,WAAW,KAAK,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AAEA,eAAS,gBAAgB,QAAO;AAC9B,eAAO,SAAS,IAAI,IAAI,KAAK,MAAM,MAAM;AAAA,MAC3C;AAEA,eAAS,UAAU,KAAK,KAAK;AAE3B,eAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI,IAAI;AAAA,MACnD;AAEA,eAAS,UAAU,KAAK;AACtB,eAAO,SAAS,KAAK,EAAE;AAAA,MACzB;AAEA,eAAS,YAAY,QAAQ;AAC3B,eAAO,OAAO,IAAI,QAAQ;AAAA,MAC5B;AAEA,eAAS,SAAS,KAAK;AACrB,YAAI,MAAM,OAAO,GAAG,EAAE,QAAQ,eAAe,EAAE;AAE/C,YAAI,IAAI,SAAS,GAAG;AAChB,gBAAM,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC;AAAA,QAClD;AAEA,eAAO;AAAA,UACL,GAAG,UAAU,IAAI,UAAU,GAAE,CAAC,CAAC;AAAA,UAC/B,GAAG,UAAU,IAAI,UAAU,GAAE,CAAC,CAAC;AAAA,UAC/B,GAAG,UAAU,IAAI,UAAU,GAAE,CAAC,CAAC;AAAA,QACjC;AAAA,MACF;AAEA,eAAS,UAAU,SAAS;AAC1B,YAAI,SAAS,KAAK,SAAS,UAAU,MAAM;AAC3C,eAAO,IAAI,KAAK,QAAQ,KAAK,MAAM;AACnC,eAAO,IAAI,KAAK,QAAQ,KAAK,MAAM;AAEnC,eAAO;AAAA,MACT;AAEA,eAAS,oBAAoB,QAAQ;AACnC,eAAO,QAAQ,SAAS,gBAAgB;AACxC,eAAO,SAAS,SAAS,gBAAgB;AAAA,MAC3C;AAEA,eAAS,kBAAkB,QAAQ;AACjC,YAAI,OAAO,OAAO,sBAAsB;AACxC,eAAO,QAAQ,KAAK;AACpB,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,eAAS,UAAU,QAAQ;AACzB,YAAI,SAAS,SAAS,cAAc,QAAQ;AAE5C,eAAO,MAAM,WAAW;AACxB,eAAO,MAAM,MAAM;AACnB,eAAO,MAAM,OAAO;AACpB,eAAO,MAAM,gBAAgB;AAC7B,eAAO,MAAM,SAAS;AAEtB,eAAO;AAAA,MACT;AAEA,eAAS,QAAQ,SAAS,GAAG,GAAG,SAAS,SAAS,UAAU,YAAY,UAAU,eAAe;AAC/F,gBAAQ,KAAK;AACb,gBAAQ,UAAU,GAAG,CAAC;AACtB,gBAAQ,OAAO,QAAQ;AACvB,gBAAQ,MAAM,SAAS,OAAO;AAC9B,gBAAQ,IAAI,GAAG,GAAG,GAAG,YAAY,UAAU,aAAa;AACxD,gBAAQ,QAAQ;AAAA,MAClB;AAEA,eAAS,cAAc,MAAM;AAC3B,YAAI,WAAW,KAAK,SAAS,KAAK,KAAK;AACvC,YAAI,YAAY,KAAK,UAAU,KAAK,KAAK;AAEzC,eAAO;AAAA,UACL,GAAG,KAAK;AAAA,UACR,GAAG,KAAK;AAAA,UACR,QAAQ,KAAK,OAAO,IAAI;AAAA,UACxB,aAAa,KAAK,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,IAAI;AAAA,UACtD,UAAW,KAAK,gBAAgB,MAAQ,KAAK,OAAO,IAAI,KAAK;AAAA,UAC7D,SAAS,CAAC,YAAa,MAAM,YAAc,KAAK,OAAO,IAAI;AAAA,UAC3D,YAAY,KAAK,OAAO,KAAK,OAAO,QAAQ,QAAQ,KAAK;AAAA,UACzD,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,YAAY,KAAK;AAAA,UACjB,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK,OAAO,IAAI;AAAA,UACxB,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS,KAAK,UAAU;AAAA,UACxB,YAAY;AAAA,UACZ,QAAQ,KAAK;AAAA,UACb,MAAM,KAAK;AAAA,QACb;AAAA,MACF;AAEA,eAAS,YAAY,SAAS,OAAO;AACnC,cAAM,KAAK,KAAK,IAAI,MAAM,OAAO,IAAI,MAAM,WAAW,MAAM;AAC5D,cAAM,KAAK,KAAK,IAAI,MAAM,OAAO,IAAI,MAAM,WAAW,MAAM;AAC5D,cAAM,YAAY,MAAM;AAExB,YAAI,MAAM,MAAM;AACd,gBAAM,SAAS;AACf,gBAAM,UAAU,MAAM,IAAK,KAAK,MAAM;AACtC,gBAAM,UAAU,MAAM,IAAK,KAAK,MAAM;AAEtC,gBAAM,UAAU;AAChB,gBAAM,UAAU;AAChB,gBAAM,SAAS;AAAA,QACjB,OAAO;AACL,gBAAM,UAAU,MAAM;AACtB,gBAAM,UAAU,MAAM,IAAM,KAAK,MAAM,SAAU,KAAK,IAAI,MAAM,MAAM;AACtE,gBAAM,UAAU,MAAM,IAAM,KAAK,MAAM,SAAU,KAAK,IAAI,MAAM,MAAM;AAEtE,gBAAM,aAAa;AACnB,gBAAM,UAAU,KAAK,IAAI,MAAM,SAAS;AACxC,gBAAM,UAAU,KAAK,IAAI,MAAM,SAAS;AACxC,gBAAM,SAAS,KAAK,OAAO,IAAI;AAAA,QACjC;AAEA,YAAI,WAAY,MAAM,SAAU,MAAM;AAEtC,YAAI,KAAK,MAAM,IAAK,MAAM,SAAS,MAAM;AACzC,YAAI,KAAK,MAAM,IAAK,MAAM,SAAS,MAAM;AACzC,YAAI,KAAK,MAAM,UAAW,MAAM,SAAS,MAAM;AAC/C,YAAI,KAAK,MAAM,UAAW,MAAM,SAAS,MAAM;AAE/C,gBAAQ,YAAY,UAAU,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,IAAI,QAAQ,IAAI,YAAY;AAEpH,gBAAQ,UAAU;AAElB,YAAI,eAAe,MAAM,MAAM,SAAS,UAAU,OAAO,MAAM,MAAM,SAAS,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,GAAG;AAC3H,kBAAQ,KAAK;AAAA,YACX,MAAM,MAAM;AAAA,YACZ,MAAM,MAAM;AAAA,YACZ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,KAAK,IAAI,KAAK,EAAE,IAAI;AAAA,YACpB,KAAK,IAAI,KAAK,EAAE,IAAI;AAAA,YACpB,KAAK,KAAK,KAAK,MAAM;AAAA,UACvB,CAAC;AAAA,QACH,WAAW,MAAM,MAAM,SAAS,UAAU;AACxC,cAAI,WAAW,KAAK,KAAK,KAAK,MAAM;AACpC,cAAI,SAAS,KAAK,IAAI,KAAK,EAAE,IAAI;AACjC,cAAI,SAAS,KAAK,IAAI,KAAK,EAAE,IAAI;AACjC,cAAI,QAAQ,MAAM,MAAM,OAAO,QAAQ,MAAM;AAC7C,cAAI,SAAS,MAAM,MAAM,OAAO,SAAS,MAAM;AAE/C,cAAI,SAAS,IAAI,UAAU;AAAA,YACzB,KAAK,IAAI,QAAQ,IAAI;AAAA,YACrB,KAAK,IAAI,QAAQ,IAAI;AAAA,YACrB,CAAC,KAAK,IAAI,QAAQ,IAAI;AAAA,YACtB,KAAK,IAAI,QAAQ,IAAI;AAAA,YACrB,MAAM;AAAA,YACN,MAAM;AAAA,UACR,CAAC;AAGD,iBAAO,aAAa,IAAI,UAAU,MAAM,MAAM,MAAM,CAAC;AAErD,cAAI,UAAU,QAAQ,cAAc,aAAa,UAAU,MAAM,MAAM,MAAM,GAAG,WAAW;AAC3F,kBAAQ,aAAa,MAAM;AAE3B,kBAAQ,cAAe,IAAI;AAC3B,kBAAQ,YAAY;AACpB,kBAAQ;AAAA,YACN,MAAM,IAAK,QAAQ;AAAA,YACnB,MAAM,IAAK,SAAS;AAAA,YACpB;AAAA,YACA;AAAA,UACF;AACA,kBAAQ,cAAc;AAAA,QACxB,WAAW,MAAM,UAAU,UAAU;AACnC,kBAAQ,UACN,QAAQ,QAAQ,MAAM,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,KAAK,KAAK,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,IACzJ,QAAQ,SAAS,MAAM,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,KAAK,KAAK,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE;AAAA,QAC9J,WAAW,MAAM,UAAU,QAAQ;AACjC,cAAI,MAAM,KAAK,KAAK,IAAI;AACxB,cAAI,cAAc,IAAI,MAAM;AAC5B,cAAI,cAAc,IAAI,MAAM;AAC5B,cAAI,IAAI,MAAM;AACd,cAAI,IAAI,MAAM;AACd,cAAI,SAAS;AACb,cAAI,OAAO,KAAK,KAAK;AAErB,iBAAO,UAAU;AACf,gBAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,gBAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,oBAAQ,OAAO,GAAG,CAAC;AACnB,mBAAO;AAEP,gBAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,gBAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,oBAAQ,OAAO,GAAG,CAAC;AACnB,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,kBAAQ,OAAO,KAAK,MAAM,MAAM,CAAC,GAAG,KAAK,MAAM,MAAM,CAAC,CAAC;AACvD,kBAAQ,OAAO,KAAK,MAAM,MAAM,OAAO,GAAG,KAAK,MAAM,EAAE,CAAC;AACxD,kBAAQ,OAAO,KAAK,MAAM,EAAE,GAAG,KAAK,MAAM,EAAE,CAAC;AAC7C,kBAAQ,OAAO,KAAK,MAAM,EAAE,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,QAC1D;AAEA,gBAAQ,UAAU;AAClB,gBAAQ,KAAK;AAEb,eAAO,MAAM,OAAO,MAAM;AAAA,MAC5B;AAEA,eAAS,QAAQ,QAAQ,QAAQ,SAAS,MAAM,MAAM;AACpD,YAAI,kBAAkB,OAAO,MAAM;AACnC,YAAI,UAAU,OAAO,WAAW,IAAI;AACpC,YAAI;AACJ,YAAI;AAEJ,YAAI,OAAO,QAAQ,SAAU,SAAS;AACpC,mBAAS,SAAS;AAChB,6BAAiB,UAAU;AAE3B,oBAAQ,UAAU,GAAG,GAAG,KAAK,OAAO,KAAK,MAAM;AAC/C,yBAAa,MAAM;AAEnB,iBAAK;AACL,oBAAQ;AAAA,UACV;AAEA,mBAAS,SAAS;AAChB,gBAAI,YAAY,EAAE,KAAK,UAAU,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS;AACvF,mBAAK,QAAQ,OAAO,QAAQ,WAAW;AACvC,mBAAK,SAAS,OAAO,SAAS,WAAW;AAAA,YAC3C;AAEA,gBAAI,CAAC,KAAK,SAAS,CAAC,KAAK,QAAQ;AAC/B,sBAAQ,MAAM;AACd,mBAAK,QAAQ,OAAO;AACpB,mBAAK,SAAS,OAAO;AAAA,YACvB;AAEA,oBAAQ,UAAU,GAAG,GAAG,KAAK,OAAO,KAAK,MAAM;AAE/C,8BAAkB,gBAAgB,OAAO,SAAU,OAAO;AACxD,qBAAO,YAAY,SAAS,KAAK;AAAA,YACnC,CAAC;AAED,gBAAI,gBAAgB,QAAQ;AAC1B,+BAAiB,IAAI,MAAM,MAAM;AAAA,YACnC,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,2BAAiB,IAAI,MAAM,MAAM;AACjC,oBAAU;AAAA,QACZ,CAAC;AAED,eAAO;AAAA,UACL,WAAW,SAAUC,SAAQ;AAC3B,8BAAkB,gBAAgB,OAAOA,OAAM;AAE/C,mBAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA,SAAS;AAAA,UACT,OAAO,WAAY;AACjB,gBAAI,gBAAgB;AAClB,kBAAI,OAAO,cAAc;AAAA,YAC3B;AAEA,gBAAI,SAAS;AACX,sBAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,eAAS,eAAe,QAAQ,YAAY;AAC1C,YAAI,cAAc,CAAC;AACnB,YAAI,cAAc,CAAC,CAAC,KAAK,cAAc,CAAC,GAAG,QAAQ;AACnD,YAAI,2BAA2B;AAC/B,YAAI,gCAAgC,KAAK,YAAY,2BAA2B,OAAO;AACvF,YAAI,kBAAkB,gBAAgB,CAAC,CAAC,KAAK,cAAc,CAAC,GAAG,WAAW;AAC1E,YAAI,SAAS,kBAAkB,UAAU,IAAI;AAC7C,YAAI,UAAU,cAAc,sBAAsB;AAClD,YAAI,cAAe,UAAU,SAAU,CAAC,CAAC,OAAO,yBAAyB;AACzE,YAAI,mBAAmB,OAAO,eAAe,cAAc,WAAW,0BAA0B,EAAE;AAClG,YAAI;AAEJ,iBAAS,UAAU,SAAS,MAAM,MAAM;AACtC,cAAI,gBAAgB,KAAK,SAAS,iBAAiB,eAAe;AAClE,cAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,cAAI,SAAS,KAAK,SAAS,UAAU,MAAM;AAC3C,cAAI,gBAAgB,KAAK,SAAS,iBAAiB,MAAM;AACzD,cAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,cAAI,UAAU,KAAK,SAAS,WAAW,MAAM;AAC7C,cAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,cAAI,SAAS,KAAK,SAAS,UAAU,WAAW;AAChD,cAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,cAAI,SAAS,KAAK,SAAS,QAAQ;AACnC,cAAI,SAAS,KAAK,SAAS,QAAQ;AACnC,cAAI,OAAO,CAAC,CAAC,KAAK,SAAS,MAAM;AACjC,cAAI,SAAS,UAAU,OAAO;AAE9B,cAAI,OAAO;AACX,cAAI,SAAS,CAAC;AAEd,cAAI,SAAS,OAAO,QAAQ,OAAO;AACnC,cAAI,SAAS,OAAO,SAAS,OAAO;AAEpC,iBAAO,QAAQ;AACb,mBAAO;AAAA,cACL,cAAc;AAAA,gBACZ,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,OAAO,OAAO,OAAO,OAAO,MAAM;AAAA,gBAClC,OAAO,OAAO,UAAU,GAAG,OAAO,MAAM,CAAC;AAAA,gBACzC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAIA,cAAI,cAAc;AAChB,mBAAO,aAAa,UAAU,MAAM;AAAA,UACtC;AAEA,yBAAe,QAAQ,QAAQ,QAAQ,SAAS,MAAO,IAAI;AAE3D,iBAAO,aAAa;AAAA,QACtB;AAEA,iBAAS,KAAK,SAAS;AACrB,cAAI,0BAA0B,iCAAiC,KAAK,SAAS,2BAA2B,OAAO;AAC/G,cAAI,SAAS,KAAK,SAAS,UAAU,MAAM;AAE3C,cAAI,2BAA2B,kBAAkB;AAC/C,mBAAO,QAAQ,SAAU,SAAS;AAChC,sBAAQ;AAAA,YACV,CAAC;AAAA,UACH;AAEA,cAAI,eAAe,cAAc;AAE/B,qBAAS,aAAa;AAAA,UACxB,WAAW,eAAe,CAAC,QAAQ;AAEjC,qBAAS,UAAU,MAAM;AACzB,qBAAS,KAAK,YAAY,MAAM;AAAA,UAClC;AAEA,cAAI,eAAe,CAAC,aAAa;AAE/B,oBAAQ,MAAM;AAAA,UAChB;AAEA,cAAI,OAAO;AAAA,YACT,OAAO,OAAO;AAAA,YACd,QAAQ,OAAO;AAAA,UACjB;AAEA,cAAI,UAAU,CAAC,aAAa;AAC1B,mBAAO,KAAK,MAAM;AAAA,UACpB;AAEA,wBAAc;AAEd,cAAI,QAAQ;AACV,mBAAO,yBAAyB;AAAA,UAClC;AAEA,mBAAS,WAAW;AAClB,gBAAI,QAAQ;AAEV,kBAAI,MAAM;AAAA,gBACR,uBAAuB,WAAY;AACjC,sBAAI,CAAC,aAAa;AAChB,2BAAO,OAAO,sBAAsB;AAAA,kBACtC;AAAA,gBACF;AAAA,cACF;AAEA,sBAAQ,GAAG;AAEX,qBAAO,YAAY;AAAA,gBACjB,QAAQ;AAAA,kBACN,OAAO,IAAI;AAAA,kBACX,QAAQ,IAAI;AAAA,gBACd;AAAA,cACF,CAAC;AACD;AAAA,YACF;AAIA,iBAAK,QAAQ,KAAK,SAAS;AAAA,UAC7B;AAEA,mBAAS,OAAO;AACd,2BAAe;AAEf,gBAAI,aAAa;AACf,yCAA2B;AAC3B,qBAAO,oBAAoB,UAAU,QAAQ;AAAA,YAC/C;AAEA,gBAAI,eAAe,QAAQ;AACzB,kBAAI,SAAS,KAAK,SAAS,MAAM,GAAG;AAClC,yBAAS,KAAK,YAAY,MAAM;AAAA,cAClC;AACA,uBAAS;AACT,4BAAc;AAAA,YAChB;AAAA,UACF;AAEA,cAAI,eAAe,CAAC,0BAA0B;AAC5C,uCAA2B;AAC3B,mBAAO,iBAAiB,UAAU,UAAU,KAAK;AAAA,UACnD;AAEA,cAAI,QAAQ;AACV,mBAAO,OAAO,KAAK,SAAS,MAAM,IAAI;AAAA,UACxC;AAEA,iBAAO,UAAU,SAAS,MAAM,IAAI;AAAA,QACtC;AAEA,aAAK,QAAQ,WAAY;AACvB,cAAI,QAAQ;AACV,mBAAO,MAAM;AAAA,UACf;AAEA,cAAI,cAAc;AAChB,yBAAa,MAAM;AAAA,UACrB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAGA,UAAI;AACJ,eAAS,iBAAiB;AACxB,YAAI,CAAC,aAAa;AAChB,wBAAc,eAAe,MAAM,EAAE,WAAW,MAAM,QAAQ,KAAK,CAAC;AAAA,QACtE;AACA,eAAO;AAAA,MACT;AAEA,eAAS,gBAAgB,YAAY,YAAY,GAAG,GAAG,QAAQ,QAAQ,UAAU;AAC/E,YAAI,SAAS,IAAI,OAAO,UAAU;AAElC,YAAI,KAAK,IAAI,OAAO;AACpB,WAAG,QAAQ,QAAQ,IAAI,UAAU,UAAU,CAAC;AAE5C,YAAI,KAAK,IAAI,OAAO;AAEpB,WAAG,QAAQ,IAAI,IAAI,UAAU;AAAA,UAC3B,KAAK,IAAI,QAAQ,IAAI;AAAA,UACrB,KAAK,IAAI,QAAQ,IAAI;AAAA,UACrB,CAAC,KAAK,IAAI,QAAQ,IAAI;AAAA,UACtB,KAAK,IAAI,QAAQ,IAAI;AAAA,UACrB;AAAA,UACA;AAAA,QACF,CAAC,CAAC;AAEF,eAAO;AAAA,MACT;AAEA,eAAS,cAAc,UAAU;AAC/B,YAAI,CAAC,aAAa;AAChB,gBAAM,IAAI,MAAM,iDAAiD;AAAA,QACnE;AAEA,YAAI,MAAM;AAEV,YAAI,OAAO,aAAa,UAAU;AAChC,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,SAAS;AAChB,mBAAS,SAAS;AAAA,QACpB;AAEA,YAAI,SAAS,IAAI,OAAO,IAAI;AAC5B,YAAI,aAAa,SAAS,cAAc,QAAQ;AAChD,YAAI,UAAU,WAAW,WAAW,IAAI;AAExC,YAAI,CAAC,QAAQ;AAEX,cAAI,UAAU;AACd,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,OAAO;AAIX,mBAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,qBAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,kBAAI,QAAQ,cAAc,QAAQ,GAAG,GAAG,SAAS,GAAG;AAClD,uBAAO,KAAK,IAAI,MAAM,CAAC;AACvB,uBAAO,KAAK,IAAI,MAAM,CAAC;AACvB,uBAAO,KAAK,IAAI,MAAM,CAAC;AACvB,uBAAO,KAAK,IAAI,MAAM,CAAC;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAEA,kBAAQ,OAAO;AACf,mBAAS,OAAO;AAEhB,cAAI,iBAAiB;AACrB,cAAI,QAAQ,KAAK,IAAI,iBAAe,OAAO,iBAAe,MAAM;AAEhE,mBAAS;AAAA,YACP;AAAA,YAAO;AAAA,YAAG;AAAA,YAAG;AAAA,YACb,CAAC,KAAK,MAAO,QAAM,IAAK,IAAI,IAAI;AAAA,YAChC,CAAC,KAAK,MAAO,SAAO,IAAK,IAAI,IAAI;AAAA,UACnC;AAAA,QACF;AAEA,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,eAAS,cAAc,UAAU;AAC/B,YAAI,MACA,SAAS,GACT,QAAQ,WAER,aAAa;AAEjB,YAAI,OAAO,aAAa,UAAU;AAChC,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,SAAS;AAChB,mBAAS,YAAY,WAAW,SAAS,SAAS;AAClD,uBAAa,gBAAgB,WAAW,SAAS,aAAa;AAC9D,kBAAQ,WAAW,WAAW,SAAS,QAAQ;AAAA,QACjD;AAIA,YAAI,WAAW,KAAK;AACpB,YAAI,OAAO,KAAK,WAAW,QAAQ;AAEnC,YAAI,SAAS,IAAI,gBAAgB,UAAU,QAAQ;AACnD,YAAI,MAAM,OAAO,WAAW,IAAI;AAEhC,YAAI,OAAO;AACX,YAAI,OAAO,IAAI,YAAY,IAAI;AAC/B,YAAI,QAAQ,KAAK,KAAK,KAAK,yBAAyB,KAAK,qBAAqB;AAC9E,YAAI,SAAS,KAAK,KAAK,KAAK,0BAA0B,KAAK,wBAAwB;AAEnF,YAAI,UAAU;AACd,YAAI,IAAI,KAAK,wBAAwB;AACrC,YAAI,IAAI,KAAK,0BAA0B;AACvC,iBAAS,UAAU;AACnB,kBAAU,UAAU;AAEpB,iBAAS,IAAI,gBAAgB,OAAO,MAAM;AAC1C,cAAM,OAAO,WAAW,IAAI;AAC5B,YAAI,OAAO;AACX,YAAI,YAAY;AAEhB,YAAI,SAAS,MAAM,GAAG,CAAC;AAEvB,YAAI,QAAQ,IAAI;AAEhB,eAAO;AAAA,UACL,MAAM;AAAA;AAAA,UAEN,QAAQ,OAAO,sBAAsB;AAAA,UACrC,QAAQ,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,SAAS,QAAQ,CAAC;AAAA,QACtE;AAAA,MACF;AAEA,MAAAF,QAAO,UAAU,WAAW;AAC1B,eAAO,eAAe,EAAE,MAAM,MAAM,SAAS;AAAA,MAC/C;AACA,MAAAA,QAAO,QAAQ,QAAQ,WAAW;AAChC,uBAAe,EAAE,MAAM;AAAA,MACzB;AACA,MAAAA,QAAO,QAAQ,SAAS;AACxB,MAAAA,QAAO,QAAQ,gBAAgB;AAC/B,MAAAA,QAAO,QAAQ,gBAAgB;AAAA,IACjC,GAAG,WAAY;AACb,UAAI,OAAO,WAAW,aAAa;AACjC,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,SAAS,aAAa;AAC/B,eAAO;AAAA,MACT;AAEA,aAAO,QAAQ,CAAC;AAAA,IAClB,EAAG,GAAG,QAAQ,KAAK;AAAA;AAAA;", "names": ["module", "worker", "fettis"]}