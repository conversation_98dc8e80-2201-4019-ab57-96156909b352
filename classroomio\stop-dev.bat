@echo off
setlocal enabledelayedexpansion

:: ClassroomIO Development Environment Stop Script
:: Grace<PERSON> stops all development services

title ClassroomIO Development Stop

:: Colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "RESET=[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%   ClassroomIO Development Stop         %RESET%
echo %CYAN%========================================%RESET%
echo.

echo %BLUE%Stopping ClassroomIO development services...%RESET%
echo.

:: Stop Node.js development servers
echo %YELLOW%Stopping Node.js development servers...%RESET%
tasklist /FI "IMAGENAME eq node.exe" /FO CSV | findstr /C:"node.exe" >nul
if !errorlevel! equ 0 (
    echo   Found Node.js processes, attempting to stop...
    
    :: Kill Node.js processes that are likely development servers
    for /f "tokens=2 delims=," %%a in ('tasklist /FI "IMAGENAME eq node.exe" /FO CSV ^| findstr /C:"node.exe"') do (
        set "pid=%%a"
        set "pid=!pid:"=!"
        
        :: Check if this process is using development ports
        netstat -ano | findstr ":5173" | findstr "!pid!" >nul
        if !errorlevel! equ 0 (
            echo   Stopping Node.js process !pid! (port 5173)
            taskkill /PID !pid! /F >nul 2>&1
        )
        
        netstat -ano | findstr ":3000" | findstr "!pid!" >nul
        if !errorlevel! equ 0 (
            echo   Stopping Node.js process !pid! (port 3000)
            taskkill /PID !pid! /F >nul 2>&1
        )
    )
    echo %GREEN%   ✅ Node.js development servers stopped%RESET%
) else (
    echo %YELLOW%   ⚠️ No Node.js processes found%RESET%
)

:: Stop Vite development servers specifically
echo %YELLOW%Checking for Vite development servers...%RESET%
tasklist /FI "WINDOWTITLE eq ClassroomIO Dev Server*" >nul 2>&1
if !errorlevel! equ 0 (
    echo   Stopping Vite development server windows...
    taskkill /FI "WINDOWTITLE eq ClassroomIO Dev Server*" /F >nul 2>&1
    echo %GREEN%   ✅ Vite development servers stopped%RESET%
) else (
    echo %YELLOW%   ⚠️ No Vite development server windows found%RESET%
)

:: Check for Supabase local instance
echo %YELLOW%Checking for local Supabase services...%RESET%
netstat -an | findstr ":54321" >nul 2>&1
if !errorlevel! equ 0 (
    echo   Local Supabase detected on port 54321
    echo %YELLOW%   Note: Use 'pnpm supabase stop' or 'npx supabase stop' to stop Supabase%RESET%
    echo %YELLOW%   This script does not automatically stop Supabase to preserve data%RESET%
) else (
    echo %GREEN%   ✅ No local Supabase services detected%RESET%
)

:: Check for any remaining processes on development ports
echo %YELLOW%Checking for remaining processes on development ports...%RESET%

set "ports_to_check=5173 3000 4173 5174"
for %%p in (%ports_to_check%) do (
    netstat -ano | findstr ":%%p " >nul 2>&1
    if !errorlevel! equ 0 (
        echo %YELLOW%   ⚠️ Port %%p is still in use%RESET%
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%%p "') do (
            set "pid=%%a"
            echo     Process ID: !pid!
        )
    )
)

:: Clean up any orphaned processes
echo %YELLOW%Cleaning up any orphaned development processes...%RESET%

:: Kill any remaining cmd windows with development server titles
tasklist /FI "IMAGENAME eq cmd.exe" /V | findstr /C:"ClassroomIO" >nul 2>&1
if !errorlevel! equ 0 (
    echo   Cleaning up ClassroomIO command windows...
    taskkill /FI "WINDOWTITLE eq *ClassroomIO*" /F >nul 2>&1
)

:: Final port check
echo %YELLOW%Final port availability check...%RESET%
netstat -an | findstr ":5173" >nul 2>&1
if !errorlevel! equ 0 (
    echo %YELLOW%   ⚠️ Port 5173 is still in use%RESET%
    echo %YELLOW%   You may need to manually stop the process or restart your terminal%RESET%
) else (
    echo %GREEN%   ✅ Port 5173 is now available%RESET%
)

echo.
echo %GREEN%🛑 ClassroomIO development environment stop completed!%RESET%
echo.
echo %CYAN%Next steps:%RESET%
echo %CYAN%• To restart: Run start-dev.bat%RESET%
echo %CYAN%• To stop Supabase: Run 'pnpm supabase stop' or 'npx supabase stop'%RESET%
echo %CYAN%• To clean node_modules: Delete node_modules folder and run start-dev.bat%RESET%
echo.

pause
exit /b 0
