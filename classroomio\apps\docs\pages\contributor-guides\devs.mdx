# Developers Contribution Guide

First and foremost, we're absolutely thrilled that you're considering becoming a part of the community

## Issue Hunters

Did you stumble upon a bug? Encountered a hiccup in deployment? Perhaps you have some user feedback to share? Your quickest route to help us out is by [raising an issue](https://github.com/rotimi-best/classroomio/issues/new/choose).

## Feature Architects

We invite innovative minds to contribute to the evolution of ClassroomIO. If you have a compelling feature proposal that could enhance our platform, please initiate a formal issue submission and apply the "Enhancement" label. We highly value and eagerly anticipate each unique idea presented by our community. To ensure thorough understanding and effective evaluation, kindly include a detailed rationale for your proposed feature. Your insights are crucial to our continuous growth and innovation.

## Pull Requests

Ready to dive into the code and make a real impact? Here's your path:

1. **Fork the Repository:** Fork our repository or use [Gitpod](https://classroomio.com/docs/contributor-guides/gitpod)

2. **Implementation:** Code it out, test it and apply your changes.

3. **Pull Request:** If you're ready to go, create a new pull request following our PR template

Would you prefer a chat before you dive into a lot of work? Our [Discord server](https://dub.sh/ciodiscord) is your harbor. Share your thoughts, and we'll meet you there with open arms. We're responsive and friendly, promise!

## Feature Ideas

If you spot a feature that isn't part of our official plan but could propel ClassroomIO forward, don't hesitate. Raise it as an enhancement issue, and let us know you're ready to take the lead. We'll be quick to respond.

Together, let's craft the future of ClassroomIO, making it better, bolder, and more brilliant!
