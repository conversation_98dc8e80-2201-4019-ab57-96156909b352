{"version": 3, "sources": ["../../../../../node_modules/.pnpm/flatpickr@4.6.9/node_modules/flatpickr/dist/esm/plugins/rangePlugin.js"], "sourcesContent": ["function rangePlugin(config = {}) {\n    return function (fp) {\n        let dateFormat = \"\", secondInput, _secondInputFocused, _prevDates;\n        const createSecondInput = () => {\n            if (config.input) {\n                secondInput =\n                    config.input instanceof Element\n                        ? config.input\n                        : window.document.querySelector(config.input);\n                if (!secondInput) {\n                    fp.config.errorHandler(new Error(\"Invalid input element specified\"));\n                    return;\n                }\n                if (fp.config.wrap) {\n                    secondInput = secondInput.querySelector(\"[data-input]\");\n                }\n            }\n            else {\n                secondInput = fp._input.cloneNode();\n                secondInput.removeAttribute(\"id\");\n                secondInput._flatpickr = undefined;\n            }\n            if (secondInput.value) {\n                const parsedDate = fp.parseDate(secondInput.value);\n                if (parsedDate)\n                    fp.selectedDates.push(parsedDate);\n            }\n            secondInput.setAttribute(\"data-fp-omit\", \"\");\n            if (fp.config.clickOpens) {\n                fp._bind(secondInput, [\"focus\", \"click\"], () => {\n                    if (fp.selectedDates[1]) {\n                        fp.latestSelectedDateObj = fp.selectedDates[1];\n                        fp._setHoursFromDate(fp.selectedDates[1]);\n                        fp.jumpToDate(fp.selectedDates[1]);\n                    }\n                    _secondInputFocused = true;\n                    fp.isOpen = false;\n                    fp.open(undefined, config.position === \"left\" ? fp._input : secondInput);\n                });\n                fp._bind(fp._input, [\"focus\", \"click\"], (e) => {\n                    e.preventDefault();\n                    fp.isOpen = false;\n                    fp.open();\n                });\n            }\n            if (fp.config.allowInput)\n                fp._bind(secondInput, \"keydown\", (e) => {\n                    if (e.key === \"Enter\") {\n                        fp.setDate([fp.selectedDates[0], secondInput.value], true, dateFormat);\n                        secondInput.click();\n                    }\n                });\n            if (!config.input)\n                fp._input.parentNode &&\n                    fp._input.parentNode.insertBefore(secondInput, fp._input.nextSibling);\n        };\n        const plugin = {\n            onParseConfig() {\n                fp.config.mode = \"range\";\n                dateFormat = fp.config.altInput\n                    ? fp.config.altFormat\n                    : fp.config.dateFormat;\n            },\n            onReady() {\n                createSecondInput();\n                fp.config.ignoredFocusElements.push(secondInput);\n                if (fp.config.allowInput) {\n                    fp._input.removeAttribute(\"readonly\");\n                    secondInput.removeAttribute(\"readonly\");\n                }\n                else {\n                    secondInput.setAttribute(\"readonly\", \"readonly\");\n                }\n                fp._bind(fp._input, \"focus\", () => {\n                    fp.latestSelectedDateObj = fp.selectedDates[0];\n                    fp._setHoursFromDate(fp.selectedDates[0]);\n                    _secondInputFocused = false;\n                    fp.jumpToDate(fp.selectedDates[0]);\n                });\n                if (fp.config.allowInput)\n                    fp._bind(fp._input, \"keydown\", (e) => {\n                        if (e.key === \"Enter\")\n                            fp.setDate([fp._input.value, fp.selectedDates[1]], true, dateFormat);\n                    });\n                fp.setDate(fp.selectedDates, false);\n                plugin.onValueUpdate(fp.selectedDates);\n                fp.loadedPlugins.push(\"range\");\n            },\n            onPreCalendarPosition() {\n                if (_secondInputFocused) {\n                    fp._positionElement = secondInput;\n                    setTimeout(() => {\n                        fp._positionElement = fp._input;\n                    }, 0);\n                }\n            },\n            onChange() {\n                if (!fp.selectedDates.length) {\n                    setTimeout(() => {\n                        if (fp.selectedDates.length)\n                            return;\n                        secondInput.value = \"\";\n                        _prevDates = [];\n                    }, 10);\n                }\n                if (_secondInputFocused) {\n                    setTimeout(() => {\n                        secondInput.focus();\n                    }, 0);\n                }\n            },\n            onDestroy() {\n                if (!config.input)\n                    secondInput.parentNode &&\n                        secondInput.parentNode.removeChild(secondInput);\n            },\n            onValueUpdate(selDates) {\n                if (!secondInput)\n                    return;\n                _prevDates =\n                    !_prevDates || selDates.length >= _prevDates.length\n                        ? [...selDates]\n                        : _prevDates;\n                if (_prevDates.length > selDates.length) {\n                    const newSelectedDate = selDates[0];\n                    const newDates = _secondInputFocused\n                        ? [_prevDates[0], newSelectedDate]\n                        : [newSelectedDate, _prevDates[1]];\n                    fp.setDate(newDates, false);\n                    _prevDates = [...newDates];\n                }\n                [\n                    fp._input.value = \"\",\n                    secondInput.value = \"\",\n                ] = fp.selectedDates.map((d) => fp.formatDate(d, dateFormat));\n            },\n        };\n        return plugin;\n    };\n}\nexport default rangePlugin;\n"], "mappings": ";;;AAAA,SAAS,YAAY,SAAS,CAAC,GAAG;AAC9B,SAAO,SAAU,IAAI;AACjB,QAAI,aAAa,IAAI,aAAa,qBAAqB;AACvD,UAAM,oBAAoB,MAAM;AAC5B,UAAI,OAAO,OAAO;AACd,sBACI,OAAO,iBAAiB,UAClB,OAAO,QACP,OAAO,SAAS,cAAc,OAAO,KAAK;AACpD,YAAI,CAAC,aAAa;AACd,aAAG,OAAO,aAAa,IAAI,MAAM,iCAAiC,CAAC;AACnE;AAAA,QACJ;AACA,YAAI,GAAG,OAAO,MAAM;AAChB,wBAAc,YAAY,cAAc,cAAc;AAAA,QAC1D;AAAA,MACJ,OACK;AACD,sBAAc,GAAG,OAAO,UAAU;AAClC,oBAAY,gBAAgB,IAAI;AAChC,oBAAY,aAAa;AAAA,MAC7B;AACA,UAAI,YAAY,OAAO;AACnB,cAAM,aAAa,GAAG,UAAU,YAAY,KAAK;AACjD,YAAI;AACA,aAAG,cAAc,KAAK,UAAU;AAAA,MACxC;AACA,kBAAY,aAAa,gBAAgB,EAAE;AAC3C,UAAI,GAAG,OAAO,YAAY;AACtB,WAAG,MAAM,aAAa,CAAC,SAAS,OAAO,GAAG,MAAM;AAC5C,cAAI,GAAG,cAAc,CAAC,GAAG;AACrB,eAAG,wBAAwB,GAAG,cAAc,CAAC;AAC7C,eAAG,kBAAkB,GAAG,cAAc,CAAC,CAAC;AACxC,eAAG,WAAW,GAAG,cAAc,CAAC,CAAC;AAAA,UACrC;AACA,gCAAsB;AACtB,aAAG,SAAS;AACZ,aAAG,KAAK,QAAW,OAAO,aAAa,SAAS,GAAG,SAAS,WAAW;AAAA,QAC3E,CAAC;AACD,WAAG,MAAM,GAAG,QAAQ,CAAC,SAAS,OAAO,GAAG,CAAC,MAAM;AAC3C,YAAE,eAAe;AACjB,aAAG,SAAS;AACZ,aAAG,KAAK;AAAA,QACZ,CAAC;AAAA,MACL;AACA,UAAI,GAAG,OAAO;AACV,WAAG,MAAM,aAAa,WAAW,CAAC,MAAM;AACpC,cAAI,EAAE,QAAQ,SAAS;AACnB,eAAG,QAAQ,CAAC,GAAG,cAAc,CAAC,GAAG,YAAY,KAAK,GAAG,MAAM,UAAU;AACrE,wBAAY,MAAM;AAAA,UACtB;AAAA,QACJ,CAAC;AACL,UAAI,CAAC,OAAO;AACR,WAAG,OAAO,cACN,GAAG,OAAO,WAAW,aAAa,aAAa,GAAG,OAAO,WAAW;AAAA,IAChF;AACA,UAAM,SAAS;AAAA,MACX,gBAAgB;AACZ,WAAG,OAAO,OAAO;AACjB,qBAAa,GAAG,OAAO,WACjB,GAAG,OAAO,YACV,GAAG,OAAO;AAAA,MACpB;AAAA,MACA,UAAU;AACN,0BAAkB;AAClB,WAAG,OAAO,qBAAqB,KAAK,WAAW;AAC/C,YAAI,GAAG,OAAO,YAAY;AACtB,aAAG,OAAO,gBAAgB,UAAU;AACpC,sBAAY,gBAAgB,UAAU;AAAA,QAC1C,OACK;AACD,sBAAY,aAAa,YAAY,UAAU;AAAA,QACnD;AACA,WAAG,MAAM,GAAG,QAAQ,SAAS,MAAM;AAC/B,aAAG,wBAAwB,GAAG,cAAc,CAAC;AAC7C,aAAG,kBAAkB,GAAG,cAAc,CAAC,CAAC;AACxC,gCAAsB;AACtB,aAAG,WAAW,GAAG,cAAc,CAAC,CAAC;AAAA,QACrC,CAAC;AACD,YAAI,GAAG,OAAO;AACV,aAAG,MAAM,GAAG,QAAQ,WAAW,CAAC,MAAM;AAClC,gBAAI,EAAE,QAAQ;AACV,iBAAG,QAAQ,CAAC,GAAG,OAAO,OAAO,GAAG,cAAc,CAAC,CAAC,GAAG,MAAM,UAAU;AAAA,UAC3E,CAAC;AACL,WAAG,QAAQ,GAAG,eAAe,KAAK;AAClC,eAAO,cAAc,GAAG,aAAa;AACrC,WAAG,cAAc,KAAK,OAAO;AAAA,MACjC;AAAA,MACA,wBAAwB;AACpB,YAAI,qBAAqB;AACrB,aAAG,mBAAmB;AACtB,qBAAW,MAAM;AACb,eAAG,mBAAmB,GAAG;AAAA,UAC7B,GAAG,CAAC;AAAA,QACR;AAAA,MACJ;AAAA,MACA,WAAW;AACP,YAAI,CAAC,GAAG,cAAc,QAAQ;AAC1B,qBAAW,MAAM;AACb,gBAAI,GAAG,cAAc;AACjB;AACJ,wBAAY,QAAQ;AACpB,yBAAa,CAAC;AAAA,UAClB,GAAG,EAAE;AAAA,QACT;AACA,YAAI,qBAAqB;AACrB,qBAAW,MAAM;AACb,wBAAY,MAAM;AAAA,UACtB,GAAG,CAAC;AAAA,QACR;AAAA,MACJ;AAAA,MACA,YAAY;AACR,YAAI,CAAC,OAAO;AACR,sBAAY,cACR,YAAY,WAAW,YAAY,WAAW;AAAA,MAC1D;AAAA,MACA,cAAc,UAAU;AACpB,YAAI,CAAC;AACD;AACJ,qBACI,CAAC,cAAc,SAAS,UAAU,WAAW,SACvC,CAAC,GAAG,QAAQ,IACZ;AACV,YAAI,WAAW,SAAS,SAAS,QAAQ;AACrC,gBAAM,kBAAkB,SAAS,CAAC;AAClC,gBAAM,WAAW,sBACX,CAAC,WAAW,CAAC,GAAG,eAAe,IAC/B,CAAC,iBAAiB,WAAW,CAAC,CAAC;AACrC,aAAG,QAAQ,UAAU,KAAK;AAC1B,uBAAa,CAAC,GAAG,QAAQ;AAAA,QAC7B;AACA;AAAA,UACI,GAAG,OAAO,QAAQ;AAAA,UAClB,YAAY,QAAQ;AAAA,QACxB,IAAI,GAAG,cAAc,IAAI,CAAC,MAAM,GAAG,WAAW,GAAG,UAAU,CAAC;AAAA,MAChE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAO,sBAAQ;", "names": []}