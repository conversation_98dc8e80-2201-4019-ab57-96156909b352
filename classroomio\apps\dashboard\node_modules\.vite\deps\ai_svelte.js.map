{"version": 3, "sources": ["../../../../../node_modules/.pnpm/nanoid@3.3.6/node_modules/nanoid/non-secure/index.js", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/.pnpm/swrev@4.0.0/node_modules/swrev/dist/swrev.mjs", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/.pnpm/sswr@2.0.0_svelte@4.2.3/node_modules/sswr/dist/sswr.mjs", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/svelte/use-chat.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/shared/stream-parts.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/shared/read-data-stream.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/shared/utils.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/shared/parse-complex-response.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/shared/call-chat-api.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/shared/process-chat-stream.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/svelte/use-completion.ts", "../../../../../node_modules/.pnpm/ai@2.2.37_react@18.3.1_solid-js@1.9.7_svelte@4.2.20_vue@3.5.16/node_modules/ai/shared/call-completion-api.ts"], "sourcesContent": ["let urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\nlet customAlphabet = (alphabet, defaultSize = 21) => {\n  return (size = defaultSize) => {\n    let id = ''\n    let i = size\n    while (i--) {\n      id += alphabet[(Math.random() * alphabet.length) | 0]\n    }\n    return id\n  }\n}\nlet nanoid = (size = 21) => {\n  let id = ''\n  let i = size\n  while (i--) {\n    id += urlAlphabet[(Math.random() * 64) | 0]\n  }\n  return id\n}\nexport { nanoid, customAlphabet }\n", "var P = Object.defineProperty;\nvar F = (r, e, t) => e in r ? P(r, e, { enumerable: !0, configurable: !0, writable: !0, value: t }) : r[e] = t;\nvar h = (r, e, t) => (F(r, typeof e != \"symbol\" ? e + \"\" : e, t), t);\nclass I {\n  constructor() {\n    /**\n     * Stores the list of active listener.s\n     */\n    h(this, \"listeners\", /* @__PURE__ */ new Map());\n  }\n  /**\n   * Subscribes a given listener.\n   */\n  subscribe(e, t) {\n    this.listeners.has(e) || this.listeners.set(e, []), !this.listeners.get(e).includes(t) && this.listeners.get(e).push(t);\n  }\n  /**\n   * Unsubscribes the given listener.\n   */\n  unsubscribe(e, t) {\n    this.listeners.has(e) && this.listeners.get(e).includes(t) && (this.listeners.get(e).splice(this.listeners.get(e).indexOf(t), 1), this.listeners.get(e).length === 0 && this.listeners.delete(e));\n  }\n  /**\n   * Emits an event to all active listeners.\n   */\n  emit(e, t) {\n    this.listeners.has(e) && this.listeners.get(e).forEach((s) => s(t));\n  }\n}\nconst L = {\n  broadcast: !1\n}, S = {\n  broadcast: !1\n};\nclass O {\n  /**\n   * Creates the cache item given the data and expiration at.\n   */\n  constructor({ data: e, expiresAt: t = null }) {\n    /**\n     * Determines the data that's stored in the cache.\n     */\n    h(this, \"data\");\n    /**\n     * Determines the expiration date for the given set of data.\n     */\n    h(this, \"expiresAt\");\n    this.data = e, this.expiresAt = t;\n  }\n  /**\n   * Determines if the current cache item is still being resolved.\n   * This returns true if data is a promise, or false if type `D`.\n   */\n  isResolving() {\n    return this.data instanceof Promise;\n  }\n  /**\n   * Determines if the given cache item has expired.\n   */\n  hasExpired() {\n    return this.expiresAt === null || this.expiresAt < /* @__PURE__ */ new Date();\n  }\n  /**\n   * Set the expiration time of the given cache item relative to now.\n   */\n  expiresIn(e) {\n    return this.expiresAt = /* @__PURE__ */ new Date(), this.expiresAt.setMilliseconds(this.expiresAt.getMilliseconds() + e), this;\n  }\n}\nclass q {\n  constructor() {\n    /**\n     * Stores the elements of the cache in a key-value pair.\n     */\n    h(this, \"elements\", /* @__PURE__ */ new Map());\n    /**\n     * Stores the event target instance to dispatch and receive events.\n     */\n    h(this, \"event\", new I());\n  }\n  /**\n   * Resolves the promise and replaces the Promise to the resolved data.\n   * It also broadcasts the value change if needed or deletes the key if\n   * the value resolves to undefined or null.\n   */\n  resolve(e, t) {\n    Promise.resolve(t.data).then((s) => {\n      if (s == null)\n        return this.remove(e);\n      t.data = s, this.broadcast(e, s);\n    });\n  }\n  /**\n   * Gets an element from the cache.\n   *\n   * It is assumed the item always exist when\n   * you get it. Use the has method to check\n   * for the existence of it.\n   */\n  get(e) {\n    return this.elements.get(e);\n  }\n  /**\n   * Sets an element to the cache.\n   */\n  set(e, t) {\n    this.elements.set(e, t), this.resolve(e, t);\n  }\n  /**\n   * Removes an key-value pair from the cache.\n   */\n  remove(e, t) {\n    const { broadcast: s } = { ...L, ...t };\n    s && this.broadcast(e, void 0), this.elements.delete(e);\n  }\n  /**\n   * Removes all the key-value pairs from the cache.\n   */\n  clear(e) {\n    const { broadcast: t } = { ...S, ...e };\n    if (t)\n      for (const s of this.elements.keys())\n        this.broadcast(s, void 0);\n    this.elements.clear();\n  }\n  /**\n   * Determines if the given key exists\n   * in the cache.\n   */\n  has(e) {\n    return this.elements.has(e);\n  }\n  /**\n   * Subscribes the callback to the given key.\n   */\n  subscribe(e, t) {\n    this.event.subscribe(e, t);\n  }\n  /**\n   * Unsubscribes to the given key events.\n   */\n  unsubscribe(e, t) {\n    this.event.unsubscribe(e, t);\n  }\n  /**\n   * Broadcasts a value change  on all subscribed instances.\n   */\n  broadcast(e, t) {\n    this.event.emit(e, t);\n  }\n}\nconst x = {\n  cache: new q(),\n  errors: new I(),\n  fetcher: async (r) => {\n    const e = await fetch(r);\n    if (!e.ok)\n      throw Error(\"Not a 2XX response.\");\n    return e.json();\n  },\n  fallbackData: void 0,\n  loadInitialCache: !0,\n  revalidateOnStart: !0,\n  dedupingInterval: 2e3,\n  revalidateOnFocus: !0,\n  focusThrottleInterval: 5e3,\n  revalidateOnReconnect: !0,\n  reconnectWhen: (r, { enabled: e }) => e && typeof window < \"u\" ? (window.addEventListener(\"online\", r), () => window.removeEventListener(\"online\", r)) : () => {\n  },\n  focusWhen: (r, { enabled: e, throttleInterval: t }) => {\n    if (e && typeof window < \"u\") {\n      let s = null;\n      const i = () => {\n        const a = Date.now();\n        (s === null || a - s > t) && (s = a, r());\n      };\n      return window.addEventListener(\"focus\", i), () => window.removeEventListener(\"focus\", i);\n    }\n    return () => {\n    };\n  },\n  revalidateFunction: void 0\n}, E = {\n  ...x,\n  force: !1\n}, T = {\n  revalidate: !0,\n  revalidateOptions: { ...E },\n  revalidateFunction: void 0\n}, X = {\n  broadcast: !1\n};\nclass H {\n  /**\n   * Creates a new instance of SWR.\n   */\n  constructor(e) {\n    /**\n     * Stores the options of the SWR.\n     */\n    h(this, \"options\");\n    this.options = { ...x, ...e };\n  }\n  /**\n   * Gets the cache of the SWR.\n   */\n  get cache() {\n    return this.options.cache;\n  }\n  /**\n   * Gets the cache of the SWR.\n   */\n  get errors() {\n    return this.options.errors;\n  }\n  /**\n   * Requests the data using the provided fetcher.\n   */\n  async requestData(e, t) {\n    return await Promise.resolve(t(e)).catch((s) => {\n      throw this.errors.emit(e, s), s;\n    });\n  }\n  /**\n   * Resolves the given to a SWRKey or undefined.\n   */\n  resolveKey(e) {\n    if (typeof e == \"function\")\n      try {\n        return e();\n      } catch {\n        return;\n      }\n    return e;\n  }\n  /**\n   * Clear the specified keys from the cache. If no keys\n   * are specified, it clears all the cache keys.\n   */\n  clear(e, t) {\n    const s = { ...X, ...t };\n    if (e == null)\n      return this.cache.clear(s);\n    if (!Array.isArray(e))\n      return this.cache.remove(e, s);\n    for (const i of e)\n      this.cache.remove(i, s);\n  }\n  /**\n   * Revalidates the key and mutates the cache if needed.\n   */\n  async revalidate(e, t) {\n    if (!e)\n      throw new Error(\"[Revalidate] Key issue: ${key}\");\n    const { fetcher: s, dedupingInterval: i } = this.options, { force: a, fetcher: o, dedupingInterval: n } = {\n      ...E,\n      fetcher: s,\n      dedupingInterval: i,\n      ...t\n    };\n    if (a || !this.cache.has(e) || this.cache.has(e) && this.cache.get(e).hasExpired()) {\n      const c = this.requestData(e, o), l = c.catch(() => {\n      });\n      return this.cache.set(e, new O({ data: l }).expiresIn(n)), await c;\n    }\n    return this.getWait(e);\n  }\n  /**\n   * Mutates the data of a given key with a new value.\n   * This is used to replace the cache contents of the\n   * given key manually.\n   */\n  async mutate(e, t, s) {\n    if (!e)\n      throw new Error(\"[Mutate] Key issue: ${key}\");\n    const {\n      revalidate: i,\n      revalidateOptions: a,\n      revalidateFunction: o\n    } = {\n      ...T,\n      ...s\n    };\n    let n;\n    if (typeof t == \"function\") {\n      let c;\n      if (this.cache.has(e)) {\n        const l = this.cache.get(e);\n        l.isResolving() || (c = l.data);\n      }\n      n = t(c);\n    } else\n      n = t;\n    return this.cache.set(e, new O({ data: n })), i ? await ((o == null ? void 0 : o(e, a)) ?? this.revalidate(e, a)) : n;\n  }\n  /**\n   * Gets the data of the given key. Keep in mind\n   * this data will be stale and revalidate in the background\n   * unless specified otherwise.\n   */\n  subscribeData(e, t) {\n    if (e) {\n      const s = (i) => t(i);\n      return this.cache.subscribe(e, s), () => this.cache.unsubscribe(e, s);\n    }\n    return () => {\n    };\n  }\n  /**\n   * Subscribes to errors on the given key.\n   */\n  subscribeErrors(e, t) {\n    if (e) {\n      const s = (i) => t(i);\n      return this.errors.subscribe(e, s), () => this.errors.unsubscribe(e, s);\n    }\n    return () => {\n    };\n  }\n  /**\n   * Gets the current cached data of the given key.\n   * This does not trigger any revalidation nor mutation\n   * of the data.\n   * - If the data has never been validated\n   * (there is no cache) it will return undefined.\n   * - If the item is pending to resolve (there is a request\n   * pending to resolve) it will return undefined.\n   */\n  get(e) {\n    if (e && this.cache.has(e)) {\n      const t = this.cache.get(e);\n      if (!t.isResolving())\n        return t.data;\n    }\n  }\n  /**\n   * Gets an element from the cache. The difference\n   * with the get is that this method returns a promise\n   * that will resolve the the value. If there's no item\n   * in the cache, it will wait for it before resolving.\n   */\n  getWait(e) {\n    return new Promise((t, s) => {\n      const i = this.subscribeData(e, (n) => {\n        if (i(), n !== void 0)\n          return t(n);\n      }), a = this.subscribeErrors(e, (n) => {\n        if (a(), n !== void 0)\n          return s(n);\n      }), o = this.get(e);\n      if (o !== void 0)\n        return t(o);\n    });\n  }\n  /**\n   * Use a SWR value given the key and\n   * subscribe to future changes.\n   */\n  subscribe(e, t, s, i) {\n    const {\n      fetcher: a,\n      fallbackData: o,\n      loadInitialCache: n,\n      revalidateOnStart: c,\n      dedupingInterval: l,\n      revalidateOnFocus: A,\n      focusThrottleInterval: C,\n      revalidateOnReconnect: R,\n      reconnectWhen: W,\n      focusWhen: D,\n      revalidateFunction: d\n    } = {\n      // Current instance options\n      // (includes default options)\n      ...this.options,\n      // Current call options.\n      ...i\n    }, K = (m) => (d == null ? void 0 : d(this.resolveKey(e), m)) ?? this.revalidate(this.resolveKey(e), m), f = () => K({ fetcher: a, dedupingInterval: l }), u = n ? this.get(this.resolveKey(e)) : o ?? void 0, g = c ? f() : Promise.resolve(void 0), M = u ? Promise.resolve(u) : g;\n    u && (t == null || t(u));\n    const v = t ? this.subscribeData(this.resolveKey(e), t) : void 0, b = s ? this.subscribeErrors(this.resolveKey(e), s) : void 0, p = D(f, {\n      throttleInterval: C,\n      enabled: A\n    }), w = W(f, {\n      enabled: R\n    });\n    return { unsubscribe: () => {\n      v == null || v(), b == null || b(), p == null || p(), w == null || w();\n    }, dataPromise: M, revalidatePromise: g };\n  }\n}\nexport {\n  O as CacheItem,\n  q as DefaultCache,\n  I as DefaultSWREventManager,\n  H as SWR,\n  S as defaultCacheClearOptions,\n  L as defaultCacheRemoveOptions,\n  X as defaultClearOptions,\n  T as defaultMutateOptions,\n  x as defaultOptions,\n  E as defaultRevalidateOptions\n};\n", "import { SWR as _ } from \"swrev\";\nimport { beforeUpdate as w, onDestroy as E } from \"svelte\";\nfunction p() {\n}\nfunction D(t) {\n  return t();\n}\nfunction q(t) {\n  t.forEach(D);\n}\nfunction x(t) {\n  return typeof t == \"function\";\n}\nfunction K(t, e) {\n  return t != t ? e == e : t !== e || t && typeof t == \"object\" || typeof t == \"function\";\n}\nfunction z(t, ...e) {\n  if (t == null) {\n    for (const r of e)\n      r(void 0);\n    return p;\n  }\n  const n = t.subscribe(...e);\n  return n.unsubscribe ? () => n.unsubscribe() : n;\n}\nconst v = [];\nfunction A(t, e) {\n  return {\n    subscribe: y(t, e).subscribe\n  };\n}\nfunction y(t, e = p) {\n  let n;\n  const r = /* @__PURE__ */ new Set();\n  function i(u) {\n    if (K(t, u) && (t = u, n)) {\n      const f = !v.length;\n      for (const s of r)\n        s[1](), v.push(s, t);\n      if (f) {\n        for (let s = 0; s < v.length; s += 2)\n          v[s][0](v[s + 1]);\n        v.length = 0;\n      }\n    }\n  }\n  function a(u) {\n    i(u(t));\n  }\n  function d(u, f = p) {\n    const s = [u, f];\n    return r.add(s), r.size === 1 && (n = e(i, a) || p), u(t), () => {\n      r.delete(s), r.size === 0 && n && (n(), n = null);\n    };\n  }\n  return { set: i, update: a, subscribe: d };\n}\nfunction S(t, e, n) {\n  const r = !Array.isArray(t), i = r ? [t] : t;\n  if (!i.every(Boolean))\n    throw new Error(\"derived() expects stores as input, got a falsy value\");\n  const a = e.length < 2;\n  return A(n, (d, u) => {\n    let f = !1;\n    const s = [];\n    let h = 0, o = p;\n    const l = () => {\n      if (h)\n        return;\n      o();\n      const b = e(r ? s[0] : s, d, u);\n      a ? d(b) : o = x(b) ? b : p;\n    }, g = i.map(\n      (b, m) => z(\n        b,\n        (R) => {\n          s[m] = R, h &= ~(1 << m), f && l();\n        },\n        () => {\n          h |= 1 << m;\n        }\n      )\n    );\n    return f = !0, l(), function() {\n      q(g), o(), f = !1;\n    };\n  });\n}\nclass O extends _ {\n  /**\n   * Svelte specific use of SWR.\n   */\n  useSWR(e, n) {\n    let r;\n    const i = y(void 0, () => () => r == null ? void 0 : r()), a = y(void 0, () => () => r == null ? void 0 : r());\n    w(() => {\n      const o = (g) => {\n        a.set(void 0), i.set(g);\n      }, l = (g) => a.set(g);\n      r || (r = this.subscribe(e, o, l, {\n        loadInitialCache: !0,\n        ...n\n      }).unsubscribe);\n    }), E(() => r == null ? void 0 : r());\n    const d = (o, l) => this.mutate(this.resolveKey(e), o, {\n      revalidateOptions: n,\n      ...l\n    }), u = (o) => this.revalidate(this.resolveKey(e), { ...n, ...o }), f = (o) => this.clear(this.resolveKey(e), o), s = S([i, a], ([o, l]) => o === void 0 && l === void 0), h = S([i, a], ([o, l]) => o !== void 0 && l === void 0);\n    return { data: i, error: a, mutate: d, revalidate: u, clear: f, isLoading: s, isValid: h };\n  }\n}\nconst W = (t) => new O(t);\nlet c = W();\nconst C = (t) => (c = W(t), c), I = (t, e) => c.subscribeData(t, e), L = (t, e) => c.subscribeErrors(t, e), U = (t) => c.get(t), V = (t) => c.getWait(t), $ = (t, e, n, r) => c.subscribe(t, e, n, r), F = (t, e) => c.useSWR(t, e), G = (t, e, n) => c.mutate(t, e, n), H = (t, e) => c.revalidate(t, e), J = (t, e) => c.clear(t, e);\nexport {\n  O as SSWR,\n  J as clear,\n  C as createDefaultSWR,\n  W as createSWR,\n  U as get,\n  V as getOrWait,\n  G as mutate,\n  H as revalidate,\n  I as subscribe,\n  L as subscribeErrors,\n  c as swr,\n  $ as use,\n  F as useSWR\n};\n", "import { useS<PERSON> } from 'sswr';\nimport { Readable, Writable, derived, get, writable } from 'svelte/store';\nimport { callChatApi } from '../shared/call-chat-api';\nimport { processChatStream } from '../shared/process-chat-stream';\nimport type {\n  ChatRequest,\n  ChatRequestOptions,\n  CreateMessage,\n  IdGenerator,\n  JSONValue,\n  Message,\n  UseChatOptions,\n} from '../shared/types';\nimport { nanoid } from '../shared/utils';\nexport type { CreateMessage, Message, UseChatOptions };\n\nexport type UseChatHelpers = {\n  /** Current messages in the chat */\n  messages: Readable<Message[]>;\n  /** The error object of the API request */\n  error: Readable<undefined | Error>;\n  /**\n   * Append a user message to the chat list. This triggers the API call to fetch\n   * the assistant's response.\n   * @param message The message to append\n   * @param chatRequestOptions Additional options to pass to the API call\n   */\n  append: (\n    message: Message | CreateMessage,\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Reload the last AI chat response for the given chat history. If the last\n   * message isn't from the assistant, it will request the API to generate a\n   * new response.\n   */\n  reload: (\n    chatRequestOptions?: ChatRequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current request immediately, keep the generated tokens if any.\n   */\n  stop: () => void;\n  /**\n   * Update the `messages` state locally. This is useful when you want to\n   * edit the messages on the client, and then trigger the `reload` method\n   * manually to regenerate the AI response.\n   */\n  setMessages: (messages: Message[]) => void;\n  /** The current value of the input */\n  input: Writable<string>;\n  /** Form submission handler to automatically reset input and append a user message  */\n  handleSubmit: (e: any, chatRequestOptions?: ChatRequestOptions) => void;\n  metadata?: Object;\n  /** Whether the API request is in progress */\n  isLoading: Readable<boolean | undefined>;\n\n  /** Additional data added on the server via StreamData */\n  data: Readable<JSONValue[] | undefined>;\n};\nconst getStreamedResponse = async (\n  api: string,\n  chatRequest: ChatRequest,\n  mutate: (messages: Message[]) => void,\n  mutateStreamData: (data: JSONValue[] | undefined) => void,\n  existingData: JSONValue[] | undefined,\n  extraMetadata: {\n    credentials?: RequestCredentials;\n    headers?: Record<string, string> | Headers;\n    body?: any;\n  },\n  previousMessages: Message[],\n  abortControllerRef: AbortController | null,\n  generateId: IdGenerator,\n  onFinish?: (message: Message) => void,\n  onResponse?: (response: Response) => void | Promise<void>,\n  sendExtraMessageFields?: boolean,\n) => {\n  // Do an optimistic update to the chat state to show the updated messages\n  // immediately.\n  mutate(chatRequest.messages);\n\n  const constructedMessagesPayload = sendExtraMessageFields\n    ? chatRequest.messages\n    : chatRequest.messages.map(\n        ({ role, content, name, function_call, tool_calls, tool_call_id }) => ({\n          role,\n          content,\n          tool_call_id,\n          ...(name !== undefined && { name }),\n          ...(function_call !== undefined && {\n            function_call: function_call,\n          }),\n          ...(tool_calls !== undefined && {\n            tool_calls: tool_calls,\n          }),\n        }),\n      );\n\n  return await callChatApi({\n    api,\n    messages: constructedMessagesPayload,\n    body: {\n      ...extraMetadata.body,\n      ...chatRequest.options?.body,\n      ...(chatRequest.functions !== undefined && {\n        functions: chatRequest.functions,\n      }),\n      ...(chatRequest.function_call !== undefined && {\n        function_call: chatRequest.function_call,\n      }),\n      ...(chatRequest.tools !== undefined && {\n        tools: chatRequest.tools,\n      }),\n      ...(chatRequest.tool_choice !== undefined && {\n        tool_choice: chatRequest.tool_choice,\n      }),\n    },\n    credentials: extraMetadata.credentials,\n    headers: {\n      ...extraMetadata.headers,\n      ...chatRequest.options?.headers,\n    },\n    abortController: () => abortControllerRef,\n    appendMessage(message) {\n      mutate([...chatRequest.messages, message]);\n    },\n    restoreMessagesOnFailure() {\n      mutate(previousMessages);\n    },\n    onResponse,\n    onUpdate(merged, data) {\n      mutate([...chatRequest.messages, ...merged]);\n      mutateStreamData([...(existingData || []), ...(data || [])]);\n    },\n    onFinish,\n    generateId,\n  });\n};\n\nlet uniqueId = 0;\n\nconst store: Record<string, Message[] | undefined> = {};\n\nexport function useChat({\n  api = '/api/chat',\n  id,\n  initialMessages = [],\n  initialInput = '',\n  sendExtraMessageFields,\n  experimental_onFunctionCall,\n  experimental_onToolCall,\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId = nanoid,\n}: UseChatOptions = {}): UseChatHelpers {\n  // Generate a unique id for the chat if not provided.\n  const chatId = id || `chat-${uniqueId++}`;\n\n  const key = `${api}|${chatId}`;\n  const {\n    data,\n    mutate: originalMutate,\n    isLoading: isSWRLoading,\n  } = useSWR<Message[]>(key, {\n    fetcher: () => store[key] || initialMessages,\n    fallbackData: initialMessages,\n  });\n\n  const streamData = writable<JSONValue[] | undefined>(undefined);\n\n  const loading = writable<boolean>(false);\n\n  // Force the `data` to be `initialMessages` if it's `undefined`.\n  data.set(initialMessages);\n\n  const mutate = (data: Message[]) => {\n    store[key] = data;\n    return originalMutate(data);\n  };\n\n  // Because of the `fallbackData` option, the `data` will never be `undefined`.\n  const messages = data as Writable<Message[]>;\n\n  // Abort controller to cancel the current API call.\n  let abortController: AbortController | null = null;\n\n  const extraMetadata = {\n    credentials,\n    headers,\n    body,\n  };\n\n  const error = writable<undefined | Error>(undefined);\n\n  // Actual mutation hook to send messages to the API endpoint and update the\n  // chat state.\n  async function triggerRequest(chatRequest: ChatRequest) {\n    try {\n      error.set(undefined);\n      loading.set(true);\n      abortController = new AbortController();\n\n      await processChatStream({\n        getStreamedResponse: () =>\n          getStreamedResponse(\n            api,\n            chatRequest,\n            mutate,\n            data => {\n              streamData.set(data);\n            },\n            get(streamData),\n            extraMetadata,\n            get(messages),\n            abortController,\n            generateId,\n            onFinish,\n            onResponse,\n            sendExtraMessageFields,\n          ),\n        experimental_onFunctionCall,\n        experimental_onToolCall,\n        updateChatRequest: chatRequestParam => {\n          chatRequest = chatRequestParam;\n        },\n        getCurrentMessages: () => get(messages),\n      });\n\n      abortController = null;\n\n      return null;\n    } catch (err) {\n      // Ignore abort errors as they are expected.\n      if ((err as any).name === 'AbortError') {\n        abortController = null;\n        return null;\n      }\n\n      if (onError && err instanceof Error) {\n        onError(err);\n      }\n\n      error.set(err as Error);\n    } finally {\n      loading.set(false);\n    }\n  }\n\n  const append: UseChatHelpers['append'] = async (\n    message: Message | CreateMessage,\n    {\n      options,\n      functions,\n      function_call,\n      tools,\n      tool_choice,\n    }: ChatRequestOptions = {},\n  ) => {\n    if (!message.id) {\n      message.id = generateId();\n    }\n\n    const chatRequest: ChatRequest = {\n      messages: get(messages).concat(message as Message),\n      options,\n      ...(functions !== undefined && { functions }),\n      ...(function_call !== undefined && { function_call }),\n      ...(tools !== undefined && { tools }),\n      ...(tool_choice !== undefined && { tool_choice }),\n    };\n    return triggerRequest(chatRequest);\n  };\n\n  const reload: UseChatHelpers['reload'] = async ({\n    options,\n    functions,\n    function_call,\n    tools,\n    tool_choice,\n  }: ChatRequestOptions = {}) => {\n    const messagesSnapshot = get(messages);\n    if (messagesSnapshot.length === 0) return null;\n\n    // Remove last assistant message and retry last user message.\n    const lastMessage = messagesSnapshot.at(-1);\n    if (lastMessage?.role === 'assistant') {\n      const chatRequest: ChatRequest = {\n        messages: messagesSnapshot.slice(0, -1),\n        options,\n        ...(functions !== undefined && { functions }),\n        ...(function_call !== undefined && { function_call }),\n        ...(tools !== undefined && { tools }),\n        ...(tool_choice !== undefined && { tool_choice }),\n      };\n\n      return triggerRequest(chatRequest);\n    }\n    const chatRequest: ChatRequest = {\n      messages: messagesSnapshot,\n      options,\n      ...(functions !== undefined && { functions }),\n      ...(function_call !== undefined && { function_call }),\n      ...(tools !== undefined && { tools }),\n      ...(tool_choice !== undefined && { tool_choice }),\n    };\n\n    return triggerRequest(chatRequest);\n  };\n\n  const stop = () => {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  };\n\n  const setMessages = (messages: Message[]) => {\n    mutate(messages);\n  };\n\n  const input = writable(initialInput);\n\n  const handleSubmit = (e: any, options: ChatRequestOptions = {}) => {\n    e.preventDefault();\n    const inputValue = get(input);\n    if (!inputValue) return;\n\n    append(\n      {\n        content: inputValue,\n        role: 'user',\n        createdAt: new Date(),\n      },\n      options,\n    );\n    input.set('');\n  };\n\n  const isLoading = derived(\n    [isSWRLoading, loading],\n    ([$isSWRLoading, $loading]) => {\n      return $isSWRLoading || $loading;\n    },\n  );\n\n  return {\n    messages,\n    error,\n    append,\n    reload,\n    stop,\n    setMessages,\n    input,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n  };\n}\n", "import {\n  AssistantMessage,\n  DataMessage,\n  FunctionCall,\n  JSONValue,\n  ToolCall,\n} from './types';\nimport { StreamString } from './utils';\n\nexport interface StreamPart<CODE extends string, NAME extends string, TYPE> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: StreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst functionCallStreamPart: StreamPart<\n  '1',\n  'function_call',\n  { function_call: FunctionCall }\n> = {\n  code: '1',\n  name: 'function_call',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('function_call' in value) ||\n      typeof value.function_call !== 'object' ||\n      value.function_call == null ||\n      !('name' in value.function_call) ||\n      !('arguments' in value.function_call) ||\n      typeof value.function_call.name !== 'string' ||\n      typeof value.function_call.arguments !== 'string'\n    ) {\n      throw new Error(\n        '\"function_call\" parts expect an object with a \"function_call\" property.',\n      );\n    }\n\n    return {\n      type: 'function_call',\n      value: value as unknown as { function_call: FunctionCall },\n    };\n  },\n};\n\nconst dataStreamPart: StreamPart<'2', 'data', Array<JSONValue>> = {\n  code: '2',\n  name: 'data',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n\n    return { type: 'data', value };\n  },\n};\n\nconst errorStreamPart: StreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst assistantMessageStreamPart: StreamPart<\n  '4',\n  'assistant_message',\n  AssistantMessage\n> = {\n  code: '4',\n  name: 'assistant_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('id' in value) ||\n      !('role' in value) ||\n      !('content' in value) ||\n      typeof value.id !== 'string' ||\n      typeof value.role !== 'string' ||\n      value.role !== 'assistant' ||\n      !Array.isArray(value.content) ||\n      !value.content.every(\n        item =>\n          item != null &&\n          typeof item === 'object' &&\n          'type' in item &&\n          item.type === 'text' &&\n          'text' in item &&\n          item.text != null &&\n          typeof item.text === 'object' &&\n          'value' in item.text &&\n          typeof item.text.value === 'string',\n      )\n    ) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_message',\n      value: value as AssistantMessage,\n    };\n  },\n};\n\nconst assistantControlDataStreamPart: StreamPart<\n  '5',\n  'assistant_control_data',\n  {\n    threadId: string;\n    messageId: string;\n  }\n> = {\n  code: '5',\n  name: 'assistant_control_data',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('threadId' in value) ||\n      !('messageId' in value) ||\n      typeof value.threadId !== 'string' ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_control_data',\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst dataMessageStreamPart: StreamPart<'6', 'data_message', DataMessage> = {\n  code: '6',\n  name: 'data_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('role' in value) ||\n      !('data' in value) ||\n      typeof value.role !== 'string' ||\n      value.role !== 'data'\n    ) {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.',\n      );\n    }\n\n    return {\n      type: 'data_message',\n      value: value as DataMessage,\n    };\n  },\n};\n\nconst toolCallStreamPart: StreamPart<\n  '7',\n  'tool_calls',\n  { tool_calls: ToolCall[] }\n> = {\n  code: '7',\n  name: 'tool_calls',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('tool_calls' in value) ||\n      typeof value.tool_calls !== 'object' ||\n      value.tool_calls == null ||\n      !Array.isArray(value.tool_calls) ||\n      value.tool_calls.some(tc => {\n        tc == null ||\n          typeof tc !== 'object' ||\n          !('id' in tc) ||\n          typeof tc.id !== 'string' ||\n          !('type' in tc) ||\n          typeof tc.type !== 'string' ||\n          !('function' in tc) ||\n          tc.function == null ||\n          typeof tc.function !== 'object' ||\n          !('arguments' in tc.function) ||\n          typeof tc.function.name !== 'string' ||\n          typeof tc.function.arguments !== 'string';\n      })\n    ) {\n      throw new Error(\n        '\"tool_calls\" parts expect an object with a ToolCallPayload.',\n      );\n    }\n\n    return {\n      type: 'tool_calls',\n      value: value as unknown as { tool_calls: ToolCall[] },\n    };\n  },\n};\n\nconst messageAnnotationsStreamPart: StreamPart<\n  '8',\n  'message_annotations',\n  Array<JSONValue>\n> = {\n  code: '8',\n  name: 'message_annotations',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n\n    return { type: 'message_annotations', value };\n  },\n};\n\nconst streamParts = [\n  textStreamPart,\n  functionCallStreamPart,\n  dataStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart,\n  toolCallStreamPart,\n  messageAnnotationsStreamPart,\n] as const;\n\n// union type of all stream parts\ntype StreamParts =\n  | typeof textStreamPart\n  | typeof functionCallStreamPart\n  | typeof dataStreamPart\n  | typeof errorStreamPart\n  | typeof assistantMessageStreamPart\n  | typeof assistantControlDataStreamPart\n  | typeof dataMessageStreamPart\n  | typeof toolCallStreamPart\n  | typeof messageAnnotationsStreamPart;\n/**\n * Maps the type of a stream part to its value type.\n */\ntype StreamPartValueType = {\n  [P in StreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type StreamPartType =\n  | ReturnType<typeof textStreamPart.parse>\n  | ReturnType<typeof functionCallStreamPart.parse>\n  | ReturnType<typeof dataStreamPart.parse>\n  | ReturnType<typeof errorStreamPart.parse>\n  | ReturnType<typeof assistantMessageStreamPart.parse>\n  | ReturnType<typeof assistantControlDataStreamPart.parse>\n  | ReturnType<typeof dataMessageStreamPart.parse>\n  | ReturnType<typeof toolCallStreamPart.parse>\n  | ReturnType<typeof messageAnnotationsStreamPart.parse>;\n\nexport const streamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [functionCallStreamPart.code]: functionCallStreamPart,\n  [dataStreamPart.code]: dataStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart,\n  [toolCallStreamPart.code]: toolCallStreamPart,\n  [messageAnnotationsStreamPart.code]: messageAnnotationsStreamPart,\n} as const;\n\n/**\n * The map of prefixes for data in the stream\n *\n * - 0: Text from the LLM response\n * - 1: (OpenAI) function_call responses\n * - 2: custom JSON added by the user using `Data`\n * - 6: (OpenAI) tool_call responses\n *\n * Example:\n * ```\n * 0:Vercel\n * 0:'s\n * 0: AI\n * 0: AI\n * 0: SDK\n * 0: is great\n * 0:!\n * 2: { \"someJson\": \"value\" }\n * 1: {\"function_call\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}\n * 6: {\"tool_call\": {\"id\": \"tool_0\", \"type\": \"function\", \"function\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}}\n *```\n */\nexport const StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [functionCallStreamPart.name]: functionCallStreamPart.code,\n  [dataStreamPart.name]: dataStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code,\n  [toolCallStreamPart.name]: toolCallStreamPart.code,\n  [messageAnnotationsStreamPart.name]: messageAnnotationsStreamPart.code,\n} as const;\n\nexport const validCodes = streamParts.map(part => part.code);\n\n/**\n * Parses a stream part from a string.\n *\n * @param line The string to parse.\n * @returns The parsed stream part.\n * @throws An error if the string cannot be parsed.\n */\nexport const parseStreamPart = (line: string): StreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof streamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof streamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return streamPartsByCode[code].parse(jsonValue);\n};\n\n/**\n * Prepends a string with a prefix from the `StreamChunkPrefixes`, JSON-ifies it,\n * and appends a new line.\n *\n * It ensures type-safety for the part type and value.\n */\nexport function formatStreamPart<T extends keyof StreamPartValueType>(\n  type: T,\n  value: StreamPartValueType[T],\n): StreamString {\n  const streamPart = streamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { StreamPartType, parseStreamPart } from './stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\nexport async function* readDataStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  {\n    isAborted,\n  }: {\n    isAborted?: () => boolean;\n  } = {},\n): AsyncGenerator<StreamPartType> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '') // splitting leaves an empty string at the end\n      .map(parseStreamPart);\n\n    for (const streamPart of streamParts) {\n      yield streamPart;\n    }\n\n    // The request has been aborted, stop reading the stream.\n    if (isAborted?.()) {\n      reader.cancel();\n      break;\n    }\n  }\n}\n", "import { custom<PERSON><PERSON><PERSON><PERSON> } from 'nanoid/non-secure';\nimport {\n  StreamPartType,\n  StreamStringPrefixes,\n  parseStreamPart,\n} from './stream-parts';\n\n// 7-character random string\nexport const nanoid = customAlphabet(\n  '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  7,\n);\n\n// simple decoder signatures:\nfunction createChunkDecoder(): (chunk: Uint8Array | undefined) => string;\nfunction createChunkDecoder(\n  complex: false,\n): (chunk: Uint8Array | undefined) => string;\n// complex decoder signature:\nfunction createChunkDecoder(\n  complex: true,\n): (chunk: Uint8Array | undefined) => StreamPartType[];\n// combined signature for when the client calls this function with a boolean:\nfunction createChunkDecoder(\n  complex?: boolean,\n): (chunk: Uint8Array | undefined) => StreamPartType[] | string;\nfunction createChunkDecoder(complex?: boolean) {\n  const decoder = new TextDecoder();\n\n  if (!complex) {\n    return function (chunk: Uint8Array | undefined): string {\n      if (!chunk) return '';\n      return decoder.decode(chunk, { stream: true });\n    };\n  }\n\n  return function (chunk: Uint8Array | undefined) {\n    const decoded = decoder\n      .decode(chunk, { stream: true })\n      .split('\\n')\n      .filter(line => line !== ''); // splitting leaves an empty string at the end\n\n    return decoded.map(parseStreamPart).filter(Boolean);\n  };\n}\n\nexport { createChunkDecoder };\n\nexport const isStreamStringEqualToType = (\n  type: keyof typeof StreamStringPrefixes,\n  value: string,\n): value is StreamString =>\n  value.startsWith(`${StreamStringPrefixes[type]}:`) && value.endsWith('\\n');\n\nexport type StreamString =\n  `${(typeof StreamStringPrefixes)[keyof typeof StreamStringPrefixes]}:${string}\\n`;\n\n/**\n * A header sent to the client so it knows how to handle parsing the stream (as a deprecated text response or using the new prefixed protocol)\n */\nexport const COMPLEX_HEADER = 'X-Experimental-Stream-Data';\n", "import { readDataStream } from './read-data-stream';\nimport type { Function<PERSON><PERSON>, JSONValue, Message, ToolCall } from './types';\nimport { nanoid } from './utils';\n\ntype PrefixMap = {\n  text?: Message;\n  function_call?: Message & {\n    role: 'assistant';\n    function_call: FunctionCall;\n  };\n  tool_calls?: Message & {\n    role: 'assistant';\n    tool_calls: ToolCall[];\n  };\n  data: JSONValue[];\n};\n\nfunction assignAnnotationsToMessage<T extends Message | null | undefined>(\n  message: T,\n  annotations: JSONValue[] | undefined,\n): T {\n  if (!message || !annotations || !annotations.length) return message;\n  return { ...message, annotations: [...annotations] } as T;\n}\n\nexport async function parseComplexResponse({\n  reader,\n  abortControllerRef,\n  update,\n  onFinish,\n  generateId = nanoid,\n  getCurrentDate = () => new Date(),\n}: {\n  reader: ReadableStreamDefaultReader<Uint8Array>;\n  abortControllerRef?: {\n    current: AbortController | null;\n  };\n  update: (merged: Message[], data: JSONValue[] | undefined) => void;\n  onFinish?: (prefixMap: PrefixMap) => void;\n  generateId?: () => string;\n  getCurrentDate?: () => Date;\n}) {\n  const createdAt = getCurrentDate();\n  const prefixMap: PrefixMap = {\n    data: [],\n  };\n\n  // keep list of current message annotations for message\n  let message_annotations: JSONValue[] | undefined = undefined;\n\n  // we create a map of each prefix, and for each prefixed message we push to the map\n  for await (const { type, value } of readDataStream(reader, {\n    isAborted: () => abortControllerRef?.current === null,\n  })) {\n    if (type === 'text') {\n      if (prefixMap['text']) {\n        prefixMap['text'] = {\n          ...prefixMap['text'],\n          content: (prefixMap['text'].content || '') + value,\n        };\n      } else {\n        prefixMap['text'] = {\n          id: generateId(),\n          role: 'assistant',\n          content: value,\n          createdAt,\n        };\n      }\n    }\n\n    let functionCallMessage: Message | null | undefined = null;\n\n    if (type === 'function_call') {\n      prefixMap['function_call'] = {\n        id: generateId(),\n        role: 'assistant',\n        content: '',\n        function_call: value.function_call,\n        name: value.function_call.name,\n        createdAt,\n      };\n\n      functionCallMessage = prefixMap['function_call'];\n    }\n\n    let toolCallMessage: Message | null | undefined = null;\n\n    if (type === 'tool_calls') {\n      prefixMap['tool_calls'] = {\n        id: generateId(),\n        role: 'assistant',\n        content: '',\n        tool_calls: value.tool_calls,\n        createdAt,\n      };\n\n      toolCallMessage = prefixMap['tool_calls'];\n    }\n\n    if (type === 'data') {\n      prefixMap['data'].push(...value);\n    }\n\n    let responseMessage = prefixMap['text'];\n\n    if (type === 'message_annotations') {\n      if (!message_annotations) {\n        message_annotations = [...value];\n      } else {\n        message_annotations.push(...value);\n      }\n\n      // Update any existing message with the latest annotations\n      functionCallMessage = assignAnnotationsToMessage(\n        prefixMap['function_call'],\n        message_annotations,\n      );\n      toolCallMessage = assignAnnotationsToMessage(\n        prefixMap['tool_calls'],\n        message_annotations,\n      );\n      responseMessage = assignAnnotationsToMessage(\n        prefixMap['text'],\n        message_annotations,\n      );\n    }\n\n    // keeps the prefixMap up to date with the latest annotations, even if annotations preceded the message\n    if (message_annotations?.length) {\n      const messagePrefixKeys: (keyof PrefixMap)[] = [\n        'text',\n        'function_call',\n        'tool_calls',\n      ];\n      messagePrefixKeys.forEach(key => {\n        if (prefixMap[key]) {\n          (prefixMap[key] as Message).annotations = [...message_annotations!];\n        }\n      });\n    }\n\n    // We add function & tool calls and response messages to the messages[], but data is its own thing\n    const merged = [functionCallMessage, toolCallMessage, responseMessage]\n      .filter(Boolean)\n      .map(message => ({\n        ...assignAnnotationsToMessage(message, message_annotations),\n      })) as Message[];\n\n    update(merged, [...prefixMap['data']]); // make a copy of the data array\n  }\n\n  onFinish?.(prefixMap);\n\n  return {\n    messages: [\n      prefixMap.text,\n      prefixMap.function_call,\n      prefixMap.tool_calls,\n    ].filter(Boolean) as Message[],\n    data: prefixMap.data,\n  };\n}\n", "import { parseComplexResponse } from './parse-complex-response';\nimport {\n  FunctionCall,\n  IdGenerator,\n  JSONValue,\n  Message,\n  ToolCall,\n} from './types';\nimport { COMPLEX_HEADER, createChunkDecoder } from './utils';\n\nexport async function callChatApi({\n  api,\n  messages,\n  body,\n  credentials,\n  headers,\n  abortController,\n  appendMessage,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  generateId,\n}: {\n  api: string;\n  messages: Omit<Message, 'id'>[];\n  body: Record<string, any>;\n  credentials?: RequestCredentials;\n  headers?: HeadersInit;\n  abortController?: () => AbortController | null;\n  restoreMessagesOnFailure: () => void;\n  appendMessage: (message: Message) => void;\n  onResponse?: (response: Response) => void | Promise<void>;\n  onUpdate: (merged: Message[], data: JSONValue[] | undefined) => void;\n  onFinish?: (message: Message) => void;\n  generateId: IdGenerator;\n}) {\n  const response = await fetch(api, {\n    method: 'POST',\n    body: JSON.stringify({\n      messages,\n      ...body,\n    }),\n    headers: {\n      'Content-Type': 'application/json',\n      ...headers,\n    },\n    signal: abortController?.()?.signal,\n    credentials,\n  }).catch(err => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (await response.text()) || 'Failed to fetch the chat response.',\n    );\n  }\n\n  if (!response.body) {\n    throw new Error('The response body is empty.');\n  }\n\n  const reader = response.body.getReader();\n  const isComplexMode = response.headers.get(COMPLEX_HEADER) === 'true';\n\n  if (isComplexMode) {\n    return await parseComplexResponse({\n      reader,\n      abortControllerRef:\n        abortController != null ? { current: abortController() } : undefined,\n      update: onUpdate,\n      onFinish(prefixMap) {\n        if (onFinish && prefixMap.text != null) {\n          onFinish(prefixMap.text);\n        }\n      },\n      generateId,\n    });\n  } else {\n    const createdAt = new Date();\n    const decode = createChunkDecoder(false);\n\n    // TODO-STREAMDATA: Remove this once Stream Data is not experimental\n    let streamedResponse = '';\n    const replyId = generateId();\n    let responseMessage: Message = {\n      id: replyId,\n      createdAt,\n      content: '',\n      role: 'assistant',\n    };\n\n    // TODO-STREAMDATA: Remove this once Stream Data is not experimental\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        break;\n      }\n      // Update the chat state with the new message tokens.\n      streamedResponse += decode(value);\n\n      if (streamedResponse.startsWith('{\"function_call\":')) {\n        // While the function call is streaming, it will be a string.\n        responseMessage['function_call'] = streamedResponse;\n      } else if (streamedResponse.startsWith('{\"tool_calls\":')) {\n        // While the tool calls are streaming, it will be a string.\n        responseMessage['tool_calls'] = streamedResponse;\n      } else {\n        responseMessage['content'] = streamedResponse;\n      }\n\n      appendMessage({ ...responseMessage });\n\n      // The request has been aborted, stop reading the stream.\n      if (abortController?.() === null) {\n        reader.cancel();\n        break;\n      }\n    }\n\n    if (streamedResponse.startsWith('{\"function_call\":')) {\n      // Once the stream is complete, the function call is parsed into an object.\n      const parsedFunctionCall: FunctionCall =\n        JSON.parse(streamedResponse).function_call;\n\n      responseMessage['function_call'] = parsedFunctionCall;\n\n      appendMessage({ ...responseMessage });\n    }\n    if (streamedResponse.startsWith('{\"tool_calls\":')) {\n      // Once the stream is complete, the tool calls are parsed into an array.\n      const parsedToolCalls: ToolCall[] =\n        JSON.parse(streamedResponse).tool_calls;\n\n      responseMessage['tool_calls'] = parsedToolCalls;\n\n      appendMessage({ ...responseMessage });\n    }\n\n    if (onFinish) {\n      onFinish(responseMessage);\n    }\n\n    return responseMessage;\n  }\n}\n", "import {\n  ChatRe<PERSON>,\n  Function<PERSON>all,\n  JSONV<PERSON>ue,\n  Message,\n  ToolCall,\n} from './types';\n\nexport async function processChatStream({\n  getStreamedResponse,\n  experimental_onFunctionCall,\n  experimental_onToolCall,\n  updateChatRequest,\n  getCurrentMessages,\n}: {\n  getStreamedResponse: () => Promise<\n    Message | { messages: Message[]; data: JSONValue[] }\n  >;\n  experimental_onFunctionCall?: (\n    chatMessages: Message[],\n    functionCall: FunctionCall,\n  ) => Promise<void | ChatRequest>;\n  experimental_onToolCall?: (\n    chatMessages: Message[],\n    toolCalls: ToolCall[],\n  ) => Promise<void | ChatRequest>;\n  updateChatRequest: (chatRequest: ChatRequest) => void;\n  getCurrentMessages: () => Message[];\n}) {\n  while (true) {\n    // TODO-STREAMDATA: This should be {  const { messages: streamedResponseMessages, data } =\n    // await getStreamedResponse(} once Stream Data is not experimental\n    const messagesAndDataOrJustMessage = await getStreamedResponse();\n\n    // Using experimental stream data\n    if ('messages' in messagesAndDataOrJustMessage) {\n      let hasFollowingResponse = false;\n\n      for (const message of messagesAndDataOrJustMessage.messages) {\n        // See if the message has a complete function call or tool call\n        if (\n          (message.function_call === undefined ||\n            typeof message.function_call === 'string') &&\n          (message.tool_calls === undefined ||\n            typeof message.tool_calls === 'string')\n        ) {\n          continue;\n        }\n\n        hasFollowingResponse = true;\n        // Try to handle function call\n        if (experimental_onFunctionCall) {\n          const functionCall = message.function_call;\n          // Make sure functionCall is an object\n          // If not, we got tool calls instead of function calls\n          if (typeof functionCall !== 'object') {\n            console.warn(\n              'experimental_onFunctionCall should not be defined when using tools',\n            );\n            continue;\n          }\n\n          // User handles the function call in their own functionCallHandler.\n          // The \"arguments\" key of the function call object will still be a string which will have to be parsed in the function handler.\n          // If the \"arguments\" JSON is malformed due to model error the user will have to handle that themselves.\n\n          const functionCallResponse: ChatRequest | void =\n            await experimental_onFunctionCall(\n              getCurrentMessages(),\n              functionCall,\n            );\n\n          // If the user does not return anything as a result of the function call, the loop will break.\n          if (functionCallResponse === undefined) {\n            hasFollowingResponse = false;\n            break;\n          }\n\n          // A function call response was returned.\n          // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n          updateChatRequest(functionCallResponse);\n        }\n        // Try to handle tool call\n        if (experimental_onToolCall) {\n          const toolCalls = message.tool_calls;\n          // Make sure toolCalls is an array of objects\n          // If not, we got function calls instead of tool calls\n          if (\n            !Array.isArray(toolCalls) ||\n            toolCalls.some(toolCall => typeof toolCall !== 'object')\n          ) {\n            console.warn(\n              'experimental_onToolCall should not be defined when using tools',\n            );\n            continue;\n          }\n\n          // User handles the function call in their own functionCallHandler.\n          // The \"arguments\" key of the function call object will still be a string which will have to be parsed in the function handler.\n          // If the \"arguments\" JSON is malformed due to model error the user will have to handle that themselves.\n          const toolCallResponse: ChatRequest | void =\n            await experimental_onToolCall(getCurrentMessages(), toolCalls);\n\n          // If the user does not return anything as a result of the function call, the loop will break.\n          if (toolCallResponse === undefined) {\n            hasFollowingResponse = false;\n            break;\n          }\n\n          // A function call response was returned.\n          // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n          updateChatRequest(toolCallResponse);\n        }\n      }\n      if (!hasFollowingResponse) {\n        break;\n      }\n    } else {\n      const streamedResponseMessage = messagesAndDataOrJustMessage;\n\n      // TODO-STREAMDATA: Remove this once Stream Data is not experimental\n      if (\n        (streamedResponseMessage.function_call === undefined ||\n          typeof streamedResponseMessage.function_call === 'string') &&\n        (streamedResponseMessage.tool_calls === undefined ||\n          typeof streamedResponseMessage.tool_calls === 'string')\n      ) {\n        break;\n      }\n\n      // If we get here and are expecting a function call, the message should have one, if not warn and continue\n      if (experimental_onFunctionCall) {\n        const functionCall = streamedResponseMessage.function_call;\n        if (!(typeof functionCall === 'object')) {\n          console.warn(\n            'experimental_onFunctionCall should not be defined when using tools',\n          );\n          continue;\n        }\n        const functionCallResponse: ChatRequest | void =\n          await experimental_onFunctionCall(getCurrentMessages(), functionCall);\n\n        // If the user does not return anything as a result of the function call, the loop will break.\n        if (functionCallResponse === undefined) break;\n        // A function call response was returned.\n        // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n        fixFunctionCallArguments(functionCallResponse);\n        updateChatRequest(functionCallResponse);\n      }\n      // If we get here and are expecting a tool call, the message should have one, if not warn and continue\n      if (experimental_onToolCall) {\n        const toolCalls = streamedResponseMessage.tool_calls;\n        if (!(typeof toolCalls === 'object')) {\n          console.warn(\n            'experimental_onToolCall should not be defined when using functions',\n          );\n          continue;\n        }\n        const toolCallResponse: ChatRequest | void =\n          await experimental_onToolCall(getCurrentMessages(), toolCalls);\n\n        // If the user does not return anything as a result of the function call, the loop will break.\n        if (toolCallResponse === undefined) break;\n        // A function call response was returned.\n        // The updated chat with function call response will be sent to the API in the next iteration of the loop.\n        fixFunctionCallArguments(toolCallResponse);\n        updateChatRequest(toolCallResponse);\n      }\n\n      // Make sure function call arguments are sent back to the API as a string\n      function fixFunctionCallArguments(response: ChatRequest) {\n        for (const message of response.messages) {\n          if (message.tool_calls !== undefined) {\n            for (const toolCall of message.tool_calls) {\n              if (typeof toolCall === 'object') {\n                if (\n                  toolCall.function.arguments &&\n                  typeof toolCall.function.arguments !== 'string'\n                ) {\n                  toolCall.function.arguments = JSON.stringify(\n                    toolCall.function.arguments,\n                  );\n                }\n              }\n            }\n          }\n          if (message.function_call !== undefined) {\n            if (typeof message.function_call === 'object') {\n              if (\n                message.function_call.arguments &&\n                typeof message.function_call.arguments !== 'string'\n              ) {\n                message.function_call.arguments = JSON.stringify(\n                  message.function_call.arguments,\n                );\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "import { useSWR } from 'sswr';\nimport { Readable, Writable, derived, get, writable } from 'svelte/store';\nimport { callCompletionApi } from '../shared/call-completion-api';\nimport type {\n  JSONValue,\n  RequestOptions,\n  UseCompletionOptions,\n} from '../shared/types';\n\nexport type UseCompletionHelpers = {\n  /** The current completion result */\n  completion: Readable<string>;\n  /** The error object of the API request */\n  error: Readable<undefined | Error>;\n  /**\n   * Send a new prompt to the API endpoint and update the completion state.\n   */\n  complete: (\n    prompt: string,\n    options?: RequestOptions,\n  ) => Promise<string | null | undefined>;\n  /**\n   * Abort the current API request but keep the generated tokens.\n   */\n  stop: () => void;\n  /**\n   * Update the `completion` state locally.\n   */\n  setCompletion: (completion: string) => void;\n  /** The current value of the input */\n  input: Writable<string>;\n  /**\n   * Form submission handler to automatically reset input and append a user message\n   * @example\n   * ```jsx\n   * <form onSubmit={handleSubmit}>\n   *  <input onChange={handleInputChange} value={input} />\n   * </form>\n   * ```\n   */\n  handleSubmit: (e: any) => void;\n  /** Whether the API request is in progress */\n  isLoading: Readable<boolean | undefined>;\n\n  /** Additional data added on the server via StreamData */\n  data: Readable<JSONValue[] | undefined>;\n};\n\nlet uniqueId = 0;\n\nconst store: Record<string, any> = {};\n\nexport function useCompletion({\n  api = '/api/completion',\n  id,\n  initialCompletion = '',\n  initialInput = '',\n  credentials,\n  headers,\n  body,\n  onResponse,\n  onFinish,\n  onError,\n}: UseCompletionOptions = {}): UseCompletionHelpers {\n  // Generate an unique id for the completion if not provided.\n  const completionId = id || `completion-${uniqueId++}`;\n\n  const key = `${api}|${completionId}`;\n  const {\n    data,\n    mutate: originalMutate,\n    isLoading: isSWRLoading,\n  } = useSWR<string>(key, {\n    fetcher: () => store[key] || initialCompletion,\n    fallbackData: initialCompletion,\n  });\n\n  const streamData = writable<JSONValue[] | undefined>(undefined);\n\n  const loading = writable<boolean>(false);\n\n  // Force the `data` to be `initialCompletion` if it's `undefined`.\n  data.set(initialCompletion);\n\n  const mutate = (data: string) => {\n    store[key] = data;\n    return originalMutate(data);\n  };\n\n  // Because of the `fallbackData` option, the `data` will never be `undefined`.\n  const completion = data as Writable<string>;\n\n  const error = writable<undefined | Error>(undefined);\n\n  let abortController: AbortController | null = null;\n\n  const complete: UseCompletionHelpers['complete'] = async (\n    prompt: string,\n    options?: RequestOptions,\n  ) => {\n    const existingData = get(streamData);\n    return callCompletionApi({\n      api,\n      prompt,\n      credentials,\n      headers: {\n        ...headers,\n        ...options?.headers,\n      },\n      body: {\n        ...body,\n        ...options?.body,\n      },\n      setCompletion: mutate,\n      setLoading: loadingState => loading.set(loadingState),\n      setError: err => error.set(err),\n      setAbortController: controller => {\n        abortController = controller;\n      },\n      onResponse,\n      onFinish,\n      onError,\n      onData(data) {\n        streamData.set([...(existingData || []), ...(data || [])]);\n      },\n    });\n  };\n\n  const stop = () => {\n    if (abortController) {\n      abortController.abort();\n      abortController = null;\n    }\n  };\n\n  const setCompletion = (completion: string) => {\n    mutate(completion);\n  };\n\n  const input = writable(initialInput);\n\n  const handleSubmit = (e: any) => {\n    e.preventDefault();\n    const inputValue = get(input);\n    if (!inputValue) return;\n    return complete(inputValue);\n  };\n\n  const isLoading = derived(\n    [isSWRLoading, loading],\n    ([$isSWRLoading, $loading]) => {\n      return $isSWRLoading || $loading;\n    },\n  );\n\n  return {\n    completion,\n    complete,\n    error,\n    stop,\n    setCompletion,\n    input,\n    handleSubmit,\n    isLoading,\n    data: streamData,\n  };\n}\n", "import { readDataStream } from './read-data-stream';\nimport { JSONValue } from './types';\nimport { COMPLEX_HEADER, createChunkDecoder } from './utils';\n\nexport async function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n}: {\n  api: string;\n  prompt: string;\n  credentials?: RequestCredentials;\n  headers?: HeadersInit;\n  body: Record<string, any>;\n  setCompletion: (completion: string) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: Error | undefined) => void;\n  setAbortController: (abortController: AbortController | null) => void;\n  onResponse?: (response: Response) => void | Promise<void>;\n  onFinish?: (prompt: string, completion: string) => void;\n  onError?: (error: Error) => void;\n  onData?: (data: JSONValue[]) => void;\n}) {\n  try {\n    setLoading(true);\n    setError(undefined);\n\n    const abortController = new AbortController();\n    setAbortController(abortController);\n\n    // Empty the completion immediately.\n    setCompletion('');\n\n    const res = await fetch(api, {\n      method: 'POST',\n      body: JSON.stringify({\n        prompt,\n        ...body,\n      }),\n      credentials,\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      signal: abortController.signal,\n    }).catch(err => {\n      throw err;\n    });\n\n    if (onResponse) {\n      try {\n        await onResponse(res);\n      } catch (err) {\n        throw err;\n      }\n    }\n\n    if (!res.ok) {\n      throw new Error(\n        (await res.text()) || 'Failed to fetch the chat response.',\n      );\n    }\n\n    if (!res.body) {\n      throw new Error('The response body is empty.');\n    }\n\n    let result = '';\n    const reader = res.body.getReader();\n\n    const isComplexMode = res.headers.get(COMPLEX_HEADER) === 'true';\n\n    if (isComplexMode) {\n      for await (const { type, value } of readDataStream(reader, {\n        isAborted: () => abortController === null,\n      })) {\n        switch (type) {\n          case 'text': {\n            result += value;\n            setCompletion(result);\n            break;\n          }\n          case 'data': {\n            onData?.(value);\n            break;\n          }\n        }\n      }\n    } else {\n      const decoder = createChunkDecoder();\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n          break;\n        }\n\n        // Update the completion state with the new message tokens.\n        result += decoder(value);\n        setCompletion(result);\n\n        // The request has been aborted, stop reading the stream.\n        if (abortController === null) {\n          reader.cancel();\n          break;\n        }\n      }\n    }\n\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    // Ignore abort errors as they are expected.\n    if ((err as any).name === 'AbortError') {\n      setAbortController(null);\n      return null;\n    }\n\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n\n    setError(err as Error);\n  } finally {\n    setLoading(false);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AAEA,IAAI,iBAAiB,CAAC,UAAU,cAAc,OAAO;AACnD,SAAO,CAAC,OAAO,gBAAgB;AAC7B,QAAI,KAAK;AACT,QAAI,IAAI;AACR,WAAO,KAAK;AACV,YAAM,SAAU,KAAK,OAAO,IAAI,SAAS,SAAU,CAAC;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACF;;;ACXA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,EAAE,GAAG,GAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAC7G,IAAI,IAAI,CAAC,GAAG,GAAG,OAAO,EAAE,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC,GAAG;AAClE,IAAM,IAAN,MAAQ;EACN,cAAc;AAIZ,MAAE,MAAM,aAA6B,oBAAI,IAAI,CAAC;EAChD;;;;EAIA,UAAU,GAAG,GAAG;AACd,SAAK,UAAU,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,UAAU,IAAI,CAAC,EAAE,SAAS,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,EAAE,KAAK,CAAC;EACxH;;;;EAIA,YAAY,GAAG,GAAG;AAChB,SAAK,UAAU,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,EAAE,SAAS,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,EAAE,OAAO,KAAK,UAAU,IAAI,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,UAAU,IAAI,CAAC,EAAE,WAAW,KAAK,KAAK,UAAU,OAAO,CAAC;EACjM;;;;EAIA,KAAK,GAAG,GAAG;AACT,SAAK,UAAU,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;EACpE;AACF;AACA,IAAM,IAAI;EACR,WAAW;AACb;AAFA,IAEG,IAAI;EACL,WAAW;AACb;AACA,IAAM,IAAN,MAAQ;;;;EAIN,YAAY,EAAE,MAAM,GAAG,WAAW,IAAI,KAAK,GAAG;AAI5C,MAAE,MAAM,MAAM;AAId,MAAE,MAAM,WAAW;AACnB,SAAK,OAAO,GAAG,KAAK,YAAY;EAClC;;;;;EAKA,cAAc;AACZ,WAAO,KAAK,gBAAgB;EAC9B;;;;EAIA,aAAa;AACX,WAAO,KAAK,cAAc,QAAQ,KAAK,YAA4B,oBAAI,KAAK;EAC9E;;;;EAIA,UAAU,GAAG;AACX,WAAO,KAAK,YAA4B,oBAAI,KAAK,GAAG,KAAK,UAAU,gBAAgB,KAAK,UAAU,gBAAgB,IAAI,CAAC,GAAG;EAC5H;AACF;AACA,IAAM,IAAN,MAAQ;EACN,cAAc;AAIZ,MAAE,MAAM,YAA4B,oBAAI,IAAI,CAAC;AAI7C,MAAE,MAAM,SAAS,IAAI,EAAE,CAAC;EAC1B;;;;;;EAMA,QAAQ,GAAG,GAAG;AACZ,YAAQ,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM;AAClC,UAAI,KAAK;AACP,eAAO,KAAK,OAAO,CAAC;AACtB,QAAE,OAAO,GAAG,KAAK,UAAU,GAAG,CAAC;IACjC,CAAC;EACH;;;;;;;;EAQA,IAAI,GAAG;AACL,WAAO,KAAK,SAAS,IAAI,CAAC;EAC5B;;;;EAIA,IAAI,GAAG,GAAG;AACR,SAAK,SAAS,IAAI,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG,CAAC;EAC5C;;;;EAIA,OAAO,GAAG,GAAG;AACX,UAAM,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE;AACtC,SAAK,KAAK,UAAU,GAAG,MAAM,GAAG,KAAK,SAAS,OAAO,CAAC;EACxD;;;;EAIA,MAAM,GAAG;AACP,UAAM,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE;AACtC,QAAI;AACF,iBAAW,KAAK,KAAK,SAAS,KAAK;AACjC,aAAK,UAAU,GAAG,MAAM;AAC5B,SAAK,SAAS,MAAM;EACtB;;;;;EAKA,IAAI,GAAG;AACL,WAAO,KAAK,SAAS,IAAI,CAAC;EAC5B;;;;EAIA,UAAU,GAAG,GAAG;AACd,SAAK,MAAM,UAAU,GAAG,CAAC;EAC3B;;;;EAIA,YAAY,GAAG,GAAG;AAChB,SAAK,MAAM,YAAY,GAAG,CAAC;EAC7B;;;;EAIA,UAAU,GAAG,GAAG;AACd,SAAK,MAAM,KAAK,GAAG,CAAC;EACtB;AACF;AACA,IAAM,IAAI;EACR,OAAO,IAAI,EAAE;EACb,QAAQ,IAAI,EAAE;EACd,SAAS,OAAO,MAAM;AACpB,UAAM,IAAI,MAAM,MAAM,CAAC;AACvB,QAAI,CAAC,EAAE;AACL,YAAM,MAAM,qBAAqB;AACnC,WAAO,EAAE,KAAK;EAChB;EACA,cAAc;EACd,kBAAkB;EAClB,mBAAmB;EACnB,kBAAkB;EAClB,mBAAmB;EACnB,uBAAuB;EACvB,uBAAuB;EACvB,eAAe,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,KAAK,OAAO,SAAS,OAAO,OAAO,iBAAiB,UAAU,CAAC,GAAG,MAAM,OAAO,oBAAoB,UAAU,CAAC,KAAK,MAAM;EAC/J;EACA,WAAW,CAAC,GAAG,EAAE,SAAS,GAAG,kBAAkB,EAAE,MAAM;AACrD,QAAI,KAAK,OAAO,SAAS,KAAK;AAC5B,UAAI,IAAI;AACR,YAAM,IAAI,MAAM;AACd,cAAM,IAAI,KAAK,IAAI;AACnB,SAAC,MAAM,QAAQ,IAAI,IAAI,OAAO,IAAI,GAAG,EAAE;MACzC;AACA,aAAO,OAAO,iBAAiB,SAAS,CAAC,GAAG,MAAM,OAAO,oBAAoB,SAAS,CAAC;IACzF;AACA,WAAO,MAAM;IACb;EACF;EACA,oBAAoB;AACtB;AA/BA,IA+BG,IAAI;EACL,GAAG;EACH,OAAO;AACT;AAlCA,IAkCG,IAAI;EACL,YAAY;EACZ,mBAAmB,EAAE,GAAG,EAAE;EAC1B,oBAAoB;AACtB;AAtCA,IAsCG,IAAI;EACL,WAAW;AACb;AACA,IAAM,IAAN,MAAQ;;;;EAIN,YAAY,GAAG;AAIb,MAAE,MAAM,SAAS;AACjB,SAAK,UAAU,EAAE,GAAG,GAAG,GAAG,EAAE;EAC9B;;;;EAIA,IAAI,QAAQ;AACV,WAAO,KAAK,QAAQ;EACtB;;;;EAIA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ;EACtB;;;;EAIA,MAAM,YAAY,GAAG,GAAG;AACtB,WAAO,MAAM,QAAQ,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM;AAC9C,YAAM,KAAK,OAAO,KAAK,GAAG,CAAC,GAAG;IAChC,CAAC;EACH;;;;EAIA,WAAW,GAAG;AACZ,QAAI,OAAO,KAAK;AACd,UAAI;AACF,eAAO,EAAE;MACX,SAAQA,IAAN;AACA;MACF;AACF,WAAO;EACT;;;;;EAKA,MAAM,GAAG,GAAG;AACV,UAAM,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE;AACvB,QAAI,KAAK;AACP,aAAO,KAAK,MAAM,MAAM,CAAC;AAC3B,QAAI,CAAC,MAAM,QAAQ,CAAC;AAClB,aAAO,KAAK,MAAM,OAAO,GAAG,CAAC;AAC/B,eAAW,KAAK;AACd,WAAK,MAAM,OAAO,GAAG,CAAC;EAC1B;;;;EAIA,MAAM,WAAW,GAAG,GAAG;AACrB,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,gCAAgC;AAClD,UAAM,EAAE,SAAS,GAAG,kBAAkB,EAAE,IAAI,KAAK,SAAS,EAAE,OAAO,GAAG,SAAS,GAAG,kBAAkB,EAAE,IAAI;MACxG,GAAG;MACH,SAAS;MACT,kBAAkB;MAClB,GAAG;IACL;AACA,QAAI,KAAK,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,EAAE,WAAW,GAAG;AAClF,YAAMC,KAAI,KAAK,YAAY,GAAG,CAAC,GAAG,IAAIA,GAAE,MAAM,MAAM;MACpD,CAAC;AACD,aAAO,KAAK,MAAM,IAAI,GAAG,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,MAAMA;IACnE;AACA,WAAO,KAAK,QAAQ,CAAC;EACvB;;;;;;EAMA,MAAM,OAAO,GAAG,GAAG,GAAG;AAhRxB,QAAA;AAiRI,QAAI,CAAC;AACH,YAAM,IAAI,MAAM,4BAA4B;AAC9C,UAAM;MACJ,YAAY;MACZ,mBAAmB;MACnB,oBAAoB;IACtB,IAAI;MACF,GAAG;MACH,GAAG;IACL;AACA,QAAI;AACJ,QAAI,OAAO,KAAK,YAAY;AAC1B,UAAIA;AACJ,UAAI,KAAK,MAAM,IAAI,CAAC,GAAG;AACrB,cAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,UAAE,YAAY,MAAMA,KAAI,EAAE;MAC5B;AACA,UAAI,EAAEA,EAAC;IACT;AACE,UAAI;AACN,WAAO,KAAK,MAAM,IAAI,GAAG,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,IAAI,QAAQ,KAAA,KAAK,OAAO,SAAS,EAAE,GAAG,CAAC,MAA3B,OAAA,KAAiC,KAAK,WAAW,GAAG,CAAC,KAAK;EACtH;;;;;;EAMA,cAAc,GAAG,GAAG;AAClB,QAAI,GAAG;AACL,YAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,aAAO,KAAK,MAAM,UAAU,GAAG,CAAC,GAAG,MAAM,KAAK,MAAM,YAAY,GAAG,CAAC;IACtE;AACA,WAAO,MAAM;IACb;EACF;;;;EAIA,gBAAgB,GAAG,GAAG;AACpB,QAAI,GAAG;AACL,YAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,aAAO,KAAK,OAAO,UAAU,GAAG,CAAC,GAAG,MAAM,KAAK,OAAO,YAAY,GAAG,CAAC;IACxE;AACA,WAAO,MAAM;IACb;EACF;;;;;;;;;;EAUA,IAAI,GAAG;AACL,QAAI,KAAK,KAAK,MAAM,IAAI,CAAC,GAAG;AAC1B,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,UAAI,CAAC,EAAE,YAAY;AACjB,eAAO,EAAE;IACb;EACF;;;;;;;EAOA,QAAQ,GAAG;AACT,WAAO,IAAI,QAAQ,CAAC,GAAG,MAAM;AAC3B,YAAM,IAAI,KAAK,cAAc,GAAG,CAAC,MAAM;AACrC,YAAI,EAAE,GAAG,MAAM;AACb,iBAAO,EAAE,CAAC;MACd,CAAC,GAAG,IAAI,KAAK,gBAAgB,GAAG,CAAC,MAAM;AACrC,YAAI,EAAE,GAAG,MAAM;AACb,iBAAO,EAAE,CAAC;MACd,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC;AAClB,UAAI,MAAM;AACR,eAAO,EAAE,CAAC;IACd,CAAC;EACH;;;;;EAKA,UAAU,GAAG,GAAG,GAAG,GAAG;AACpB,UAAM;MACJ,SAAS;MACT,cAAc;MACd,kBAAkB;MAClB,mBAAmBA;MACnB,kBAAkB;MAClB,mBAAmBC;MACnB,uBAAuB;MACvB,uBAAuB;MACvB,eAAeC;MACf,WAAWC;MACX,oBAAoB;IACtB,IAAI;;;MAGF,GAAG,KAAK;;MAER,GAAG;IACL,GAAGC,KAAI,CAAC,MAAG;AAzXf,UAAA;AAyXmB,cAAA,KAAA,KAAK,OAAO,SAAS,EAAE,KAAK,WAAW,CAAC,GAAG,CAAC,MAA5C,OAAA,KAAkD,KAAK,WAAW,KAAK,WAAW,CAAC,GAAG,CAAC;IAAA,GAAG,IAAI,MAAMA,GAAE,EAAE,SAAS,GAAG,kBAAkB,EAAE,CAAC,GAAG,IAAI,IAAI,KAAK,IAAI,KAAK,WAAW,CAAC,CAAC,IAAI,KAAA,OAAA,IAAK,QAAQ,IAAIJ,KAAI,EAAE,IAAI,QAAQ,QAAQ,MAAM,GAAG,IAAI,IAAI,QAAQ,QAAQ,CAAC,IAAI;AACnR,UAAM,KAAK,QAAQ,EAAE,CAAC;AACtB,UAAMK,KAAI,IAAI,KAAK,cAAc,KAAK,WAAW,CAAC,GAAG,CAAC,IAAI,QAAQ,IAAI,IAAI,KAAK,gBAAgB,KAAK,WAAW,CAAC,GAAG,CAAC,IAAI,QAAQC,KAAIH,GAAE,GAAG;MACvI,kBAAkB;MAClB,SAASF;IACX,CAAC,GAAGM,KAAIL,GAAE,GAAG;MACX,SAAS;IACX,CAAC;AACD,WAAO,EAAE,aAAa,MAAM;AAC1BG,YAAK,QAAQA,GAAE,GAAG,KAAK,QAAQ,EAAE,GAAGC,MAAK,QAAQA,GAAE,GAAGC,MAAK,QAAQA,GAAE;IACvE,GAAG,aAAa,GAAG,mBAAmB,EAAE;EAC1C;AACF;ACnYA,SAAS,IAAI;AACb;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE;AACX;AACA,SAASC,GAAE,GAAG;AACZ,IAAE,QAAQ,CAAC;AACb;AACA,SAASC,GAAE,GAAG;AACZ,SAAO,OAAO,KAAK;AACrB;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK;AAC/E;AACA,SAAS,EAAE,MAAM,GAAG;AAClB,MAAI,KAAK,MAAM;AACb,eAAW,KAAK;AACd,QAAE,MAAM;AACV,WAAO;EACT;AACA,QAAM,IAAI,EAAE,UAAU,GAAG,CAAC;AAC1B,SAAO,EAAE,cAAc,MAAM,EAAE,YAAY,IAAI;AACjD;AACA,IAAM,IAAI,CAAC;AACX,SAAS,EAAE,GAAG,GAAG;AACf,SAAO;IACL,WAAW,EAAE,GAAG,CAAC,EAAE;EACrB;AACF;AACA,SAAS,EAAE,GAAG,IAAI,GAAG;AACnB,MAAI;AACJ,QAAM,IAAoB,oBAAI,IAAI;AAClC,WAAS,EAAE,GAAG;AACZ,QAAI,EAAE,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI;AACzB,YAAM,IAAI,CAAC,EAAE;AACb,iBAAW,KAAK;AACd,UAAE,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC;AACrB,UAAI,GAAG;AACL,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,YAAE,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAClB,UAAE,SAAS;MACb;IACF;EACF;AACA,WAAS,EAAE,GAAG;AACZ,MAAE,EAAE,CAAC,CAAC;EACR;AACA,WAAS,EAAE,GAAG,IAAI,GAAG;AACnB,UAAM,IAAI,CAAC,GAAG,CAAC;AACf,WAAO,EAAE,IAAI,CAAC,GAAG,EAAE,SAAS,MAAM,IAAI,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,MAAM;AAC/D,QAAE,OAAO,CAAC,GAAG,EAAE,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;IAC9C;EACF;AACA,SAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,WAAW,EAAE;AAC3C;AACA,SAASC,GAAE,GAAG,GAAG,GAAG;AAClB,QAAM,IAAI,CAAC,MAAM,QAAQ,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI;AAC3C,MAAI,CAAC,EAAE,MAAM,OAAO;AAClB,UAAM,IAAI,MAAM,sDAAsD;AACxE,QAAM,IAAI,EAAE,SAAS;AACrB,SAAO,EAAE,GAAG,CAAC,GAAG,MAAM;AACpB,QAAI,IAAI;AACR,UAAM,IAAI,CAAC;AACX,QAAIC,KAAI,GAAG,IAAI;AACf,UAAM,IAAI,MAAM;AACd,UAAIA;AACF;AACF,QAAE;AACF,YAAM,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC;AAC9B,UAAI,EAAE,CAAC,IAAI,IAAIF,GAAE,CAAC,IAAI,IAAI;IAC5B,GAAG,IAAI,EAAE;MACP,CAAC,GAAG,MAAM;QACR;QACA,CAAC,MAAM;AACL,YAAE,CAAC,IAAI,GAAGE,MAAK,EAAE,KAAK,IAAI,KAAK,EAAE;QACnC;QACA,MAAM;AACJA,gBAAK,KAAK;QACZ;MACF;IACF;AACA,WAAO,IAAI,MAAI,EAAE,GAAG,WAAW;AAC7BH,SAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACjB;EACF,CAAC;AACH;AACA,IAAMI,KAAN,cAAgB,EAAE;;;;EAIhB,OAAO,GAAG,GAAG;AACX,QAAI;AACJ,UAAM,IAAI,EAAE,QAAQ,MAAM,MAAM,KAAK,OAAO,SAAS,EAAE,CAAC,GAAG,IAAI,EAAE,QAAQ,MAAM,MAAM,KAAK,OAAO,SAAS,EAAE,CAAC;AAC7G,iBAAE,MAAM;AACN,YAAM,IAAI,CAAC,MAAM;AACf,UAAE,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC;MACxB,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC;AACrB,YAAM,IAAI,KAAK,UAAU,GAAG,GAAG,GAAG;QAChC,kBAAkB;QAClB,GAAG;MACL,CAAC,EAAE;IACL,CAAC,GAAGC,UAAE,MAAM,KAAK,OAAO,SAAS,EAAE,CAAC;AACpC,UAAM,IAAI,CAAC,GAAG,MAAM,KAAK,OAAO,KAAK,WAAW,CAAC,GAAG,GAAG;MACrD,mBAAmB;MACnB,GAAG;IACL,CAAC,GAAG,IAAI,CAAC,MAAM,KAAK,WAAW,KAAK,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,KAAK,MAAM,KAAK,WAAW,CAAC,GAAG,CAAC,GAAG,IAAIH,GAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,MAAM,UAAU,MAAM,MAAM,GAAGC,KAAID,GAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,MAAM,UAAU,MAAM,MAAM;AACjO,WAAO,EAAE,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,YAAY,GAAG,OAAO,GAAG,WAAW,GAAG,SAASC,GAAE;EAC3F;AACF;AACA,IAAM,IAAI,CAAC,MAAM,IAAIC,GAAE,CAAC;AACxB,IAAI,IAAI,EAAE;AACV,IAAuME,KAAI,CAAC,GAAG,MAAM,EAAE,OAAO,GAAG,CAAC;AElGlO,IAAM,iBAAkD;EACtD,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,qCAAqC;IACvD;AACA,WAAO,EAAE,MAAM,QAAQ,MAAM;EAC/B;AACF;AAEA,IAAM,yBAIF;EACF,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,mBAAmB,UACrB,OAAO,MAAM,kBAAkB,YAC/B,MAAM,iBAAiB,QACvB,EAAE,UAAU,MAAM,kBAClB,EAAE,eAAe,MAAM,kBACvB,OAAO,MAAM,cAAc,SAAS,YACpC,OAAO,MAAM,cAAc,cAAc,UACzC;AACA,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO;MACL,MAAM;MACN;IACF;EACF;AACF;AAEA,IAAM,iBAA4D;EAChE,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,MAAM,qCAAqC;IACvD;AAEA,WAAO,EAAE,MAAM,QAAQ,MAAM;EAC/B;AACF;AAEA,IAAM,kBAAoD;EACxD,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,sCAAsC;IACxD;AACA,WAAO,EAAE,MAAM,SAAS,MAAM;EAChC;AACF;AAEA,IAAM,6BAIF;EACF,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,QAAQ,UACV,EAAE,UAAU,UACZ,EAAE,aAAa,UACf,OAAO,MAAM,OAAO,YACpB,OAAO,MAAM,SAAS,YACtB,MAAM,SAAS,eACf,CAAC,MAAM,QAAQ,MAAM,OAAO,KAC5B,CAAC,MAAM,QAAQ;MACb,CAAA,SACE,QAAQ,QACR,OAAO,SAAS,YAChB,UAAU,QACV,KAAK,SAAS,UACd,UAAU,QACV,KAAK,QAAQ,QACb,OAAO,KAAK,SAAS,YACrB,WAAW,KAAK,QAChB,OAAO,KAAK,KAAK,UAAU;IAC/B,GACA;AACA,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO;MACL,MAAM;MACN;IACF;EACF;AACF;AAEA,IAAM,iCAOF;EACF,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,cAAc,UAChB,EAAE,eAAe,UACjB,OAAO,MAAM,aAAa,YAC1B,OAAO,MAAM,cAAc,UAC3B;AACA,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO;MACL,MAAM;MACN,OAAO;QACL,UAAU,MAAM;QAChB,WAAW,MAAM;MACnB;IACF;EACF;AACF;AAEA,IAAM,wBAAsE;EAC1E,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,UAAU,UACZ,EAAE,UAAU,UACZ,OAAO,MAAM,SAAS,YACtB,MAAM,SAAS,QACf;AACA,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO;MACL,MAAM;MACN;IACF;EACF;AACF;AAEA,IAAM,qBAIF;EACF,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,MAAM,cAAc,QACpB,CAAC,MAAM,QAAQ,MAAM,UAAU,KAC/B,MAAM,WAAW,KAAK,CAAA,OAAM;AAC1B,YAAM,QACJ,OAAO,OAAO,YACd,EAAE,QAAQ,OACV,OAAO,GAAG,OAAO,YACjB,EAAE,UAAU,OACZ,OAAO,GAAG,SAAS,YACnB,EAAE,cAAc,OAChB,GAAG,YAAY,QACf,OAAO,GAAG,aAAa,YACvB,EAAE,eAAe,GAAG,aACpB,OAAO,GAAG,SAAS,SAAS,YAC5B,OAAO,GAAG,SAAS,cAAc;IACrC,CAAC,GACD;AACA,YAAM,IAAI;QACR;MACF;IACF;AAEA,WAAO;MACL,MAAM;MACN;IACF;EACF;AACF;AAEA,IAAM,+BAIF;EACF,MAAM;EACN,MAAM;EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,MAAM,oDAAoD;IACtE;AAEA,WAAO,EAAE,MAAM,uBAAuB,MAAM;EAC9C;AACF;AAEA,IAAM,cAAc;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AA+BO,IAAM,oBAAoB;EAC/B,CAAC,eAAe,IAAI,GAAG;EACvB,CAAC,uBAAuB,IAAI,GAAG;EAC/B,CAAC,eAAe,IAAI,GAAG;EACvB,CAAC,gBAAgB,IAAI,GAAG;EACxB,CAAC,2BAA2B,IAAI,GAAG;EACnC,CAAC,+BAA+B,IAAI,GAAG;EACvC,CAAC,sBAAsB,IAAI,GAAG;EAC9B,CAAC,mBAAmB,IAAI,GAAG;EAC3B,CAAC,6BAA6B,IAAI,GAAG;AACvC;AAwBO,IAAM,uBAAuB;EAClC,CAAC,eAAe,IAAI,GAAG,eAAe;EACtC,CAAC,uBAAuB,IAAI,GAAG,uBAAuB;EACtD,CAAC,eAAe,IAAI,GAAG,eAAe;EACtC,CAAC,gBAAgB,IAAI,GAAG,gBAAgB;EACxC,CAAC,2BAA2B,IAAI,GAAG,2BAA2B;EAC9D,CAAC,+BAA+B,IAAI,GAAG,+BAA+B;EACtE,CAAC,sBAAsB,IAAI,GAAG,sBAAsB;EACpD,CAAC,mBAAmB,IAAI,GAAG,mBAAmB;EAC9C,CAAC,6BAA6B,IAAI,GAAG,6BAA6B;AACpE;AAEO,IAAM,aAAa,YAAY,IAAI,CAAA,SAAQ,KAAK,IAAI;AASpD,IAAM,kBAAkB,CAAC,SAAiC;AAC/D,QAAM,sBAAsB,KAAK,QAAQ,GAAG;AAE5C,MAAI,wBAAwB,IAAI;AAC9B,UAAM,IAAI,MAAM,oDAAoD;EACtE;AAEA,QAAM,SAAS,KAAK,MAAM,GAAG,mBAAmB;AAEhD,MAAI,CAAC,WAAW,SAAS,MAAwC,GAAG;AAClE,UAAM,IAAI,MAAM,+CAA+C,MAAA,GAAS;EAC1E;AAEA,QAAM,OAAO;AAEb,QAAM,YAAY,KAAK,MAAM,sBAAsB,CAAC;AACpD,QAAM,YAAuB,KAAK,MAAM,SAAS;AAEjD,SAAO,kBAAkB,IAAI,EAAE,MAAM,SAAS;AAChD;AChWA,IAAM,UAAU,KAAK,WAAW,CAAC;AAGjC,SAAS,aAAa,QAAsB,aAAqB;AAC/D,QAAM,qBAAqB,IAAI,WAAW,WAAW;AAErD,MAAI,SAAS;AACb,aAAW,SAAS,QAAQ;AAC1B,uBAAmB,IAAI,OAAO,MAAM;AACpC,cAAU,MAAM;EAClB;AACA,SAAO,SAAS;AAEhB,SAAO;AACT;AAEA,gBAAuB,eACrB,QACA;EACE;AACF,IAEI,CAAC,GAC2B;AAIhC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,SAAuB,CAAC;AAC9B,MAAI,cAAc;AAElB,SAAO,MAAM;AACX,UAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK;AAEpC,QAAI,OAAO;AACT,aAAO,KAAK,KAAK;AACjB,qBAAe,MAAM;AACrB,UAAI,MAAM,MAAM,SAAS,CAAC,MAAM,SAAS;AAEvC;MACF;IACF;AAEA,QAAI,OAAO,WAAW,GAAG;AACvB;IACF;AAEA,UAAM,qBAAqB,aAAa,QAAQ,WAAW;AAC3D,kBAAc;AAEd,UAAMC,eAAc,QACjB,OAAO,oBAAoB,EAAE,QAAQ,KAAK,CAAC,EAC3C,MAAM,IAAI,EACV,OAAO,CAAA,SAAQ,SAAS,EAAE,EAC1B,IAAI,eAAe;AAEtB,eAAW,cAAcA,cAAa;AACpC,YAAM;IACR;AAGA,QAAI,aAAA,OAAA,SAAA,UAAA,GAAe;AACjB,aAAO,OAAO;AACd;IACF;EACF;AACF;AC5DO,IAAM,SAAS;EACpB;EACA;AACF;AAeA,SAAS,mBAAmB,SAAmB;AAC7C,QAAM,UAAU,IAAI,YAAY;AAEhC,MAAI,CAAC,SAAS;AACZ,WAAO,SAAU,OAAuC;AACtD,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,QAAQ,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;IAC/C;EACF;AAEA,SAAO,SAAU,OAA+B;AAC9C,UAAM,UAAU,QACb,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,EAC9B,MAAM,IAAI,EACV,OAAO,CAAA,SAAQ,SAAS,EAAE;AAE7B,WAAO,QAAQ,IAAI,eAAe,EAAE,OAAO,OAAO;EACpD;AACF;AAgBO,IAAM,iBAAiB;AC3C9B,SAAS,2BACP,SACA,aACG;AACH,MAAI,CAAC,WAAW,CAAC,eAAe,CAAC,YAAY;AAAQ,WAAO;AAC5D,SAAO,EAAE,GAAG,SAAS,aAAa,CAAC,GAAG,WAAW,EAAE;AACrD;AAEA,eAAsB,qBAAqB;EACzC;EACA;EACA;EACA;EACA,aAAa;EACb,iBAAiB,MAAM,oBAAI,KAAK;AAClC,GASG;AACD,QAAM,YAAY,eAAe;AACjC,QAAM,YAAuB;IAC3B,MAAM,CAAC;EACT;AAGA,MAAI,sBAA+C;AAGnD,mBAAiB,EAAE,MAAM,MAAM,KAAK,eAAe,QAAQ;IACzD,WAAW,OAAM,sBAAA,OAAA,SAAA,mBAAoB,aAAY;EACnD,CAAC,GAAG;AACF,QAAI,SAAS,QAAQ;AACnB,UAAI,UAAU,MAAM,GAAG;AACrB,kBAAU,MAAM,IAAI;UAClB,GAAG,UAAU,MAAM;UACnB,UAAU,UAAU,MAAM,EAAE,WAAW,MAAM;QAC/C;MACF,OAAO;AACL,kBAAU,MAAM,IAAI;UAClB,IAAI,WAAW;UACf,MAAM;UACN,SAAS;UACT;QACF;MACF;IACF;AAEA,QAAI,sBAAkD;AAEtD,QAAI,SAAS,iBAAiB;AAC5B,gBAAU,eAAe,IAAI;QAC3B,IAAI,WAAW;QACf,MAAM;QACN,SAAS;QACT,eAAe,MAAM;QACrB,MAAM,MAAM,cAAc;QAC1B;MACF;AAEA,4BAAsB,UAAU,eAAe;IACjD;AAEA,QAAI,kBAA8C;AAElD,QAAI,SAAS,cAAc;AACzB,gBAAU,YAAY,IAAI;QACxB,IAAI,WAAW;QACf,MAAM;QACN,SAAS;QACT,YAAY,MAAM;QAClB;MACF;AAEA,wBAAkB,UAAU,YAAY;IAC1C;AAEA,QAAI,SAAS,QAAQ;AACnB,gBAAU,MAAM,EAAE,KAAK,GAAG,KAAK;IACjC;AAEA,QAAI,kBAAkB,UAAU,MAAM;AAEtC,QAAI,SAAS,uBAAuB;AAClC,UAAI,CAAC,qBAAqB;AACxB,8BAAsB,CAAC,GAAG,KAAK;MACjC,OAAO;AACL,4BAAoB,KAAK,GAAG,KAAK;MACnC;AAGA,4BAAsB;QACpB,UAAU,eAAe;QACzB;MACF;AACA,wBAAkB;QAChB,UAAU,YAAY;QACtB;MACF;AACA,wBAAkB;QAChB,UAAU,MAAM;QAChB;MACF;IACF;AAGA,QAAI,uBAAA,OAAA,SAAA,oBAAqB,QAAQ;AAC/B,YAAM,oBAAyC;QAC7C;QACA;QACA;MACF;AACA,wBAAkB,QAAQ,CAAA,QAAO;AAC/B,YAAI,UAAU,GAAG,GAAG;AACjB,oBAAU,GAAG,EAAc,cAAc,CAAC,GAAG,mBAAoB;QACpE;MACF,CAAC;IACH;AAGA,UAAM,SAAS,CAAC,qBAAqB,iBAAiB,eAAe,EAClE,OAAO,OAAO,EACd,IAAI,CAAA,aAAY;MACf,GAAG,2BAA2B,SAAS,mBAAmB;IAC5D,EAAE;AAEJ,WAAO,QAAQ,CAAC,GAAG,UAAU,MAAM,CAAC,CAAC;EACvC;AAEA,cAAA,OAAA,SAAA,SAAW,SAAA;AAEX,SAAO;IACL,UAAU;MACR,UAAU;MACV,UAAU;MACV,UAAU;IACZ,EAAE,OAAO,OAAO;IAChB,MAAM,UAAU;EAClB;AACF;ACvJA,eAAsB,YAAY;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,GAaG;AApCH,MAAA;AAqCE,QAAM,WAAW,MAAM,MAAM,KAAK;IAChC,QAAQ;IACR,MAAM,KAAK,UAAU;MACnB;MACA,GAAG;IACL,CAAC;IACD,SAAS;MACP,gBAAgB;MAChB,GAAG;IACL;IACA,SAAQ,KAAA,mBAAA,OAAA,SAAA,gBAAA,MAAA,OAAA,SAAA,GAAqB;IAC7B;EACF,CAAC,EAAE,MAAM,CAAA,QAAO;AACd,6BAAyB;AACzB,UAAM;EACR,CAAC;AAED,MAAI,YAAY;AACd,QAAI;AACF,YAAM,WAAW,QAAQ;IAC3B,SAAS,KAAP;AACA,YAAM;IACR;EACF;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,6BAAyB;AACzB,UAAM,IAAI;MACP,MAAM,SAAS,KAAK,KAAM;IAC7B;EACF;AAEA,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAI,MAAM,6BAA6B;EAC/C;AAEA,QAAM,SAAS,SAAS,KAAK,UAAU;AACvC,QAAM,gBAAgB,SAAS,QAAQ,IAAI,cAAc,MAAM;AAE/D,MAAI,eAAe;AACjB,WAAO,MAAM,qBAAqB;MAChC;MACA,oBACE,mBAAmB,OAAO,EAAE,SAAS,gBAAgB,EAAE,IAAI;MAC7D,QAAQ;MACR,SAAS,WAAW;AAClB,YAAI,YAAY,UAAU,QAAQ,MAAM;AACtC,mBAAS,UAAU,IAAI;QACzB;MACF;MACA;IACF,CAAC;EACH,OAAO;AACL,UAAM,YAAY,oBAAI,KAAK;AAC3B,UAAM,SAAS,mBAAmB,KAAK;AAGvC,QAAI,mBAAmB;AACvB,UAAM,UAAU,WAAW;AAC3B,QAAI,kBAA2B;MAC7B,IAAI;MACJ;MACA,SAAS;MACT,MAAM;IACR;AAGA,WAAO,MAAM;AACX,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,UAAI,MAAM;AACR;MACF;AAEA,0BAAoB,OAAO,KAAK;AAEhC,UAAI,iBAAiB,WAAW,mBAAmB,GAAG;AAEpD,wBAAgB,eAAe,IAAI;MACrC,WAAW,iBAAiB,WAAW,gBAAgB,GAAG;AAExD,wBAAgB,YAAY,IAAI;MAClC,OAAO;AACL,wBAAgB,SAAS,IAAI;MAC/B;AAEA,oBAAc,EAAE,GAAG,gBAAgB,CAAC;AAGpC,WAAI,mBAAA,OAAA,SAAA,gBAAA,OAAwB,MAAM;AAChC,eAAO,OAAO;AACd;MACF;IACF;AAEA,QAAI,iBAAiB,WAAW,mBAAmB,GAAG;AAEpD,YAAM,qBACJ,KAAK,MAAM,gBAAgB,EAAE;AAE/B,sBAAgB,eAAe,IAAI;AAEnC,oBAAc,EAAE,GAAG,gBAAgB,CAAC;IACtC;AACA,QAAI,iBAAiB,WAAW,gBAAgB,GAAG;AAEjD,YAAM,kBACJ,KAAK,MAAM,gBAAgB,EAAE;AAE/B,sBAAgB,YAAY,IAAI;AAEhC,oBAAc,EAAE,GAAG,gBAAgB,CAAC;IACtC;AAEA,QAAI,UAAU;AACZ,eAAS,eAAe;IAC1B;AAEA,WAAO;EACT;AACF;ACpJA,eAAsB,kBAAkB;EACtC,qBAAAC;EACA;EACA;EACA;EACA;AACF,GAcG;AACD,SAAO,MAAM;AAGX,UAAM,+BAA+B,MAAMA,qBAAoB;AAG/D,QAAI,cAAc,8BAA8B;AAC9C,UAAI,uBAAuB;AAE3B,iBAAW,WAAW,6BAA6B,UAAU;AAE3D,aACG,QAAQ,kBAAkB,UACzB,OAAO,QAAQ,kBAAkB,cAClC,QAAQ,eAAe,UACtB,OAAO,QAAQ,eAAe,WAChC;AACA;QACF;AAEA,+BAAuB;AAEvB,YAAI,6BAA6B;AAC/B,gBAAM,eAAe,QAAQ;AAG7B,cAAI,OAAO,iBAAiB,UAAU;AACpC,oBAAQ;cACN;YACF;AACA;UACF;AAMA,gBAAM,uBACJ,MAAM;YACJ,mBAAmB;YACnB;UACF;AAGF,cAAI,yBAAyB,QAAW;AACtC,mCAAuB;AACvB;UACF;AAIA,4BAAkB,oBAAoB;QACxC;AAEA,YAAI,yBAAyB;AAC3B,gBAAM,YAAY,QAAQ;AAG1B,cACE,CAAC,MAAM,QAAQ,SAAS,KACxB,UAAU,KAAK,CAAA,aAAY,OAAO,aAAa,QAAQ,GACvD;AACA,oBAAQ;cACN;YACF;AACA;UACF;AAKA,gBAAM,mBACJ,MAAM,wBAAwB,mBAAmB,GAAG,SAAS;AAG/D,cAAI,qBAAqB,QAAW;AAClC,mCAAuB;AACvB;UACF;AAIA,4BAAkB,gBAAgB;QACpC;MACF;AACA,UAAI,CAAC,sBAAsB;AACzB;MACF;IACF,OAAO;AAqDL,UAASC,4BAAT,SAAkC,UAAuB;AACvD,mBAAW,WAAW,SAAS,UAAU;AACvC,cAAI,QAAQ,eAAe,QAAW;AACpC,uBAAW,YAAY,QAAQ,YAAY;AACzC,kBAAI,OAAO,aAAa,UAAU;AAChC,oBACE,SAAS,SAAS,aAClB,OAAO,SAAS,SAAS,cAAc,UACvC;AACA,2BAAS,SAAS,YAAY,KAAK;oBACjC,SAAS,SAAS;kBACpB;gBACF;cACF;YACF;UACF;AACA,cAAI,QAAQ,kBAAkB,QAAW;AACvC,gBAAI,OAAO,QAAQ,kBAAkB,UAAU;AAC7C,kBACE,QAAQ,cAAc,aACtB,OAAO,QAAQ,cAAc,cAAc,UAC3C;AACA,wBAAQ,cAAc,YAAY,KAAK;kBACrC,QAAQ,cAAc;gBACxB;cACF;YACF;UACF;QACF;MACF;AA7BS,UAAA,2BAAAA;AApDT,YAAM,0BAA0B;AAGhC,WACG,wBAAwB,kBAAkB,UACzC,OAAO,wBAAwB,kBAAkB,cAClD,wBAAwB,eAAe,UACtC,OAAO,wBAAwB,eAAe,WAChD;AACA;MACF;AAGA,UAAI,6BAA6B;AAC/B,cAAM,eAAe,wBAAwB;AAC7C,YAAI,EAAE,OAAO,iBAAiB,WAAW;AACvC,kBAAQ;YACN;UACF;AACA;QACF;AACA,cAAM,uBACJ,MAAM,4BAA4B,mBAAmB,GAAG,YAAY;AAGtE,YAAI,yBAAyB;AAAW;AAGxCA,kCAAyB,oBAAoB;AAC7C,0BAAkB,oBAAoB;MACxC;AAEA,UAAI,yBAAyB;AAC3B,cAAM,YAAY,wBAAwB;AAC1C,YAAI,EAAE,OAAO,cAAc,WAAW;AACpC,kBAAQ;YACN;UACF;AACA;QACF;AACA,cAAM,mBACJ,MAAM,wBAAwB,mBAAmB,GAAG,SAAS;AAG/D,YAAI,qBAAqB;AAAW;AAGpCA,kCAAyB,gBAAgB;AACzC,0BAAkB,gBAAgB;MACpC;IAiCF;EACF;AACF;AN9IA,IAAM,sBAAsB,OAC1B,KACA,aACA,QACA,kBACA,cACA,eAKA,kBACA,oBACA,YACA,UACA,YACA,2BACG;AA7EL,MAAA,IAAA;AAgFE,SAAO,YAAY,QAAQ;AAE3B,QAAM,6BAA6B,yBAC/B,YAAY,WACZ,YAAY,SAAS;IACnB,CAAC,EAAE,MAAM,SAAS,MAAM,eAAe,YAAY,aAAa,OAAO;MACrE;MACA;MACA;MACA,GAAI,SAAS,UAAa,EAAE,KAAK;MACjC,GAAI,kBAAkB,UAAa;QACjC;MACF;MACA,GAAI,eAAe,UAAa;QAC9B;MACF;IACF;EACF;AAEJ,SAAO,MAAM,YAAY;IACvB;IACA,UAAU;IACV,MAAM;MACJ,GAAG,cAAc;MACjB,IAAG,KAAA,YAAY,YAAZ,OAAA,SAAA,GAAqB;MACxB,GAAI,YAAY,cAAc,UAAa;QACzC,WAAW,YAAY;MACzB;MACA,GAAI,YAAY,kBAAkB,UAAa;QAC7C,eAAe,YAAY;MAC7B;MACA,GAAI,YAAY,UAAU,UAAa;QACrC,OAAO,YAAY;MACrB;MACA,GAAI,YAAY,gBAAgB,UAAa;QAC3C,aAAa,YAAY;MAC3B;IACF;IACA,aAAa,cAAc;IAC3B,SAAS;MACP,GAAG,cAAc;MACjB,IAAG,KAAA,YAAY,YAAZ,OAAA,SAAA,GAAqB;IAC1B;IACA,iBAAiB,MAAM;IACvB,cAAc,SAAS;AACrB,aAAO,CAAC,GAAG,YAAY,UAAU,OAAO,CAAC;IAC3C;IACA,2BAA2B;AACzB,aAAO,gBAAgB;IACzB;IACA;IACA,SAAS,QAAQ,MAAM;AACrB,aAAO,CAAC,GAAG,YAAY,UAAU,GAAG,MAAM,CAAC;AAC3C,uBAAiB,CAAC,GAAI,gBAAgB,CAAC,GAAI,GAAI,QAAQ,CAAC,CAAE,CAAC;IAC7D;IACA;IACA;EACF,CAAC;AACH;AAEA,IAAI,WAAW;AAEf,IAAM,QAA+C,CAAC;AAE/C,SAAS,QAAQ;EACtB,MAAM;EACN;EACA,kBAAkB,CAAC;EACnB,eAAe;EACf;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,aAAa;AACf,IAAoB,CAAC,GAAmB;AAEtC,QAAM,SAAS,MAAM,QAAQ,UAAA;AAE7B,QAAM,MAAM,GAAG,GAAA,IAAO,MAAA;AACtB,QAAM;IACJ;IACA,QAAQ;IACR,WAAW;EACb,IAAIH,GAAkB,KAAK;IACzB,SAAS,MAAM,MAAM,GAAG,KAAK;IAC7B,cAAc;EAChB,CAAC;AAED,QAAM,aAAa,SAAkC,MAAS;AAE9D,QAAM,UAAU,SAAkB,KAAK;AAGvC,OAAK,IAAI,eAAe;AAExB,QAAM,SAAS,CAACI,UAAoB;AAClC,UAAM,GAAG,IAAIA;AACb,WAAO,eAAeA,KAAI;EAC5B;AAGA,QAAM,WAAW;AAGjB,MAAI,kBAA0C;AAE9C,QAAM,gBAAgB;IACpB;IACA;IACA;EACF;AAEA,QAAM,QAAQ,SAA4B,MAAS;AAInD,iBAAe,eAAe,aAA0B;AACtD,QAAI;AACF,YAAM,IAAI,MAAS;AACnB,cAAQ,IAAI,IAAI;AAChB,wBAAkB,IAAI,gBAAgB;AAEtC,YAAM,kBAAkB;QACtB,qBAAqB,MACnB;UACE;UACA;UACA;UACA,CAAAA,UAAQ;AACN,uBAAW,IAAIA,KAAI;UACrB;UACA,gBAAI,UAAU;UACd;UACA,gBAAI,QAAQ;UACZ;UACA;UACA;UACA;UACA;QACF;QACF;QACA;QACA,mBAAmB,CAAA,qBAAoB;AACrC,wBAAc;QAChB;QACA,oBAAoB,MAAM,gBAAI,QAAQ;MACxC,CAAC;AAED,wBAAkB;AAElB,aAAO;IACT,SAAS,KAAP;AAEA,UAAK,IAAY,SAAS,cAAc;AACtC,0BAAkB;AAClB,eAAO;MACT;AAEA,UAAI,WAAW,eAAe,OAAO;AACnC,gBAAQ,GAAG;MACb;AAEA,YAAM,IAAI,GAAY;IACxB,UAAA;AACE,cAAQ,IAAI,KAAK;IACnB;EACF;AAEA,QAAM,SAAmC,OACvC,SACA;IACE;IACA;IACA;IACA;IACA;EACF,IAAwB,CAAC,MACtB;AACH,QAAI,CAAC,QAAQ,IAAI;AACf,cAAQ,KAAK,WAAW;IAC1B;AAEA,UAAM,cAA2B;MAC/B,UAAU,gBAAI,QAAQ,EAAE,OAAO,OAAkB;MACjD;MACA,GAAI,cAAc,UAAa,EAAE,UAAU;MAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;MACnD,GAAI,UAAU,UAAa,EAAE,MAAM;MACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;IACjD;AACA,WAAO,eAAe,WAAW;EACnC;AAEA,QAAM,SAAmC,OAAO;IAC9C;IACA;IACA;IACA;IACA;EACF,IAAwB,CAAC,MAAM;AAC7B,UAAM,mBAAmB,gBAAI,QAAQ;AACrC,QAAI,iBAAiB,WAAW;AAAG,aAAO;AAG1C,UAAM,cAAc,iBAAiB,GAAG,EAAE;AAC1C,SAAI,eAAA,OAAA,SAAA,YAAa,UAAS,aAAa;AACrC,YAAMC,eAA2B;QAC/B,UAAU,iBAAiB,MAAM,GAAG,EAAE;QACtC;QACA,GAAI,cAAc,UAAa,EAAE,UAAU;QAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;QACnD,GAAI,UAAU,UAAa,EAAE,MAAM;QACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;MACjD;AAEA,aAAO,eAAeA,YAAW;IACnC;AACA,UAAM,cAA2B;MAC/B,UAAU;MACV;MACA,GAAI,cAAc,UAAa,EAAE,UAAU;MAC3C,GAAI,kBAAkB,UAAa,EAAE,cAAc;MACnD,GAAI,UAAU,UAAa,EAAE,MAAM;MACnC,GAAI,gBAAgB,UAAa,EAAE,YAAY;IACjD;AAEA,WAAO,eAAe,WAAW;EACnC;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;IACpB;EACF;AAEA,QAAM,cAAc,CAACC,cAAwB;AAC3C,WAAOA,SAAQ;EACjB;AAEA,QAAM,QAAQ,SAAS,YAAY;AAEnC,QAAM,eAAe,CAAC,GAAQ,UAA8B,CAAC,MAAM;AACjE,MAAE,eAAe;AACjB,UAAM,aAAa,gBAAI,KAAK;AAC5B,QAAI,CAAC;AAAY;AAEjB;MACE;QACE,SAAS;QACT,MAAM;QACN,WAAW,oBAAI,KAAK;MACtB;MACA;IACF;AACA,UAAM,IAAI,EAAE;EACd;AAEA,QAAM,YAAY;IAChB,CAAC,cAAc,OAAO;IACtB,CAAC,CAAC,eAAe,QAAQ,MAAM;AAC7B,aAAO,iBAAiB;IAC1B;EACF;AAEA,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM;EACR;AACF;AQtWA,eAAsB,kBAAkB;EACtC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,GAcG;AACD,MAAI;AACF,eAAW,IAAI;AACf,aAAS,MAAS;AAElB,UAAM,kBAAkB,IAAI,gBAAgB;AAC5C,uBAAmB,eAAe;AAGlC,kBAAc,EAAE;AAEhB,UAAM,MAAM,MAAM,MAAM,KAAK;MAC3B,QAAQ;MACR,MAAM,KAAK,UAAU;QACnB;QACA,GAAG;MACL,CAAC;MACD;MACA,SAAS;QACP,gBAAgB;QAChB,GAAG;MACL;MACA,QAAQ,gBAAgB;IAC1B,CAAC,EAAE,MAAM,CAAA,QAAO;AACd,YAAM;IACR,CAAC;AAED,QAAI,YAAY;AACd,UAAI;AACF,cAAM,WAAW,GAAG;MACtB,SAAS,KAAP;AACA,cAAM;MACR;IACF;AAEA,QAAI,CAAC,IAAI,IAAI;AACX,YAAM,IAAI;QACP,MAAM,IAAI,KAAK,KAAM;MACxB;IACF;AAEA,QAAI,CAAC,IAAI,MAAM;AACb,YAAM,IAAI,MAAM,6BAA6B;IAC/C;AAEA,QAAI,SAAS;AACb,UAAM,SAAS,IAAI,KAAK,UAAU;AAElC,UAAM,gBAAgB,IAAI,QAAQ,IAAI,cAAc,MAAM;AAE1D,QAAI,eAAe;AACjB,uBAAiB,EAAE,MAAM,MAAM,KAAK,eAAe,QAAQ;QACzD,WAAW,MAAM,oBAAoB;MACvC,CAAC,GAAG;AACF,gBAAQ,MAAM;UACZ,KAAK,QAAQ;AACX,sBAAU;AACV,0BAAc,MAAM;AACpB;UACF;UACA,KAAK,QAAQ;AACX,sBAAA,OAAA,SAAA,OAAS,KAAA;AACT;UACF;QACF;MACF;IACF,OAAO;AACL,YAAM,UAAU,mBAAmB;AAEnC,aAAO,MAAM;AACX,cAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,YAAI,MAAM;AACR;QACF;AAGA,kBAAU,QAAQ,KAAK;AACvB,sBAAc,MAAM;AAGpB,YAAI,oBAAoB,MAAM;AAC5B,iBAAO,OAAO;AACd;QACF;MACF;IACF;AAEA,QAAI,UAAU;AACZ,eAAS,QAAQ,MAAM;IACzB;AAEA,uBAAmB,IAAI;AACvB,WAAO;EACT,SAAS,KAAP;AAEA,QAAK,IAAY,SAAS,cAAc;AACtC,yBAAmB,IAAI;AACvB,aAAO;IACT;AAEA,QAAI,eAAe,OAAO;AACxB,UAAI,SAAS;AACX,gBAAQ,GAAG;MACb;IACF;AAEA,aAAS,GAAY;EACvB,UAAA;AACE,eAAW,KAAK;EAClB;AACF;AD9FA,IAAIC,YAAW;AAEf,IAAMC,SAA6B,CAAC;AAE7B,SAAS,cAAc;EAC5B,MAAM;EACN;EACA,oBAAoB;EACpB,eAAe;EACf;EACA;EACA;EACA;EACA;EACA;AACF,IAA0B,CAAC,GAAyB;AAElD,QAAM,eAAe,MAAM,cAAcD,WAAAA;AAEzC,QAAM,MAAM,GAAG,GAAA,IAAO,YAAA;AACtB,QAAM;IACJ;IACA,QAAQ;IACR,WAAW;EACb,IAAIP,GAAe,KAAK;IACtB,SAAS,MAAMQ,OAAM,GAAG,KAAK;IAC7B,cAAc;EAChB,CAAC;AAED,QAAM,aAAaC,SAAkC,MAAS;AAE9D,QAAM,UAAUA,SAAkB,KAAK;AAGvC,OAAK,IAAI,iBAAiB;AAE1B,QAAM,SAAS,CAACL,UAAiB;AAC/BI,WAAM,GAAG,IAAIJ;AACb,WAAO,eAAeA,KAAI;EAC5B;AAGA,QAAM,aAAa;AAEnB,QAAM,QAAQK,SAA4B,MAAS;AAEnD,MAAI,kBAA0C;AAE9C,QAAM,WAA6C,OACjD,QACA,YACG;AACH,UAAM,eAAeC,gBAAI,UAAU;AACnC,WAAO,kBAAkB;MACvB;MACA;MACA;MACA,SAAS;QACP,GAAG;QACH,GAAG,WAAA,OAAA,SAAA,QAAS;MACd;MACA,MAAM;QACJ,GAAG;QACH,GAAG,WAAA,OAAA,SAAA,QAAS;MACd;MACA,eAAe;MACf,YAAY,CAAA,iBAAgB,QAAQ,IAAI,YAAY;MACpD,UAAU,CAAA,QAAO,MAAM,IAAI,GAAG;MAC9B,oBAAoB,CAAA,eAAc;AAChC,0BAAkB;MACpB;MACA;MACA;MACA;MACA,OAAON,OAAM;AACX,mBAAW,IAAI,CAAC,GAAI,gBAAgB,CAAC,GAAI,GAAIA,SAAQ,CAAC,CAAE,CAAC;MAC3D;IACF,CAAC;EACH;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,iBAAiB;AACnB,sBAAgB,MAAM;AACtB,wBAAkB;IACpB;EACF;AAEA,QAAM,gBAAgB,CAACO,gBAAuB;AAC5C,WAAOA,WAAU;EACnB;AAEA,QAAM,QAAQF,SAAS,YAAY;AAEnC,QAAM,eAAe,CAAC,MAAW;AAC/B,MAAE,eAAe;AACjB,UAAM,aAAaC,gBAAI,KAAK;AAC5B,QAAI,CAAC;AAAY;AACjB,WAAO,SAAS,UAAU;EAC5B;AAEA,QAAM,YAAYE;IAChB,CAAC,cAAc,OAAO;IACtB,CAAC,CAAC,eAAe,QAAQ,MAAM;AAC7B,aAAO,iBAAiB;IAC1B;EACF;AAEA,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM;EACR;AACF;", "names": ["e", "c", "A", "W", "D", "K", "v", "p", "w", "q", "x", "S", "h", "O", "E", "F", "streamParts", "getStreamedResponse", "fixFunctionCallArguments", "data", "chatRequest", "messages", "uniqueId", "store", "writable", "get", "completion", "derived"]}