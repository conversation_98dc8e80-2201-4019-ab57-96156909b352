{"version": 3, "sources": ["../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/guard.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/ColorError.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/parseToRgba.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/parseToHsla.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/hsla.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/adjustHue.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/darken.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/desaturate.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/getLuminance.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/getContrast.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/rgba.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/mix.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/getScale.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/hasBadContrast.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/lighten.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/transparentize.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/opacify.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/readableColorIsBlack.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/readableColor.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/saturate.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/toHex.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/toRgba.ts", "../../../../../node_modules/.pnpm/color2k@2.0.3/node_modules/color2k/src/toHsla.ts"], "sourcesContent": ["/**\n * A simple guard function:\n *\n * ```js\n * Math.min(Math.max(low, value), high)\n * ```\n */\nfunction guard(low: number, high: number, value: number): number {\n  return Math.min(Math.max(low, value), high);\n}\n\nexport default guard;\n", "class ColorError extends Error {\n  constructor(color: string) {\n    super(`Failed to parse color: \"${color}\"`);\n  }\n}\n\nexport default ColorError;\n", "import guard from './guard';\nimport ColorError from './ColorError';\n\n/**\n * Parses a color into red, gree, blue, alpha parts\n *\n * @param color the input color. Can be a RGB, RBGA, HSL, HSLA, or named color\n */\nfunction parseToRgba(color: string): [number, number, number, number] {\n  if (typeof color !== 'string') throw new ColorError(color);\n  if (color.trim().toLowerCase() === 'transparent') return [0, 0, 0, 0];\n\n  let normalizedColor = color.trim();\n  normalizedColor = namedColorRegex.test(color) ? nameToHex(color) : color;\n\n  const reducedHexMatch = reducedHexRegex.exec(normalizedColor);\n  if (reducedHexMatch) {\n    const arr = Array.from(reducedHexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(r(x, 2), 16)),\n      parseInt(r(arr[3] || 'f', 2), 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const hexMatch = hexRegex.exec(normalizedColor);\n  if (hexMatch) {\n    const arr = Array.from(hexMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 16)),\n      parseInt(arr[3] || 'ff', 16) / 255,\n    ] as [number, number, number, number];\n  }\n\n  const rgbaMatch = rgbaRegex.exec(normalizedColor);\n  if (rgbaMatch) {\n    const arr = Array.from(rgbaMatch).slice(1);\n    return [\n      ...arr.slice(0, 3).map((x) => parseInt(x, 10)),\n      parseFloat(arr[3] || '1'),\n    ] as [number, number, number, number];\n  }\n\n  const hslaMatch = hslaRegex.exec(normalizedColor);\n  if (hslaMatch) {\n    const [h, s, l, a] = Array.from(hslaMatch).slice(1).map(parseFloat);\n    if (guard(0, 100, s) !== s) throw new ColorError(color);\n    if (guard(0, 100, l) !== l) throw new ColorError(color);\n    return [...hslToRgb(h, s, l), Number.isNaN(a) ? 1 : a] as [\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n\n  throw new ColorError(color);\n}\n\nfunction hash(str: string) {\n  let hash = 5381;\n  let i = str.length;\n\n  while (i) {\n    hash = (hash * 33) ^ str.charCodeAt(--i);\n  }\n\n  /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */\n  return (hash >>> 0) % 2341;\n}\n\nconst colorToInt = (x: string) => parseInt(x.replace(/_/g, ''), 36);\n\nconst compressedColorMap =\n  '1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm'\n    .split(' ')\n    .reduce((acc, next) => {\n      const key = colorToInt(next.substring(0, 3));\n      const hex = colorToInt(next.substring(3)).toString(16);\n\n      // NOTE: padStart could be used here but it breaks Node 6 compat\n      // https://github.com/ricokahler/color2k/issues/351\n      let prefix = '';\n      for (let i = 0; i < 6 - hex.length; i++) {\n        prefix += '0';\n      }\n\n      acc[key] = `${prefix}${hex}`;\n      return acc;\n    }, {} as { [key: string]: string });\n\n/**\n * Checks if a string is a CSS named color and returns its equivalent hex value, otherwise returns the original color.\n */\nfunction nameToHex(color: string): string {\n  const normalizedColorName = color.toLowerCase().trim();\n  const result = compressedColorMap[hash(normalizedColorName)];\n  if (!result) throw new ColorError(color);\n  return `#${result}`;\n}\n\nconst r = (str: string, amount: number) =>\n  Array.from(Array(amount))\n    .map(() => str)\n    .join('');\n\nconst reducedHexRegex = new RegExp(`^#${r('([a-f0-9])', 3)}([a-f0-9])?$`, 'i');\nconst hexRegex = new RegExp(`^#${r('([a-f0-9]{2})', 3)}([a-f0-9]{2})?$`, 'i');\nconst rgbaRegex = new RegExp(\n  `^rgba?\\\\(\\\\s*(\\\\d+)\\\\s*${r(\n    ',\\\\s*(\\\\d+)\\\\s*',\n    2\n  )}(?:,\\\\s*([\\\\d.]+))?\\\\s*\\\\)$`,\n  'i'\n);\nconst hslaRegex =\n  /^hsla?\\(\\s*([\\d.]+)\\s*,\\s*([\\d.]+)%\\s*,\\s*([\\d.]+)%(?:\\s*,\\s*([\\d.]+))?\\s*\\)$/i;\nconst namedColorRegex = /^[a-z]+$/i;\n\nconst roundColor = (color: number): number => {\n  return Math.round(color * 255);\n};\n\nconst hslToRgb = (\n  hue: number,\n  saturation: number,\n  lightness: number\n): [number, number, number] => {\n  let l = lightness / 100;\n  if (saturation === 0) {\n    // achromatic\n    return [l, l, l].map(roundColor) as [number, number, number];\n  }\n\n  // formulae from https://en.wikipedia.org/wiki/HSL_and_HSV\n  const huePrime = (((hue % 360) + 360) % 360) / 60;\n  const chroma = (1 - Math.abs(2 * l - 1)) * (saturation / 100);\n  const secondComponent = chroma * (1 - Math.abs((huePrime % 2) - 1));\n\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n\n  if (huePrime >= 0 && huePrime < 1) {\n    red = chroma;\n    green = secondComponent;\n  } else if (huePrime >= 1 && huePrime < 2) {\n    red = secondComponent;\n    green = chroma;\n  } else if (huePrime >= 2 && huePrime < 3) {\n    green = chroma;\n    blue = secondComponent;\n  } else if (huePrime >= 3 && huePrime < 4) {\n    green = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 4 && huePrime < 5) {\n    red = secondComponent;\n    blue = chroma;\n  } else if (huePrime >= 5 && huePrime < 6) {\n    red = chroma;\n    blue = secondComponent;\n  }\n\n  const lightnessModification = l - chroma / 2;\n  const finalRed = red + lightnessModification;\n  const finalGreen = green + lightnessModification;\n  const finalBlue = blue + lightnessModification;\n\n  return [finalRed, finalGreen, finalBlue].map(roundColor) as [\n    number,\n    number,\n    number\n  ];\n};\n\nexport default parseToRgba;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/a23a6a2bb26802b3d922d9c3b67bac3f3a54a310/src/internalHelpers/_rgbToHsl.js\nimport parseToRgba from './parseToRgba';\n\n/**\n * Parses a color in hue, saturation, lightness, and the alpha channel.\n *\n * Hue is a number between 0 and 360, saturation, lightness, and alpha are\n * decimal percentages between 0 and 1\n */\nfunction parseToHsla(color: string): [number, number, number, number] {\n  const [red, green, blue, alpha] = parseToRgba(color).map((value, index) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? value : value / 255\n  );\n\n  const max = Math.max(red, green, blue);\n  const min = Math.min(red, green, blue);\n  const lightness = (max + min) / 2;\n\n  // achromatic\n  if (max === min) return [0, 0, lightness, alpha];\n\n  const delta = max - min;\n  const saturation =\n    lightness > 0.5 ? delta / (2 - max - min) : delta / (max + min);\n\n  const hue =\n    60 *\n    (red === max\n      ? (green - blue) / delta + (green < blue ? 6 : 0)\n      : green === max\n      ? (blue - red) / delta + 2\n      : (red - green) / delta + 4);\n\n  return [hue, saturation, lightness, alpha];\n}\n\nexport default parseToHsla;\n", "import guard from './guard';\n\n/**\n * Takes in hsla parts and constructs an hsla string\n *\n * @param hue The color circle (from 0 to 360) - 0 (or 360) is red, 120 is green, 240 is blue\n * @param saturation Percentage of saturation, given as a decimal between 0 and 1\n * @param lightness Percentage of lightness, given as a decimal between 0 and 1\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction hsla(\n  hue: number,\n  saturation: number,\n  lightness: number,\n  alpha: number\n): string {\n  return `hsla(${(hue % 360).toFixed()}, ${guard(\n    0,\n    100,\n    saturation * 100\n  ).toFixed()}%, ${guard(0, 100, lightness * 100).toFixed()}%, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default hsla;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Adjusts the current hue of the color by the given degrees. Wraps around when\n * over 360.\n *\n * @param color input color\n * @param degrees degrees to adjust the input color, accepts degree integers\n * (0 - 360) and wraps around on overflow\n */\nfunction adjustHue(color: string, degrees: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h + degrees, s, l, a);\n}\n\nexport default adjustHue;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Darkens using lightness. This is equivalent to subtracting the lightness\n * from the L in HSL.\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction darken(color: string, amount: number): string {\n  const [hue, saturation, lightness, alpha] = parseToHsla(color);\n  return hsla(hue, saturation, lightness - amount, alpha);\n}\n\nexport default darken;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Desaturates the input color by the given amount via subtracting from the `s`\n * in `hsla`.\n *\n * @param amount The amount to desaturate, given as a decimal between 0 and 1\n */\nfunction desaturate(color: string, amount: number): string {\n  const [h, s, l, a] = parseToHsla(color);\n  return hsla(h, s - amount, l, a);\n}\n\nexport default desaturate;\n", "import parseToRgba from './parseToRgba';\n// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getLuminance.js\n\n/**\n * Returns a number (float) representing the luminance of a color.\n */\nfunction getLuminance(color: string): number {\n  if (color === 'transparent') return 0;\n\n  function f(x: number) {\n    const channel = x / 255;\n    return channel <= 0.04045\n      ? channel / 12.92\n      : Math.pow(((channel + 0.055) / 1.055), 2.4);\n  }\n\n  const [r, g, b] = parseToRgba(color);\n  return 0.2126 * f(r) + 0.7152 * f(g) + 0.0722 * f(b);\n}\n\nexport default getLuminance;\n", "// taken from:\n// https://github.com/styled-components/polished/blob/0764c982551b487469043acb56281b0358b3107b/src/color/getContrast.js\nimport getLuminance from './getLuminance';\n\n/**\n * Returns the contrast ratio between two colors based on\n * [W3's recommended equation for calculating contrast](http://www.w3.org/TR/WCAG20/#contrast-ratiodef).\n */\nfunction getContrast(color1: string, color2: string): number {\n  const luminance1 = getLuminance(color1);\n  const luminance2 = getLuminance(color2);\n\n  return luminance1 > luminance2\n    ? (luminance1 + 0.05) / (luminance2 + 0.05)\n    : (luminance2 + 0.05) / (luminance1 + 0.05);\n}\n\nexport default getContrast;\n", "import guard from './guard';\n\n/**\n * Takes in rgba parts and returns an rgba string\n *\n * @param red The amount of red in the red channel, given in a number between 0 and 255 inclusive\n * @param green The amount of green in the red channel, given in a number between 0 and 255 inclusive\n * @param blue The amount of blue in the red channel, given in a number between 0 and 255 inclusive\n * @param alpha Percentage of opacity, given as a decimal between 0 and 1\n */\nfunction rgba(red: number, green: number, blue: number, alpha: number): string {\n  return `rgba(${guard(0, 255, red).toFixed()}, ${guard(\n    0,\n    255,\n    green\n  ).toFixed()}, ${guard(0, 255, blue).toFixed()}, ${parseFloat(\n    guard(0, 1, alpha).toFixed(3)\n  )})`;\n}\n\nexport default rgba;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Mixes two colors together. Taken from sass's implementation.\n */\nfunction mix(color1: string, color2: string, weight: number): string {\n  const normalize = (n: number, index: number) =>\n    // 3rd index is alpha channel which is already normalized\n    index === 3 ? n : n / 255;\n\n  const [r1, g1, b1, a1] = parseToRgba(color1).map(normalize);\n  const [r2, g2, b2, a2] = parseToRgba(color2).map(normalize);\n\n  // The formula is copied from the original Sass implementation:\n  // http://sass-lang.com/documentation/Sass/Script/Functions.html#mix-instance_method\n  const alphaDelta = a2 - a1;\n  const normalizedWeight = weight * 2 - 1;\n  const combinedWeight =\n    normalizedWeight * alphaDelta === -1\n      ? normalizedWeight\n      : normalizedWeight + alphaDelta / (1 + normalizedWeight * alphaDelta);\n  const weight2 = (combinedWeight + 1) / 2;\n  const weight1 = 1 - weight2;\n\n  const r = (r1 * weight1 + r2 * weight2) * 255;\n  const g = (g1 * weight1 + g2 * weight2) * 255;\n  const b = (b1 * weight1 + b2 * weight2) * 255;\n  const a = a2 * weight + a1 * (1 - weight);\n\n  return rgba(r, g, b, a);\n}\n\nexport default mix;\n", "import mix from './mix';\nimport guard from './guard';\n\n/**\n * Given a series colors, this function will return a `scale(x)` function that\n * accepts a percentage as a decimal between 0 and 1 and returns the color at\n * that percentage in the scale.\n *\n * ```js\n * const scale = getScale('red', 'yellow', 'green');\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(0.5)); // rgba(255, 255, 0, 1)\n * console.log(scale(1)); // rgba(0, 128, 0, 1)\n * ```\n *\n * If you'd like to limit the domain and range like chroma-js, we recommend\n * wrapping scale again.\n *\n * ```js\n * const _scale = getScale('red', 'yellow', 'green');\n * const scale = x => _scale(x / 100);\n *\n * console.log(scale(0)); // rgba(255, 0, 0, 1)\n * console.log(scale(50)); // rgba(255, 255, 0, 1)\n * console.log(scale(100)); // rgba(0, 128, 0, 1)\n * ```\n */\nfunction getScale(...colors: string[]): (n: number) => string {\n  return (n) => {\n    const lastIndex = colors.length - 1;\n    const lowIndex = guard(0, lastIndex, Math.floor(n * lastIndex));\n    const highIndex = guard(0, lastIndex, Math.ceil(n * lastIndex));\n\n    const color1 = colors[lowIndex];\n    const color2 = colors[highIndex];\n\n    const unit = 1 / lastIndex;\n    const weight = (n - unit * lowIndex) / unit;\n\n    return mix(color1, color2, weight);\n  };\n}\n\nexport default getScale;\n", "import getContrast from './getContrast';\n\nconst guidelines = {\n  decorative: 1.5,\n  readable: 3,\n  aa: 4.5,\n  aaa: 7,\n};\n\n/**\n * Returns whether or not a color has bad contrast against a background\n * according to a given standard.\n */\nfunction hasBadContrast(\n  color: string,\n  standard: 'decorative' | 'readable' | 'aa' | 'aaa' = 'aa',\n  background: string = '#fff'\n): boolean {\n  return getContrast(color, background) < guidelines[standard];\n}\n\nexport default hasBadContrast;\n", "import darken from './darken';\n/**\n * Lightens a color by a given amount. This is equivalent to\n * `darken(color, -amount)`\n *\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction lighten(color: string, amount: number): string {\n  return darken(color, -amount);\n}\n\nexport default lighten;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in a color and makes it more transparent by convert to `rgba` and\n * decreasing the amount in the alpha channel.\n *\n * @param amount The amount to increase the transparency by, given as a decimal between 0 and 1\n */\nfunction transparentize(color: string, amount: number): string {\n  const [r, g, b, a] = parseToRgba(color);\n  return rgba(r, g, b, a - amount);\n}\n\nexport default transparentize;\n", "import transparentize from './transparentize';\n\n/**\n * Takes a color and un-transparentizes it. Equivalent to\n * `transparentize(color, -amount)`\n *\n * @param amount The amount to increase the opacity by, given as a decimal between 0 and 1\n */\nfunction opacify(color: string, amount: number): string {\n  return transparentize(color, -amount);\n}\n\nexport default opacify;\n", "import getLuminance from './getLuminance';\n\n/**\n * An alternative function to `readableColor`. Returns whether or not the \n * readable color (i.e. the color to be place on top the input color) should be\n * black.\n */\nfunction readableColorIsBlack(color: string): boolean {\n  return getLuminance(color) > 0.179;\n}\n\nexport default readableColorIsBlack;\n", "import readableColorIsBlack from './readableColorIsBlack';\n\n/**\n * Returns black or white for best contrast depending on the luminosity of the\n * given color.\n */\nfunction readableColor(color: string): string {\n  return readableColorIsBlack(color) ? '#000' : '#fff';\n}\n\nexport default readableColor;\n", "import desaturate from './desaturate';\n\n/**\n * Saturates a color by converting it to `hsl` and increasing the saturation\n * amount. Equivalent to `desaturate(color, -amount)`\n * \n * @param color Input color\n * @param amount The amount to darken, given as a decimal between 0 and 1\n */\nfunction saturate(color: string, amount: number): string {\n  return desaturate(color, -amount);\n}\n\nexport default saturate;\n", "import parseToRgba from './parseToRgba';\nimport guard from './guard';\n\n/**\n * Takes in any color and returns it as a hex code.\n */\nfunction toHex(color: string): string {\n  const [r, g, b, a] = parseToRgba(color);\n\n  let hex = (x: number) => {\n    const h = guard(0, 255, x).toString(16);\n    // NOTE: padStart could be used here but it breaks Node 6 compat\n    // https://github.com/ricokahler/color2k/issues/351\n    return h.length === 1 ? `0${h}` : h;\n  };\n\n  return `#${hex(r)}${hex(g)}${hex(b)}${a < 1 ? hex(Math.round(a * 255)) : ''}`;\n}\n\nexport default toHex;\n", "import parseToRgba from './parseToRgba';\nimport rgba from './rgba';\n\n/**\n * Takes in any color and returns it as an rgba string.\n */\nfunction toRgba(color: string): string {\n  return rgba(...parseToRgba(color));\n}\n\nexport default toRgba;\n", "import parseToHsla from './parseToHsla';\nimport hsla from './hsla';\n\n/**\n * Takes in any color and returns it as an hsla string.\n */\nfunction toHsla(color: string): string {\n  return hsla(...parseToHsla(color));\n}\n\nexport default toHsla;\n"], "mappings": ";;;AAOA,SAASA,MAAMC,KAAaC,MAAcC,OAAuB;AAC/D,SAAOC,KAAKC,IAAID,KAAKE,IAAIL,KAAKE,KAAK,GAAGD,IAAI;AAC5C;ACTA,IAAMK,aAAN,cAAyBC,MAAM;EAC7BC,YAAYC,OAAe;AACzB,UAAO,2BAA0BA,KAAM,GAAE;EAC3C;AACF;AAEA,IAAA,eAAeH;ACEf,SAASI,YAAYD,OAAiD;AACpE,MAAI,OAAOA,UAAU;AAAU,UAAM,IAAIH,aAAWG,KAAK;AACzD,MAAIA,MAAME,KAAI,EAAGC,YAAW,MAAO;AAAe,WAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAEpE,MAAIC,kBAAkBJ,MAAME,KAAI;AAChCE,oBAAkBC,gBAAgBC,KAAKN,KAAK,IAAIO,UAAUP,KAAK,IAAIA;AAEnE,QAAMQ,kBAAkBC,gBAAgBC,KAAKN,eAAe;AAC5D,MAAII,iBAAiB;AACnB,UAAMG,MAAMC,MAAMC,KAAKL,eAAe,EAAEM,MAAM,CAAC;AAC/C,WAAO,CACL,GAAGH,IAAIG,MAAM,GAAG,CAAC,EAAEC,IAAKC,OAAMC,SAASC,EAAEF,GAAG,CAAC,GAAG,EAAE,CAAC,GACnDC,SAASC,EAAEP,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG;EAE3C;AAEA,QAAMQ,WAAWC,SAASV,KAAKN,eAAe;AAC9C,MAAIe,UAAU;AACZ,UAAMR,MAAMC,MAAMC,KAAKM,QAAQ,EAAEL,MAAM,CAAC;AACxC,WAAO,CACL,GAAGH,IAAIG,MAAM,GAAG,CAAC,EAAEC,IAAKC,OAAMC,SAASD,GAAG,EAAE,CAAC,GAC7CC,SAASN,IAAI,CAAC,KAAK,MAAM,EAAE,IAAI,GAAG;EAEtC;AAEA,QAAMU,YAAYC,UAAUZ,KAAKN,eAAe;AAChD,MAAIiB,WAAW;AACb,UAAMV,MAAMC,MAAMC,KAAKQ,SAAS,EAAEP,MAAM,CAAC;AACzC,WAAO,CACL,GAAGH,IAAIG,MAAM,GAAG,CAAC,EAAEC,IAAKC,OAAMC,SAASD,GAAG,EAAE,CAAC,GAC7CO,WAAWZ,IAAI,CAAC,KAAK,GAAG,CAAC;EAE7B;AAEA,QAAMa,YAAYC,UAAUf,KAAKN,eAAe;AAChD,MAAIoB,WAAW;AACb,UAAM,CAACE,GAAGC,GAAGC,GAAGC,CAAC,IAAIjB,MAAMC,KAAKW,SAAS,EAAEV,MAAM,CAAC,EAAEC,IAAIQ,UAAU;AAClE,QAAIjC,MAAM,GAAG,KAAKqC,CAAC,MAAMA;AAAG,YAAM,IAAI9B,aAAWG,KAAK;AACtD,QAAIV,MAAM,GAAG,KAAKsC,CAAC,MAAMA;AAAG,YAAM,IAAI/B,aAAWG,KAAK;AACtD,WAAO,CAAC,GAAG8B,SAASJ,GAAGC,GAAGC,CAAC,GAAGG,OAAOC,MAAMH,CAAC,IAAI,IAAIA,CAAC;EAMvD;AAEA,QAAM,IAAIhC,aAAWG,KAAK;AAC5B;AAEA,SAASiC,KAAKC,KAAa;AACzB,MAAID,QAAO;AACX,MAAIE,IAAID,IAAIE;AAEZ,SAAOD,GAAG;AACRF,IAAAA,QAAQA,QAAO,KAAMC,IAAIG,WAAW,EAAEF,CAAC;EACzC;AAKA,UAAQF,UAAS,KAAK;AACxB;AAEA,IAAMK,aAActB,OAAcC,SAASD,EAAEuB,QAAQ,MAAM,EAAE,GAAG,EAAE;AAElE,IAAMC,qBACJ,szCACGC,MAAM,GAAG,EACTC,OAAO,CAACC,KAAKC,SAAS;AACrB,QAAMC,MAAMP,WAAWM,KAAKE,UAAU,GAAG,CAAC,CAAC;AAC3C,QAAMC,MAAMT,WAAWM,KAAKE,UAAU,CAAC,CAAC,EAAEE,SAAS,EAAE;AAIrD,MAAIC,SAAS;AACb,WAASd,IAAI,GAAGA,IAAI,IAAIY,IAAIX,QAAQD,KAAK;AACvCc,cAAU;EACZ;AAEAN,MAAIE,GAAG,IAAK,GAAEI,MAAO,GAAEF,GAAI;AAC3B,SAAOJ;AACT,GAAG,CAAA,CAA+B;AAKtC,SAASpC,UAAUP,OAAuB;AACxC,QAAMkD,sBAAsBlD,MAAMG,YAAW,EAAGD,KAAI;AACpD,QAAMiD,SAASX,mBAAmBP,KAAKiB,mBAAmB,CAAC;AAC3D,MAAI,CAACC;AAAQ,UAAM,IAAItD,aAAWG,KAAK;AACvC,SAAQ,IAAGmD,MAAO;AACpB;AAEA,IAAMjC,IAAIA,CAACgB,KAAakB,WACtBxC,MAAMC,KAAKD,MAAMwC,MAAM,CAAC,EACrBrC,IAAI,MAAMmB,GAAG,EACbmB,KAAK,EAAE;AAEZ,IAAM5C,kBAAkB,IAAI6C,OAAQ,KAAIpC,EAAE,cAAc,CAAC,CAAE,gBAAe,GAAG;AAC7E,IAAME,WAAW,IAAIkC,OAAQ,KAAIpC,EAAE,iBAAiB,CAAC,CAAE,mBAAkB,GAAG;AAC5E,IAAMI,YAAY,IAAIgC,OACnB,0BAAyBpC,EACxB,mBACA,CACF,CAAE,+BACF,GACF;AACA,IAAMO,YACJ;AACF,IAAMpB,kBAAkB;AAExB,IAAMkD,aAAcvD,WAA0B;AAC5C,SAAON,KAAK8D,MAAMxD,QAAQ,GAAG;AAC/B;AAEA,IAAM8B,WAAWA,CACf2B,KACAC,YACAC,cAC6B;AAC7B,MAAI/B,IAAI+B,YAAY;AACpB,MAAID,eAAe,GAAG;AAEpB,WAAO,CAAC9B,GAAGA,GAAGA,CAAC,EAAEb,IAAIwC,UAAU;EACjC;AAGA,QAAMK,YAAcH,MAAM,MAAO,OAAO,MAAO;AAC/C,QAAMI,UAAU,IAAInE,KAAKoE,IAAI,IAAIlC,IAAI,CAAC,MAAM8B,aAAa;AACzD,QAAMK,kBAAkBF,UAAU,IAAInE,KAAKoE,IAAKF,WAAW,IAAK,CAAC;AAEjE,MAAII,MAAM;AACV,MAAIC,QAAQ;AACZ,MAAIC,OAAO;AAEX,MAAIN,YAAY,KAAKA,WAAW,GAAG;AACjCI,UAAMH;AACNI,YAAQF;aACCH,YAAY,KAAKA,WAAW,GAAG;AACxCI,UAAMD;AACNE,YAAQJ;aACCD,YAAY,KAAKA,WAAW,GAAG;AACxCK,YAAQJ;AACRK,WAAOH;aACEH,YAAY,KAAKA,WAAW,GAAG;AACxCK,YAAQF;AACRG,WAAOL;aACED,YAAY,KAAKA,WAAW,GAAG;AACxCI,UAAMD;AACNG,WAAOL;aACED,YAAY,KAAKA,WAAW,GAAG;AACxCI,UAAMH;AACNK,WAAOH;EACT;AAEA,QAAMI,wBAAwBvC,IAAIiC,SAAS;AAC3C,QAAMO,WAAWJ,MAAMG;AACvB,QAAME,aAAaJ,QAAQE;AAC3B,QAAMG,YAAYJ,OAAOC;AAEzB,SAAO,CAACC,UAAUC,YAAYC,SAAS,EAAEvD,IAAIwC,UAAU;AAKzD;ACpKA,SAASgB,YAAYvE,OAAiD;AACpE,QAAM,CAACgE,KAAKC,OAAOC,MAAMM,KAAK,IAAIvE,YAAYD,KAAK,EAAEe,IAAI,CAACtB,OAAOgF;;IAE/DA,UAAU,IAAIhF,QAAQA,QAAQ;GAChC;AAEA,QAAMG,MAAMF,KAAKE,IAAIoE,KAAKC,OAAOC,IAAI;AACrC,QAAMvE,MAAMD,KAAKC,IAAIqE,KAAKC,OAAOC,IAAI;AACrC,QAAMP,aAAa/D,MAAMD,OAAO;AAGhC,MAAIC,QAAQD;AAAK,WAAO,CAAC,GAAG,GAAGgE,WAAWa,KAAK;AAE/C,QAAME,QAAQ9E,MAAMD;AACpB,QAAM+D,aACJC,YAAY,MAAMe,SAAS,IAAI9E,MAAMD,OAAO+E,SAAS9E,MAAMD;AAE7D,QAAM8D,MACJ,MACCO,QAAQpE,OACJqE,QAAQC,QAAQQ,SAAST,QAAQC,OAAO,IAAI,KAC7CD,UAAUrE,OACTsE,OAAOF,OAAOU,QAAQ,KACtBV,MAAMC,SAASS,QAAQ;AAE9B,SAAO,CAACjB,KAAKC,YAAYC,WAAWa,KAAK;AAC3C;AC1BA,SAASG,KACPlB,KACAC,YACAC,WACAa,OACQ;AACR,SAAQ,SAAQf,MAAM,KAAKmB,QAAO,CAAG,KAAItF,MACvC,GACA,KACAoE,aAAa,GACf,EAAEkB,QAAO,CAAG,MAAKtF,MAAM,GAAG,KAAKqE,YAAY,GAAG,EAAEiB,QAAO,CAAG,MAAKrD,WAC7DjC,MAAM,GAAG,GAAGkF,KAAK,EAAEI,QAAQ,CAAC,CAC9B,CAAE;AACJ;ACZA,SAASC,UAAU7E,OAAe8E,SAAyB;AACzD,QAAM,CAACpD,GAAGC,GAAGC,GAAGC,CAAC,IAAI0C,YAAYvE,KAAK;AACtC,SAAO2E,KAAKjD,IAAIoD,SAASnD,GAAGC,GAAGC,CAAC;AAClC;ACLA,SAASkD,OAAO/E,OAAeoD,QAAwB;AACrD,QAAM,CAACK,KAAKC,YAAYC,WAAWa,KAAK,IAAID,YAAYvE,KAAK;AAC7D,SAAO2E,KAAKlB,KAAKC,YAAYC,YAAYP,QAAQoB,KAAK;AACxD;ACHA,SAASQ,WAAWhF,OAAeoD,QAAwB;AACzD,QAAM,CAAC1B,GAAGC,GAAGC,GAAGC,CAAC,IAAI0C,YAAYvE,KAAK;AACtC,SAAO2E,KAAKjD,GAAGC,IAAIyB,QAAQxB,GAAGC,CAAC;AACjC;ACLA,SAASoD,aAAajF,OAAuB;AAC3C,MAAIA,UAAU;AAAe,WAAO;AAEpC,WAASkF,EAAElE,GAAW;AACpB,UAAMmE,UAAUnE,IAAI;AACpB,WAAOmE,WAAW,UACdA,UAAU,QACVzF,KAAK0F,KAAMD,UAAU,SAAS,OAAQ,GAAG;EAC/C;AAEA,QAAM,CAACjE,IAAGmE,GAAGC,CAAC,IAAIrF,YAAYD,KAAK;AACnC,SAAO,SAASkF,EAAEhE,EAAC,IAAI,SAASgE,EAAEG,CAAC,IAAI,SAASH,EAAEI,CAAC;AACrD;ACXA,SAASC,YAAYC,QAAgBC,QAAwB;AAC3D,QAAMC,aAAaT,aAAaO,MAAM;AACtC,QAAMG,aAAaV,aAAaQ,MAAM;AAEtC,SAAOC,aAAaC,cACfD,aAAa,SAASC,aAAa,SACnCA,aAAa,SAASD,aAAa;AAC1C;ACLA,SAASE,KAAK5B,KAAaC,OAAeC,MAAcM,OAAuB;AAC7E,SAAQ,QAAOlF,MAAM,GAAG,KAAK0E,GAAG,EAAEY,QAAO,CAAG,KAAItF,MAC9C,GACA,KACA2E,KACF,EAAEW,QAAO,CAAG,KAAItF,MAAM,GAAG,KAAK4E,IAAI,EAAEU,QAAO,CAAG,KAAIrD,WAChDjC,MAAM,GAAG,GAAGkF,KAAK,EAAEI,QAAQ,CAAC,CAC9B,CAAE;AACJ;ACZA,SAASiB,IAAIL,QAAgBC,QAAgBK,QAAwB;AACnE,QAAMC,YAAYA,CAACC,GAAWvB;;IAE5BA,UAAU,IAAIuB,IAAIA,IAAI;;AAExB,QAAM,CAACC,IAAIC,IAAIC,IAAIC,EAAE,IAAInG,YAAYuF,MAAM,EAAEzE,IAAIgF,SAAS;AAC1D,QAAM,CAACM,IAAIC,IAAIC,IAAIC,EAAE,IAAIvG,YAAYwF,MAAM,EAAE1E,IAAIgF,SAAS;AAI1D,QAAMU,aAAaD,KAAKJ;AACxB,QAAMM,mBAAmBZ,SAAS,IAAI;AACtC,QAAMa,iBACJD,mBAAmBD,eAAe,KAC9BC,mBACAA,mBAAmBD,cAAc,IAAIC,mBAAmBD;AAC9D,QAAMG,WAAWD,iBAAiB,KAAK;AACvC,QAAME,UAAU,IAAID;AAEpB,QAAM1F,MAAK+E,KAAKY,UAAUR,KAAKO,WAAW;AAC1C,QAAMvB,KAAKa,KAAKW,UAAUP,KAAKM,WAAW;AAC1C,QAAMtB,KAAKa,KAAKU,UAAUN,KAAKK,WAAW;AAC1C,QAAM/E,IAAI2E,KAAKV,SAASM,MAAM,IAAIN;AAElC,SAAOF,KAAK1E,IAAGmE,GAAGC,GAAGzD,CAAC;AACxB;ACJA,SAASiF,YAAYC,QAAyC;AAC5D,SAAQf,OAAM;AACZ,UAAMgB,YAAYD,OAAO3E,SAAS;AAClC,UAAM6E,WAAW3H,MAAM,GAAG0H,WAAWtH,KAAKwH,MAAMlB,IAAIgB,SAAS,CAAC;AAC9D,UAAMG,YAAY7H,MAAM,GAAG0H,WAAWtH,KAAK0H,KAAKpB,IAAIgB,SAAS,CAAC;AAE9D,UAAMxB,SAASuB,OAAOE,QAAQ;AAC9B,UAAMxB,SAASsB,OAAOI,SAAS;AAE/B,UAAME,OAAO,IAAIL;AACjB,UAAMlB,UAAUE,IAAIqB,OAAOJ,YAAYI;AAEvC,WAAOxB,IAAIL,QAAQC,QAAQK,MAAM;;AAErC;ACvCA,IAAMwB,aAAa;EACjBC,YAAY;EACZC,UAAU;EACVC,IAAI;EACJC,KAAK;AACP;AAMA,SAASC,eACP3H,OACA4H,WAAqD,MACrDC,aAAqB,QACZ;AACT,SAAOtC,YAAYvF,OAAO6H,UAAU,IAAIP,WAAWM,QAAQ;AAC7D;ACZA,SAASE,QAAQ9H,OAAeoD,QAAwB;AACtD,SAAO2B,OAAO/E,OAAO,CAACoD,MAAM;AAC9B;ACAA,SAAS2E,eAAe/H,OAAeoD,QAAwB;AAC7D,QAAM,CAAClC,IAAGmE,GAAGC,GAAGzD,CAAC,IAAI5B,YAAYD,KAAK;AACtC,SAAO4F,KAAK1E,IAAGmE,GAAGC,GAAGzD,IAAIuB,MAAM;AACjC;ACJA,SAAS4E,QAAQhI,OAAeoD,QAAwB;AACtD,SAAO2E,eAAe/H,OAAO,CAACoD,MAAM;AACtC;ACHA,SAAS6E,qBAAqBjI,OAAwB;AACpD,SAAOiF,aAAajF,KAAK,IAAI;AAC/B;ACHA,SAASkI,cAAclI,OAAuB;AAC5C,SAAOiI,qBAAqBjI,KAAK,IAAI,SAAS;AAChD;ACCA,SAASmI,SAASnI,OAAeoD,QAAwB;AACvD,SAAO4B,WAAWhF,OAAO,CAACoD,MAAM;AAClC;ACLA,SAASgF,MAAMpI,OAAuB;AACpC,QAAM,CAACkB,IAAGmE,GAAGC,GAAGzD,CAAC,IAAI5B,YAAYD,KAAK;AAEtC,MAAI+C,MAAO/B,OAAc;AACvB,UAAMU,IAAIpC,MAAM,GAAG,KAAK0B,CAAC,EAAEgC,SAAS,EAAE;AAGtC,WAAOtB,EAAEU,WAAW,IAAK,IAAGV,CAAE,KAAIA;;AAGpC,SAAQ,IAAGqB,IAAI7B,EAAC,CAAE,GAAE6B,IAAIsC,CAAC,CAAE,GAAEtC,IAAIuC,CAAC,CAAE,GAAEzD,IAAI,IAAIkB,IAAIrD,KAAK8D,MAAM3B,IAAI,GAAG,CAAC,IAAI,EAAG;AAC9E;ACXA,SAASwG,OAAOrI,OAAuB;AACrC,SAAO4F,KAAK,GAAG3F,YAAYD,KAAK,CAAC;AACnC;ACFA,SAASsI,OAAOtI,OAAuB;AACrC,SAAO2E,KAAK,GAAGJ,YAAYvE,KAAK,CAAC;AACnC;", "names": ["guard", "low", "high", "value", "Math", "min", "max", "ColorError", "Error", "constructor", "color", "parseToRgba", "trim", "toLowerCase", "normalizedColor", "namedColorRegex", "test", "nameToHex", "reducedHexMatch", "reducedHexRegex", "exec", "arr", "Array", "from", "slice", "map", "x", "parseInt", "r", "hexMatch", "hexRegex", "rgbaMatch", "rgbaRegex", "parseFloat", "hslaMatch", "hslaRegex", "h", "s", "l", "a", "hslToRgb", "Number", "isNaN", "hash", "str", "i", "length", "charCodeAt", "colorToInt", "replace", "compressedColorMap", "split", "reduce", "acc", "next", "key", "substring", "hex", "toString", "prefix", "normalizedColorName", "result", "amount", "join", "RegExp", "roundColor", "round", "hue", "saturation", "lightness", "huePrime", "chroma", "abs", "secondComponent", "red", "green", "blue", "lightnessModification", "finalRed", "finalGreen", "finalBlue", "parseToHsla", "alpha", "index", "delta", "hsla", "toFixed", "adjustHue", "degrees", "darken", "desaturate", "getLuminance", "f", "channel", "pow", "g", "b", "getContrast", "color1", "color2", "luminance1", "luminance2", "rgba", "mix", "weight", "normalize", "n", "r1", "g1", "b1", "a1", "r2", "g2", "b2", "a2", "alphaDelta", "normalizedWeight", "combinedWeight", "weight2", "weight1", "getScale", "colors", "lastIndex", "lowIndex", "floor", "highIndex", "ceil", "unit", "guidelines", "decorative", "readable", "aa", "aaa", "hasBadContrast", "standard", "background", "lighten", "transparentize", "opacify", "readableColorIsBlack", "readableColor", "saturate", "toHex", "toRgba", "toHsla"]}