<script lang="ts">
  import { onMount, onD<PERSON>roy, createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import type { MessagingChannel, Message, MessagingChannelMember } from '$lib/utils/types/communication';
  import { messagingService } from '$lib/utils/services/communication';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Send, 
    Attachment, 
    Microphone,
    MicrophoneOff,
    Add,
    Group,
    User,
    Time,
    CheckmarkFilled,
    Close,
    Search,
    Settings
  } from 'carbon-icons-svelte';

  export let batchId: string | null = null;
  export let subjectId: string | null = null;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    messageSent: { message: Message };
    channelCreated: { channel: MessagingChannel };
  }>();

  let channels = writable<MessagingChannel[]>([]);
  let selectedChannel = writable<MessagingChannel | null>(null);
  let messages = writable<Message[]>([]);
  let channelMembers = writable<MessagingChannelMember[]>([]);
  
  let loading = true;
  let error: string | null = null;
  let sending = false;
  let showCreateChannel = false;
  let isRecording = false;
  let typingUsers = writable<string[]>([]);

  // Form data
  let messageText = '';
  let channelName = '';
  let channelDescription = '';
  let channelType: 'batch' | 'subject' | 'private' | 'announcement' | 'support' = 'batch';
  let attachments: File[] = [];

  // Real-time updates
  let messagePollingInterval: NodeJS.Timeout | null = null;
  let typingTimeout: NodeJS.Timeout | null = null;

  $: userId = $globalStore.user?.id;
  $: isInstructor = $globalStore.role === 'teacher' || $globalStore.isOrgAdmin;

  onMount(async () => {
    await loadChannels();
    startMessagePolling();
  });

  onDestroy(() => {
    if (messagePollingInterval) {
      clearInterval(messagePollingInterval);
    }
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
  });

  async function loadChannels() {
    if (!userId) return;

    try {
      loading = true;
      error = null;

      const channelsData = await messagingService.getUserChannels(userId);
      channels.set(channelsData);

      // Auto-select first channel if available
      if (channelsData.length > 0) {
        await selectChannel(channelsData[0]);
      }

    } catch (err) {
      console.error('Error loading channels:', err);
      error = err.message || 'Failed to load channels';
    } finally {
      loading = false;
    }
  }

  async function selectChannel(channel: MessagingChannel) {
    selectedChannel.set(channel);
    await loadMessages(channel.id);
    await markAsRead(channel.id);
  }

  async function loadMessages(channelId: string) {
    try {
      const messagesData = await messagingService.getChannelMessages(channelId);
      messages.set(messagesData);
      
      // Scroll to bottom
      setTimeout(() => {
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
      }, 100);

    } catch (err) {
      console.error('Error loading messages:', err);
      error = err.message || 'Failed to load messages';
    }
  }

  async function sendMessage() {
    if (!$selectedChannel || !userId || (!messageText.trim() && attachments.length === 0)) return;

    try {
      sending = true;
      error = null;

      const messageData = {
        channel_id: $selectedChannel.id,
        sender_id: userId,
        message_type: 'text' as const,
        content: messageText.trim(),
        attachments: [], // File upload would be handled separately
        metadata: {}
      };

      const message = await messagingService.sendMessage(messageData);
      
      // Add message to local state
      messages.update(current => [...current, message]);
      
      // Reset form
      messageText = '';
      attachments = [];
      
      // Scroll to bottom
      setTimeout(() => {
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
      }, 100);

      dispatch('messageSent', { message });

    } catch (err) {
      console.error('Error sending message:', err);
      error = err.message || 'Failed to send message';
    } finally {
      sending = false;
    }
  }

  async function createChannel() {
    if (!userId || !channelName.trim()) return;

    try {
      const channelData = {
        name: channelName.trim(),
        description: channelDescription.trim() || undefined,
        channel_type: channelType,
        batch_id: channelType === 'batch' ? batchId : undefined,
        subject_id: channelType === 'subject' ? subjectId : undefined,
        created_by: userId,
        is_active: true,
        is_archived: false,
        permissions: {
          can_send_messages: ['student', 'teacher', 'admin'],
          can_add_members: ['teacher', 'admin'],
          can_remove_members: ['admin'],
          can_edit_channel: ['admin']
        },
        settings: {
          allow_file_sharing: true,
          allow_voice_messages: true,
          message_retention_days: 365,
          auto_delete_messages: false
        }
      };

      const channelId = await messagingService.createChannel(channelData);
      
      // Reload channels
      await loadChannels();
      
      // Reset form
      resetChannelForm();
      showCreateChannel = false;

      dispatch('channelCreated', { channel: { ...channelData, id: channelId } as MessagingChannel });

    } catch (err) {
      console.error('Error creating channel:', err);
      error = err.message || 'Failed to create channel';
    }
  }

  async function markAsRead(channelId: string) {
    if (!userId) return;

    try {
      await messagingService.markAsRead(channelId, userId);
    } catch (err) {
      console.error('Error marking as read:', err);
    }
  }

  function startMessagePolling() {
    messagePollingInterval = setInterval(async () => {
      if ($selectedChannel) {
        // In a real implementation, this would use WebSocket or Server-Sent Events
        // For now, we'll poll for new messages
        const latestMessages = await messagingService.getChannelMessages($selectedChannel.id, 10);
        const currentMessageIds = $messages.map(m => m.id);
        const newMessages = latestMessages.filter(m => !currentMessageIds.includes(m.id));
        
        if (newMessages.length > 0) {
          messages.update(current => [...current, ...newMessages]);
        }
      }
    }, 5000); // Poll every 5 seconds
  }

  function handleTyping() {
    // Simulate typing indicator
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
    
    typingTimeout = setTimeout(() => {
      // Stop typing indicator
    }, 2000);
  }

  function resetChannelForm() {
    channelName = '';
    channelDescription = '';
    channelType = 'batch';
  }

  function formatTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    return date.toLocaleDateString();
  }

  function getChannelIcon(type: string) {
    switch (type) {
      case 'batch': return '👥';
      case 'subject': return '📚';
      case 'private': return '🔒';
      case 'announcement': return '📢';
      case 'support': return '🆘';
      default: return '💬';
    }
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    } else {
      handleTyping();
    }
  }

  function handleFileUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files) {
      attachments = Array.from(target.files);
    }
  }

  async function startVoiceRecording() {
    try {
      isRecording = true;
      // Voice recording implementation would go here
      // This would use MediaRecorder API to record audio
    } catch (err) {
      console.error('Error starting voice recording:', err);
      isRecording = false;
    }
  }

  function stopVoiceRecording() {
    isRecording = false;
    // Stop recording and send voice message
  }
</script>

<div class="messaging-system {className} h-full flex">
  <!-- Channels Sidebar -->
  <div class="w-1/3 border-r border-gray-200 dark:border-gray-700 flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
          {$t('messaging.channels', { default: 'Channels' })}
        </h2>

        {#if isInstructor}
          <PrimaryButton
            variant={VARIANTS.OUTLINED}
            onClick={() => showCreateChannel = true}
            size="sm"
          >
            <Add size={16} class="mr-1" />
            {$t('messaging.new_channel', { default: 'New' })}
          </PrimaryButton>
        {/if}
      </div>

      <!-- Search -->
      <div class="relative">
        <Search size={16} class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <input
          type="text"
          placeholder={$t('messaging.search_channels', { default: 'Search channels...' })}
          class="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md"
        />
      </div>
    </div>

    <!-- Channels List -->
    <div class="flex-1 overflow-y-auto">
      {#if loading}
        <div class="p-4 space-y-3">
          {#each Array(5) as _}
            <div class="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          {/each}
        </div>
      {:else if $channels.length === 0}
        <div class="p-4 text-center">
          <Group size={32} class="text-gray-400 mx-auto mb-2" />
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {$t('messaging.no_channels', { default: 'No channels yet' })}
          </p>
        </div>
      {:else}
        <div class="p-2">
          {#each $channels as channel (channel.id)}
            <button
              on:click={() => selectChannel(channel)}
              class="w-full text-left p-3 rounded-lg mb-1 transition-colors {$selectedChannel?.id === channel.id
                ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700'}"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center flex-1 min-w-0">
                  <span class="text-lg mr-2">{getChannelIcon(channel.channel_type)}</span>
                  <div class="flex-1 min-w-0">
                    <p class="font-medium text-gray-900 dark:text-white truncate">
                      {channel.name}
                    </p>
                    {#if channel.last_message_at}
                      <p class="text-xs text-gray-600 dark:text-gray-400">
                        {formatTime(channel.last_message_at)}
                      </p>
                    {/if}
                  </div>
                </div>

                <div class="flex items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    {channel.member_count}
                  </span>
                </div>
              </div>
            </button>
          {/each}
        </div>
      {/if}
    </div>
  </div>

  <!-- Chat Area -->
  <div class="flex-1 flex flex-col">
    {#if $selectedChannel}
      <!-- Chat Header -->
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="text-2xl mr-3">{getChannelIcon($selectedChannel.channel_type)}</span>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                {$selectedChannel.name}
              </h3>
              {#if $selectedChannel.description}
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {$selectedChannel.description}
                </p>
              {/if}
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {$selectedChannel.member_count} {$t('messaging.members', { default: 'members' })}
            </span>
            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <Settings size={20} />
            </button>
          </div>
        </div>
      </div>

      <!-- Messages -->
      <div id="messages-container" class="flex-1 overflow-y-auto p-4 space-y-4">
        {#each $messages as message (message.id)}
          <div class="flex items-start space-x-3 {message.sender_id === userId ? 'flex-row-reverse space-x-reverse' : ''}">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-primary-600 dark:text-primary-300 text-sm font-semibold">
                {message.sender?.fullname?.charAt(0) || 'U'}
              </span>
            </div>

            <div class="flex-1 min-w-0 {message.sender_id === userId ? 'text-right' : ''}">
              <div class="flex items-center space-x-2 mb-1 {message.sender_id === userId ? 'justify-end' : ''}">
                <p class="text-sm font-medium text-gray-900 dark:text-white">
                  {message.sender?.fullname || 'Unknown'}
                </p>
                <p class="text-xs text-gray-600 dark:text-gray-400">
                  {formatTime(message.created_at)}
                </p>
                {#if message.delivery_status === 'read'}
                  <CheckmarkFilled size={12} class="text-blue-500" />
                {/if}
              </div>

              <div class="inline-block max-w-xs lg:max-w-md px-3 py-2 rounded-lg {message.sender_id === userId
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'}">
                {#if message.message_type === 'text'}
                  <p class="text-sm whitespace-pre-wrap">{message.content}</p>
                {:else if message.message_type === 'voice'}
                  <div class="flex items-center space-x-2">
                    <Microphone size={16} />
                    <span class="text-sm">Voice message</span>
                  </div>
                {:else if message.message_type === 'file'}
                  <div class="flex items-center space-x-2">
                    <Attachment size={16} />
                    <span class="text-sm">File attachment</span>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        {/each}

        <!-- Typing Indicator -->
        {#if $typingUsers.length > 0}
          <div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
            <span class="text-sm">
              {$typingUsers.join(', ')} {$typingUsers.length === 1 ? 'is' : 'are'} typing...
            </span>
          </div>
        {/if}
      </div>

      <!-- Message Input -->
      <div class="p-4 border-t border-gray-200 dark:border-gray-700">
        {#if attachments.length > 0}
          <div class="mb-3 flex flex-wrap gap-2">
            {#each attachments as file}
              <div class="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded">
                <Attachment size={16} />
                <span class="text-sm">{file.name}</span>
                <button
                  on:click={() => attachments = attachments.filter(f => f !== file)}
                  class="text-gray-500 hover:text-gray-700"
                >
                  ×
                </button>
              </div>
            {/each}
          </div>
        {/if}

        <div class="flex items-end space-x-2">
          <div class="flex-1">
            <textarea
              bind:value={messageText}
              on:keydown={handleKeyPress}
              placeholder={$t('messaging.type_message', { default: 'Type a message...' })}
              rows="1"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md resize-none"
              style="min-height: 40px; max-height: 120px;"
            ></textarea>
          </div>

          <div class="flex items-center space-x-2">
            <!-- File Upload -->
            <label class="cursor-pointer text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <Attachment size={20} />
              <input
                type="file"
                multiple
                class="hidden"
                on:change={handleFileUpload}
              />
            </label>

            <!-- Voice Recording -->
            <button
              on:mousedown={startVoiceRecording}
              on:mouseup={stopVoiceRecording}
              on:mouseleave={stopVoiceRecording}
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 {isRecording ? 'text-red-500' : ''}"
            >
              {#if isRecording}
                <MicrophoneOff size={20} />
              {:else}
                <Microphone size={20} />
              {/if}
            </button>

            <!-- Send Button -->
            <PrimaryButton
              variant={VARIANTS.CONTAINED}
              onClick={sendMessage}
              disabled={(!messageText.trim() && attachments.length === 0) || sending}
              size="sm"
            >
              {#if sending}
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              {:else}
                <Send size={16} />
              {/if}
            </PrimaryButton>
          </div>
        </div>
      </div>

    {:else}
      <!-- No Channel Selected -->
      <div class="flex-1 flex items-center justify-center">
        <div class="text-center">
          <Group size={48} class="text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {$t('messaging.select_channel', { default: 'Select a Channel' })}
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            {$t('messaging.select_channel_desc', { default: 'Choose a channel to start messaging' })}
          </p>
        </div>
      </div>
    {/if}
  </div>
</div>

<!-- Create Channel Modal -->
{#if showCreateChannel}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
          {$t('messaging.create_channel', { default: 'Create Channel' })}
        </h2>
        <button
          on:click={() => showCreateChannel = false}
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <Close size={24} />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('messaging.channel_name', { default: 'Channel Name' })} *
          </label>
          <input
            type="text"
            bind:value={channelName}
            placeholder={$t('messaging.channel_name_placeholder', { default: 'Enter channel name' })}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            required
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('messaging.channel_type', { default: 'Channel Type' })}
          </label>
          <select
            bind:value={channelType}
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="batch">{$t('messaging.type_batch', { default: 'Batch Channel' })}</option>
            <option value="subject">{$t('messaging.type_subject', { default: 'Subject Channel' })}</option>
            <option value="private">{$t('messaging.type_private', { default: 'Private Channel' })}</option>
            <option value="announcement">{$t('messaging.type_announcement', { default: 'Announcement Channel' })}</option>
            <option value="support">{$t('messaging.type_support', { default: 'Support Channel' })}</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {$t('messaging.description', { default: 'Description' })}
          </label>
          <textarea
            bind:value={channelDescription}
            placeholder={$t('messaging.description_placeholder', { default: 'Optional channel description' })}
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          ></textarea>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => showCreateChannel = false}
        >
          {$t('messaging.cancel', { default: 'Cancel' })}
        </PrimaryButton>

        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={createChannel}
          disabled={!channelName.trim()}
        >
          <Add size={20} class="mr-2" />
          {$t('messaging.create', { default: 'Create Channel' })}
        </PrimaryButton>
      </div>
    </div>
  </div>
{/if}
