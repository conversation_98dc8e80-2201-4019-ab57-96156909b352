<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import type { DashboardWidget, ThemeConfig } from '$lib/utils/types/analytics';
  import { t } from '$lib/utils/functions/translations';
  import { ChartLine, Warning } from 'carbon-icons-svelte';

  export let widget: DashboardWidget;
  export let data: any;
  export let theme: ThemeConfig;

  const dispatch = createEventDispatcher<{
    click: { widget: DashboardWidget };
  }>();

  let chartContainer: HTMLDivElement;
  let chartInstance: any = null;

  $: chartType = widget.config.chart_type || 'line';
  $: chartData = prepareChartData(data);
  $: chartOptions = getChartOptions();

  onMount(async () => {
    // Dynamically import Chart.js to avoid SSR issues
    try {
      const { Chart, registerables } = await import('chart.js');
      Chart.register(...registerables);
      
      if (chartContainer && chartData) {
        createChart(Chart);
      }
    } catch (error) {
      console.error('Failed to load Chart.js:', error);
    }

    return () => {
      if (chartInstance) {
        chartInstance.destroy();
      }
    };
  });

  $: if (chartInstance && chartData) {
    updateChart();
  }

  function prepareChartData(rawData: any) {
    if (!rawData) return null;

    // Handle different data formats
    if (Array.isArray(rawData)) {
      return formatArrayData(rawData);
    } else if (rawData.datasets) {
      return rawData; // Already in Chart.js format
    } else if (rawData.labels && rawData.data) {
      return formatSimpleData(rawData);
    }

    return null;
  }

  function formatArrayData(data: any[]) {
    if (data.length === 0) return null;

    const firstItem = data[0];
    const keys = Object.keys(firstItem);
    
    // Assume first key is label, rest are data series
    const labelKey = keys[0];
    const dataKeys = keys.slice(1);

    return {
      labels: data.map(item => item[labelKey]),
      datasets: dataKeys.map((key, index) => ({
        label: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        data: data.map(item => item[key]),
        backgroundColor: getColor(index, 0.2),
        borderColor: getColor(index, 1),
        borderWidth: 2,
        fill: chartType === 'area'
      }))
    };
  }

  function formatSimpleData(data: any) {
    return {
      labels: data.labels,
      datasets: [{
        label: data.label || widget.title,
        data: data.data,
        backgroundColor: getColor(0, 0.2),
        borderColor: getColor(0, 1),
        borderWidth: 2,
        fill: chartType === 'area'
      }]
    };
  }

  function getColor(index: number, alpha: number = 1): string {
    const colors = widget.config.colors || [
      theme?.primary_color || '#3B82F6',
      theme?.secondary_color || '#10B981',
      '#F59E0B',
      '#EF4444',
      '#8B5CF6',
      '#06B6D4',
      '#84CC16',
      '#F97316'
    ];

    const color = colors[index % colors.length];
    
    if (alpha < 1) {
      // Convert hex to rgba
      const hex = color.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    
    return color;
  }

  function getChartOptions() {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index'
      },
      plugins: {
        legend: {
          display: widget.config.show_legend !== false,
          position: 'top',
          labels: {
            color: theme?.text_color || '#111827',
            font: {
              family: theme?.font_family || 'Inter'
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: theme?.primary_color || '#3B82F6',
          borderWidth: 1
        }
      },
      scales: {
        x: {
          display: true,
          grid: {
            display: widget.config.show_grid !== false,
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: theme?.text_color || '#111827',
            font: {
              family: theme?.font_family || 'Inter'
            }
          }
        },
        y: {
          display: true,
          grid: {
            display: widget.config.show_grid !== false,
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: theme?.text_color || '#111827',
            font: {
              family: theme?.font_family || 'Inter'
            }
          }
        }
      },
      animation: {
        duration: widget.config.animation !== false ? 1000 : 0
      },
      onClick: (event: any, elements: any[]) => {
        if (elements.length > 0) {
          dispatch('click', { widget });
        }
      }
    };

    // Chart type specific options
    switch (chartType) {
      case 'pie':
      case 'doughnut':
        return {
          ...baseOptions,
          scales: {}, // Remove scales for pie charts
          plugins: {
            ...baseOptions.plugins,
            legend: {
              ...baseOptions.plugins.legend,
              position: 'right'
            }
          }
        };
      
      case 'bar':
        return {
          ...baseOptions,
          scales: {
            ...baseOptions.scales,
            y: {
              ...baseOptions.scales.y,
              beginAtZero: true
            }
          }
        };
      
      case 'line':
      case 'area':
        return {
          ...baseOptions,
          elements: {
            point: {
              radius: 4,
              hoverRadius: 6
            },
            line: {
              tension: 0.4
            }
          }
        };
      
      default:
        return baseOptions;
    }
  }

  function createChart(Chart: any) {
    if (!chartContainer || !chartData) return;

    const ctx = chartContainer.getContext('2d');
    
    chartInstance = new Chart(ctx, {
      type: getChartJSType(chartType),
      data: chartData,
      options: chartOptions
    });
  }

  function updateChart() {
    if (!chartInstance || !chartData) return;

    chartInstance.data = chartData;
    chartInstance.options = chartOptions;
    chartInstance.update('none'); // No animation for updates
  }

  function getChartJSType(type: string): string {
    switch (type) {
      case 'area': return 'line';
      case 'scatter': return 'scatter';
      default: return type;
    }
  }

  function handleClick() {
    dispatch('click', { widget });
  }
</script>

<div class="chart-widget h-full flex flex-col">
  {#if !chartData}
    <!-- No Data State -->
    <div class="flex items-center justify-center h-full">
      <div class="text-center">
        <ChartLine size={32} class="text-gray-400 mx-auto mb-2" />
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {$t('analytics.no_chart_data', { default: 'No chart data available' })}
        </p>
      </div>
    </div>

  {:else}
    <!-- Chart Container -->
    <div class="flex-1 relative min-h-[200px]">
      <canvas
        bind:this={chartContainer}
        class="w-full h-full cursor-pointer"
        on:click={handleClick}
        on:keydown={(e) => e.key === 'Enter' && handleClick()}
        role="img"
        tabindex="0"
        aria-label={`${widget.title} chart`}
      ></canvas>
    </div>

    <!-- Chart Summary (if data available) -->
    {#if data?.summary}
      <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          {#if data.summary.total !== undefined}
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {data.summary.total.toLocaleString()}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {$t('analytics.total', { default: 'Total' })}
              </div>
            </div>
          {/if}

          {#if data.summary.average !== undefined}
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {data.summary.average.toFixed(1)}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {$t('analytics.average', { default: 'Average' })}
              </div>
            </div>
          {/if}

          {#if data.summary.max !== undefined}
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {data.summary.max.toLocaleString()}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {$t('analytics.max', { default: 'Max' })}
              </div>
            </div>
          {/if}

          {#if data.summary.min !== undefined}
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-white">
                {data.summary.min.toLocaleString()}
              </div>
              <div class="text-xs text-gray-600 dark:text-gray-400">
                {$t('analytics.min', { default: 'Min' })}
              </div>
            </div>
          {/if}
        </div>
      </div>
    {/if}
  {/if}
</div>

<style>
  .chart-widget canvas {
    max-height: 100%;
  }

  .chart-widget canvas:focus {
    outline: 2px solid var(--primary-color, #3B82F6);
    outline-offset: 2px;
  }
</style>
