# Worker Dockerfile for ClassroomIO
# Background job processing and scheduled tasks

FROM node:18-alpine

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S worker -u 1001

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init \
    python3 \
    make \
    g++ \
    ffmpeg \
    imagemagick \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./
COPY apps/worker/package*.json ./apps/worker/

# Install dependencies
RUN npm ci --only=production

# Copy worker source code
COPY apps/worker ./apps/worker
COPY packages ./packages

# Copy scripts
COPY scripts/worker-health-check.js ./health-check.js
COPY scripts/start-worker.sh ./

# Create necessary directories
RUN mkdir -p /app/logs /app/temp && \
    chown -R worker:nodejs /app/logs /app/temp

# Set environment variables
ENV NODE_ENV=production
ENV WORKER_CONCURRENCY=5
ENV WORKER_TIMEOUT=300000

# Switch to non-root user
USER worker

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD node health-check.js

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the worker
CMD ["./start-worker.sh"]
