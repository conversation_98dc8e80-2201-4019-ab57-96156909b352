# ClassroomIO Local Development Environment
# Development configuration for localhost

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
APP_NAME=ClassroomIO
APP_URL=http://localhost:5173
API_URL=http://localhost:5173/api

# Self-hosted mode (true for local development without external dependencies)
IS_SELFHOSTED=true

# Server Configuration
PORT=5173
HOST=localhost

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Local Supabase (if running locally) or use demo/development instance
DATABASE_URL=postgresql://postgres:postgres@localhost:54321/postgres

# Supabase Configuration - Using demo/development values
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# =============================================================================
# REDIS CONFIGURATION (Optional for development)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=development-jwt-secret-key-change-in-production-minimum-32-chars
JWT_EXPIRES_IN=7d
SESSION_SECRET=development-session-secret-change-in-production-minimum-32-chars

# =============================================================================
# EMAIL CONFIGURATION (Development - Console logging)
# =============================================================================
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER=
SMTP_PASS=

# Email Templates
FROM_EMAIL=<EMAIL>
FROM_NAME=ClassroomIO Development

# =============================================================================
# FILE STORAGE CONFIGURATION (Local development)
# =============================================================================
# Local Storage for development
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100MB

# AWS S3 Configuration (Optional for development)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=

# =============================================================================
# VIDEO STREAMING CONFIGURATION (Development)
# =============================================================================
# Video Processing (disabled for development)
VIDEO_PROCESSING_ENABLED=false
VIDEO_TRANSCODING_ENABLED=false
VIDEO_THUMBNAIL_ENABLED=false

# Video Security (relaxed for development)
VIDEO_WATERMARK_ENABLED=false
VIDEO_DRM_ENABLED=false
VIDEO_DOWNLOAD_PROTECTION=false

# =============================================================================
# LIVE STREAMING CONFIGURATION (Optional)
# =============================================================================
# Daily.co Configuration (Optional for development)
DAILY_API_KEY=
DAILY_DOMAIN=

# =============================================================================
# AI INTEGRATION (Optional for development)
# =============================================================================
# OpenAI Configuration (Optional)
OPENAI_API_KEY=
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000

# AI Features (disabled for development)
AI_DOUBT_CATEGORIZATION=false
AI_CONTENT_GENERATION=false
AI_ANALYTICS_INSIGHTS=false

# =============================================================================
# ANALYTICS CONFIGURATION (Development)
# =============================================================================
# Analytics (disabled for development)
ANALYTICS_ENABLED=false
ANALYTICS_RETENTION_DAYS=30

# =============================================================================
# MONITORING & OBSERVABILITY (Development)
# =============================================================================
# Logging
LOG_LEVEL=debug
LOG_FORMAT=pretty
LOG_FILE=./logs/app.log

# =============================================================================
# SECURITY CONFIGURATION (Development)
# =============================================================================
# CORS Settings (permissive for development)
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true

# Rate Limiting (relaxed for development)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Security Headers (relaxed for development)
SECURITY_HEADERS_ENABLED=false
CSP_ENABLED=false

# Device Security (disabled for development)
DEVICE_LOCKING_ENABLED=false
MAX_DEVICES_PER_USER=10

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Debug Settings
DEBUG=true
VERBOSE_LOGGING=true

# Development Tools
HOT_RELOAD=true
SOURCE_MAPS=true

# =============================================================================
# FEATURE FLAGS (Development)
# =============================================================================
# Feature Toggles (enabled for development testing)
FEATURE_LIVE_STREAMING=true
FEATURE_AI_INTEGRATION=false
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_VIDEO_SECURITY=false
FEATURE_MOBILE_APP=false
FEATURE_BLOCKCHAIN_CERTIFICATES=false

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
TIMEZONE=UTC
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en

# =============================================================================
# CUSTOM CONFIGURATION (Development)
# =============================================================================
# Organization Settings
DEFAULT_ORG_NAME=ClassroomIO Development
DEFAULT_ORG_LOGO=
DEFAULT_ORG_THEME=blue

# Branding
CUSTOM_BRANDING_ENABLED=false
CUSTOM_LOGO_URL=
CUSTOM_FAVICON_URL=
CUSTOM_PRIMARY_COLOR=#3B82F6
