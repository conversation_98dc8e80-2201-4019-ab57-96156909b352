import {
  SvelteComponentDev,
  add_location,
  append_hydration_dev,
  assign,
  attr_dev,
  children,
  claim_svg_element,
  claim_text,
  compute_rest_props,
  detach_dev,
  dispatch_dev,
  exclude_internal_props,
  get_spread_update,
  init,
  insert_hydration_dev,
  noop,
  safe_not_equal,
  set_data_dev,
  set_svg_attributes,
  svg_element,
  text,
  validate_slots
} from "./chunk-E4ZC5ETH.js";

// ../../node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/Sun.svelte
var file = "C:/Users/<USER>/Downloads/New folder (2)/classroomio/node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/Sun.svelte";
function create_if_block(ctx) {
  let title_1;
  let t;
  const block = {
    c: function create() {
      title_1 = svg_element("title");
      t = text(
        /*title*/
        ctx[1]
      );
      this.h();
    },
    l: function claim(nodes) {
      title_1 = claim_svg_element(nodes, "title", {});
      var title_1_nodes = children(title_1);
      t = claim_text(
        title_1_nodes,
        /*title*/
        ctx[1]
      );
      title_1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      add_location(title_1, file, 22, 13, 543);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, title_1, anchor);
      append_hydration_dev(title_1, t);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*title*/
      2)
        set_data_dev(
          t,
          /*title*/
          ctx2[1]
        );
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(title_1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block.name,
    type: "if",
    source: "(23:2) {#if title}",
    ctx
  });
  return block;
}
function create_fragment(ctx) {
  let svg;
  let path0;
  let path1;
  let path2;
  let path3;
  let path4;
  let path5;
  let path6;
  let path7;
  let path8;
  let if_block = (
    /*title*/
    ctx[1] && create_if_block(ctx)
  );
  let svg_levels = [
    { xmlns: "http://www.w3.org/2000/svg" },
    { viewBox: "0 0 32 32" },
    { fill: "currentColor" },
    { preserveAspectRatio: "xMidYMid meet" },
    { width: (
      /*size*/
      ctx[0]
    ) },
    { height: (
      /*size*/
      ctx[0]
    ) },
    /*attributes*/
    ctx[2],
    /*$$restProps*/
    ctx[3]
  ];
  let svg_data = {};
  for (let i = 0; i < svg_levels.length; i += 1) {
    svg_data = assign(svg_data, svg_levels[i]);
  }
  const block = {
    c: function create() {
      svg = svg_element("svg");
      if (if_block)
        if_block.c();
      path0 = svg_element("path");
      path1 = svg_element("path");
      path2 = svg_element("path");
      path3 = svg_element("path");
      path4 = svg_element("path");
      path5 = svg_element("path");
      path6 = svg_element("path");
      path7 = svg_element("path");
      path8 = svg_element("path");
      this.h();
    },
    l: function claim(nodes) {
      svg = claim_svg_element(nodes, "svg", {
        xmlns: true,
        viewBox: true,
        fill: true,
        preserveAspectRatio: true,
        width: true,
        height: true
      });
      var svg_nodes = children(svg);
      if (if_block)
        if_block.l(svg_nodes);
      path0 = claim_svg_element(svg_nodes, "path", { d: true, transform: true });
      children(path0).forEach(detach_dev);
      path1 = claim_svg_element(svg_nodes, "path", { d: true, transform: true });
      children(path1).forEach(detach_dev);
      path2 = claim_svg_element(svg_nodes, "path", { d: true });
      children(path2).forEach(detach_dev);
      path3 = claim_svg_element(svg_nodes, "path", { d: true, transform: true });
      children(path3).forEach(detach_dev);
      path4 = claim_svg_element(svg_nodes, "path", { d: true });
      children(path4).forEach(detach_dev);
      path5 = claim_svg_element(svg_nodes, "path", { d: true, transform: true });
      children(path5).forEach(detach_dev);
      path6 = claim_svg_element(svg_nodes, "path", { d: true });
      children(path6).forEach(detach_dev);
      path7 = claim_svg_element(svg_nodes, "path", { d: true, transform: true });
      children(path7).forEach(detach_dev);
      path8 = claim_svg_element(svg_nodes, "path", { d: true });
      children(path8).forEach(detach_dev);
      svg_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(path0, "d", "M16,12a4,4,0,1,1-4,4,4.0045,4.0045,0,0,1,4-4m0-2a6,6,0,1,0,6,6,6,6,0,0,0-6-6Z");
      attr_dev(path0, "transform", "translate(0 .005)");
      add_location(path0, file, 23, 2, 573);
      attr_dev(path1, "d", "M6.854 5.375H8.854V10.333H6.854z");
      attr_dev(path1, "transform", "rotate(-45 7.86 7.856)");
      add_location(path1, file, 23, 127, 698);
      attr_dev(path2, "d", "M2 15.005H7V17.005000000000003H2z");
      add_location(path2, file, 23, 212, 783);
      attr_dev(path3, "d", "M5.375 23.147H10.333V25.147H5.375z");
      attr_dev(path3, "transform", "rotate(-45 7.86 24.149)");
      add_location(path3, file, 23, 263, 834);
      attr_dev(path4, "d", "M15 25.005H17V30.005H15z");
      add_location(path4, file, 23, 351, 922);
      attr_dev(path5, "d", "M23.147 21.668H25.147V26.625999999999998H23.147z");
      attr_dev(path5, "transform", "rotate(-45 24.152 24.149)");
      add_location(path5, file, 23, 393, 964);
      attr_dev(path6, "d", "M25 15.005H30V17.005000000000003H25z");
      add_location(path6, file, 23, 497, 1068);
      attr_dev(path7, "d", "M21.668 6.854H26.625999999999998V8.854H21.668z");
      attr_dev(path7, "transform", "rotate(-45 24.152 7.856)");
      add_location(path7, file, 23, 551, 1122);
      attr_dev(path8, "d", "M15 2.005H17V7.005H15z");
      add_location(path8, file, 23, 652, 1223);
      set_svg_attributes(svg, svg_data);
      add_location(svg, file, 13, 0, 337);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, svg, anchor);
      if (if_block)
        if_block.m(svg, null);
      append_hydration_dev(svg, path0);
      append_hydration_dev(svg, path1);
      append_hydration_dev(svg, path2);
      append_hydration_dev(svg, path3);
      append_hydration_dev(svg, path4);
      append_hydration_dev(svg, path5);
      append_hydration_dev(svg, path6);
      append_hydration_dev(svg, path7);
      append_hydration_dev(svg, path8);
    },
    p: function update(ctx2, [dirty]) {
      if (
        /*title*/
        ctx2[1]
      ) {
        if (if_block) {
          if_block.p(ctx2, dirty);
        } else {
          if_block = create_if_block(ctx2);
          if_block.c();
          if_block.m(svg, path0);
        }
      } else if (if_block) {
        if_block.d(1);
        if_block = null;
      }
      set_svg_attributes(svg, svg_data = get_spread_update(svg_levels, [
        { xmlns: "http://www.w3.org/2000/svg" },
        { viewBox: "0 0 32 32" },
        { fill: "currentColor" },
        { preserveAspectRatio: "xMidYMid meet" },
        dirty & /*size*/
        1 && { width: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*size*/
        1 && { height: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*attributes*/
        4 && /*attributes*/
        ctx2[2],
        dirty & /*$$restProps*/
        8 && /*$$restProps*/
        ctx2[3]
      ]));
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(svg);
      }
      if (if_block)
        if_block.d();
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance($$self, $$props, $$invalidate) {
  let labelled;
  let attributes;
  const omit_props_names = ["size", "title"];
  let $$restProps = compute_rest_props($$props, omit_props_names);
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Sun", slots, []);
  let { size = 16 } = $$props;
  let { title = void 0 } = $$props;
  $$self.$$set = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    $$invalidate(3, $$restProps = compute_rest_props($$props, omit_props_names));
    if ("size" in $$new_props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$new_props)
      $$invalidate(1, title = $$new_props.title);
  };
  $$self.$capture_state = () => ({ size, title, labelled, attributes });
  $$self.$inject_state = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), $$new_props));
    if ("size" in $$props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$props)
      $$invalidate(1, title = $$new_props.title);
    if ("labelled" in $$props)
      $$invalidate(4, labelled = $$new_props.labelled);
    if ("attributes" in $$props)
      $$invalidate(2, attributes = $$new_props.attributes);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    $:
      $$invalidate(4, labelled = $$props["aria-label"] || $$props["aria-labelledby"] || title);
    $:
      $$invalidate(2, attributes = {
        "aria-hidden": labelled ? void 0 : true,
        role: labelled ? "img" : void 0,
        focusable: Number($$props["tabindex"]) === 0 ? true : void 0
      });
  };
  $$props = exclude_internal_props($$props);
  return [size, title, attributes, $$restProps, labelled];
}
var Sun = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance, create_fragment, safe_not_equal, { size: 0, title: 1 });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Sun",
      options,
      id: create_fragment.name
    });
  }
  get size() {
    throw new Error("<Sun>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Sun>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get title() {
    throw new Error("<Sun>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set title(value) {
    throw new Error("<Sun>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Sun_default = Sun;

export {
  Sun_default
};
//# sourceMappingURL=chunk-KNNNUGPQ.js.map
