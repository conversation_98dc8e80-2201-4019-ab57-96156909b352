@echo off
setlocal enabledelayedexpansion

:: ClassroomIO Development Environment Startup Script
:: Automates the complete development setup and startup process

title ClassroomIO Development Startup

:: Debug mode - set to 1 for verbose output
set "DEBUG_MODE=1"

:: Configuration
set "PROJECT_NAME=ClassroomIO"
set "DEV_PORT=5173"
set "SUPABASE_PORT=54321"
set "REQUIRED_NODE_VERSION=18"

:: Initialize error tracking
set "SCRIPT_ERROR=0"

:: Simple color output (compatible with all Windows versions)
echo ========================================
echo    %PROJECT_NAME% Development Startup
echo ========================================
echo.

:: Debug output
if "%DEBUG_MODE%"=="1" (
    echo [DEBUG] Script started successfully
    echo [DEBUG] Current directory: %CD%
    echo [DEBUG] Windows version: %OS%
    echo.
)

:: Step 1: Check Prerequisites
echo [1/7] Checking Prerequisites...
call :check_prerequisites
if errorlevel 1 (
    echo ERROR: Prerequisites check failed!
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo SUCCESS: Prerequisites check passed
echo.

:: Step 2: Environment Setup
echo [2/7] Setting up Environment...
call :setup_environment
if errorlevel 1 (
    echo ERROR: Environment setup failed!
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo SUCCESS: Environment setup completed
echo.

:: Step 3: Check Package Manager
echo [3/7] Detecting Package Manager...
call :detect_package_manager
if errorlevel 1 (
    echo ERROR: No package manager found!
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo SUCCESS: Using package manager: %PACKAGE_MANAGER%
echo.

:: Step 4: Install Dependencies
echo [4/7] Installing Dependencies...
call :install_dependencies
if errorlevel 1 (
    echo ERROR: Dependency installation failed!
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo SUCCESS: Dependencies installed successfully
echo.

:: Step 5: Check and Start Services
echo [5/7] Checking Local Services...
call :check_services
echo SUCCESS: Services check completed
echo.

:: Step 6: Start Development Server
echo [6/7] Starting Development Server...
call :start_dev_server
if errorlevel 1 (
    echo ERROR: Failed to start development server!
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

:: Step 7: Open Browser (Optional)
echo [7/7] Opening Browser...
call :open_browser

echo.
echo SUCCESS: %PROJECT_NAME% development environment is ready!
echo Application URL: http://localhost:%DEV_PORT%
echo Health Check: http://localhost:%DEV_PORT%/health
echo API Health: http://localhost:%DEV_PORT%/api/health
echo.
echo Press Ctrl+C to stop the development server
echo Press any key to exit this script (server will continue running)
pause >nul
exit /b 0

:: ============================================================================
:: FUNCTIONS
:: ============================================================================

:check_prerequisites
    echo   Checking Node.js installation...

    if "%DEBUG_MODE%"=="1" echo [DEBUG] Testing node command...
    node --version >nul 2>&1
    if errorlevel 1 (
        echo   ERROR: Node.js is not installed or not in PATH
        echo   Please install Node.js %REQUIRED_NODE_VERSION%+ from https://nodejs.org
        exit /b 1
    )

    if "%DEBUG_MODE%"=="1" echo [DEBUG] Node.js found, checking version...

    :: Get Node.js version (simplified approach)
    for /f "tokens=1" %%a in ('node --version') do set "NODE_VERSION=%%a"
    echo   Node.js version: %NODE_VERSION%

    :: Check if we're in the correct directory
    if "%DEBUG_MODE%"=="1" echo [DEBUG] Checking for package.json...
    if not exist "package.json" (
        echo   ERROR: package.json not found
        echo   Please run this script from the ClassroomIO root directory
        echo   Current directory: %CD%
        exit /b 1
    )

    echo   SUCCESS: Running from correct directory
    exit /b 0

:setup_environment
    echo   Checking environment configuration...

    if "%DEBUG_MODE%"=="1" echo [DEBUG] Checking for .env.local...
    if not exist ".env.local" (
        echo   WARNING: .env.local not found, creating from .env.example...

        if not exist ".env.example" (
            echo   ERROR: .env.example not found
            exit /b 1
        )

        copy ".env.example" ".env.local" >nul 2>&1
        if errorlevel 1 (
            echo   ERROR: Failed to create .env.local
            exit /b 1
        )

        echo   SUCCESS: Created .env.local from .env.example
    ) else (
        echo   SUCCESS: .env.local already exists
    )

    :: Create logs directory if it doesn't exist
    if not exist "logs" (
        if "%DEBUG_MODE%"=="1" echo [DEBUG] Creating logs directory...
        mkdir logs 2>nul
        echo   SUCCESS: Created logs directory
    )

    :: Create uploads directory if it doesn't exist
    if not exist "uploads" (
        if "%DEBUG_MODE%"=="1" echo [DEBUG] Creating uploads directory...
        mkdir uploads 2>nul
        echo   SUCCESS: Created uploads directory
    )

    exit /b 0

:detect_package_manager
    if "%DEBUG_MODE%"=="1" echo [DEBUG] Checking for pnpm...
    :: Check for pnpm first (preferred)
    pnpm --version >nul 2>&1
    if not errorlevel 1 (
        set "PACKAGE_MANAGER=pnpm"
        set "INSTALL_CMD=pnpm install"
        set "DEV_CMD=pnpm dev"
        set "SUPABASE_CMD=pnpm supabase"
        echo   Found pnpm package manager
        exit /b 0
    )

    if "%DEBUG_MODE%"=="1" echo [DEBUG] pnpm not found, checking for npm...
    :: Fallback to npm
    npm --version >nul 2>&1
    if not errorlevel 1 (
        set "PACKAGE_MANAGER=npm"
        set "INSTALL_CMD=npm install"
        set "DEV_CMD=npm run dev"
        set "SUPABASE_CMD=npx supabase"
        echo   Found npm package manager
        exit /b 0
    )

    echo   ERROR: No package manager found (npm or pnpm required)
    echo   Please install Node.js from https://nodejs.org (includes npm)
    echo   Or install pnpm: npm install -g pnpm
    exit /b 1

:install_dependencies
    echo   Installing dependencies with !PACKAGE_MANAGER!...
    
    :: Check if node_modules exists and is recent
    if exist "node_modules" (
        echo %YELLOW%   ⚠️ node_modules exists, checking if update needed...%RESET%
        
        :: Compare package.json and lock file timestamps (simplified check)
        if exist "pnpm-lock.yaml" (
            for %%i in (package.json) do set "pkg_time=%%~ti"
            for %%i in (pnpm-lock.yaml) do set "lock_time=%%~ti"
        ) else if exist "package-lock.json" (
            for %%i in (package.json) do set "pkg_time=%%~ti"
            for %%i in (package-lock.json) do set "lock_time=%%~ti"
        )
    )
    
    echo   Running: !INSTALL_CMD!
    !INSTALL_CMD!
    if !errorlevel! neq 0 (
        echo %RED%   ❌ Failed to install dependencies%RESET%
        echo %YELLOW%   Try running: !PACKAGE_MANAGER! install --force%RESET%
        exit /b 1
    )
    
    exit /b 0

:check_services
    echo   Checking for local services...
    
    :: Check if Supabase CLI is available
    !SUPABASE_CMD! --version >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%   ✅ Supabase CLI available%RESET%
        
        :: Check if Supabase is already running
        netstat -an | findstr ":54321" >nul 2>&1
        if !errorlevel! equ 0 (
            echo %GREEN%   ✅ Supabase appears to be running on port 54321%RESET%
        ) else (
            echo %YELLOW%   ⚠️ Supabase not running. You may want to start it manually:%RESET%
            echo %YELLOW%     !SUPABASE_CMD! start%RESET%
        )
    ) else (
        echo %YELLOW%   ⚠️ Supabase CLI not available (optional for development)%RESET%
    )
    
    :: Check if development port is available
    netstat -an | findstr ":%DEV_PORT%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %YELLOW%   ⚠️ Port %DEV_PORT% appears to be in use%RESET%
        echo %YELLOW%   The development server may use a different port%RESET%
    ) else (
        echo %GREEN%   ✅ Port %DEV_PORT% is available%RESET%
    )
    
    exit /b 0

:start_dev_server
    echo   Starting development server...
    echo %CYAN%   Command: !DEV_CMD!%RESET%
    echo %CYAN%   This will start the server on http://localhost:%DEV_PORT%%RESET%
    echo.
    
    :: Start the development server
    start "ClassroomIO Dev Server" cmd /k "!DEV_CMD!"
    
    :: Wait a moment for the server to start
    echo   Waiting for server to start...
    timeout /t 5 /nobreak >nul
    
    :: Check if the server is responding
    set "retry_count=0"
    :check_server
    if !retry_count! gtr 30 (
        echo %RED%   ❌ Server failed to start within 30 seconds%RESET%
        exit /b 1
    )
    
    :: Try to connect to the health endpoint
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:%DEV_PORT%/health' -TimeoutSec 2 -UseBasicParsing; exit 0 } catch { exit 1 }" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%   ✅ Development server is running and responding%RESET%
        exit /b 0
    )
    
    set /a retry_count+=1
    timeout /t 1 /nobreak >nul
    goto check_server

:open_browser
    echo   Opening browser...
    
    :: Wait a moment to ensure server is fully ready
    timeout /t 2 /nobreak >nul
    
    :: Open the default browser
    start http://localhost:%DEV_PORT%
    
    echo %GREEN%   ✅ Browser opened%RESET%
    exit /b 0

:: End of script
