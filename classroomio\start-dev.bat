@echo off
setlocal enabledelayedexpansion

:: ClassroomIO Development Environment Startup Script
:: Automates the complete development setup and startup process

title ClassroomIO Development Startup

:: Colors for output (using Windows color codes)
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "RESET=[0m"

:: Configuration
set "PROJECT_NAME=ClassroomIO"
set "DEV_PORT=5173"
set "SUPABASE_PORT=54321"
set "REQUIRED_NODE_VERSION=18"

echo %CYAN%========================================%RESET%
echo %CYAN%   %PROJECT_NAME% Development Startup   %RESET%
echo %CYAN%========================================%RESET%
echo.

:: Step 1: Check Prerequisites
echo %BLUE%[1/7] Checking Prerequisites...%RESET%
call :check_prerequisites
if !errorlevel! neq 0 (
    echo %RED%❌ Prerequisites check failed!%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ Prerequisites check passed%RESET%
echo.

:: Step 2: Environment Setup
echo %BLUE%[2/7] Setting up Environment...%RESET%
call :setup_environment
if !errorlevel! neq 0 (
    echo %RED%❌ Environment setup failed!%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ Environment setup completed%RESET%
echo.

:: Step 3: Check Package Manager
echo %BLUE%[3/7] Detecting Package Manager...%RESET%
call :detect_package_manager
echo %GREEN%✅ Using package manager: !PACKAGE_MANAGER!%RESET%
echo.

:: Step 4: Install Dependencies
echo %BLUE%[4/7] Installing Dependencies...%RESET%
call :install_dependencies
if !errorlevel! neq 0 (
    echo %RED%❌ Dependency installation failed!%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ Dependencies installed successfully%RESET%
echo.

:: Step 5: Check and Start Services
echo %BLUE%[5/7] Checking Local Services...%RESET%
call :check_services
echo %GREEN%✅ Services check completed%RESET%
echo.

:: Step 6: Start Development Server
echo %BLUE%[6/7] Starting Development Server...%RESET%
call :start_dev_server
if !errorlevel! neq 0 (
    echo %RED%❌ Failed to start development server!%RESET%
    pause
    exit /b 1
)

:: Step 7: Open Browser (Optional)
echo %BLUE%[7/7] Opening Browser...%RESET%
call :open_browser

echo.
echo %GREEN%🎉 %PROJECT_NAME% development environment is ready!%RESET%
echo %CYAN%📱 Application URL: http://localhost:%DEV_PORT%%RESET%
echo %CYAN%🔍 Health Check: http://localhost:%DEV_PORT%/health%RESET%
echo %CYAN%🔧 API Health: http://localhost:%DEV_PORT%/api/health%RESET%
echo.
echo %YELLOW%Press Ctrl+C to stop the development server%RESET%
echo %YELLOW%Press any key to exit this script (server will continue running)%RESET%
pause >nul
exit /b 0

:: ============================================================================
:: FUNCTIONS
:: ============================================================================

:check_prerequisites
    echo   Checking Node.js installation...
    node --version >nul 2>&1
    if !errorlevel! neq 0 (
        echo %RED%   ❌ Node.js is not installed or not in PATH%RESET%
        echo %YELLOW%   Please install Node.js %REQUIRED_NODE_VERSION%+ from https://nodejs.org%RESET%
        exit /b 1
    )
    
    :: Get Node.js version
    for /f "tokens=1 delims=." %%a in ('node --version') do (
        set "NODE_MAJOR=%%a"
        set "NODE_MAJOR=!NODE_MAJOR:v=!"
    )
    
    if !NODE_MAJOR! lss %REQUIRED_NODE_VERSION% (
        echo %RED%   ❌ Node.js version !NODE_MAJOR! is too old (requires %REQUIRED_NODE_VERSION%+)%RESET%
        exit /b 1
    )
    
    echo %GREEN%   ✅ Node.js version: %RESET%
    node --version
    
    :: Check if we're in the correct directory
    if not exist "package.json" (
        echo %RED%   ❌ package.json not found. Please run this script from the ClassroomIO root directory%RESET%
        exit /b 1
    )
    
    echo %GREEN%   ✅ Running from correct directory%RESET%
    exit /b 0

:setup_environment
    echo   Checking environment configuration...
    
    if not exist ".env.local" (
        echo %YELLOW%   ⚠️ .env.local not found, creating from .env.example...%RESET%
        
        if not exist ".env.example" (
            echo %RED%   ❌ .env.example not found%RESET%
            exit /b 1
        )
        
        copy ".env.example" ".env.local" >nul
        if !errorlevel! neq 0 (
            echo %RED%   ❌ Failed to create .env.local%RESET%
            exit /b 1
        )
        
        echo %GREEN%   ✅ Created .env.local from .env.example%RESET%
    ) else (
        echo %GREEN%   ✅ .env.local already exists%RESET%
    )
    
    :: Create logs directory if it doesn't exist
    if not exist "logs" (
        mkdir logs
        echo %GREEN%   ✅ Created logs directory%RESET%
    )
    
    :: Create uploads directory if it doesn't exist
    if not exist "uploads" (
        mkdir uploads
        echo %GREEN%   ✅ Created uploads directory%RESET%
    )
    
    exit /b 0

:detect_package_manager
    :: Check for pnpm first (preferred)
    pnpm --version >nul 2>&1
    if !errorlevel! equ 0 (
        set "PACKAGE_MANAGER=pnpm"
        set "INSTALL_CMD=pnpm install"
        set "DEV_CMD=pnpm dev"
        set "SUPABASE_CMD=pnpm supabase"
        exit /b 0
    )
    
    :: Fallback to npm
    npm --version >nul 2>&1
    if !errorlevel! equ 0 (
        set "PACKAGE_MANAGER=npm"
        set "INSTALL_CMD=npm install"
        set "DEV_CMD=npm run dev"
        set "SUPABASE_CMD=npx supabase"
        exit /b 0
    )
    
    echo %RED%   ❌ No package manager found (npm or pnpm required)%RESET%
    exit /b 1

:install_dependencies
    echo   Installing dependencies with !PACKAGE_MANAGER!...
    
    :: Check if node_modules exists and is recent
    if exist "node_modules" (
        echo %YELLOW%   ⚠️ node_modules exists, checking if update needed...%RESET%
        
        :: Compare package.json and lock file timestamps (simplified check)
        if exist "pnpm-lock.yaml" (
            for %%i in (package.json) do set "pkg_time=%%~ti"
            for %%i in (pnpm-lock.yaml) do set "lock_time=%%~ti"
        ) else if exist "package-lock.json" (
            for %%i in (package.json) do set "pkg_time=%%~ti"
            for %%i in (package-lock.json) do set "lock_time=%%~ti"
        )
    )
    
    echo   Running: !INSTALL_CMD!
    !INSTALL_CMD!
    if !errorlevel! neq 0 (
        echo %RED%   ❌ Failed to install dependencies%RESET%
        echo %YELLOW%   Try running: !PACKAGE_MANAGER! install --force%RESET%
        exit /b 1
    )
    
    exit /b 0

:check_services
    echo   Checking for local services...
    
    :: Check if Supabase CLI is available
    !SUPABASE_CMD! --version >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%   ✅ Supabase CLI available%RESET%
        
        :: Check if Supabase is already running
        netstat -an | findstr ":54321" >nul 2>&1
        if !errorlevel! equ 0 (
            echo %GREEN%   ✅ Supabase appears to be running on port 54321%RESET%
        ) else (
            echo %YELLOW%   ⚠️ Supabase not running. You may want to start it manually:%RESET%
            echo %YELLOW%     !SUPABASE_CMD! start%RESET%
        )
    ) else (
        echo %YELLOW%   ⚠️ Supabase CLI not available (optional for development)%RESET%
    )
    
    :: Check if development port is available
    netstat -an | findstr ":%DEV_PORT%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %YELLOW%   ⚠️ Port %DEV_PORT% appears to be in use%RESET%
        echo %YELLOW%   The development server may use a different port%RESET%
    ) else (
        echo %GREEN%   ✅ Port %DEV_PORT% is available%RESET%
    )
    
    exit /b 0

:start_dev_server
    echo   Starting development server...
    echo %CYAN%   Command: !DEV_CMD!%RESET%
    echo %CYAN%   This will start the server on http://localhost:%DEV_PORT%%RESET%
    echo.
    
    :: Start the development server
    start "ClassroomIO Dev Server" cmd /k "!DEV_CMD!"
    
    :: Wait a moment for the server to start
    echo   Waiting for server to start...
    timeout /t 5 /nobreak >nul
    
    :: Check if the server is responding
    set "retry_count=0"
    :check_server
    if !retry_count! gtr 30 (
        echo %RED%   ❌ Server failed to start within 30 seconds%RESET%
        exit /b 1
    )
    
    :: Try to connect to the health endpoint
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:%DEV_PORT%/health' -TimeoutSec 2 -UseBasicParsing; exit 0 } catch { exit 1 }" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%   ✅ Development server is running and responding%RESET%
        exit /b 0
    )
    
    set /a retry_count+=1
    timeout /t 1 /nobreak >nul
    goto check_server

:open_browser
    echo   Opening browser...
    
    :: Wait a moment to ensure server is fully ready
    timeout /t 2 /nobreak >nul
    
    :: Open the default browser
    start http://localhost:%DEV_PORT%
    
    echo %GREEN%   ✅ Browser opened%RESET%
    exit /b 0

:: End of script
