// Authentication E2E Tests
import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the login page
    await page.goto('/login');
  });

  test('should display login form', async ({ page }) => {
    // Check if login form elements are present
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
    
    // Check page title
    await expect(page).toHaveTitle(/Login.*ClassroomIO/);
  });

  test('should show validation errors for empty form', async ({ page }) => {
    // Click login button without filling form
    await page.click('[data-testid="login-button"]');
    
    // Check for validation errors
    await expect(page.locator('[data-testid="email-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    // Fill form with invalid credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="login-button"]');
    
    // Check for error message
    await expect(page.locator('[data-testid="login-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="login-error"]')).toContainText('Invalid credentials');
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    // Fill form with valid credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'TestPassword123!');
    await page.click('[data-testid="login-button"]');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Check if user is logged in
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    await expect(page.locator('[data-testid="logout-button"]')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // Login first
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'TestPassword123!');
    await page.click('[data-testid="login-button"]');
    
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*\/login/);
    
    // Check if user is logged out
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
  });

  test('should redirect to login when accessing protected route', async ({ page }) => {
    // Try to access dashboard without login
    await page.goto('/dashboard');
    
    // Should redirect to login
    await expect(page).toHaveURL(/.*\/login/);
    
    // Check for redirect message
    await expect(page.locator('[data-testid="redirect-message"]')).toBeVisible();
  });

  test('should remember user after page refresh', async ({ page }) => {
    // Login
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'TestPassword123!');
    await page.click('[data-testid="login-button"]');
    
    await expect(page).toHaveURL(/.*\/dashboard/);
    
    // Refresh page
    await page.reload();
    
    // Should still be logged in
    await expect(page).toHaveURL(/.*\/dashboard/);
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });
});

test.describe('Registration Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/register');
  });

  test('should display registration form', async ({ page }) => {
    // Check if registration form elements are present
    await expect(page.locator('[data-testid="fullname-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="confirm-password-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="register-button"]')).toBeVisible();
  });

  test('should validate password confirmation', async ({ page }) => {
    // Fill form with mismatched passwords
    await page.fill('[data-testid="fullname-input"]', 'Test User');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'Password123!');
    await page.fill('[data-testid="confirm-password-input"]', 'DifferentPassword123!');
    await page.click('[data-testid="register-button"]');
    
    // Check for password mismatch error
    await expect(page.locator('[data-testid="confirm-password-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="confirm-password-error"]')).toContainText('Passwords do not match');
  });

  test('should register new user successfully', async ({ page }) => {
    // Fill registration form
    const timestamp = Date.now();
    await page.fill('[data-testid="fullname-input"]', 'New Test User');
    await page.fill('[data-testid="email-input"]', `newuser${timestamp}@example.com`);
    await page.fill('[data-testid="password-input"]', 'NewPassword123!');
    await page.fill('[data-testid="confirm-password-input"]', 'NewPassword123!');
    await page.click('[data-testid="register-button"]');
    
    // Should redirect to verification page or dashboard
    await expect(page).toHaveURL(/.*\/(verify|dashboard)/);
    
    // Check for success message
    await expect(page.locator('[data-testid="registration-success"]')).toBeVisible();
  });
});

test.describe('Password Reset Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/forgot-password');
  });

  test('should display forgot password form', async ({ page }) => {
    await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="reset-button"]')).toBeVisible();
  });

  test('should send reset email for valid email', async ({ page }) => {
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.click('[data-testid="reset-button"]');
    
    // Check for success message
    await expect(page.locator('[data-testid="reset-success"]')).toBeVisible();
    await expect(page.locator('[data-testid="reset-success"]')).toContainText('Reset email sent');
  });

  test('should show error for invalid email', async ({ page }) => {
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.click('[data-testid="reset-button"]');
    
    // Check for error message
    await expect(page.locator('[data-testid="reset-error"]')).toBeVisible();
  });
});
