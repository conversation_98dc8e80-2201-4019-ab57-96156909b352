// Security Services for Educational Platform
// Date: 2025-06-30

import { supabase } from '$lib/utils/functions/supabase';
import type { 
  SecurityEvent, 
  EnhancedDeviceSession, 
  VideoAccessToken, 
  DRMKey,
  SecurityPolicy,
  ExamSession,
  WatermarkTemplate,
  DeviceFingerprint,
  SecurityMetrics
} from '$lib/utils/types/security';

// Security Event Service
export const securityEventService = {
  // Log security event
  async logEvent(event: Omit<SecurityEvent, 'id' | 'created_at'>): Promise<string> {
    const { data, error } = await supabase
      .rpc('log_security_event', {
        p_event_type: event.event_type,
        p_severity: event.severity,
        p_user_id: event.user_id,
        p_session_id: event.session_id,
        p_device_fingerprint: event.device_fingerprint,
        p_ip_address: event.ip_address,
        p_user_agent: event.user_agent,
        p_event_data: event.event_data,
        p_location: event.location
      });

    if (error) throw error;
    return data;
  },

  // Get security events with filters
  async getEvents(filters: {
    user_id?: string;
    event_type?: string;
    severity?: string;
    start_date?: string;
    end_date?: string;
    limit?: number;
    offset?: number;
  }): Promise<SecurityEvent[]> {
    let query = supabase
      .from('security_events')
      .select('*');

    if (filters.user_id) query = query.eq('user_id', filters.user_id);
    if (filters.event_type) query = query.eq('event_type', filters.event_type);
    if (filters.severity) query = query.eq('severity', filters.severity);
    if (filters.start_date) query = query.gte('created_at', filters.start_date);
    if (filters.end_date) query = query.lte('created_at', filters.end_date);

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(filters.limit || 100)
      .range(filters.offset || 0, (filters.offset || 0) + (filters.limit || 100) - 1);

    if (error) throw error;
    return data || [];
  },

  // Get security metrics
  async getMetrics(organizationId: string, timeRange: '24h' | '7d' | '30d' = '24h'): Promise<SecurityMetrics> {
    const now = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case '24h':
        startDate.setHours(now.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
    }

    const { data: events, error } = await supabase
      .from('security_events')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Calculate metrics
    const totalEvents = events?.length || 0;
    const eventsBySeverity = events?.reduce((acc, event) => {
      acc[event.severity] = (acc[event.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const eventsByType = events?.reduce((acc, event) => {
      acc[event.event_type] = (acc[event.event_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const blockedAttempts = events?.filter(e => e.is_blocked).length || 0;
    const criticalEvents = events?.filter(e => e.severity === 'critical').length || 0;

    // Calculate security score (0-100)
    const securityScore = Math.max(0, 100 - (criticalEvents * 10) - (blockedAttempts * 5));

    return {
      total_events: totalEvents,
      events_by_severity: eventsBySeverity,
      events_by_type: eventsByType,
      blocked_attempts: blockedAttempts,
      active_sessions: 0, // Would need to query active sessions
      suspicious_activities: events?.filter(e => e.event_type === 'suspicious_activity').length || 0,
      device_violations: events?.filter(e => e.event_type === 'device_change').length || 0,
      exam_violations: events?.filter(e => e.event_type.includes('exam')).length || 0,
      top_threats: Object.entries(eventsByType)
        .map(([type, count]) => ({
          type,
          count,
          last_occurrence: events?.find(e => e.event_type === type)?.created_at || ''
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5),
      security_score: securityScore,
      trends: {
        daily_events: [], // Would need more complex aggregation
        weekly_summary: {
          total_events: totalEvents,
          critical_events: criticalEvents,
          blocked_attempts: blockedAttempts
        }
      }
    };
  }
};

// Device Management Service
export const deviceManagementService = {
  // Generate device fingerprint
  generateFingerprint(): DeviceFingerprint {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx?.fillText('Device fingerprint', 10, 10);
    
    return {
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      color_depth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      platform: navigator.platform,
      cookie_enabled: navigator.cookieEnabled,
      do_not_track: navigator.doNotTrack === '1',
      device_memory: (navigator as any).deviceMemory,
      hardware_concurrency: navigator.hardwareConcurrency,
      max_touch_points: navigator.maxTouchPoints,
      canvas_fingerprint: canvas.toDataURL(),
      composite_hash: '', // Would be calculated from all values
      confidence_score: 0.95
    };
  },

  // Register new device
  async registerDevice(deviceData: Omit<EnhancedDeviceSession, 'id' | 'created_at' | 'updated_at'>): Promise<EnhancedDeviceSession> {
    const { data, error } = await supabase
      .from('device_session')
      .insert(deviceData)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get user devices
  async getUserDevices(userId: string): Promise<EnhancedDeviceSession[]> {
    const { data, error } = await supabase
      .from('device_session')
      .select('*')
      .eq('profile_id', userId)
      .order('last_activity', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Approve device
  async approveDevice(deviceId: string, approvedBy: string): Promise<void> {
    const { error } = await supabase
      .from('device_session')
      .update({
        is_approved: true,
        approved_by: approvedBy,
        approved_at: new Date().toISOString(),
        trust_score: 100
      })
      .eq('id', deviceId);

    if (error) throw error;
  },

  // Block device
  async blockDevice(deviceId: string, reason: string): Promise<void> {
    const { error } = await supabase
      .from('device_session')
      .update({
        is_active: false,
        trust_score: 0
      })
      .eq('id', deviceId);

    if (error) throw error;

    // Log security event
    await securityEventService.logEvent({
      event_type: 'device_blocked',
      severity: 'high',
      user_id: undefined, // Would need to get from device record
      event_data: { device_id: deviceId, reason },
      location: {},
      is_blocked: true,
      response_action: 'device_blocked',
      metadata: {}
    });
  }
};

// Video Security Service
export const videoSecurityService = {
  // Generate secure video access token
  async generateAccessToken(videoId: string, userId: string, sessionId?: string): Promise<string> {
    const { data, error } = await supabase
      .rpc('generate_video_access_token', {
        p_video_id: videoId,
        p_user_id: userId,
        p_session_id: sessionId,
        p_expires_minutes: 60,
        p_max_uses: 3
      });

    if (error) throw error;
    return data;
  },

  // Validate video access token
  async validateAccessToken(
    token: string, 
    userId: string, 
    ipAddress?: string, 
    deviceFingerprint?: string
  ): Promise<boolean> {
    const { data, error } = await supabase
      .rpc('validate_video_access_token', {
        p_access_token: token,
        p_user_id: userId,
        p_ip_address: ipAddress,
        p_device_fingerprint: deviceFingerprint
      });

    if (error) throw error;
    return data;
  },

  // Get DRM keys for video
  async getDRMKeys(videoId: string): Promise<DRMKey[]> {
    const { data, error } = await supabase
      .from('drm_keys')
      .select('*')
      .eq('video_id', videoId)
      .eq('is_active', true);

    if (error) throw error;
    return data || [];
  },

  // Create DRM key
  async createDRMKey(drmKey: Omit<DRMKey, 'id' | 'created_at'>): Promise<DRMKey> {
    const { data, error } = await supabase
      .from('drm_keys')
      .insert(drmKey)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Revoke video access token
  async revokeAccessToken(tokenId: string, reason: string): Promise<void> {
    const { error } = await supabase
      .from('video_access_tokens')
      .update({
        is_revoked: true,
        revoked_at: new Date().toISOString(),
        revoked_reason: reason
      })
      .eq('id', tokenId);

    if (error) throw error;
  }
};

// Security Policy Service
export const securityPolicyService = {
  // Get organization security policies
  async getOrganizationPolicies(organizationId: string): Promise<SecurityPolicy[]> {
    const { data, error } = await supabase
      .from('security_policies')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('is_active', true);

    if (error) throw error;
    return data || [];
  },

  // Create security policy
  async createPolicy(policy: Omit<SecurityPolicy, 'id' | 'created_at' | 'updated_at'>): Promise<SecurityPolicy> {
    const { data, error } = await supabase
      .from('security_policies')
      .insert(policy)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update security policy
  async updatePolicy(policyId: string, updates: Partial<SecurityPolicy>): Promise<SecurityPolicy> {
    const { data, error } = await supabase
      .from('security_policies')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', policyId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get policy by type
  async getPolicyByType(organizationId: string, policyType: string): Promise<SecurityPolicy | null> {
    const { data, error } = await supabase
      .from('security_policies')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('policy_type', policyType)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }
};
