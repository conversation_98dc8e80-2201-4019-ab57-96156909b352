import {
  SvelteComponentDev,
  add_location,
  append_hydration_dev,
  assign,
  attr_dev,
  children,
  claim_svg_element,
  claim_text,
  compute_rest_props,
  detach_dev,
  dispatch_dev,
  exclude_internal_props,
  get_spread_update,
  init,
  insert_hydration_dev,
  noop,
  safe_not_equal,
  set_data_dev,
  set_svg_attributes,
  svg_element,
  text,
  validate_slots
} from "./chunk-E4ZC5ETH.js";

// ../../node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/Checkmark.svelte
var file = "C:/Users/<USER>/Downloads/New folder (2)/classroomio/node_modules/.pnpm/carbon-icons-svelte@12.17.0/node_modules/carbon-icons-svelte/lib/Checkmark.svelte";
function create_if_block(ctx) {
  let title_1;
  let t;
  const block = {
    c: function create() {
      title_1 = svg_element("title");
      t = text(
        /*title*/
        ctx[1]
      );
      this.h();
    },
    l: function claim(nodes) {
      title_1 = claim_svg_element(nodes, "title", {});
      var title_1_nodes = children(title_1);
      t = claim_text(
        title_1_nodes,
        /*title*/
        ctx[1]
      );
      title_1_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      add_location(title_1, file, 22, 13, 543);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, title_1, anchor);
      append_hydration_dev(title_1, t);
    },
    p: function update(ctx2, dirty) {
      if (dirty & /*title*/
      2)
        set_data_dev(
          t,
          /*title*/
          ctx2[1]
        );
    },
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(title_1);
      }
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_if_block.name,
    type: "if",
    source: "(23:2) {#if title}",
    ctx
  });
  return block;
}
function create_fragment(ctx) {
  let svg;
  let path;
  let if_block = (
    /*title*/
    ctx[1] && create_if_block(ctx)
  );
  let svg_levels = [
    { xmlns: "http://www.w3.org/2000/svg" },
    { viewBox: "0 0 32 32" },
    { fill: "currentColor" },
    { preserveAspectRatio: "xMidYMid meet" },
    { width: (
      /*size*/
      ctx[0]
    ) },
    { height: (
      /*size*/
      ctx[0]
    ) },
    /*attributes*/
    ctx[2],
    /*$$restProps*/
    ctx[3]
  ];
  let svg_data = {};
  for (let i = 0; i < svg_levels.length; i += 1) {
    svg_data = assign(svg_data, svg_levels[i]);
  }
  const block = {
    c: function create() {
      svg = svg_element("svg");
      if (if_block)
        if_block.c();
      path = svg_element("path");
      this.h();
    },
    l: function claim(nodes) {
      svg = claim_svg_element(nodes, "svg", {
        xmlns: true,
        viewBox: true,
        fill: true,
        preserveAspectRatio: true,
        width: true,
        height: true
      });
      var svg_nodes = children(svg);
      if (if_block)
        if_block.l(svg_nodes);
      path = claim_svg_element(svg_nodes, "path", { d: true });
      children(path).forEach(detach_dev);
      svg_nodes.forEach(detach_dev);
      this.h();
    },
    h: function hydrate() {
      attr_dev(path, "d", "M13 24L4 15 5.414 13.586 13 21.171 26.586 7.586 28 9 13 24z");
      add_location(path, file, 23, 2, 573);
      set_svg_attributes(svg, svg_data);
      add_location(svg, file, 13, 0, 337);
    },
    m: function mount(target, anchor) {
      insert_hydration_dev(target, svg, anchor);
      if (if_block)
        if_block.m(svg, null);
      append_hydration_dev(svg, path);
    },
    p: function update(ctx2, [dirty]) {
      if (
        /*title*/
        ctx2[1]
      ) {
        if (if_block) {
          if_block.p(ctx2, dirty);
        } else {
          if_block = create_if_block(ctx2);
          if_block.c();
          if_block.m(svg, path);
        }
      } else if (if_block) {
        if_block.d(1);
        if_block = null;
      }
      set_svg_attributes(svg, svg_data = get_spread_update(svg_levels, [
        { xmlns: "http://www.w3.org/2000/svg" },
        { viewBox: "0 0 32 32" },
        { fill: "currentColor" },
        { preserveAspectRatio: "xMidYMid meet" },
        dirty & /*size*/
        1 && { width: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*size*/
        1 && { height: (
          /*size*/
          ctx2[0]
        ) },
        dirty & /*attributes*/
        4 && /*attributes*/
        ctx2[2],
        dirty & /*$$restProps*/
        8 && /*$$restProps*/
        ctx2[3]
      ]));
    },
    i: noop,
    o: noop,
    d: function destroy(detaching) {
      if (detaching) {
        detach_dev(svg);
      }
      if (if_block)
        if_block.d();
    }
  };
  dispatch_dev("SvelteRegisterBlock", {
    block,
    id: create_fragment.name,
    type: "component",
    source: "",
    ctx
  });
  return block;
}
function instance($$self, $$props, $$invalidate) {
  let labelled;
  let attributes;
  const omit_props_names = ["size", "title"];
  let $$restProps = compute_rest_props($$props, omit_props_names);
  let { $$slots: slots = {}, $$scope } = $$props;
  validate_slots("Checkmark", slots, []);
  let { size = 16 } = $$props;
  let { title = void 0 } = $$props;
  $$self.$$set = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), exclude_internal_props($$new_props)));
    $$invalidate(3, $$restProps = compute_rest_props($$props, omit_props_names));
    if ("size" in $$new_props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$new_props)
      $$invalidate(1, title = $$new_props.title);
  };
  $$self.$capture_state = () => ({ size, title, labelled, attributes });
  $$self.$inject_state = ($$new_props) => {
    $$invalidate(5, $$props = assign(assign({}, $$props), $$new_props));
    if ("size" in $$props)
      $$invalidate(0, size = $$new_props.size);
    if ("title" in $$props)
      $$invalidate(1, title = $$new_props.title);
    if ("labelled" in $$props)
      $$invalidate(4, labelled = $$new_props.labelled);
    if ("attributes" in $$props)
      $$invalidate(2, attributes = $$new_props.attributes);
  };
  if ($$props && "$$inject" in $$props) {
    $$self.$inject_state($$props.$$inject);
  }
  $$self.$$.update = () => {
    $:
      $$invalidate(4, labelled = $$props["aria-label"] || $$props["aria-labelledby"] || title);
    $:
      $$invalidate(2, attributes = {
        "aria-hidden": labelled ? void 0 : true,
        role: labelled ? "img" : void 0,
        focusable: Number($$props["tabindex"]) === 0 ? true : void 0
      });
  };
  $$props = exclude_internal_props($$props);
  return [size, title, attributes, $$restProps, labelled];
}
var Checkmark = class extends SvelteComponentDev {
  constructor(options) {
    super(options);
    init(this, options, instance, create_fragment, safe_not_equal, { size: 0, title: 1 });
    dispatch_dev("SvelteRegisterComponent", {
      component: this,
      tagName: "Checkmark",
      options,
      id: create_fragment.name
    });
  }
  get size() {
    throw new Error("<Checkmark>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set size(value) {
    throw new Error("<Checkmark>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  get title() {
    throw new Error("<Checkmark>: Props cannot be read directly from the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
  set title(value) {
    throw new Error("<Checkmark>: Props cannot be set directly on the component instance unless compiling with 'accessors: true' or '<svelte:options accessors/>'");
  }
};
var Checkmark_default = Checkmark;

export {
  Checkmark_default
};
//# sourceMappingURL=chunk-WEG2Y67N.js.map
