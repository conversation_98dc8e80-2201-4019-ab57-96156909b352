// Integration Tests
// End-to-end integration tests for ClassroomIO platform

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import { 
  setupTest, 
  cleanupTest, 
  TestDataFactory, 
  TestUtils,
  PerformanceTestUtils
} from './test-setup';

describe('ClassroomIO Integration Tests', () => {
  beforeEach(() => {
    setupTest();
  });

  afterEach(() => {
    cleanupTest();
  });

  describe('User Authentication Flow', () => {
    it('should complete full authentication flow', async () => {
      const mockAuthService = {
        signIn: vi.fn().mockResolvedValue({
          user: TestDataFactory.createUser(),
          session: { access_token: 'test-token' }
        }),
        signOut: vi.fn().mockResolvedValue({}),
        getUser: vi.fn().mockResolvedValue(TestDataFactory.createUser())
      };

      vi.mock('$lib/utils/services/auth', () => ({
        authService: mockAuthService
      }));

      // Test sign in
      const credentials = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const result = await mockAuthService.signIn(credentials);
      expect(result.user).toBeDefined();
      expect(result.session.access_token).toBe('test-token');

      // Test user retrieval
      const user = await mockAuthService.getUser();
      expect(user.email).toBe('<EMAIL>');

      // Test sign out
      await mockAuthService.signOut();
      expect(mockAuthService.signOut).toHaveBeenCalled();
    });

    it('should handle authentication errors gracefully', async () => {
      const mockAuthService = {
        signIn: vi.fn().mockRejectedValue(new Error('Invalid credentials'))
      };

      vi.mock('$lib/utils/services/auth', () => ({
        authService: mockAuthService
      }));

      try {
        await mockAuthService.signIn({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
      } catch (error) {
        expect(error.message).toBe('Invalid credentials');
      }
    });
  });

  describe('Batch Management Integration', () => {
    it('should create and manage batch lifecycle', async () => {
      const mockBatchService = {
        createBatch: vi.fn().mockResolvedValue(TestDataFactory.createBatch()),
        getBatch: vi.fn().mockResolvedValue(TestDataFactory.createBatch()),
        updateBatch: vi.fn().mockResolvedValue(TestDataFactory.createBatch()),
        deleteBatch: vi.fn().mockResolvedValue({}),
        addStudentToBatch: vi.fn().mockResolvedValue({}),
        removeStudentFromBatch: vi.fn().mockResolvedValue({})
      };

      vi.mock('$lib/utils/services/batch', () => ({
        batchService: mockBatchService
      }));

      // Create batch
      const batchData = {
        name: 'Test Batch',
        description: 'Integration test batch',
        organization_id: 'test-org-id',
        start_date: '2024-01-01',
        end_date: '2024-12-31'
      };

      const createdBatch = await mockBatchService.createBatch(batchData);
      expect(createdBatch.name).toBe('Test Batch');

      // Get batch
      const retrievedBatch = await mockBatchService.getBatch(createdBatch.id);
      expect(retrievedBatch.id).toBe(createdBatch.id);

      // Add student to batch
      await mockBatchService.addStudentToBatch(createdBatch.id, 'student-id');
      expect(mockBatchService.addStudentToBatch).toHaveBeenCalledWith(
        createdBatch.id,
        'student-id'
      );

      // Update batch
      const updateData = { name: 'Updated Batch Name' };
      await mockBatchService.updateBatch(createdBatch.id, updateData);
      expect(mockBatchService.updateBatch).toHaveBeenCalledWith(
        createdBatch.id,
        updateData
      );

      // Remove student from batch
      await mockBatchService.removeStudentFromBatch(createdBatch.id, 'student-id');
      expect(mockBatchService.removeStudentFromBatch).toHaveBeenCalledWith(
        createdBatch.id,
        'student-id'
      );

      // Delete batch
      await mockBatchService.deleteBatch(createdBatch.id);
      expect(mockBatchService.deleteBatch).toHaveBeenCalledWith(createdBatch.id);
    });
  });

  describe('Video Learning Integration', () => {
    it('should complete video learning workflow', async () => {
      const mockVideoService = {
        getVideo: vi.fn().mockResolvedValue({
          id: 'video-1',
          title: 'Test Video',
          src: 'https://example.com/video.mp4',
          duration: 300
        }),
        trackProgress: vi.fn().mockResolvedValue({})
      };

      const mockAnalyticsService = {
        trackVideoAnalytics: vi.fn().mockResolvedValue({}),
        trackEvent: vi.fn().mockResolvedValue('event-id')
      };

      vi.mock('$lib/utils/services/video', () => ({
        videoService: mockVideoService
      }));

      vi.mock('$lib/utils/services/analytics', () => ({
        analyticsService: mockAnalyticsService
      }));

      // Get video
      const video = await mockVideoService.getVideo('video-1');
      expect(video.title).toBe('Test Video');

      // Simulate video play
      await mockAnalyticsService.trackEvent({
        event_type: 'video_play',
        event_category: 'learning',
        video_id: 'video-1',
        user_id: 'test-user-id'
      });

      // Track video progress
      await mockVideoService.trackProgress('video-1', {
        current_time: 150,
        completion_percentage: 50
      });

      // Track video completion
      await mockAnalyticsService.trackVideoAnalytics({
        video_id: 'video-1',
        user_id: 'test-user-id',
        completion_percentage: 100,
        total_watch_time_seconds: 300
      });

      expect(mockAnalyticsService.trackEvent).toHaveBeenCalled();
      expect(mockVideoService.trackProgress).toHaveBeenCalled();
      expect(mockAnalyticsService.trackVideoAnalytics).toHaveBeenCalled();
    });
  });

  describe('Communication System Integration', () => {
    it('should handle complete doubt resolution workflow', async () => {
      const mockDoubtService = {
        submitDoubt: vi.fn().mockResolvedValue({ id: 'doubt-1' }),
        getDoubts: vi.fn().mockResolvedValue([]),
        respondToDoubt: vi.fn().mockResolvedValue({ id: 'response-1' }),
        markAsResolved: vi.fn().mockResolvedValue({})
      };

      const mockAIService = {
        categorizeDoubt: vi.fn().mockResolvedValue({
          category: 'mathematics',
          confidence: 0.95
        }),
        findSimilarDoubts: vi.fn().mockResolvedValue([])
      };

      vi.mock('$lib/utils/services/doubt', () => ({
        doubtService: mockDoubtService
      }));

      vi.mock('$lib/utils/services/ai', () => ({
        aiService: mockAIService
      }));

      // Submit doubt
      const doubtData = {
        title: 'Help with calculus',
        description: 'I need help understanding derivatives',
        batch_id: 'test-batch-id',
        subject_id: 'math-subject-id',
        user_id: 'student-id'
      };

      const submittedDoubt = await mockDoubtService.submitDoubt(doubtData);
      expect(submittedDoubt.id).toBe('doubt-1');

      // AI categorization
      const categorization = await mockAIService.categorizeDoubt(doubtData.description);
      expect(categorization.category).toBe('mathematics');

      // Instructor responds
      const responseData = {
        response: 'Here is the explanation for derivatives...',
        instructor_id: 'instructor-id'
      };

      await mockDoubtService.respondToDoubt('doubt-1', responseData);
      expect(mockDoubtService.respondToDoubt).toHaveBeenCalledWith('doubt-1', responseData);

      // Mark as resolved
      await mockDoubtService.markAsResolved('doubt-1');
      expect(mockDoubtService.markAsResolved).toHaveBeenCalledWith('doubt-1');
    });
  });

  describe('Analytics Integration', () => {
    it('should track and aggregate analytics data', async () => {
      const mockAnalyticsService = {
        trackEvent: vi.fn().mockResolvedValue('event-id'),
        updateLearningAnalytics: vi.fn().mockResolvedValue({}),
        getStudentEngagementMetrics: vi.fn().mockResolvedValue({
          total_study_time_minutes: 120,
          videos_completed: 5,
          engagement_score: 85
        }),
        getBatchPerformanceMetrics: vi.fn().mockResolvedValue({
          total_students: 25,
          average_engagement_score: 78,
          completion_rate: 82
        })
      };

      vi.mock('$lib/utils/services/analytics', () => ({
        analyticsService: mockAnalyticsService
      }));

      // Track learning events
      await mockAnalyticsService.trackEvent({
        event_type: 'lesson_complete',
        event_category: 'learning',
        user_id: 'student-id',
        lesson_id: 'lesson-1'
      });

      // Update learning analytics
      await mockAnalyticsService.updateLearningAnalytics(
        'student-id',
        'org-id',
        'batch-id',
        {
          study_time_minutes: 60,
          videos_completed: 1,
          assignments_completed: 1
        }
      );

      // Get student metrics
      const studentMetrics = await mockAnalyticsService.getStudentEngagementMetrics(
        'student-id',
        'batch-id'
      );

      expect(studentMetrics.engagement_score).toBe(85);

      // Get batch metrics
      const batchMetrics = await mockAnalyticsService.getBatchPerformanceMetrics('batch-id');
      expect(batchMetrics.total_students).toBe(25);

      expect(mockAnalyticsService.trackEvent).toHaveBeenCalled();
      expect(mockAnalyticsService.updateLearningAnalytics).toHaveBeenCalled();
    });
  });

  describe('Live Streaming Integration', () => {
    it('should handle live session lifecycle', async () => {
      const mockLiveStreamService = {
        createSession: vi.fn().mockResolvedValue({ id: 'session-1', room_url: 'https://meet.example.com/session-1' }),
        joinSession: vi.fn().mockResolvedValue({ access_token: 'session-token' }),
        endSession: vi.fn().mockResolvedValue({}),
        trackAttendance: vi.fn().mockResolvedValue({})
      };

      vi.mock('$lib/utils/services/livestream', () => ({
        liveStreamService: mockLiveStreamService
      }));

      // Create live session
      const sessionData = {
        title: 'Math Class',
        batch_id: 'test-batch-id',
        instructor_id: 'instructor-id',
        scheduled_at: new Date().toISOString()
      };

      const session = await mockLiveStreamService.createSession(sessionData);
      expect(session.id).toBe('session-1');

      // Student joins session
      const joinResult = await mockLiveStreamService.joinSession('session-1', 'student-id');
      expect(joinResult.access_token).toBe('session-token');

      // Track attendance
      await mockLiveStreamService.trackAttendance('session-1', 'student-id', {
        join_time: new Date().toISOString(),
        duration_minutes: 45
      });

      // End session
      await mockLiveStreamService.endSession('session-1');

      expect(mockLiveStreamService.createSession).toHaveBeenCalled();
      expect(mockLiveStreamService.joinSession).toHaveBeenCalled();
      expect(mockLiveStreamService.trackAttendance).toHaveBeenCalled();
      expect(mockLiveStreamService.endSession).toHaveBeenCalled();
    });
  });

  describe('Performance Integration Tests', () => {
    it('should handle concurrent user operations', async () => {
      const operations = Array.from({ length: 10 }, async (_, i) => {
        return PerformanceTestUtils.measureAsyncOperation(async () => {
          // Simulate concurrent user operations
          await Promise.all([
            TestUtils.mockFetch({ '*': { success: true } }),
            new Promise(resolve => setTimeout(resolve, 10)),
            TestUtils.mockFetch({ '*': { data: `user-${i}` } })
          ]);
        });
      });

      const results = await Promise.all(operations);
      
      // All operations should complete within reasonable time
      results.forEach(time => {
        expect(time).toBeLessThan(100);
      });
    });

    it('should maintain performance under load', async () => {
      const loadTest = async () => {
        const promises = Array.from({ length: 50 }, () => 
          PerformanceTestUtils.measureAsyncOperation(async () => {
            await TestUtils.mockFetch({ '*': { data: 'test' } });
          })
        );

        const times = await Promise.all(promises);
        const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
        
        return averageTime;
      };

      const averageTime = await loadTest();
      
      // Average response time should be under 50ms even under load
      expect(averageTime).toBeLessThan(50);
    });
  });

  describe('Error Recovery Integration', () => {
    it('should recover from network failures', async () => {
      let failCount = 0;
      const mockServiceWithRetry = {
        getData: vi.fn().mockImplementation(() => {
          failCount++;
          if (failCount <= 2) {
            return Promise.reject(new Error('Network error'));
          }
          return Promise.resolve({ data: 'success' });
        })
      };

      // Simulate retry logic
      const retryOperation = async (operation: () => Promise<any>, maxRetries = 3) => {
        for (let i = 0; i < maxRetries; i++) {
          try {
            return await operation();
          } catch (error) {
            if (i === maxRetries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      };

      const result = await retryOperation(() => mockServiceWithRetry.getData());
      expect(result.data).toBe('success');
      expect(mockServiceWithRetry.getData).toHaveBeenCalledTimes(3);
    });
  });

  describe('Data Consistency Integration', () => {
    it('should maintain data consistency across services', async () => {
      const mockDatabase = new Map();
      
      const mockUserService = {
        createUser: vi.fn().mockImplementation((userData) => {
          const user = { id: 'user-1', ...userData };
          mockDatabase.set('user-1', user);
          return Promise.resolve(user);
        }),
        getUser: vi.fn().mockImplementation((id) => {
          return Promise.resolve(mockDatabase.get(id));
        })
      };

      const mockBatchService = {
        addUserToBatch: vi.fn().mockImplementation((batchId, userId) => {
          const user = mockDatabase.get(userId);
          if (!user) throw new Error('User not found');
          
          const membership = { batch_id: batchId, user_id: userId };
          mockDatabase.set(`membership-${batchId}-${userId}`, membership);
          return Promise.resolve(membership);
        })
      };

      // Create user
      const user = await mockUserService.createUser({
        email: '<EMAIL>',
        name: 'Test User'
      });

      // Verify user exists
      const retrievedUser = await mockUserService.getUser('user-1');
      expect(retrievedUser.email).toBe('<EMAIL>');

      // Add user to batch
      await mockBatchService.addUserToBatch('batch-1', 'user-1');

      // Verify membership was created
      const membership = mockDatabase.get('membership-batch-1-user-1');
      expect(membership.user_id).toBe('user-1');
      expect(membership.batch_id).toBe('batch-1');
    });
  });
});
