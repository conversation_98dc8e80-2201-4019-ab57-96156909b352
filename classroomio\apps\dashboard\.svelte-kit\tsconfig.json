{"compilerOptions": {"paths": {"$lib": ["../src/lib"], "$lib/*": ["../src/lib/*"], "$mail": ["../src/mail"], "$mail/*": ["../src/mail/*"]}, "rootDirs": ["..", "./types"], "importsNotUsedAsValues": "error", "isolatedModules": true, "preserveValueImports": true, "lib": ["esnext", "DOM", "DOM.Iterable"], "moduleResolution": "node", "module": "esnext", "noEmit": true, "target": "esnext", "ignoreDeprecations": "5.0"}, "include": ["ambient.d.ts", "./types/**/$types.d.ts", "../vite.config.js", "../vite.config.ts", "../src/**/*.js", "../src/**/*.ts", "../src/**/*.svelte", "../tests/**/*.js", "../tests/**/*.ts", "../tests/**/*.svelte"], "exclude": ["../node_modules/**", "./[!ambient.d.ts]**", "../src/service-worker.js", "../src/service-worker.ts", "../src/service-worker.d.ts"]}