# ClassroomIO Development Docker Compose
# Simplified setup for local development

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: classroomio-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: classroomio
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "54322:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: classroomio-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Supabase Local (Alternative to external Supabase)
  supabase:
    image: supabase/supabase:latest
    container_name: classroomio-supabase
    restart: unless-stopped
    environment:
      POSTGRES_HOST: postgres
      POSTGRES_DB: classroomio
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SERVICE_ROLE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
    ports:
      - "54321:8000"  # Supabase API
      - "54323:3000"  # Supabase Studio
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  # ClassroomIO Dashboard (Main App)
  dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        - NODE_ENV=development
    container_name: classroomio-dashboard
    restart: unless-stopped
    ports:
      - "5173:5173"  # Vite dev server
      - "24678:24678"  # Vite HMR
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/dashboard/node_modules
    environment:
      - NODE_ENV=development
      - VITE_HOST=0.0.0.0
      - VITE_PORT=5173
      # Database
      - DATABASE_URL=********************************************/classroomio
      - SUPABASE_URL=http://supabase:8000
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      # Redis
      - REDIS_URL=redis://redis:6379
      # Development settings
      - DEBUG=true
      - LOG_LEVEL=debug
      - HOT_RELOAD=true
      # Security (relaxed for development)
      - JWT_SECRET=development-jwt-secret-key-change-in-production
      - SESSION_SECRET=development-session-secret-change-in-production
      # Features (simplified for development)
      - FEATURE_LIVE_STREAMING=false
      - FEATURE_AI_INTEGRATION=false
      - FEATURE_VIDEO_SECURITY=false
      - VIDEO_PROCESSING_ENABLED=false
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Background Worker (Optional)
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    container_name: classroomio-worker
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/classroomio
      - REDIS_URL=redis://redis:6379
      - DEBUG=true
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    profiles:
      - worker  # Optional service, start with: docker-compose --profile worker up

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: classroomio-dev
