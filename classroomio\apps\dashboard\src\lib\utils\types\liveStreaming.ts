// Live Streaming Types for Educational Platform
// Date: 2025-06-30

export interface LiveSession {
  id: string;
  created_at: string;
  updated_at: string;
  title: string;
  description?: string;
  batch_id: string;
  subject_id?: string;
  chapter_id?: string;
  lesson_id?: string;
  instructor_id: string;
  scheduled_start: string;
  scheduled_end: string;
  actual_start?: string;
  actual_end?: string;
  status: 'scheduled' | 'live' | 'ended' | 'cancelled';
  session_type: 'class' | 'exam' | 'meeting' | 'workshop';
  max_participants: number;
  is_recorded: boolean;
  recording_url?: string;
  recording_duration?: number; // in seconds
  meeting_config: MeetingConfig;
  security_settings: LiveSessionSecuritySettings;
  metadata: Record<string, any>;
}

export interface MeetingConfig {
  provider: 'bigbluebutton' | 'jitsi' | 'zoom' | 'teams';
  meeting_id?: string;
  meeting_url?: string;
  moderator_password?: string;
  attendee_password?: string;
  waiting_room_enabled: boolean;
  chat_enabled: boolean;
  screen_sharing_enabled: boolean;
  recording_enabled: boolean;
  breakout_rooms_enabled: boolean;
  polls_enabled: boolean;
  whiteboard_enabled: boolean;
  max_video_streams: number;
  audio_only_mode: boolean;
  layout_settings: {
    default_layout: 'gallery' | 'speaker' | 'presentation';
    allow_layout_change: boolean;
  };
  quality_settings: {
    video_quality: 'low' | 'medium' | 'high' | 'auto';
    audio_quality: 'low' | 'medium' | 'high';
    bandwidth_limit?: number;
  };
}

export interface LiveSessionSecuritySettings {
  device_verification_required: boolean;
  ip_restrictions: string[];
  geo_restrictions: string[];
  watermark_enabled: boolean;
  recording_protection: boolean;
  screenshot_protection: boolean;
  attendance_tracking: boolean;
  proctoring_enabled: boolean;
  lockdown_mode: boolean;
  session_timeout_minutes: number;
}

export interface LiveSessionParticipant {
  id: string;
  created_at: string;
  updated_at: string;
  session_id: string;
  user_id: string;
  role: 'instructor' | 'student' | 'moderator' | 'observer';
  joined_at?: string;
  left_at?: string;
  total_duration: number; // in seconds
  device_fingerprint?: string;
  ip_address?: string;
  user_agent?: string;
  connection_quality: ConnectionQuality;
  participation_data: ParticipationData;
  is_approved: boolean;
  approval_status: 'pending' | 'approved' | 'rejected' | 'auto_approved';
}

export interface ConnectionQuality {
  video_quality: 'excellent' | 'good' | 'fair' | 'poor';
  audio_quality: 'excellent' | 'good' | 'fair' | 'poor';
  bandwidth_mbps: number;
  latency_ms: number;
  packet_loss_percent: number;
  connection_type: 'wifi' | 'ethernet' | 'cellular' | 'unknown';
  last_updated: string;
}

export interface ParticipationData {
  chat_messages_sent: number;
  polls_participated: number;
  screen_shares: number;
  microphone_usage_seconds: number;
  camera_usage_seconds: number;
  attention_score: number; // 0-100
  engagement_events: Array<{
    type: 'chat' | 'poll' | 'reaction' | 'question' | 'screen_share';
    timestamp: string;
    data: any;
  }>;
}

export interface BreakoutRoom {
  id: string;
  created_at: string;
  updated_at: string;
  session_id: string;
  room_name: string;
  room_number: number;
  max_participants: number;
  duration_minutes: number;
  is_active: boolean;
  started_at?: string;
  ended_at?: string;
  room_config: {
    auto_assign: boolean;
    allow_self_select: boolean;
    moderator_can_join_all: boolean;
    recording_enabled: boolean;
  };
  assigned_participants: string[]; // user IDs
}

export interface LivePoll {
  id: string;
  created_at: string;
  updated_at: string;
  session_id: string;
  created_by: string;
  question: string;
  poll_type: 'multiple_choice' | 'single_choice' | 'text' | 'rating' | 'yes_no';
  options: Array<{
    id: string;
    text: string;
    color?: string;
  }>;
  is_active: boolean;
  is_anonymous: boolean;
  allow_multiple_answers: boolean;
  started_at?: string;
  ended_at?: string;
  duration_seconds: number;
  results: PollResults;
}

export interface PollResults {
  total_responses: number;
  response_breakdown: Record<string, number>;
  response_percentage: Record<string, number>;
  average_rating?: number; // for rating polls
  text_responses?: string[]; // for text polls
}

export interface LivePollResponse {
  id: string;
  created_at: string;
  poll_id: string;
  user_id?: string;
  response_data: {
    selected_options?: string[];
    text_response?: string;
    rating?: number;
  };
  response_time: string;
  is_anonymous: boolean;
}

export interface LiveChatMessage {
  id: string;
  created_at: string;
  session_id: string;
  user_id: string;
  message: string;
  message_type: 'text' | 'emoji' | 'file' | 'poll' | 'announcement';
  is_private: boolean;
  recipient_id?: string;
  is_moderated: boolean;
  moderation_status: 'pending' | 'approved' | 'rejected';
  moderated_by?: string;
  metadata: {
    file_url?: string;
    file_type?: string;
    file_size?: number;
    emoji_code?: string;
    poll_id?: string;
  };
}

export interface WhiteboardSession {
  id: string;
  created_at: string;
  updated_at: string;
  session_id: string;
  title: string;
  whiteboard_data: WhiteboardData;
  is_active: boolean;
  created_by: string;
  collaborators: Array<{
    user_id: string;
    permissions: 'view' | 'edit' | 'admin';
    joined_at: string;
  }>;
  version: number;
  last_modified_by?: string;
}

export interface WhiteboardData {
  canvas_size: {
    width: number;
    height: number;
  };
  background_color: string;
  elements: Array<WhiteboardElement>;
  layers: Array<{
    id: string;
    name: string;
    visible: boolean;
    locked: boolean;
  }>;
}

export interface WhiteboardElement {
  id: string;
  type: 'pen' | 'text' | 'shape' | 'image' | 'sticky_note';
  position: { x: number; y: number };
  size: { width: number; height: number };
  style: {
    color: string;
    fill_color?: string;
    stroke_width: number;
    font_size?: number;
    font_family?: string;
  };
  data: any; // element-specific data
  created_by: string;
  created_at: string;
  layer_id: string;
}

export interface LiveSessionAnalytics {
  id: string;
  created_at: string;
  session_id: string;
  analytics_type: 'attendance' | 'engagement' | 'participation' | 'technical';
  data_point: string;
  value: number;
  metadata: Record<string, any>;
  timestamp: string;
}

export interface MeetingProvider {
  id: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  provider_name: 'bigbluebutton' | 'jitsi' | 'zoom' | 'teams';
  provider_config: {
    server_url?: string;
    api_endpoint?: string;
    secret_key?: string;
    default_settings?: MeetingConfig;
  };
  is_active: boolean;
  is_default: boolean;
  api_credentials: {
    api_key?: string;
    secret?: string;
    client_id?: string;
    client_secret?: string;
  };
  webhook_url?: string;
  rate_limits: {
    requests_per_minute: number;
    concurrent_meetings: number;
    max_participants_per_meeting: number;
  };
}

export interface LiveSessionSummary {
  session: LiveSession;
  participants_count: number;
  attendance_rate: number;
  average_duration: number;
  engagement_score: number;
  polls_conducted: number;
  chat_messages: number;
  recording_available: boolean;
  technical_issues: number;
}

export interface AttendanceReport {
  session_id: string;
  session_title: string;
  scheduled_start: string;
  actual_start?: string;
  total_enrolled: number;
  total_attended: number;
  attendance_rate: number;
  participants: Array<{
    user_id: string;
    user_name: string;
    role: string;
    joined_at?: string;
    left_at?: string;
    duration_minutes: number;
    attendance_status: 'present' | 'late' | 'absent' | 'partial';
  }>;
}

export interface EngagementMetrics {
  session_id: string;
  overall_engagement_score: number;
  participation_metrics: {
    chat_participation_rate: number;
    poll_participation_rate: number;
    question_asking_rate: number;
    screen_sharing_usage: number;
  };
  attention_metrics: {
    average_attention_score: number;
    attention_drops: number;
    multitasking_detected: number;
  };
  interaction_timeline: Array<{
    timestamp: string;
    event_type: string;
    participant_count: number;
    engagement_level: 'high' | 'medium' | 'low';
  }>;
}

export interface LiveStreamingSettings {
  organization_id: string;
  default_provider: 'bigbluebutton' | 'jitsi' | 'zoom' | 'teams';
  auto_recording: boolean;
  waiting_room_default: boolean;
  max_session_duration_hours: number;
  breakout_rooms_enabled: boolean;
  polls_enabled: boolean;
  chat_moderation: boolean;
  attendance_tracking: boolean;
  engagement_analytics: boolean;
  security_settings: LiveSessionSecuritySettings;
  notification_settings: {
    session_reminders: boolean;
    attendance_alerts: boolean;
    engagement_alerts: boolean;
    technical_alerts: boolean;
  };
}
