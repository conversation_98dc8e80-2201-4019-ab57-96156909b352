<script lang="ts">
  import { onMount, onDestroy, createEventDispatcher } from 'svelte';
  import { browser } from '$app/environment';
  import type { ExamSession, SecurityPolicy } from '$lib/utils/types/security';
  import { AntiPiracyProtection } from '$lib/utils/security/antiPiracy';
  import { DeviceFingerprintGenerator } from '$lib/utils/security/deviceFingerprint';
  import { securityEventService } from '$lib/utils/services/security';
  import { globalStore } from '$lib/utils/store/app';
  import { t } from '$lib/utils/functions/translations';
  import Box from '$lib/components/Box/index.svelte';
  import PrimaryButton from '$lib/components/PrimaryButton/index.svelte';
  import { VARIANTS } from '$lib/components/PrimaryButton/constants';
  import { 
    Lock, 
    Locked, 
    View, 
    ViewOff, 
    Microphone, 
    MicrophoneOff,
    Warning,
    CheckmarkFilled
  } from 'carbon-icons-svelte';

  export let examId: string;
  export let lessonId: string | null = null;
  export let batchId: string | null = null;
  export let securityPolicy: SecurityPolicy;
  export let onExamStart: (() => void) | null = null;
  export let onExamEnd: (() => void) | null = null;
  export let className: string = '';

  const dispatch = createEventDispatcher<{
    examStarted: { sessionId: string };
    examEnded: { sessionId: string; violations: any[] };
    violationDetected: { type: string; severity: string; data: any };
    lockdownActivated: {};
    lockdownDeactivated: {};
  }>();

  let examSession: ExamSession | null = null;
  let isLockdownActive = false;
  let isFullscreen = false;
  let webcamStream: MediaStream | null = null;
  let microphoneStream: MediaStream | null = null;
  let webcamEnabled = false;
  let microphoneEnabled = false;
  let violations: any[] = [];
  let antiPiracyProtection: AntiPiracyProtection | null = null;
  let deviceFingerprint: string = '';
  let isInitializing = false;
  let initializationError: string | null = null;
  let permissionsGranted = false;

  // Security checks
  let securityChecks = {
    fullscreen: false,
    webcam: false,
    microphone: false,
    device_fingerprint: false,
    anti_piracy: false
  };

  $: allChecksComplete = Object.values(securityChecks).every(check => check);
  $: userId = $globalStore.user?.id;
  $: requiresWebcam = securityPolicy.settings.webcam_required || false;
  $: requiresMicrophone = securityPolicy.settings.microphone_required || false;
  $: requiresLockdown = securityPolicy.settings.lockdown_browser_required || false;

  onMount(async () => {
    if (!browser) return;
    await initializeSecureEnvironment();
  });

  onDestroy(() => {
    cleanup();
  });

  async function initializeSecureEnvironment() {
    try {
      isInitializing = true;
      initializationError = null;

      // Generate device fingerprint
      const fingerprintGenerator = DeviceFingerprintGenerator.getInstance();
      const fingerprint = await fingerprintGenerator.generateFingerprint();
      deviceFingerprint = fingerprint.composite_hash;
      securityChecks.device_fingerprint = true;

      // Initialize anti-piracy protection
      if (securityPolicy.settings.screenshot_protection || 
          securityPolicy.settings.recording_protection ||
          securityPolicy.settings.developer_tools_blocked) {
        
        antiPiracyProtection = AntiPiracyProtection.getInstance({
          screenshot_protection: securityPolicy.settings.screenshot_protection || false,
          recording_protection: securityPolicy.settings.recording_protection || false,
          right_click_disabled: securityPolicy.settings.right_click_disabled || false,
          developer_tools_blocked: securityPolicy.settings.developer_tools_blocked || false,
          copy_paste_disabled: securityPolicy.settings.copy_paste_disabled || false,
          text_selection_disabled: true,
          print_disabled: true,
          drag_drop_disabled: true
        });
        
        securityChecks.anti_piracy = true;
      }

      // Request permissions
      await requestPermissions();

    } catch (error) {
      console.error('Error initializing secure environment:', error);
      initializationError = error.message || 'Failed to initialize secure environment';
    } finally {
      isInitializing = false;
    }
  }

  async function requestPermissions() {
    try {
      // Request webcam permission if required
      if (requiresWebcam) {
        webcamStream = await navigator.mediaDevices.getUserMedia({ 
          video: { 
            width: { ideal: 640 },
            height: { ideal: 480 },
            facingMode: 'user'
          } 
        });
        webcamEnabled = true;
        securityChecks.webcam = true;
      } else {
        securityChecks.webcam = true; // Not required, so mark as complete
      }

      // Request microphone permission if required
      if (requiresMicrophone) {
        microphoneStream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true
          }
        });
        microphoneEnabled = true;
        securityChecks.microphone = true;
      } else {
        securityChecks.microphone = true; // Not required, so mark as complete
      }

      permissionsGranted = true;

    } catch (error) {
      console.error('Error requesting permissions:', error);
      throw new Error('Required permissions not granted. Please allow camera and microphone access.');
    }
  }

  async function enterFullscreen() {
    try {
      if (document.documentElement.requestFullscreen) {
        await document.documentElement.requestFullscreen();
      } else if ((document.documentElement as any).webkitRequestFullscreen) {
        await (document.documentElement as any).webkitRequestFullscreen();
      } else if ((document.documentElement as any).msRequestFullscreen) {
        await (document.documentElement as any).msRequestFullscreen();
      }
      
      isFullscreen = true;
      securityChecks.fullscreen = true;
      
      // Listen for fullscreen changes
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.addEventListener('msfullscreenchange', handleFullscreenChange);

    } catch (error) {
      console.error('Error entering fullscreen:', error);
      throw new Error('Fullscreen mode is required for secure exams');
    }
  }

  function handleFullscreenChange() {
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement
    );

    if (!isCurrentlyFullscreen && isLockdownActive) {
      // Fullscreen exited during exam - violation
      recordViolation('fullscreen_exit', 'critical', {
        timestamp: new Date().toISOString(),
        message: 'Student exited fullscreen mode during exam'
      });
    }

    isFullscreen = isCurrentlyFullscreen;
    securityChecks.fullscreen = isCurrentlyFullscreen || !requiresLockdown;
  }

  async function startExam() {
    if (!allChecksComplete || !userId) {
      throw new Error('Security checks not complete');
    }

    try {
      // Create exam session
      examSession = {
        id: crypto.randomUUID(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        student_id: userId,
        exam_id: examId,
        lesson_id: lessonId,
        batch_id: batchId,
        session_token: crypto.randomUUID(),
        device_fingerprint: deviceFingerprint,
        start_time: new Date().toISOString(),
        expected_duration: 60, // Default 60 minutes
        is_active: true,
        is_lockdown_active: false,
        lockdown_violations: [],
        proctoring_data: {
          webcam_enabled: webcamEnabled,
          microphone_enabled: microphoneEnabled,
          face_detection_events: [],
          audio_events: []
        },
        security_events: [],
        is_completed: false,
        metadata: {}
      };

      // Activate lockdown if required
      if (requiresLockdown) {
        await activateLockdown();
      }

      // Start monitoring
      startSecurityMonitoring();

      dispatch('examStarted', { sessionId: examSession.id });
      
      if (onExamStart) {
        onExamStart();
      }

      // Log exam start event
      await securityEventService.logEvent({
        event_type: 'exam_started',
        severity: 'low',
        user_id: userId,
        session_id: examSession.id,
        device_fingerprint: deviceFingerprint,
        event_data: {
          exam_id: examId,
          lesson_id: lessonId,
          batch_id: batchId,
          security_settings: securityPolicy.settings
        },
        location: {},
        is_blocked: false,
        metadata: {}
      });

    } catch (error) {
      console.error('Error starting exam:', error);
      throw error;
    }
  }

  async function activateLockdown() {
    if (!examSession) return;

    try {
      // Enter fullscreen
      if (requiresLockdown && !isFullscreen) {
        await enterFullscreen();
      }

      // Activate anti-piracy protection
      if (antiPiracyProtection) {
        antiPiracyProtection.activate();
      }

      // Disable browser navigation
      window.addEventListener('beforeunload', preventNavigation);
      window.addEventListener('popstate', preventNavigation);

      // Block common exit shortcuts
      document.addEventListener('keydown', blockExitShortcuts);

      isLockdownActive = true;
      examSession.is_lockdown_active = true;

      dispatch('lockdownActivated', {});

      // Log lockdown activation
      await securityEventService.logEvent({
        event_type: 'lockdown_activated',
        severity: 'medium',
        user_id: userId,
        session_id: examSession.id,
        event_data: {
          fullscreen: isFullscreen,
          anti_piracy_active: antiPiracyProtection?.isProtectionActive() || false
        },
        location: {},
        is_blocked: false,
        metadata: {}
      });

    } catch (error) {
      console.error('Error activating lockdown:', error);
      throw error;
    }
  }

  function startSecurityMonitoring() {
    // Monitor tab visibility
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Monitor window focus
    window.addEventListener('blur', handleWindowBlur);
    window.addEventListener('focus', handleWindowFocus);

    // Start face detection if webcam is enabled
    if (webcamEnabled && webcamStream) {
      startFaceDetection();
    }

    // Start audio monitoring if microphone is enabled
    if (microphoneEnabled && microphoneStream) {
      startAudioMonitoring();
    }
  }

  function handleVisibilityChange() {
    if (document.hidden && isLockdownActive) {
      recordViolation('tab_switch', 'high', {
        timestamp: new Date().toISOString(),
        visibility_state: document.visibilityState
      });
    }
  }

  function handleWindowBlur() {
    if (isLockdownActive) {
      recordViolation('window_blur', 'medium', {
        timestamp: new Date().toISOString(),
        reason: 'window_lost_focus'
      });
    }
  }

  function handleWindowFocus() {
    // Window regained focus - could log this as well
  }

  function preventNavigation(e: Event) {
    e.preventDefault();
    e.returnValue = false;
    
    recordViolation('navigation_attempt', 'critical', {
      timestamp: new Date().toISOString(),
      event_type: e.type
    });
    
    return false;
  }

  function blockExitShortcuts(e: KeyboardEvent) {
    // Block Alt+Tab, Alt+F4, Ctrl+W, etc.
    if (
      (e.altKey && e.key === 'Tab') ||
      (e.altKey && e.key === 'F4') ||
      (e.ctrlKey && e.key === 'w') ||
      (e.ctrlKey && e.key === 'W') ||
      (e.ctrlKey && e.shiftKey && e.key === 'T') ||
      e.key === 'F11'
    ) {
      e.preventDefault();
      e.stopPropagation();
      
      recordViolation('exit_shortcut', 'high', {
        timestamp: new Date().toISOString(),
        key_combination: `${e.altKey ? 'Alt+' : ''}${e.ctrlKey ? 'Ctrl+' : ''}${e.shiftKey ? 'Shift+' : ''}${e.key}`
      });
      
      return false;
    }
  }

  function startFaceDetection() {
    // Simplified face detection - in production, use a proper face detection library
    // This is just a placeholder for the concept
    setInterval(() => {
      if (webcamStream && examSession) {
        // Simulate face detection
        const facesDetected = Math.random() > 0.1 ? 1 : 0; // 90% chance of detecting a face
        
        examSession.proctoring_data.face_detection_events?.push({
          timestamp: new Date().toISOString(),
          faces_detected: facesDetected,
          confidence: 0.85
        });

        if (facesDetected === 0) {
          recordViolation('no_face_detected', 'medium', {
            timestamp: new Date().toISOString(),
            duration: 5000 // 5 seconds
          });
        }
      }
    }, 5000);
  }

  function startAudioMonitoring() {
    // Simplified audio monitoring - in production, use proper audio analysis
    setInterval(() => {
      if (microphoneStream && examSession) {
        // Simulate audio event detection
        const hasAudio = Math.random() > 0.8; // 20% chance of detecting audio
        
        if (hasAudio) {
          examSession.proctoring_data.audio_events?.push({
            timestamp: new Date().toISOString(),
            event_type: 'voice_detected',
            confidence: 0.75
          });

          recordViolation('unauthorized_audio', 'medium', {
            timestamp: new Date().toISOString(),
            type: 'voice_detected'
          });
        }
      }
    }, 10000);
  }

  function recordViolation(type: string, severity: string, data: any) {
    const violation = {
      type,
      severity,
      timestamp: new Date().toISOString(),
      description: `Security violation: ${type}`,
      data
    };

    violations.push(violation);
    
    if (examSession) {
      examSession.lockdown_violations.push(violation);
    }

    dispatch('violationDetected', { type, severity, data });

    // Log security event
    securityEventService.logEvent({
      event_type: type as any,
      severity: severity as any,
      user_id: userId,
      session_id: examSession?.id,
      device_fingerprint: deviceFingerprint,
      event_data: data,
      location: {},
      is_blocked: true,
      response_action: 'violation_recorded',
      metadata: {}
    });

    // Auto-submit exam if too many violations
    const criticalViolations = violations.filter(v => v.severity === 'critical').length;
    if (criticalViolations >= 3) {
      endExam(true); // Force end due to violations
    }
  }

  async function endExam(forcedEnd: boolean = false) {
    if (!examSession) return;

    try {
      // Deactivate lockdown
      await deactivateLockdown();

      // Update exam session
      examSession.end_time = new Date().toISOString();
      examSession.is_active = false;
      examSession.is_completed = !forcedEnd;

      dispatch('examEnded', { 
        sessionId: examSession.id, 
        violations: violations 
      });

      if (onExamEnd) {
        onExamEnd();
      }

      // Log exam end event
      await securityEventService.logEvent({
        event_type: 'exam_ended',
        severity: forcedEnd ? 'high' : 'low',
        user_id: userId,
        session_id: examSession.id,
        event_data: {
          forced_end: forcedEnd,
          total_violations: violations.length,
          critical_violations: violations.filter(v => v.severity === 'critical').length,
          duration: Date.now() - new Date(examSession.start_time).getTime()
        },
        location: {},
        is_blocked: false,
        metadata: {}
      });

    } catch (error) {
      console.error('Error ending exam:', error);
    }
  }

  async function deactivateLockdown() {
    if (!isLockdownActive) return;

    try {
      // Exit fullscreen
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      }

      // Deactivate anti-piracy protection
      if (antiPiracyProtection) {
        antiPiracyProtection.deactivate();
      }

      // Remove event listeners
      window.removeEventListener('beforeunload', preventNavigation);
      window.removeEventListener('popstate', preventNavigation);
      document.removeEventListener('keydown', blockExitShortcuts);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleWindowBlur);
      window.removeEventListener('focus', handleWindowFocus);

      isLockdownActive = false;

      dispatch('lockdownDeactivated', {});

    } catch (error) {
      console.error('Error deactivating lockdown:', error);
    }
  }

  function cleanup() {
    // Stop media streams
    if (webcamStream) {
      webcamStream.getTracks().forEach(track => track.stop());
    }
    if (microphoneStream) {
      microphoneStream.getTracks().forEach(track => track.stop());
    }

    // Deactivate lockdown
    if (isLockdownActive) {
      deactivateLockdown();
    }

    // Deactivate anti-piracy protection
    if (antiPiracyProtection) {
      antiPiracyProtection.deactivate();
    }
  }
</script>

<div class="secure-exam-environment {className}">
  {#if isInitializing}
    <Box className="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('exam.initializing', { default: 'Initializing Secure Environment' })}
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        {$t('exam.initializing_desc', { default: 'Setting up security measures and requesting permissions...' })}
      </p>
    </Box>

  {:else if initializationError}
    <Box className="text-center py-12">
      <Warning size={48} class="text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {$t('exam.initialization_failed', { default: 'Initialization Failed' })}
      </h3>
      <p class="text-red-600 dark:text-red-400 mb-4">{initializationError}</p>
      <PrimaryButton onClick={initializeSecureEnvironment}>
        {$t('exam.retry', { default: 'Retry' })}
      </PrimaryButton>
    </Box>

  {:else if !examSession}
    <Box>
      <div class="mb-6">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
          {$t('exam.security_check', { default: 'Security Check' })}
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          {$t('exam.security_check_desc', { default: 'Please complete all security checks before starting the exam.' })}
        </p>
      </div>

      <!-- Security Checks -->
      <div class="space-y-4 mb-6">
        <!-- Device Fingerprint -->
        <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center">
            <Lock size={20} class="mr-3 text-gray-500" />
            <span class="text-gray-900 dark:text-white">
              {$t('exam.device_verification', { default: 'Device Verification' })}
            </span>
          </div>
          {#if securityChecks.device_fingerprint}
            <CheckmarkFilled size={20} class="text-green-500" />
          {:else}
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
          {/if}
        </div>

        <!-- Webcam Check -->
        {#if requiresWebcam}
          <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center">
              {#if webcamEnabled}
                <View size={20} class="mr-3 text-green-500" />
              {:else}
                <ViewOff size={20} class="mr-3 text-gray-500" />
              {/if}
              <span class="text-gray-900 dark:text-white">
                {$t('exam.webcam_access', { default: 'Webcam Access' })}
              </span>
            </div>
            {#if securityChecks.webcam}
              <CheckmarkFilled size={20} class="text-green-500" />
            {:else}
              <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
            {/if}
          </div>
        {/if}

        <!-- Microphone Check -->
        {#if requiresMicrophone}
          <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center">
              {#if microphoneEnabled}
                <Microphone size={20} class="mr-3 text-green-500" />
              {:else}
                <MicrophoneOff size={20} class="mr-3 text-gray-500" />
              {/if}
              <span class="text-gray-900 dark:text-white">
                {$t('exam.microphone_access', { default: 'Microphone Access' })}
              </span>
            </div>
            {#if securityChecks.microphone}
              <CheckmarkFilled size={20} class="text-green-500" />
            {:else}
              <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
            {/if}
          </div>
        {/if}

        <!-- Fullscreen Check -->
        {#if requiresLockdown}
          <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div class="flex items-center">
              {#if isFullscreen}
                <Locked size={20} class="mr-3 text-green-500" />
              {:else}
                <Lock size={20} class="mr-3 text-gray-500" />
              {/if}
              <span class="text-gray-900 dark:text-white">
                {$t('exam.fullscreen_mode', { default: 'Fullscreen Mode' })}
              </span>
            </div>
            {#if securityChecks.fullscreen}
              <CheckmarkFilled size={20} class="text-green-500" />
            {:else if !isFullscreen}
              <PrimaryButton
                variant={VARIANTS.OUTLINED}
                onClick={enterFullscreen}
                size="sm"
              >
                {$t('exam.enter_fullscreen', { default: 'Enter Fullscreen' })}
              </PrimaryButton>
            {/if}
          </div>
        {/if}

        <!-- Anti-Piracy Protection -->
        <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center">
            <Lock size={20} class="mr-3 text-gray-500" />
            <span class="text-gray-900 dark:text-white">
              {$t('exam.security_protection', { default: 'Security Protection' })}
            </span>
          </div>
          {#if securityChecks.anti_piracy}
            <CheckmarkFilled size={20} class="text-green-500" />
          {:else}
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
          {/if}
        </div>
      </div>

      <!-- Start Exam Button -->
      <div class="text-center">
        <PrimaryButton
          variant={VARIANTS.CONTAINED}
          onClick={startExam}
          disabled={!allChecksComplete}
          size="lg"
        >
          <Lock size={20} class="mr-2" />
          {$t('exam.start_secure_exam', { default: 'Start Secure Exam' })}
        </PrimaryButton>
        
        {#if !allChecksComplete}
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
            {$t('exam.complete_checks', { default: 'Please complete all security checks to continue' })}
          </p>
        {/if}
      </div>
    </Box>

  {:else}
    <!-- Exam is active -->
    <Box>
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
          <Locked size={24} class="text-green-500 mr-3" />
          <div>
            <h3 class="font-semibold text-gray-900 dark:text-white">
              {$t('exam.secure_mode_active', { default: 'Secure Exam Mode Active' })}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {$t('exam.monitoring_active', { default: 'Security monitoring is active' })}
            </p>
          </div>
        </div>
        
        <PrimaryButton
          variant={VARIANTS.OUTLINED}
          onClick={() => endExam(false)}
        >
          {$t('exam.end_exam', { default: 'End Exam' })}
        </PrimaryButton>
      </div>

      <!-- Violations Display -->
      {#if violations.length > 0}
        <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
          <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
            {$t('exam.security_violations', { default: 'Security Violations' })} ({violations.length})
          </h4>
          <div class="space-y-2 max-h-32 overflow-y-auto">
            {#each violations.slice(-5) as violation}
              <div class="text-sm text-yellow-700 dark:text-yellow-300">
                <span class="font-medium">{violation.type}:</span>
                {violation.description}
                <span class="text-xs opacity-75">
                  ({new Date(violation.timestamp).toLocaleTimeString()})
                </span>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </Box>
  {/if}
</div>

<style>
  .secure-exam-environment {
    @apply w-full;
  }
</style>
