<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { DashboardWidget, ThemeConfig } from '$lib/utils/types/analytics';
  import { t } from '$lib/utils/functions/translations';
  import { TextAlignLeft, Information } from 'carbon-icons-svelte';

  export let widget: DashboardWidget;
  export let data: any;
  export let theme: ThemeConfig;

  const dispatch = createEventDispatcher<{
    click: { widget: DashboardWidget };
  }>();

  $: textContent = getTextContent(data);
  $: textStyle = getTextStyle();
  $: alignment = widget.config.text_align || 'left';
  $: fontSize = widget.config.font_size || 'base';
  $: fontWeight = widget.config.font_weight || 'normal';

  function getTextContent(data: any): string {
    if (typeof data === 'string') {
      return data;
    } else if (data?.content) {
      return data.content;
    } else if (data?.text) {
      return data.text;
    } else if (data?.message) {
      return data.message;
    } else if (widget.config.default_text) {
      return widget.config.default_text;
    }
    
    return $t('analytics.no_text_content', { default: 'No text content available' });
  }

  function getTextStyle(): string {
    let styles = [];
    
    if (theme?.text_color) {
      styles.push(`color: ${theme.text_color}`);
    }
    
    if (theme?.font_family) {
      styles.push(`font-family: ${theme.font_family}`);
    }
    
    return styles.join('; ');
  }

  function getFontSizeClass(size: string): string {
    switch (size) {
      case 'xs': return 'text-xs';
      case 'sm': return 'text-sm';
      case 'base': return 'text-base';
      case 'lg': return 'text-lg';
      case 'xl': return 'text-xl';
      case '2xl': return 'text-2xl';
      case '3xl': return 'text-3xl';
      case '4xl': return 'text-4xl';
      default: return 'text-base';
    }
  }

  function getFontWeightClass(weight: string): string {
    switch (weight) {
      case 'light': return 'font-light';
      case 'normal': return 'font-normal';
      case 'medium': return 'font-medium';
      case 'semibold': return 'font-semibold';
      case 'bold': return 'font-bold';
      case 'extrabold': return 'font-extrabold';
      default: return 'font-normal';
    }
  }

  function getAlignmentClass(align: string): string {
    switch (align) {
      case 'left': return 'text-left';
      case 'center': return 'text-center';
      case 'right': return 'text-right';
      case 'justify': return 'text-justify';
      default: return 'text-left';
    }
  }

  function handleClick() {
    dispatch('click', { widget });
  }

  function processTextContent(content: string): string {
    // Process markdown-like formatting
    let processed = content;
    
    // Bold text: **text** or __text__
    processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    processed = processed.replace(/__(.*?)__/g, '<strong>$1</strong>');
    
    // Italic text: *text* or _text_
    processed = processed.replace(/\*(.*?)\*/g, '<em>$1</em>');
    processed = processed.replace(/_(.*?)_/g, '<em>$1</em>');
    
    // Line breaks
    processed = processed.replace(/\n/g, '<br>');
    
    // Links: [text](url)
    processed = processed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary-600 hover:text-primary-700 underline" target="_blank" rel="noopener noreferrer">$1</a>');
    
    return processed;
  }

  $: processedContent = widget.config.enable_markdown !== false ? processTextContent(textContent) : textContent;
</script>

<div 
  class="text-widget h-full flex flex-col cursor-pointer p-4"
  on:click={handleClick}
  on:keydown={(e) => e.key === 'Enter' && handleClick()}
  role="button"
  tabindex="0"
  style={textStyle}
>
  {#if widget.config.show_icon !== false}
    <!-- Icon (if configured) -->
    <div class="flex items-center mb-3">
      {#if widget.config.icon}
        <span class="text-2xl mr-2">{widget.config.icon}</span>
      {:else}
        <TextAlignLeft size={20} class="text-gray-600 dark:text-gray-400 mr-2" />
      {/if}
      
      {#if widget.config.subtitle}
        <span class="text-sm text-gray-600 dark:text-gray-400">
          {widget.config.subtitle}
        </span>
      {/if}
    </div>
  {/if}

  <!-- Main Text Content -->
  <div 
    class="flex-1 {getFontSizeClass(fontSize)} {getFontWeightClass(fontWeight)} {getAlignmentClass(alignment)}"
    class:text-gray-900={!theme?.text_color}
    class:dark:text-white={!theme?.text_color}
  >
    {#if widget.config.enable_markdown !== false}
      {@html processedContent}
    {:else}
      {textContent}
    {/if}
  </div>

  <!-- Additional Data Display -->
  {#if data && typeof data === 'object' && data.metadata}
    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      {#if data.metadata.author}
        <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
          <strong>{$t('analytics.author', { default: 'Author' })}:</strong> {data.metadata.author}
        </div>
      {/if}
      
      {#if data.metadata.date}
        <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
          <strong>{$t('analytics.date', { default: 'Date' })}:</strong> {new Date(data.metadata.date).toLocaleDateString()}
        </div>
      {/if}
      
      {#if data.metadata.source}
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <strong>{$t('analytics.source', { default: 'Source' })}:</strong> {data.metadata.source}
        </div>
      {/if}
    </div>
  {/if}

  <!-- Call to Action (if configured) -->
  {#if widget.config.cta_text && widget.config.cta_url}
    <div class="mt-4">
      <a
        href={widget.config.cta_url}
        target="_blank"
        rel="noopener noreferrer"
        class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors"
        style="
          background-color: {theme?.primary_color || '#3B82F6'};
          color: white;
        "
        on:click|stopPropagation
      >
        {widget.config.cta_text}
        {#if widget.config.cta_icon}
          <span class="ml-2">{widget.config.cta_icon}</span>
        {/if}
      </a>
    </div>
  {/if}

  <!-- Status Indicator (if data includes status) -->
  {#if data?.status}
    <div class="mt-4 flex items-center">
      <div 
        class="w-3 h-3 rounded-full mr-2"
        class:bg-green-500={data.status === 'success' || data.status === 'active'}
        class:bg-yellow-500={data.status === 'warning' || data.status === 'pending'}
        class:bg-red-500={data.status === 'error' || data.status === 'inactive'}
        class:bg-gray-500={!['success', 'active', 'warning', 'pending', 'error', 'inactive'].includes(data.status)}
      ></div>
      <span class="text-sm text-gray-600 dark:text-gray-400 capitalize">
        {data.status}
      </span>
    </div>
  {/if}

  <!-- Progress Bar (if data includes progress) -->
  {#if data?.progress !== undefined}
    <div class="mt-4">
      <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
        <span>{$t('analytics.progress', { default: 'Progress' })}</span>
        <span>{Math.round(data.progress)}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          class="h-2 rounded-full transition-all duration-300"
          style="
            width: {Math.min(100, Math.max(0, data.progress))}%;
            background-color: {theme?.secondary_color || '#10B981'};
          "
        ></div>
      </div>
    </div>
  {/if}

  <!-- Tags (if data includes tags) -->
  {#if data?.tags && Array.isArray(data.tags) && data.tags.length > 0}
    <div class="mt-4">
      <div class="flex flex-wrap gap-2">
        {#each data.tags as tag}
          <span 
            class="px-2 py-1 text-xs rounded-full"
            style="
              background-color: {theme?.primary_color || '#3B82F6'}20;
              color: {theme?.primary_color || '#3B82F6'};
            "
          >
            {tag}
          </span>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Metrics (if data includes metrics) -->
  {#if data?.metrics && Array.isArray(data.metrics)}
    <div class="mt-4 grid grid-cols-2 gap-4">
      {#each data.metrics as metric}
        <div class="text-center">
          <div class="text-lg font-semibold text-gray-900 dark:text-white">
            {metric.value}
          </div>
          <div class="text-xs text-gray-600 dark:text-gray-400">
            {metric.label}
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .text-widget {
    transition: transform 0.2s ease-in-out;
  }

  .text-widget:hover {
    transform: translateY(-1px);
  }

  .text-widget:focus {
    outline: 2px solid var(--primary-color, #3B82F6);
    outline-offset: 2px;
  }

  /* Style for processed HTML content */
  .text-widget :global(a) {
    transition: color 0.2s ease-in-out;
  }

  .text-widget :global(strong) {
    font-weight: 600;
  }

  .text-widget :global(em) {
    font-style: italic;
  }
</style>
