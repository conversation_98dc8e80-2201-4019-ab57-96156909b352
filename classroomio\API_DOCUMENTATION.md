# ClassroomIO API Documentation

## 🔌 **API Overview**

The ClassroomIO API provides comprehensive access to all platform features through RESTful endpoints. All API requests require authentication and follow standard HTTP conventions.

**Base URL**: `https://api.classroomio.com/v1`
**Authentication**: <PERSON><PERSON> (JWT)
**Content-Type**: `application/json`

---

## 🔐 **Authentication**

### **Login**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "email": "<EMAIL>",
      "fullname": "<PERSON>",
      "role": "student"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-12-31T23:59:59Z"
  }
}
```

### **Register**
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "fullname": "<PERSON>",
  "organization_id": "org-456"
}
```

### **Refresh Token**
```http
POST /api/auth/refresh
Authorization: Bearer <token>
```

### **Logout**
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

---

## 👥 **User Management**

### **Get User Profile**
```http
GET /api/users/profile
Authorization: Bearer <token>
```

### **Update Profile**
```http
PUT /api/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "fullname": "Updated Name",
  "avatar_url": "https://example.com/avatar.jpg",
  "bio": "Updated bio"
}
```

### **Get Users (Admin)**
```http
GET /api/users?page=1&limit=20&role=student
Authorization: Bearer <token>
```

---

## 🏢 **Organization Management**

### **Get Organizations**
```http
GET /api/organizations
Authorization: Bearer <token>
```

### **Create Organization**
```http
POST /api/organizations
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Tech Academy",
  "description": "Leading technology education",
  "logo": "https://example.com/logo.jpg",
  "settings": {
    "allow_public_signup": true,
    "require_approval": false
  }
}
```

### **Update Organization**
```http
PUT /api/organizations/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated Academy Name",
  "description": "Updated description"
}
```

---

## 📚 **Batch Management**

### **Get Batches**
```http
GET /api/batches?organization_id=org-123&status=active
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "batch-123",
      "name": "Web Development Batch 2024",
      "description": "Full-stack web development course",
      "organization_id": "org-123",
      "start_date": "2024-01-15",
      "end_date": "2024-06-15",
      "status": "active",
      "total_students": 25,
      "active_students": 23,
      "completion_rate": 78.5,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

### **Create Batch**
```http
POST /api/batches
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Data Science Batch 2024",
  "description": "Comprehensive data science program",
  "organization_id": "org-123",
  "start_date": "2024-02-01",
  "end_date": "2024-08-01",
  "max_students": 30,
  "settings": {
    "allow_late_enrollment": true,
    "require_prerequisites": false
  }
}
```

### **Add Student to Batch**
```http
POST /api/batches/:id/students
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": "user-456",
  "enrollment_date": "2024-01-15"
}
```

---

## 📖 **Course & Lesson Management**

### **Get Courses**
```http
GET /api/courses?batch_id=batch-123
Authorization: Bearer <token>
```

### **Create Course**
```http
POST /api/courses
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Introduction to JavaScript",
  "description": "Learn JavaScript fundamentals",
  "batch_id": "batch-123",
  "instructor_id": "user-789",
  "duration_weeks": 4,
  "difficulty_level": "beginner"
}
```

### **Get Lessons**
```http
GET /api/lessons?course_id=course-123
Authorization: Bearer <token>
```

### **Create Lesson**
```http
POST /api/lessons
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Variables and Data Types",
  "description": "Understanding JavaScript variables",
  "course_id": "course-123",
  "content": "Lesson content here...",
  "video_url": "https://stream.example.com/video-123",
  "duration_minutes": 45,
  "order": 1
}
```

---

## 🎥 **Video Management**

### **Get Video Details**
```http
GET /api/videos/:id
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "video-123",
    "title": "JavaScript Basics",
    "description": "Introduction to JavaScript",
    "url": "https://stream.example.com/video-123",
    "thumbnail": "https://cdn.example.com/thumb-123.jpg",
    "duration": 2700,
    "quality_levels": ["360p", "720p", "1080p"],
    "subtitles": [
      {
        "language": "en",
        "url": "https://cdn.example.com/subtitles-en.vtt"
      }
    ],
    "security": {
      "watermark_enabled": true,
      "download_protected": true,
      "device_restricted": false
    }
  }
}
```

### **Track Video Progress**
```http
POST /api/videos/:id/progress
Authorization: Bearer <token>
Content-Type: application/json

{
  "current_time": 1350,
  "completion_percentage": 50,
  "session_id": "session-456"
}
```

### **Get Video Analytics**
```http
GET /api/videos/:id/analytics?user_id=user-123
Authorization: Bearer <token>
```

---

## 💬 **Communication System**

### **Submit Doubt**
```http
POST /api/doubts
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Understanding Async/Await",
  "description": "I'm confused about how async/await works in JavaScript",
  "batch_id": "batch-123",
  "subject_id": "javascript",
  "priority": "medium",
  "attachments": [
    {
      "filename": "code-example.js",
      "url": "https://cdn.example.com/files/code-example.js"
    }
  ]
}
```

### **Get Doubts**
```http
GET /api/doubts?batch_id=batch-123&status=pending&page=1
Authorization: Bearer <token>
```

### **Respond to Doubt**
```http
POST /api/doubts/:id/responses
Authorization: Bearer <token>
Content-Type: application/json

{
  "response": "Async/await is syntactic sugar for Promises...",
  "attachments": [
    {
      "filename": "solution.js",
      "url": "https://cdn.example.com/files/solution.js"
    }
  ]
}
```

### **Forum Posts**
```http
GET /api/forum/posts?batch_id=batch-123&category=general
POST /api/forum/posts
PUT /api/forum/posts/:id
DELETE /api/forum/posts/:id
```

---

## 📊 **Analytics & Reporting**

### **Get Dashboard Data**
```http
GET /api/analytics/dashboard?type=student&batch_id=batch-123
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "engagement_score": 85,
    "total_study_time": 1200,
    "videos_completed": 15,
    "assignments_completed": 8,
    "forum_posts": 5,
    "doubts_resolved": 3,
    "attendance_rate": 92,
    "progress_percentage": 68
  }
}
```

### **Track Analytics Event**
```http
POST /api/analytics/events
Authorization: Bearer <token>
Content-Type: application/json

{
  "event_type": "video_play",
  "event_category": "learning",
  "event_action": "play",
  "event_label": "javascript-basics",
  "event_value": 1,
  "properties": {
    "video_id": "video-123",
    "lesson_id": "lesson-456",
    "current_time": 0
  },
  "context": {
    "user_agent": "Mozilla/5.0...",
    "ip_address": "***********",
    "referrer": "https://classroomio.com/course/123"
  }
}
```

### **Generate Report**
```http
POST /api/analytics/reports/generate
Authorization: Bearer <token>
Content-Type: application/json

{
  "report_type": "student_progress",
  "batch_id": "batch-123",
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  },
  "format": "pdf",
  "email_to": "<EMAIL>"
}
```

---

## 📡 **Live Streaming**

### **Create Live Session**
```http
POST /api/live/sessions
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "JavaScript Advanced Concepts",
  "description": "Deep dive into advanced JavaScript",
  "batch_id": "batch-123",
  "scheduled_at": "2024-01-15T10:00:00Z",
  "duration_minutes": 90,
  "settings": {
    "allow_recording": true,
    "require_approval": false,
    "max_participants": 50
  }
}
```

### **Join Live Session**
```http
POST /api/live/sessions/:id/join
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "room_url": "https://meet.daily.co/classroomio-session-123",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "participant_id": "participant-456",
    "permissions": {
      "can_share_screen": true,
      "can_use_chat": true,
      "can_use_microphone": true
    }
  }
}
```

### **Track Attendance**
```http
POST /api/live/sessions/:id/attendance
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": "user-123",
  "join_time": "2024-01-15T10:05:00Z",
  "leave_time": "2024-01-15T11:30:00Z",
  "duration_minutes": 85,
  "participation_score": 92
}
```

---

## 📁 **File Management**

### **Upload File**
```http
POST /api/files/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <binary-data>
folder: "assignments"
```

### **Get File**
```http
GET /api/files/:id
Authorization: Bearer <token>
```

### **Delete File**
```http
DELETE /api/files/:id
Authorization: Bearer <token>
```

---

## ⚙️ **System Administration**

### **System Health**
```http
GET /api/system/health
```

### **System Metrics**
```http
GET /api/system/metrics
Authorization: Bearer <admin-token>
```

### **User Management (Admin)**
```http
GET /api/admin/users
POST /api/admin/users
PUT /api/admin/users/:id
DELETE /api/admin/users/:id
```

---

## 🔍 **Error Handling**

### **Error Response Format**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  },
  "request_id": "req-123456"
}
```

### **HTTP Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error

---

## 🚀 **Rate Limiting**

- **General API**: 1000 requests/hour per user
- **Authentication**: 10 requests/minute per IP
- **File Upload**: 100 requests/hour per user
- **Analytics**: 500 requests/hour per user

---

## 📝 **API Versioning**

- **Current Version**: v1
- **Version Header**: `API-Version: v1`
- **Deprecation Notice**: 6 months advance notice
- **Backward Compatibility**: Maintained for 1 year

---

## 🔗 **SDKs and Libraries**

### **JavaScript SDK**
```javascript
import { ClassroomIOClient } from '@classroomio/sdk';

const client = new ClassroomIOClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.classroomio.com/v1'
});

// Get user profile
const profile = await client.users.getProfile();
```

### **Python SDK**
```python
from classroomio import ClassroomIOClient

client = ClassroomIOClient(api_key='your-api-key')
profile = client.users.get_profile()
```

---

**Last Updated**: December 2024
**API Version**: v1.0.0
**Support**: <EMAIL>
