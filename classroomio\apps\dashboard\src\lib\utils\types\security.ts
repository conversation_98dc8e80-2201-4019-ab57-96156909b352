// Security Types for Educational Platform
// Date: 2025-06-30

export interface SecurityEvent {
  id: string;
  created_at: string;
  event_type: 'login' | 'logout' | 'device_change' | 'suspicious_activity' | 'screenshot_attempt' | 
             'recording_attempt' | 'tab_switch' | 'developer_tools' | 'copy_attempt' | 'exam_violation' |
             'unauthorized_access' | 'token_expired' | 'device_blocked' | 'ip_blocked';
  severity: 'low' | 'medium' | 'high' | 'critical';
  user_id?: string;
  session_id?: string;
  device_fingerprint?: string;
  ip_address?: string;
  user_agent?: string;
  event_data: Record<string, any>;
  location: {
    country?: string;
    region?: string;
    city?: string;
    latitude?: number;
    longitude?: number;
  };
  is_blocked: boolean;
  response_action?: string;
  metadata: Record<string, any>;
}

export interface EnhancedDeviceSession {
  id: string;
  created_at: string;
  updated_at: string;
  profile_id: string;
  device_fingerprint: string;
  hardware_fingerprint?: string;
  browser_fingerprint?: string;
  device_info: {
    browser?: string;
    os?: string;
    device_type?: string;
    screen_resolution?: string;
    timezone?: string;
    language?: string;
    platform?: string;
    device_memory?: number;
    cpu_cores?: number;
  };
  is_active: boolean;
  is_approved: boolean;
  approved_by?: string;
  approved_at?: string;
  device_name?: string;
  trust_score: number;
  last_activity: string;
  ip_address?: string;
  user_agent?: string;
  session_token?: string;
}

export interface VideoAccessToken {
  id: string;
  created_at: string;
  video_id: string;
  user_id: string;
  session_id?: string;
  access_token: string;
  expires_at: string;
  max_uses: number;
  current_uses: number;
  ip_restrictions: string[];
  device_restrictions: string[];
  is_revoked: boolean;
  revoked_at?: string;
  revoked_reason?: string;
  metadata: Record<string, any>;
}

export interface DRMKey {
  id: string;
  created_at: string;
  video_id: string;
  key_id: string;
  encryption_key: string;
  drm_provider: 'vdocipher' | 'aws_elemental' | 'custom';
  provider_video_id?: string;
  provider_otp?: string;
  provider_playback_info: Record<string, any>;
  expires_at?: string;
  is_active: boolean;
  metadata: Record<string, any>;
}

export interface SecurityPolicy {
  id: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  policy_name: string;
  policy_type: 'device_management' | 'video_protection' | 'exam_security' | 'general';
  settings: {
    // Device Management Settings
    max_devices_per_user?: number;
    device_approval_required?: boolean;
    auto_approve_trusted_devices?: boolean;
    device_trust_threshold?: number;
    session_timeout_minutes?: number;
    concurrent_session_limit?: number;
    
    // Video Protection Settings
    drm_enabled?: boolean;
    watermark_enabled?: boolean;
    watermark_template_id?: string;
    screenshot_protection?: boolean;
    recording_protection?: boolean;
    right_click_disabled?: boolean;
    developer_tools_blocked?: boolean;
    
    // Exam Security Settings
    lockdown_browser_required?: boolean;
    fullscreen_enforcement?: boolean;
    tab_switching_blocked?: boolean;
    copy_paste_disabled?: boolean;
    proctoring_enabled?: boolean;
    webcam_required?: boolean;
    microphone_required?: boolean;
    
    // General Security Settings
    ip_whitelist?: string[];
    geo_restrictions?: string[];
    time_restrictions?: {
      start_time: string;
      end_time: string;
      timezone: string;
    };
    suspicious_activity_threshold?: number;
    auto_block_suspicious_users?: boolean;
  };
  is_active: boolean;
  created_by?: string;
  updated_by?: string;
}

export interface ExamSession {
  id: string;
  created_at: string;
  updated_at: string;
  student_id: string;
  exam_id?: string;
  lesson_id?: string;
  batch_id?: string;
  session_token: string;
  device_fingerprint: string;
  start_time: string;
  end_time?: string;
  expected_duration?: number; // in minutes
  is_active: boolean;
  is_lockdown_active: boolean;
  lockdown_violations: Array<{
    type: string;
    timestamp: string;
    description: string;
    severity: string;
  }>;
  proctoring_data: {
    webcam_enabled?: boolean;
    microphone_enabled?: boolean;
    screen_recording?: boolean;
    face_detection_events?: Array<{
      timestamp: string;
      faces_detected: number;
      confidence: number;
    }>;
    audio_events?: Array<{
      timestamp: string;
      event_type: string;
      confidence: number;
    }>;
  };
  security_events: string[]; // Array of security event IDs
  final_score?: number;
  is_completed: boolean;
  metadata: Record<string, any>;
}

export interface WatermarkTemplate {
  id: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  name: string;
  template_type: 'text' | 'image' | 'qr_code';
  template_data: {
    // Text watermark
    text?: string;
    font_family?: string;
    font_size?: number;
    font_weight?: string;
    color?: string;
    
    // Image watermark
    image_url?: string;
    image_opacity?: number;
    
    // QR Code watermark
    qr_data?: string;
    qr_size?: number;
    
    // Dynamic variables
    variables?: Array<{
      name: string;
      source: 'user' | 'session' | 'video' | 'timestamp';
      field: string;
    }>;
  };
  position_rules: {
    position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center' | 'random';
    offset_x?: number;
    offset_y?: number;
    rotation?: number;
    movement?: {
      enabled: boolean;
      speed: number;
      pattern: 'linear' | 'circular' | 'random';
    };
  };
  style_settings: {
    opacity: number;
    blend_mode?: string;
    shadow?: {
      enabled: boolean;
      color: string;
      blur: number;
      offset_x: number;
      offset_y: number;
    };
  };
  is_active: boolean;
  created_by?: string;
}

export interface SecurityAlert {
  id: string;
  type: 'device_violation' | 'exam_violation' | 'content_violation' | 'access_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  user_id?: string;
  organization_id: string;
  event_ids: string[];
  is_acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
  auto_resolved: boolean;
  resolution_action?: string;
  created_at: string;
  metadata: Record<string, any>;
}

export interface DeviceFingerprint {
  // Browser fingerprint
  user_agent: string;
  screen_resolution: string;
  color_depth: number;
  timezone: string;
  language: string;
  platform: string;
  cookie_enabled: boolean;
  do_not_track: boolean;
  
  // Hardware fingerprint
  device_memory?: number;
  hardware_concurrency?: number;
  max_touch_points?: number;
  
  // Canvas fingerprint
  canvas_fingerprint?: string;
  webgl_fingerprint?: string;
  
  // Audio fingerprint
  audio_fingerprint?: string;
  
  // Font fingerprint
  available_fonts?: string[];
  
  // Plugin fingerprint
  plugins?: Array<{
    name: string;
    version: string;
  }>;
  
  // Network fingerprint
  connection_type?: string;
  effective_type?: string;
  
  // Composite fingerprint
  composite_hash: string;
  confidence_score: number;
}

export interface SecurityConfiguration {
  organization_id: string;
  video_security: {
    drm_provider: 'vdocipher' | 'aws_elemental' | 'custom' | 'none';
    drm_settings: Record<string, any>;
    watermark_enabled: boolean;
    watermark_template_id?: string;
    token_expiry_minutes: number;
    max_concurrent_streams: number;
  };
  device_security: {
    max_devices_per_user: number;
    device_approval_required: boolean;
    trust_score_threshold: number;
    session_timeout_minutes: number;
  };
  exam_security: {
    lockdown_browser_enabled: boolean;
    proctoring_enabled: boolean;
    violation_threshold: number;
    auto_submit_on_violation: boolean;
  };
  monitoring: {
    real_time_alerts: boolean;
    alert_webhooks: string[];
    retention_days: number;
    auto_block_threshold: number;
  };
}

export interface SecurityMetrics {
  total_events: number;
  events_by_severity: Record<string, number>;
  events_by_type: Record<string, number>;
  blocked_attempts: number;
  active_sessions: number;
  suspicious_activities: number;
  device_violations: number;
  exam_violations: number;
  top_threats: Array<{
    type: string;
    count: number;
    last_occurrence: string;
  }>;
  security_score: number;
  trends: {
    daily_events: Array<{
      date: string;
      count: number;
    }>;
    weekly_summary: {
      total_events: number;
      critical_events: number;
      blocked_attempts: number;
    };
  };
}
