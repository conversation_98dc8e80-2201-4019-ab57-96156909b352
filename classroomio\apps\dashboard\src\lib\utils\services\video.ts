// Video Management Services
// Date: 2025-06-30

import { supabase } from '$lib/utils/functions/supabase';
import type { 
  VideoContent, 
  VideoProgress, 
  DownloadItem, 
  VideoUpload,
  VideoAnalytics
} from '$lib/utils/types/batch';

// Video Progress Service
export const videoProgressService = {
  // Get video progress for a student
  async getVideoProgress(videoId: string, studentId: string): Promise<VideoProgress | null> {
    const { data, error } = await supabase
      .from('video_progress')
      .select('*')
      .eq('video_id', videoId)
      .eq('student_id', studentId)
      .single();

    if (error && error.code !== 'PGRST116') throw error; // Ignore "not found" errors
    return data;
  },

  // Update video progress
  async updateVideoProgress(progress: {
    video_id: string;
    student_id: string;
    lesson_id?: string;
    current_time: number;
    duration: number;
    watch_time?: number;
    engagement_data?: any;
  }): Promise<void> {
    const { error } = await supabase
      .rpc('update_video_progress', {
        p_video_id: progress.video_id,
        p_student_id: progress.student_id,
        p_lesson_id: progress.lesson_id,
        p_current_time: progress.current_time,
        p_duration: progress.duration,
        p_engagement_data: progress.engagement_data || null
      });

    if (error) throw error;
  },

  // Get progress for multiple videos (for analytics)
  async getBatchVideoProgress(videoIds: string[], studentId?: string): Promise<VideoProgress[]> {
    let query = supabase
      .from('video_progress')
      .select('*')
      .in('video_id', videoIds);

    if (studentId) {
      query = query.eq('student_id', studentId);
    }

    const { data, error } = await query.order('last_watched_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Get student's overall progress summary
  async getStudentProgressSummary(studentId: string, batchId?: string): Promise<{
    total_videos: number;
    completed_videos: number;
    total_watch_time: number;
    completion_rate: number;
  }> {
    let query = supabase
      .from('video_progress')
      .select(`
        *,
        video_content!inner(
          lesson!inner(
            lesson_section!inner(
              chapter!inner(
                subject!inner(batch_id)
              )
            )
          )
        )
      `)
      .eq('student_id', studentId);

    if (batchId) {
      query = query.eq('video_content.lesson.lesson_section.chapter.subject.batch_id', batchId);
    }

    const { data, error } = await query;

    if (error) throw error;

    const totalVideos = data?.length || 0;
    const completedVideos = data?.filter(p => p.is_completed).length || 0;
    const totalWatchTime = data?.reduce((sum, p) => sum + (p.watch_time || 0), 0) || 0;
    const completionRate = totalVideos > 0 ? (completedVideos / totalVideos) * 100 : 0;

    return {
      total_videos: totalVideos,
      completed_videos: completedVideos,
      total_watch_time: totalWatchTime,
      completion_rate: completionRate
    };
  }
};

// Download Management Service
export const downloadService = {
  // Get download queue for a student
  async getDownloadQueue(studentId: string): Promise<DownloadItem[]> {
    const { data, error } = await supabase
      .from('download_queue')
      .select(`
        *,
        video_content(title, duration, file_size)
      `)
      .eq('student_id', studentId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Add video to download queue
  async addToDownloadQueue(videoId: string, studentId: string, quality: string = 'auto'): Promise<DownloadItem> {
    // Check if already in queue
    const { data: existing } = await supabase
      .from('download_queue')
      .select('id')
      .eq('video_id', videoId)
      .eq('student_id', studentId)
      .eq('status', 'pending')
      .single();

    if (existing) {
      throw new Error('Video is already in download queue');
    }

    // Generate download token and expiration
    const downloadToken = crypto.randomUUID();
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiration

    const { data, error } = await supabase
      .from('download_queue')
      .insert({
        video_id: videoId,
        student_id: studentId,
        quality: quality,
        download_token: downloadToken,
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update download progress
  async updateDownloadProgress(downloadId: string, progress: number, status?: string): Promise<void> {
    const updates: any = { progress, updated_at: new Date().toISOString() };
    if (status) updates.status = status;

    const { error } = await supabase
      .from('download_queue')
      .update(updates)
      .eq('id', downloadId);

    if (error) throw error;
  },

  // Mark download as completed
  async completeDownload(downloadId: string, localPath: string): Promise<void> {
    const { error } = await supabase
      .from('download_queue')
      .update({
        status: 'completed',
        progress: 100,
        downloaded_at: new Date().toISOString(),
        local_path: localPath,
        updated_at: new Date().toISOString()
      })
      .eq('id', downloadId);

    if (error) throw error;
  },

  // Remove from download queue
  async removeFromQueue(downloadId: string): Promise<void> {
    const { error } = await supabase
      .from('download_queue')
      .delete()
      .eq('id', downloadId);

    if (error) throw error;
  },

  // Clean expired downloads
  async cleanExpiredDownloads(): Promise<void> {
    const { error } = await supabase
      .from('download_queue')
      .update({ status: 'expired' })
      .lt('expires_at', new Date().toISOString())
      .in('status', ['pending', 'downloading']);

    if (error) throw error;
  }
};

// Video Content Management Service
export const videoContentService = {
  // Get video content with progress for a student
  async getVideoWithProgress(videoId: string, studentId?: string): Promise<VideoContent & { progress?: VideoProgress }> {
    const { data: video, error: videoError } = await supabase
      .from('video_content')
      .select('*')
      .eq('id', videoId)
      .single();

    if (videoError) throw videoError;

    let progress = null;
    if (studentId) {
      progress = await videoProgressService.getVideoProgress(videoId, studentId);
    }

    return { ...video, progress };
  },

  // Create new video content
  async createVideoContent(video: Omit<VideoContent, 'id' | 'created_at' | 'updated_at'>): Promise<VideoContent> {
    const { data, error } = await supabase
      .from('video_content')
      .insert(video)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update video content
  async updateVideoContent(videoId: string, updates: Partial<VideoContent>): Promise<VideoContent> {
    const { data, error } = await supabase
      .from('video_content')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', videoId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete video content
  async deleteVideoContent(videoId: string): Promise<void> {
    const { error } = await supabase
      .from('video_content')
      .delete()
      .eq('id', videoId);

    if (error) throw error;
  },

  // Get videos for a lesson
  async getLessonVideos(lessonId: string, studentId?: string): Promise<(VideoContent & { progress?: VideoProgress })[]> {
    const { data: videos, error } = await supabase
      .from('video_content')
      .select('*')
      .eq('lesson_id', lessonId)
      .order('created_at', { ascending: true });

    if (error) throw error;

    if (!studentId) return videos || [];

    // Get progress for all videos
    const videoIds = videos?.map(v => v.id) || [];
    const progressData = await videoProgressService.getBatchVideoProgress(videoIds, studentId);
    const progressMap = new Map(progressData.map(p => [p.video_id, p]));

    return (videos || []).map(video => ({
      ...video,
      progress: progressMap.get(video.id)
    }));
  }
};

// Video Upload Service
export const videoUploadService = {
  // Create upload record
  async createUpload(upload: Omit<VideoUpload, 'id' | 'created_at' | 'updated_at'>): Promise<VideoUpload> {
    const { data, error } = await supabase
      .from('video_upload')
      .insert(upload)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update upload progress
  async updateUploadProgress(uploadId: string, progress: number, status?: string): Promise<void> {
    const updates: any = { upload_progress: progress, updated_at: new Date().toISOString() };
    if (status) updates.upload_status = status;

    const { error } = await supabase
      .from('video_upload')
      .update(updates)
      .eq('id', uploadId);

    if (error) throw error;
  },

  // Update processing progress
  async updateProcessingProgress(uploadId: string, progress: number): Promise<void> {
    const { error } = await supabase
      .from('video_upload')
      .update({
        processing_progress: progress,
        updated_at: new Date().toISOString()
      })
      .eq('id', uploadId);

    if (error) throw error;
  },

  // Complete upload
  async completeUpload(uploadId: string, videoContentId: string): Promise<void> {
    const { error } = await supabase
      .from('video_upload')
      .update({
        upload_status: 'completed',
        video_content_id: videoContentId,
        upload_progress: 100,
        processing_progress: 100,
        updated_at: new Date().toISOString()
      })
      .eq('id', uploadId);

    if (error) throw error;
  },

  // Get uploads for a user
  async getUserUploads(userId: string): Promise<VideoUpload[]> {
    const { data, error } = await supabase
      .from('video_upload')
      .select('*')
      .eq('uploaded_by', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
};
