@echo off

:: ClassroomIO Docker Stop Script
:: Stops all Docker development services

echo ========================================
echo   ClassroomIO Docker Stop
echo ========================================
echo.

echo Stopping ClassroomIO Docker development environment...
echo.

:: Check if Dock<PERSON> is running
docker info >nul 2>&1
if errorlevel 1 (
    echo WARNING: Docker is not running or not accessible
    echo The containers may already be stopped.
    echo.
    goto :cleanup
)

:: Stop and remove containers
echo [1/3] Stopping containers...
docker-compose -f docker-compose.dev.yml down

if errorlevel 1 (
    echo WARNING: Some containers may not have stopped cleanly
) else (
    echo SUCCESS: Containers stopped successfully
)
echo.

:: Optional: Remove volumes (uncomment if you want to reset data)
:: echo [2/3] Removing volumes...
:: docker-compose -f docker-compose.dev.yml down -v
:: echo SUCCESS: Volumes removed

:: Show remaining containers (if any)
echo [2/3] Checking for remaining containers...
docker ps -a --filter "name=classroomio" --format "table {{.Names}}\t{{.Status}}"

echo.

:: Clean up unused resources
echo [3/3] Cleaning up unused Docker resources...
docker system prune -f >nul 2>&1
echo SUCCESS: Cleanup completed

:cleanup
echo.
echo ========================================
echo   ClassroomIO Docker Environment Stopped
echo ========================================
echo.
echo All ClassroomIO services have been stopped.
echo.
echo To restart the development environment:
echo   start-docker.bat
echo.
echo To completely reset (remove all data):
echo   docker-compose -f docker-compose.dev.yml down -v
echo   docker system prune -a
echo.

echo Press any key to exit...
pause >nul

exit /b 0
